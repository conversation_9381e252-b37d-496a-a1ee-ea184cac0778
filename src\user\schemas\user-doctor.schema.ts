import * as mongoose from "mongoose";

import { USER_COLLECTION_NAME, USER_DOCTOR_COLLECTION_NAME } from "./constants";
import { v4 as uuidv4 } from "uuid";
import { HOSPITAL_COLLECTION_NAME } from "../../hospital-mongo/schemas/constants";
import { DOCTOR_COLLECTION_NAME } from "../../doctor-mongo/schemas/constants";

const schema = mongoose.Schema;

export const UserDoctorSchema = new schema(
    {
        id: { type: String, default: () => uuidv4().replace(/-/g, "") },
        partnerId: { type: String },
        partner: { type: schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
        userId: { type: schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
        email: { type: String },
        doctorId: { type: String },
        doctor: { type: schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME }
    },
    {
        collection: USER_DOCTOR_COLLECTION_NAME,
        timestamps: true
    }
);
