import { Module, HttpModule } from '@nestjs/common';
import { ReExamController } from './re-exam.controller';
import { ReExamService } from './re-exam.service';
import { MongooseModule } from '@nestjs/mongoose';
import { RE_EXAM_COLLECTION_NAME, RE_EXAM_VERIFY_COLLECTION_NAME } from './schemas/constants';
import { ReExamSchema } from './schemas/re-exam.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { PatientMongoService } from 'src/patient-mongo/patient-mongo.service';
import {
    PATIENT_COLLECTION_NAME,
    PATIENT_CODE_COLLECTION_NAME,
    PATIENT_SEARCH_LOG_COLLECTION_NAME,
    PATIENT_VERSION_COLLECTION_NAME,
    RELATIVE_TYPE_COLLECTION_NAME,
    PATIENT_PROFILE_COLLECTION_NAME,
} from 'src/patient-mongo/schemas/constants';
import { PatientSchema } from 'src/patient-mongo/schemas/patient.schema';
import {
    USER_COLLECTION_NAME,
    SIGNIN_PROVIDER_COLLECTION_NAME,
    CONSTRAINTS_PROVIDER_COLLECTION_NAME,
    CONSTRAINTS_USER_COLLECTION_NAME,
    USER_PROFILE_COLLECTION_NAME,
    HOC_VI_COLLECTION_NAME,
    VI_TRI_CONG_VIEC_COLLECTION_NAME,
    ORG_PROFILE_COLLECTION_NAME,
    REFERRAL_CODE_REGISTER_COLLECTION_NAME,
    USER_APP_COLLECTION_NAME,
    USER_REQUEST_COLLECTION_NAME,
} from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { CountrySchema } from 'src/country-mongo/schemas/country.schema';
import { NationSchema } from 'src/nation-mongo/schemas/nation.schema';
import { NATION_COLLECTION_NAME } from 'src/nation-mongo/schemas/constants';
import { PROFESSION_COLLECTION_NAME } from 'src/profession-mongo/schemas/constants';
import { ProfessionSchema } from 'src/profession-mongo/schemas/profession.schema';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { CitySchema } from 'src/city-mongo/schemas/city.schema';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { DistrictSchema } from 'src/district-mongo/schemas/district.schema';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { WardSchema } from 'src/ward-mongo/schemas/ward.schema';
import { PatientService } from 'src/patient/patient.service';
import { UserService } from 'src/user/user.service';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { LocalUserStrategy } from 'src/user/local.user.strategy';
import { UserJwtStrategy } from 'src/user/jwt.strategy';
import { SessionService } from 'src/session/session.service';
import { PhoneLoginService } from 'src/phone-login/phone-login.service';
import { SmsService } from 'src/sms/sms.service';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { ServiceSchema } from 'src/service-mongo/schemas/service.schema';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { SubjectSchema } from 'src/subject-mongo/schemas/subject.schema';
import { ROOM_COLLECTION_NAME } from 'src/room-mongo/schemas/constants';
import { RoomSchema } from 'src/room-mongo/schemas/room.schema';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { DoctorSchema } from 'src/doctor-mongo/schemas/doctor.schema';
import { PatientCodeSchema } from 'src/patient-mongo/schemas/patient-codes.schema';
import { PatientSearchLogSchema } from 'src/patient-mongo/schemas/patient-search-log.schema';
import { PatientVersionSchema } from 'src/patient-mongo/schemas/patient-version.schema';
import { SignInProviderSchema } from 'src/user/schemas/signin-provider.schema';
import { RelativeSchema } from 'src/patient-mongo/schemas/relative-mongo.schema';
import { ConstraintsSchema } from 'src/booking-gateway/schemas/constraints.schema';
import {
    CONSTRAINTS_COLLECTION_NAME,
    BOOKING_SEARCH_COLLECTION_NAME,
    BOOKING_COLLECTION_NAME,
    SECTION_COLLECTION_NAME,
    BOOKING_ORDER_COLLECTION_NAME,
    NEW_BILL_LOG_COLLECTION_NAME,
} from 'src/booking-gateway/schemas/constants';
import { ProviderConstraintSchema } from 'src/user/schemas/provider-constraints.schema';
import { UserConstraintSchema } from 'src/user/schemas/user-constraints.schema';
import { UserProfileSchema } from 'src/user/schemas/user-profile.schema';
import { BookingSearchSchema } from 'src/booking-gateway/schemas/search-booking.schema';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { USER_ACCOUNT_COLLECTION_NAME } from 'src/user-account/schemas/constants';
import { UserAccountSchema } from 'src/user-account/schemas/user-account.schema';
import { FilesService } from 'src/files/files.service';
import { HocViSchema } from 'src/user/schemas/hoc-vi.schema';
import { ViTriCongViecSchema } from 'src/user/schemas/vi-tri-cong-viec.schema';
import { ReferralCodeSchema } from 'src/referral-code/schemas/referral-code.schema';
import { REFERRAL_CODE_COLLECTION_NAME } from 'src/referral-code/schemas/constants';
import { ReferralCodeService } from 'src/referral-code/referral-code.service';
import { SYNC_DALIEU_PATIENT, SYNC_DALIEU_BOOKING, SYNC_NHI_DONG_1_PATIENT, SYNC_NHI_DONG_1_BOOKING, SYNC_DHYD_PATIENT, SYNC_DHYD_BOOKING } from 'src/event/schemas/constants';
import { SyncDaLieuPatientSchema } from 'src/event/schemas/sync-dalieu-patient.schema';
import { SyncDaLieuBookingSchema } from 'src/event/schemas/sync-dalieu-booking.schema';
import { OrgProfileSchema } from 'src/user/schemas/org-profile.schema';
import { SyncNhiDong1PatientSchema } from 'src/event/schemas/sync-nhidong1-patient.schema';
import { SyncNhiDong1BookingSchema } from 'src/event/schemas/sync-nhidong1-booking.schema';
import { SectionSchema } from 'src/booking-gateway/schemas/section.schema';
import { ReferralCodeRegisterSchema } from 'src/user/schemas/referral-code-register';
import { PatientProfileSchema } from 'src/patient-mongo/schemas/patient-profile.schema';
import { SyncDHYDPatientSchema } from 'src/event/schemas/sync-dhyd-patient.schema';
import { SyncDHYDBookingSchema } from 'src/event/schemas/sync-dhyd-booking.schema';
import { BookingOrderSchema } from 'src/booking-gateway/schemas/booking-order';
import { NewBillLogSchema } from 'src/booking-gateway/schemas/new-bill-log.schema';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { UserAppSchema } from 'src/user/schemas/user-app.schema';
import { UserRequestsSchema } from 'src/user/schemas/user-requests.schema';
import { PatientMongoModule } from '../patient-mongo/patient-mongo.module';
import { UserModule } from 'src/user/user.module';
import { ReExamVerifySchema } from './schemas/re-exam-verify.schema';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'user-jwt' }),
    JwtModule.registerAsync({
      useExisting: JwtUserConfigService,
    }),
    HttpModule,
    MongooseModule.forFeature([
      { name: RE_EXAM_COLLECTION_NAME, schema: ReExamSchema },
      { name: PATIENT_CODE_COLLECTION_NAME, schema: PatientCodeSchema },
      { name: PATIENT_SEARCH_LOG_COLLECTION_NAME, schema: PatientSearchLogSchema },
      { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
      { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
      { name: PATIENT_VERSION_COLLECTION_NAME, schema: PatientVersionSchema },
      { name: USER_COLLECTION_NAME, schema: UserSchema },
      { name: SIGNIN_PROVIDER_COLLECTION_NAME, schema: SignInProviderSchema },
      { name: COUNTRY_COLLECTION_NAME, schema: CountrySchema },
      { name: NATION_COLLECTION_NAME, schema: NationSchema },
      { name: PROFESSION_COLLECTION_NAME, schema: ProfessionSchema },
      { name: CITY_COLLECTION_NAME, schema: CitySchema },
      { name: DISTRICT_COLLECTION_NAME, schema: DistrictSchema },
      { name: WARD_COLLECTION_NAME, schema: WardSchema },
      { name: SERVICE_COLLECTION_NAME, schema: ServiceSchema },
      { name: SECTION_COLLECTION_NAME, schema: SectionSchema },
      { name: SUBJECT_COLLECTION_NAME, schema: SubjectSchema },
      { name: ROOM_COLLECTION_NAME, schema: RoomSchema },
      { name: DOCTOR_COLLECTION_NAME, schema: DoctorSchema },
      { name: RELATIVE_TYPE_COLLECTION_NAME, schema: RelativeSchema },
      { name: CONSTRAINTS_COLLECTION_NAME, schema: ConstraintsSchema },
      { name: CONSTRAINTS_PROVIDER_COLLECTION_NAME, schema: ProviderConstraintSchema },
      { name: CONSTRAINTS_USER_COLLECTION_NAME, schema: UserConstraintSchema },
      { name: USER_PROFILE_COLLECTION_NAME, schema: UserProfileSchema },
      { name: ORG_PROFILE_COLLECTION_NAME, schema: OrgProfileSchema },
      { name: BOOKING_SEARCH_COLLECTION_NAME, schema: BookingSearchSchema },
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
      { name: USER_ACCOUNT_COLLECTION_NAME, schema: UserAccountSchema },
      { name: HOC_VI_COLLECTION_NAME, schema: HocViSchema },
      { name: VI_TRI_CONG_VIEC_COLLECTION_NAME, schema: ViTriCongViecSchema },
      { name: REFERRAL_CODE_REGISTER_COLLECTION_NAME, schema: ReferralCodeRegisterSchema },
      { name: REFERRAL_CODE_COLLECTION_NAME, schema: ReferralCodeSchema },
      { name: SYNC_DALIEU_PATIENT, schema: SyncDaLieuPatientSchema },
      { name: SYNC_DALIEU_BOOKING, schema: SyncDaLieuBookingSchema },
      { name: SYNC_NHI_DONG_1_PATIENT, schema: SyncNhiDong1PatientSchema },
      { name: SYNC_NHI_DONG_1_BOOKING, schema: SyncNhiDong1BookingSchema },
      { name: SYNC_DHYD_PATIENT, schema: SyncDHYDPatientSchema },
      { name: SYNC_DHYD_BOOKING, schema: SyncDHYDBookingSchema },
      { name: PATIENT_PROFILE_COLLECTION_NAME, schema: PatientProfileSchema },
      { name: BOOKING_ORDER_COLLECTION_NAME, schema: BookingOrderSchema },
      { name: NEW_BILL_LOG_COLLECTION_NAME, schema: NewBillLogSchema },
      { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
      { name: USER_APP_COLLECTION_NAME, schema: UserAppSchema },
      { name: USER_REQUEST_COLLECTION_NAME, schema: UserRequestsSchema },
        { name: RE_EXAM_VERIFY_COLLECTION_NAME, schema: ReExamVerifySchema },
    ]),
    PatientMongoModule,
    UserModule,
  ],
  controllers: [ReExamController],
  providers: [
    ReExamService, PatientService, FilesService,
    LocalUserStrategy, UserJwtStrategy, SessionService, PhoneLoginService, SmsService, ReferralCodeService, GlobalSettingService],
})
export class ReExamModule { }
