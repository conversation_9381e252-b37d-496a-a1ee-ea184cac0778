import { <PERSON>, Get, Param, Headers, UseGuards, Req, Post, Body, Query } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { UserAccountService } from './user-account.service';
import { AuthGuard } from '@nestjs/passport';
import { PayHospitalFeeDTO } from './dto/pay-hospital-fee.dto';
import { WithdrawalDTO } from './dto/withdrawal.dto';
import { DepositDTO } from './dto/deposit.dto';
import { AdminUserLoginDTO } from './dto/admin-user-login.dto';
import { SearchPatientDTO } from './dto/search-patient.dto';
import { SearchPatientExtraInfoMongoDTO } from 'src/patient/dto/search-patient-extra-info-mongo.dto';
import { LinkMSBNDTO } from './dto/link-msbn.dto';
import { LinkCardIdDTO } from './dto/link-card-id.dto';
import { CreatePINDTO } from './dto/create-pin.dto';
import { SearchPatientInfoMongoDTO } from 'src/patient/dto/search-patient-info-mongo.dto';
import { CheckLinkMSBNDTO } from './dto/check-link-msbn.dto';
import { CheckLinkCardDTO } from './dto/check-link-card.dto';
import { GetHospitalFeeDTO } from './dto/get-hospital-fee.dto';
import { GetCardInfoDTO } from './dto/get-card-info.dto';
import { PaymentFeeByCardDTO } from './dto/payment-fee-card.dto';
import { KiotGetCardInfoDTO } from './dto/kiot-get-card-info';
import { CheckDepositBalanceGatewayDTO } from 'src/booking-gateway/dto/check-deposit-balance-gateway.dto';
import { KiotCreatePINDTO } from './dto/kiot-create-pin.dto';
import { KiotChangePINDTO } from './dto/kiot-change-pin';
import { GetBillInfoDTO } from './dto/get-bill-info.dto';

@Controller('v1/userAccount')
@ApiTags('user-account')
export class UserAccountController {

    constructor(
        private readonly service: UserAccountService,
    ) { }

    /* common */
    @Get('deposit-method')
    async depositMethod(): Promise<any> {
        return [
            {
                name: 'Chuyển khoản',
                value: 1,
            },
            {
                name: 'Tiền mặt',
                value: 2,
            },
        ];
    }

    @Post('card-info')
    async cardInfo(
        @Body() formData: GetCardInfoDTO,
    ): Promise<any> {
        return this.service.cardInfo(formData);
    }

    /* Dành cho kiot */
    @Post('kiot/card-info-short')
    async kiotCardInfoShort(
        @Body() formData: GetCardInfoDTO,
        @Headers('partnerid') partnerId: string,
    ): Promise<any> {
        return this.service.kiotCardInfoShort(partnerId, formData);
    }

    @Post('kiot/create-new-pin')
    async kiotCreateNewPIN(
        @Body() formData: KiotCreatePINDTO,
    ): Promise<any> {
        return this.service.kiotCreateNewPIN(formData);
    }

    @Post('kiot/card-info-login')
    async kiotCardInfo(
        @Body() formData: KiotGetCardInfoDTO,
        @Headers('partnerid') partnerId: string,
    ): Promise<any> {
        return this.service.kiotCardInfo(partnerId, formData);
    }

    @Post('kiot/change-pin')
    async kiotChangePIN(
        @Body() formData: KiotChangePINDTO,
    ): Promise<any> {
        return this.service.kiotChangePIN(formData);
    }

    /* Dành cho cán bộ y tế */

    @Post('login')
    async login(
        @Headers('partnerid') partnerId: string,
        @Body() formData: AdminUserLoginDTO,
    ): Promise<any> {
        return this.service.login(partnerId, formData);
    }

    @Post('update-admin-user-status')
    async updateAdminUserStatus(
        @Headers('partnerid') partnerId: string,
        @Body() formData: { email: string, userId: number, status: boolean, apiKey: string },
    ): Promise<any> {
        return this.service.updateAdminUserStatus(partnerId, formData);
    }

    @Get('can-bo-yte/info')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-cbyt-jwt'))
    async getUserInfo(@Req() req) {
        // console.log('userINfo req.user ', req.user);
        return req.user;
    }

    @Post('search-patient-extra')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-cbyt-jwt'))
    async searchPatientExtra(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: SearchPatientExtraInfoMongoDTO,
    ): Promise<any> {
        return this.service.searchPatientExtra(appId, partnerId, formData);
    }

    @Post('search-patient')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-cbyt-jwt'))
    async searchPatient(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: SearchPatientInfoMongoDTO,
    ): Promise<any> {
        return this.service.searchPatient(appId, partnerId, formData);
    }

    @Post('checkLinkMSBN')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-cbyt-jwt'))
    async checkLinkMSBN(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: CheckLinkMSBNDTO,
    ): Promise<any> {
        return this.service.checkLinkMSBN(appId, partnerId, formData);
    }

    @Post('checkLinkCardId')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-cbyt-jwt'))
    async checkLinkCardId(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: CheckLinkCardDTO,
    ): Promise<any> {
        return this.service.checkLinkCardId(appId, partnerId, formData);
    }

    @Post('linkMSBN')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-cbyt-jwt'))
    async linkMSBN(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: LinkMSBNDTO,
    ): Promise<any> {
        return this.service.linkMSBN(appId, partnerId, formData);
    }

    @Post('linkCardId')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-cbyt-jwt'))
    async linkCardId(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: LinkCardIdDTO,
    ): Promise<any> {
        return this.service.linkCardId(appId, partnerId, formData);
    }

    @Post('createPIN')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-cbyt-jwt'))
    async createPIN(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: CreatePINDTO,
    ): Promise<any> {
        return this.service.createPINWarning(appId, partnerId, formData);
    }

    @Post('detail-info')
    async detailInfo(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Query('id') id: string,
    ): Promise<any> {
        return this.service.detailInfo(appId, partnerId, id);
    }

    @Post('admin-create-deposit')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-cbyt-jwt'))
    async adminCreateDeposit(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: DepositDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        // const { userMongoId } = user;
        return this.service.adminCreateDeposit(appId, partnerId, formData);
    }

    /* Dành cho bệnh nhân */

    @Post('check-deposit-balance')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async checkDepositBalance(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: CheckDepositBalanceGatewayDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.checkDepositBalance(appId, partnerId, userMongoId, formData);
    }

    @Post('payment-fee-by-card')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async paymentFeeByCard(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: PaymentFeeByCardDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.paymentFeeByCard(appId, partnerId, userMongoId, formData);
    }

    @Get('transaction-tracking')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async transactionTracking(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        // @Body() formData: PaymentFeeByCardDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.transactionTracking(appId, partnerId, userMongoId);
    }

    @Get('get-hospistal-fee')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getHospitalFee(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        // @Body() formData: GetBillInfoDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getHospitalFee(appId, partnerId, userMongoId);
    }

    @Post('bill-info')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getBillInfo(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: GetBillInfoDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBillInfo(appId, partnerId, userMongoId, formData);
    }

    @Post('check-available')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async checkAvailable(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.checkAvailable(appId, partnerId, userMongoId);
    }

    @Get('createAccount/:patientCode')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async createAccount(
        @Param('patientCode') patientCode: string,
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        console.log('userINfo', user);
        const { userMongoId } = user;
        return this.service.createAccount(appId, partnerId, userMongoId, patientCode);
    }

    @Get('getDetail')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getDetail(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getDetail(appId, partnerId, userMongoId);
    }

    @Get('getBalance/:patientCode')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getBalance(
        @Param('patientCode') patientCode: string,
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBalance(appId, partnerId, userMongoId, patientCode);
    }

    @Get('getTransaction/:patientCode')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getTransactions(
        @Param('patientCode') patientCode: string,
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getTransactions(appId, partnerId, userMongoId, patientCode);
    }

    @Get('getTransactionDetail/:accountId/:transactionId')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getTransactionDetail(
        @Param('accountId') accountId: number,
        @Param('transactionId') transactionId: number,
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getTransactionDetail(appId, partnerId, userMongoId, accountId, transactionId);
    }

    // @Post('payHospitalFee')
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    // async payHospitalFee(
    //     @Headers('appid') appId: string,
    //     @Headers('partnerid') partnerId: string,
    //     @Body() formData: PayHospitalFeeDTO,
    //     @Req() req,
    // ): Promise<any> {
    //     const user = req.user;
    //     const { userMongoId } = user;
    //     return this.service.payHospitalFee(appId, partnerId, userMongoId, formData);
    // }

    @Post('withdrawal')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async withdrawal(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: WithdrawalDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.withdrawal(appId, partnerId, userMongoId, formData);
    }

    @Post('deposit')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async deposit(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: DepositDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.deposit(appId, partnerId, userMongoId, formData);
    }

}
