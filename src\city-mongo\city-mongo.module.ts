import { Module } from '@nestjs/common';
import { CityMongoController } from './city-mongo.controller';
import { CityMongoService } from './city-mongo.service';
import { MongooseModule } from '@nestjs/mongoose';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { CountrySchema } from 'src/country-mongo/schemas/country.schema';
import { CITY_COLLECTION_NAME } from './schemas/constants';
import { CitySchema } from './schemas/city.schema';
import { TranslateModule } from '../translate/translate.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: CITY_COLLECTION_NAME, schema: CitySchema },
      { name: COUNTRY_COLLECTION_NAME, schema: CountrySchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
    ]),
    TranslateModule,
  ],
  controllers: [CityMongoController],
  providers: [CityMongoService],
})
export class CityMongoModule { }
