import { ApiProperty } from '@nestjs/swagger';
import { IsMobilePhone, IsNotEmpty, IsEnum, ValidateIf } from 'class-validator';
import { Transform } from 'class-transformer';

export class PhoneRegisterDTO {

    @ApiProperty({
        description: 'Fullname',
        required: true,
        type: String,
    })
    readonly fullname: string;

    @ApiProperty({
        description: 'Số điện thoại',
        required: true,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập số điện thoại',
    })
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Số điện thoại không hợp lệ.',
    })
    readonly phone: string;

    @ApiProperty({
        description: 'Device Id',
        required: true,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: '<PERSON><PERSON> lòng g<PERSON>i lên deviceId',
    })
    readonly deviceId: string;

    @ApiProperty({
        description: 'type',
        required: false,
        type: String,
        enum: [
            'password', 'zalo', 'firebase',
        ],
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @IsEnum({
        password: 'password',
        zalo: 'zalo',
        firebase: 'firebase',
    }, {
        message: 'Vui lòng gửi lên đúng thông tin [password, zalo, firebase] ',
    })
    @ValidateIf(o => o.type)
    readonly type: string;

    captchaResponse?: string;
    captchaType?: string;
}
