import * as jsonMongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';
import { BOOKING_COMPLAINS_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const BookingComplainSchema = new Schema({
    id: { type: String },
    transactionId: { type: String },
    complain: { type: String },
    userId: { type: String },
    bookingId: { type: String },
}, {
    collection: BOOKING_COMPLAINS_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
