import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsMobilePhone, IsNotEmpty, MaxLength } from 'class-validator';

export class VerifyOTPDto {
    @ApiProperty({ description: 'Số điện thoại', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập số điện thoại!' })
    @IsMobilePhone('vi-VN', { strictMode: false }, { message: 'Số điện thoại gửi lên không đúng định dạng!' })
    readonly phone: string;

    @ApiProperty({ description: 'Vui lòng nhập partnerId', required: true, type: String })
    @IsNotEmpty({ message: 'partnerId is not empty' })
    readonly partnerId: string;

    @ApiProperty({ description: 'Vui lòng nhập appId', required: true, type: String })
    @IsNotEmpty({ message: 'appId is not empty' })
    readonly appId: string;

    @ApiProperty({ description: 'Vui lòng nhập otpToken', required: true, type: String })
    @IsNotEmpty({ message: 'otpToken is not empty' })
    @IsIn([6], { message: 'OTP bao gồm 6 kí tự' })
    readonly otpCode: string;
}
