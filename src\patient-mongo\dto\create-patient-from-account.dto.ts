import { IsDateString, IsEmail, IsNotEmpty, Validate, ValidateIf } from 'class-validator';
import { Transform } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { CheckBirthDateSignInProvider } from '../../user/dto/checkBirthDateSignInProvider';

export class CreatePatientFromAccountDto {
    @IsNotEmpty()
    @Transform(v => `${v.trim()}`)
    mobile: string;

    @IsNotEmpty()
    @Transform(v => `${v.trim()}`)
    fullname: string;


    @ApiProperty({
        description: 'Năm sinh',
        required: false,
        type: Number,
    })
    @Transform(value => Number(value))
    readonly birthyear?: number;

    @ApiProperty({
        description: 'Ngày tháng năm sinh',
        required: false,
        type: String,
    })
    @IsDateString({ strict: true }, { message: 'Ng<PERSON>y sinh theo ISOString' })
    @Validate(CheckBirthDateSignInProvider, [1900], {
        message: 'Thông tin ngày sinh không hợp lệ.',
    })
    @ValidateIf(o => o.birthdate)
    readonly birthdate?: string;

    @ApiProperty({
        description: 'Giới tính',
        required: false,
        type: Number,
    })
    @Transform(value => Number(value))
    readonly sex?: number;

    @ApiProperty({
        description: 'Email',
        required: false,
        type: String,
    })
    @IsEmail({}, { message: 'Email không đúng định dạng' })
    @ValidateIf(o => o.email)
    @Transform(value => `${value}`.trim())
    readonly email?: string;

    user: string;

    source?: string;
}
