import { Document } from 'mongoose';

export interface IService extends Document {
    id?: string;
    readonly code: string;
    readonly name: string;
    readonly shortName: string;
    readonly partnerId: string;
    readonly description: string;
    price?: number;
    originalPrice?: number;
    hospitalId?: string;
    createTime?: Date;
    searchUnicode?: string;
    serviceType?: string;
    isRequiredCheckInsurance?: boolean;
    type: string;
    addonServiceIds?: string[];
    daysOff: string;
}
