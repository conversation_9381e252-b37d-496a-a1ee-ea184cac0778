import { ErrorMessage } from './../common/enums/message-error.enum';
import { IUser } from './../user/interfaces/user.interface';
import { USER_COLLECTION_NAME } from './../user/schemas/constants';
import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PermissionPageOptions } from './dto/permission-page-options.dto';
import { IPermission } from './interface/permission.interface';
import { IUserPermission } from './interface/user-permission.interface';
import { PERMISSION_COLLECTION_NAME, USER_PERMISSION_COLLECTION_NAME } from './schema/constant';
import { PermissionAddUserDto } from './dto/permission-add-user.dto';
import { isEmpty } from 'lodash';
import { IAddIcsPermissionToUser } from './dto/add-ics-permission-user.dto';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';

@Injectable()
export class UserPermissionService {
    constructor(
        @InjectModel(USER_PERMISSION_COLLECTION_NAME) private readonly userPermissionModel: Model<IUserPermission>,
        @InjectModel(USER_COLLECTION_NAME) private readonly userModel: Model<IUser>,
        @InjectModel(PERMISSION_COLLECTION_NAME) private readonly permissionModel: Model<IPermission>,
        private globalSettingService: GlobalSettingService,
    ) {}

    async getPermissions(options?: PermissionPageOptions): Promise<any> {
        if (options?.page >= 0) {
            const [data, count] = await Promise.all([
                this.permissionModel.find().skip(options.skip).limit(options.limit).exec(),
                this.permissionModel.countDocuments(),
            ]);
            return {
                data,
                count,
            };
        } else {
            return this.permissionModel.find().exec();
        }
    }

    async addPermissionToUser(formData: PermissionAddUserDto): Promise<any> {
        const [user, userPermission, permissions] = await Promise.all([
            this.userModel.findById(formData.userMongoId).exec(),
            this.userPermissionModel.findOne({ user: formData.userMongoId }).exec(),
            this.permissionModel.find({ _id: { $in: formData.permissions } }).exec(),
        ]);
        if (!user) {
            throw new HttpException(`Không tìm thấy user với id: ${formData.userMongoId}`, HttpStatus.NOT_FOUND);
        }
        if (user?.isCS === false) {
            throw new HttpException('User không thuộc nhóm Chăm sóc khách hàng', HttpStatus.BAD_REQUEST);
        }
        let result: IUserPermission;

        try {
            if (userPermission) {
                result = await this.userPermissionModel.findByIdAndUpdate(userPermission._id, {
                    permissions,
                }, { new: true }).exec();
            } else {
                result = await this.userPermissionModel.create({
                    user,
                    permissions,
                });
            }

            return result;
        } catch (error) {
            throw new HttpException(error?.message || ErrorMessage.BAD_REQUEST, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async addIcsPermissionToUser(userId: string, formData: IAddIcsPermissionToUser): Promise<any> {
        const [user, userPermission, permissions] = await Promise.all([
            this.userModel.findById(formData.userId).exec(),
            this.userPermissionModel.findOne({ user: formData.userId }).exec(),
            this.permissionModel.find({ _id: { $in: formData.permissions } }).exec(),
        ]);
    
        if (!user) {
            throw new HttpException(`Không tìm thấy người dùng với id: ${formData.userId}`, HttpStatus.NOT_FOUND);
        }
    
        if (user.isCS !== formData.isCS) {
            user.set({ isCS: formData.isCS });
        }
        
        if (user.isCare247 !== formData.isCare247) {
            user.set({ isCare247: formData.isCare247 });
        } 
        await user.save();

        let result: IUserPermission;
    
        try {
            if (userPermission) {
                result = await this.userPermissionModel
                    .findByIdAndUpdate(
                        userPermission._id,
                        {
                            permissions,
                        },
                        { new: true },
                    )
                    .exec();
            } else {
                result = await this.userPermissionModel.create({
                    user: user._id,
                    permissions,
                });
            }
            return result;
        } catch (error) {
            console.error('Lỗi khi cập nhật quyền cho người dùng:', error);
            throw new HttpException(error?.message || 'Lỗi hệ thống, vui lòng thử lại!', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    

    async checkPermissionUser(userMongoId: string, permissionTypes: string[]): Promise<boolean> {
        const user = await this.userModel.findById(userMongoId).exec();
        if (!user || user?.isCS === false) {
            return true;
        }
        const [userPermission, permissions] = await Promise.all([
            this.userPermissionModel.findOne({ user: userMongoId}).exec(),
            this.permissionModel.find({ type: { $in: permissionTypes } }).exec(),
        ]);
        if (!userPermission || userPermission.permissions.length === 0) {
            throw new HttpException('Bạn không có quyền thực hiện thao tác này!', HttpStatus.UNAUTHORIZED);
        }
        const setPermission = new Set([...userPermission.permissions.map(permision => `${permision}`)]);
        for (const permision of permissions) {
            if (setPermission.has(`${permision._id}`)) {
                return true;
            }
        }
        return false;
    }

    async getPermissionByUser(userMongoId: string): Promise<any> {
        const user = await this.userModel.findById(userMongoId).exec();
        if (!user) {
            throw new HttpException(`Không tìm thấy user với id: ${userMongoId}!`, HttpStatus.UNAUTHORIZED);
        }
        const userPermission = await this.userPermissionModel.findOne({ user: userMongoId }).exec();
        if (!userPermission) {
            return {
                user: user._id,
                permissions: [],
            };
        }
        const permissions = await this.permissionModel.find({ _id: { $in: userPermission.permissions }});

        return {
            user: user._id,
            permissions,
        };
    }
}
