import { Modu<PERSON> } from '@nestjs/common';
import { TreeNodeController } from './tree-node.controller';
import { TreeNodeService } from './tree-node.service';
import { MongooseModule } from '@nestjs/mongoose';
import { TreeNodeSchema } from './schemas/tree-node.schema';
import { TREE_NODE_COLLECTION_NAME } from './schemas/constants';

@Module({
    imports: [MongooseModule.forFeature([{ name: TREE_NODE_COLLECTION_NAME, schema: TreeNodeSchema }])],
    controllers: [TreeNodeController],
    providers: [TreeNodeService],
})
export class TreeNodeModule {}
