import { ConfigCacheManagerService } from './../config/config.cache-manager.service';
import { ConfigModule } from './../config/config.module';
import { CacheModule, Global, HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { FEATURE_COLLECTION_NAME } from 'src/feature-mongo/schemas/constants';
import { FeatureSchema } from 'src/feature-mongo/schemas/feature.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { AppIdController } from './app-id.controller';
import { AppIdService } from './app-id.service';
import { AppIdSchema } from './schemas/app-id.schema';
import { APP_ID_COLLECTION_NAME } from './schemas/constants';

@Global()
@Module({
    imports: [
        HttpModule,
        MongooseModule.forFeature([
            { name: APP_ID_COLLECTION_NAME, schema: AppIdSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: FEATURE_COLLECTION_NAME, schema: FeatureSchema },
        ]),
        CacheModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigCacheManagerService],
            useFactory: (config: ConfigCacheManagerService) => config.createCacheOptions,
        }),
    ],
    controllers: [AppIdController],
    providers: [AppIdService],
    exports: [AppIdService],
})
export class AppIdModule {}
