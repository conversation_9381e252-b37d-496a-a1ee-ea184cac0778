
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Expose, Type } from 'class-transformer';
import { IsNotEmpty, IsDateString, ValidateIf, Validate, IsOptional, IsArray, ArrayMaxSize } from 'class-validator';
import { InsuranceChoiceValue } from './insurance-choice.dt';
import { CheckInsuranceChoice } from './check-insurance-choice';
import { BookingSlotItemDTO } from './booking-slot-item.dto';

export class BookingMultipleDTO {

    @ApiProperty({
        description: 'danh sách phiếu',
        required: false,
        type: BookingSlotItemDTO,
        isArray: true,
    })
    bookings: BookingSlotItemDTO[];

    @ApiProperty({
        description: 'Platform',
        required: false,
        type: String,
        enum: [
            'ios', 'android', 'pc', 'web',
        ],
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.platform)
    platform: string;

    @ApiProperty({
        description: 'Phương thức thanh toán',
        required: true,
        type: String,
        default: 'momo',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    methodId: string;

    @ApiProperty({
        description: 'Thông tin payment type detail',
        required: true,
        type: String,
        default: 'momo',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    paymentTypeDetail: string;

    @ApiProperty({
        description: 'Id của bệnh nhân',
        required: true,
        type: String,
        default: '5ed076b8fc0f320019712ae0',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'vui lòng bổ sung thông tin',
    })
    patientId: string;

    @ApiProperty({
        description: 'Redirect URL',
        required: true,
        type: String,
    })
    redirectUrl: string;

    @ApiProperty({
        description: 'hasInsuranceCode',
        required: true,
        type: Boolean,
        default: false,
    })
    @Transform(value => Boolean(value))
    hasInsuranceCode: boolean;

    @ApiProperty({
        description: 'insuranceCode',
        required: true,
        type: String,
        default: '',
    })
    @Transform(value => `${value}`.trim())
    insuranceCode: string;

    @ApiProperty({
        description: 'insuranceTransferCode',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    insuranceTransferCode?: string;

    @ApiProperty({
        description: 'patientProfileId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    patientProfileId?: string;

    @ApiProperty({
        description: 'referralCode',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    referralCode?: string;

    @ApiProperty({
        description: 'insuranceChoice',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @Validate(CheckInsuranceChoice, [InsuranceChoiceValue.DUNG_TUYEN, InsuranceChoiceValue.CHUYEN_TUYEN, InsuranceChoiceValue.TAI_KHAM], {
        message: 'Giá trị không hợp lệ.',
    })
    @ValidateIf(o => o.insuranceChoice)
    insuranceChoice?: string;

    @ApiProperty({
        description: 'cbWebView',
        required: false,
        type: Number,
    })
    @Transform(value => Number(value))
    @ValidateIf(o => o.cbWebView)
    cbWebView?: number;

    @ApiProperty({
        description: 'groupId',
        required: false,
        type: Number,
    })
    @Transform(value => Number(value))
    @ValidateIf(o => o.groupId)
    groupId?: number;

    @Expose()
    @ApiProperty({
        description: 'partnerId',
        required: false,
        type: String,
    })
    partnerId: string;

    @Expose()
    patient: string;

    customerIpAddress?: string
    browserScreenHeight?: number
    browserScreenWidth?: number

    @ApiProperty({ description: 'medproCare', required: false })
    @IsOptional()
    @IsArray()
    @ArrayMaxSize(1)
    medproCareServiceIds?: string[];
}
