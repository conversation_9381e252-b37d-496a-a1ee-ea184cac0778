import { Injectable, Inject, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import * as uuid from 'uuid';
import * as moment from 'moment';
import { find, get } from 'lodash';
import { PkhHttpService } from 'src/config/config.http.service';
import { UrlConfigService } from 'src/config/config.url.service';
import { InjectModel } from '@nestjs/mongoose';
import { BOOKING_COLLECTION_NAME, PAYMENT_COLLECTION_NAME, BOOKING_SEARCH_COLLECTION_NAME, SECTION_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { Model } from 'mongoose';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { ROOM_COLLECTION_NAME } from 'src/room-mongo/schemas/constants';
import { IRoom } from 'src/room-mongo/interfaces/room.interface';
import { IDoctor } from 'src/doctor-mongo/interfaces/doctor.interface';
import { ISubject } from 'src/subject-mongo/interfaces/subject.interface';
import { IService } from 'src/service-mongo/interfaces/service.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { IPayment } from 'src/booking-gateway/intefaces/payment.inteface';
import { PartnerOldHospital } from './dto/old-hospital.dto';
import { CONSTRAINTS_BOOKING_CODE_COLLECTION_NAME, CONSTRAINTS_PAYMENT_TRANSACTION_COLLECTION_NAME } from './schemas/constants';
import { IConstraintsBookingCode } from './interfaces/constraints-booking-code.interface';
import { IConstraintsPaymentTransaction } from './interfaces/constraints-payment-transaction.interface';
import { IBookingSearch } from 'src/booking-gateway/intefaces/booking-search.interface';
import { PATIENT_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME, PATIENT_CODE_COLLECTION_NAME, RELATIVE_TYPE_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { IPatient } from 'src/patient-mongo/intefaces/patient.inteface';
import { IPatientCodes } from 'src/patient-mongo/intefaces/patient-codes.inteface';
import { IPatientVersion } from 'src/patient-mongo/intefaces/patient-version.inteface';
import { ICountry } from 'src/country-mongo/interfaces/country.interface';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { NATION_COLLECTION_NAME } from 'src/nation-mongo/schemas/constants';
import { PROFESSION_COLLECTION_NAME } from 'src/profession-mongo/schemas/constants';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { INation } from 'src/nation-mongo/interfaces/nation.interface';
import { IProfession } from 'src/profession-mongo/interfaces/profession.interface';
import { ICity } from 'src/city-mongo/interfaces/city.interface';
import { IDistrict } from 'src/district-mongo/interfaces/district.interface';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { IWard } from 'src/ward-mongo/interfaces/ward.interface';
import { IRelative } from 'src/patient-mongo/intefaces/relative.inteface';
import { SessionService } from 'src/session/session.service';
import { RestfulAPIOldHospitalConfigService } from 'src/config/config.rest-api.old.hospital';
import { SyncUserService } from 'src/sync-user/sync-user.service';
import { IUser } from 'src/user/interfaces/user.interface';
import { ADDRESS_TRACKING_COLLECTION_NAME, SYNC_BOOKING_PROCESS, SYNC_BOOKING_PROCESS_FAILED, SYNC_BOOKING_SUCCESS } from 'src/event/schemas/constants';
import { IAddressTracking } from 'src/event/intefaces/address-tracking.inteface';
import { ISection } from 'src/booking-gateway/intefaces/section.inteface';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { UtilService } from 'src/config/util.service';
import { SyncBookingDTO } from './dto/sync-booking.dto';
import { ISyncBookingProcess } from 'src/event/intefaces/sync-booking-process.inteface';
import { ISyncBookingProcessFailed } from 'src/event/intefaces/sync-booking-process-failed.inteface';
import { ISyncBookingSuccess } from 'src/event/intefaces/sync-booking-success.inteface';
import { SyncProcessStatus } from 'src/event/dto/sync-status.dto';
import { BaseResponse } from './interfaces/base-response.interface';

@Injectable()
export class SyncBookingService {

    private logger = new Logger(SyncBookingService.name);
    private paymentTable = 'payment';
    private paymentSkinTable = 'skin_payment';
    private paymentNd1Table = 'nd1_payment';
    private bookingTable = 'booking';
    private bookingSkinTable = 'skin_booking';
    private bookingNd1Table = 'nd1_booking';
    private pTimeTable = 'ptime';
    private patientTable = 'patient';
    private patientSkinTable = 'skin_patient';
    private patientNd1Table = 'nd1_patient';
    private relativeTable = 'relative';
    private relativeSkinTable = 'skin_relative';
    private relativeNd1Table = 'nd1_relative';
    private scheduleTable = 'schedule';
    private scheduleSkinTable = 'skin_schedule';
    private scheduleNd1Table = 'nd1_schedule';
    private subjectTable = 'subject';
    private roomTable = 'room';
    private doctorTable = 'doctor';
    private hospitalSubject = 'hospital_subject';

    /* url transform */
    private urlTransform: string = '';
    private getEnv: string = '';

    private umcConfig = [
        {
            id: 1,
            name: 'Thanh toán bằng thẻ khám bệnh',
            paymentMethod: 'THEKHAMBENH',
            paymentMethodDetail: 'thekhambanh',
            gatewayId: 'vnpay',
        },
        {
            id: 2,
            name: 'Thanh toán bằng Thẻ quốc tế Visa, Master, JCB',
            paymentMethod: 'VISA/MASTER/JCB',
            paymentMethodDetail: 'creditcard',
            gatewayId: 'cybersource',
        },
        {
            id: 3,
            name: 'Thanh toán bằng Thẻ ATM nội địa',
            paymentMethod: 'NO_CONFIG', // lay thong tin mã ngan hang
            paymentMethodDetail: 'atm',
            gatewayId: 'vnpay',
        },
        {
            id: 6,
            name: 'Hỗ trợ thanh toán',
            paymentMethod: 'NO_CONFIG', // lay thong tin mã ngan hang
            paymentMethodDetail: 'atm',
            gatewayId: 'vnpay',
        },
        {
            id: 7,
            name: 'Thanh toán bằng Ví MoMo',
            paymentMethod: 'MOMO',
            paymentMethodDetail: 'qrcode',
            gatewayId: 'momo',
        },
    ];

    private skinConfig = [

        {
            id: 2,
            name: 'Thanh toán bằng Thẻ quốc tế Visa, Master, JCB',
            paymentMethod: 'VISA/MASTER/JCB',
            paymentMethodDetail: 'creditcard',
            gatewayId: 'payoo',
        },
        {
            id: 3,
            name: 'Thanh toán bằng Thẻ ATM nội địa',
            paymentMethod: 'ATM', // lay thong tin mã ngan hang
            paymentMethodDetail: 'atm',
            gatewayId: 'payoo',
        },
        {
            id: 5,
            name: 'Thanh toán đại lý',
            paymentMethod: 'THANHTOANDAILY', // lay thong tin mã ngan hang
            paymentMethodDetail: 'thanhtoandaily',
            gatewayId: 'payoo',
        },
        {
            id: 6,
            name: 'Hỗ trợ thanh toán',
            paymentMethod: 'ATM', // lay thong tin mã ngan hang
            paymentMethodDetail: 'atm',
            gatewayId: 'payoo',
        },
        {
            id: 7,
            name: 'Thanh toán bằng Ví MoMo',
            paymentMethod: 'MOMO',
            paymentMethodDetail: 'qrcode',
            gatewayId: 'momo',
        },
    ];

    private nd1Config = [
        {
            id: 2,
            name: 'Thanh toán bằng Thẻ quốc tế Visa, Master, JCB',
            paymentMethod: 'VISA/MASTER/JCB',
            paymentMethodDetail: 'creditcard',
            gatewayId: 'alepay',
        },
        {
            id: 3,
            name: 'Thanh toán bằng Thẻ ATM nội địa',
            paymentMethod: 'ATM', // lay thong tin mã ngan hang
            paymentMethodDetail: 'atm',
            gatewayId: 'alepay',
        },
        {
            id: 6,
            name: 'Hỗ trợ thanh toán',
            paymentMethod: 'ATM', // lay thong tin mã ngan hang
            paymentMethodDetail: 'atm',
            gatewayId: 'momo',
        },
        {
            id: 7,
            name: 'Thanh toán bằng Ví MoMo',
            paymentMethod: 'MOMO',
            paymentMethodDetail: 'qrcode',
            gatewayId: 'momo',
        },
    ];

    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @InjectModel(COUNTRY_COLLECTION_NAME) private countryModel: Model<ICountry>,
        @InjectModel(NATION_COLLECTION_NAME) private nationModel: Model<INation>,
        @InjectModel(PROFESSION_COLLECTION_NAME) private professionModel: Model<IProfession>,
        @InjectModel(CITY_COLLECTION_NAME) private cityModel: Model<ICity>,
        @InjectModel(DISTRICT_COLLECTION_NAME) private districtModel: Model<IDistrict>,
        @InjectModel(RELATIVE_TYPE_COLLECTION_NAME) private relativeModel: Model<IRelative>,
        @InjectModel(WARD_COLLECTION_NAME) private wardModel: Model<IWard>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(BOOKING_SEARCH_COLLECTION_NAME) private bookingSearchModel: Model<IBookingSearch>,
        @InjectModel(PAYMENT_COLLECTION_NAME) private paymentModel: Model<IPayment>,
        @InjectModel(SERVICE_COLLECTION_NAME) private serviceModel: Model<IService>,
        @InjectModel(SUBJECT_COLLECTION_NAME) private subjectModel: Model<ISubject>,
        @InjectModel(DOCTOR_COLLECTION_NAME) private doctorModel: Model<IDoctor>,
        @InjectModel(SECTION_COLLECTION_NAME) private sectionModel: Model<ISection>,
        @InjectModel(PATIENT_COLLECTION_NAME) private patientModel: Model<IPatient>,
        @InjectModel(PATIENT_VERSION_COLLECTION_NAME) private patientVersionModel: Model<IPatientVersion>,
        @InjectModel(PATIENT_CODE_COLLECTION_NAME) private patientCodeModel: Model<IPatientCodes>,
        @InjectModel(ROOM_COLLECTION_NAME) private roomModel: Model<IRoom>,
        @InjectModel(CONSTRAINTS_BOOKING_CODE_COLLECTION_NAME) private constraintsBookingCodeModel: Model<IConstraintsBookingCode>,
        @InjectModel(CONSTRAINTS_PAYMENT_TRANSACTION_COLLECTION_NAME)
        private constraintsPaymentTransactionModel: Model<IConstraintsPaymentTransaction>,
        @InjectModel(ADDRESS_TRACKING_COLLECTION_NAME) private addressTrackingModel: Model<IAddressTracking>,
        private readonly httpService: PkhHttpService,
        private readonly urlConfigService: UrlConfigService,
        private readonly sessionService: SessionService,
        private readonly syncUserService: SyncUserService,
        private readonly restApiMapping: RestfulAPIOldHospitalConfigService,
        private readonly eventEmitter: EventEmitter2,
        private readonly utilService: UtilService,
        @InjectModel(SYNC_BOOKING_PROCESS) private readonly syncBookingProcessModel: Model<ISyncBookingProcess>,
        @InjectModel(SYNC_BOOKING_PROCESS_FAILED) private readonly syncBookingProcessFailModel: Model<ISyncBookingProcessFailed>,
        @InjectModel(SYNC_BOOKING_SUCCESS) private readonly syncBookingSuccessModel: Model<ISyncBookingSuccess>,
    ) {
        this.urlTransform = `${this.urlConfigService.getBookingTreeUrl()}/his-connector/api/convertPartnerData`;
        this.getEnv = this.urlConfigService.getEnv();
    }

    getPaymentMethod(partnerId: string, methodId: number, jsonData: string): any {
        switch (partnerId) {
            case PartnerOldHospital.UMC:
                return this.getPaymentMethodDetail(methodId, jsonData);
            case PartnerOldHospital.SKIN:
                return this.getPaymentMethodDetailSkin(methodId, jsonData);
            case PartnerOldHospital.NHIDONG1:
                return this.getPaymentMethodDetailNd1(methodId, jsonData);
            default:
                break;
        }
    }

    getPaymentMethodDetailNd1(methodId: number, jsonData: string): any {
        const findConfig = find(this.nd1Config, { id: methodId });
        if (typeof findConfig !== typeof undefined) {
            /* kiểm tra jsonData có gì ko để tìm lại ngân hàng thanh toán */
            if (jsonData) {
                try {
                    const data = JSON.parse(jsonData);
                    const { vnp_BankCode } = data;
                    if (vnp_BankCode) {
                        return {
                            ...findConfig,
                            paymentMethod: vnp_BankCode,
                        };
                    }
                } catch (error) {
                    this.logger.error(error);
                }
            }
            return findConfig;
        }
        return false;
    }

    getPaymentMethodDetailSkin(methodId: number, jsonData: string): any {
        const findConfig = find(this.skinConfig, { id: methodId });
        if (typeof findConfig !== typeof undefined) {
            /* kiểm tra jsonData có gì ko để tìm lại ngân hàng thanh toán */
            if (jsonData) {
                try {
                    const data = JSON.parse(jsonData);
                    const { vnp_BankCode } = data;
                    if (vnp_BankCode) {
                        return {
                            ...findConfig,
                            paymentMethod: vnp_BankCode,
                        };
                    }
                } catch (error) {
                    this.logger.error(error);
                }
            }
            return findConfig;
        }
        return false;
    }

    getPaymentMethodDetail(methodId: number, jsonData: string): any {
        const findConfig = find(this.umcConfig, { id: methodId });
        if (typeof findConfig !== typeof undefined) {
            /* kiểm tra jsonData có gì ko để tìm lại ngân hàng thanh toán */
            if (!!jsonData) {
                try {
                    const data = JSON.parse(jsonData);
                    const { vnp_BankCode = null } = data;
                    if (vnp_BankCode) {
                        return {
                            ...findConfig,
                            paymentMethod: vnp_BankCode,
                        };
                    } else {
                        return findConfig;
                    }
                } catch (error) {
                    return findConfig;
                }
            } else {
                return findConfig;
            }
        } else {
            return false;
        }
    }

    transformPaymentMethod(findConfig: any): any {
        if (findConfig) {
            const { paymentMethod, paymentMethodDetail, gatewayId } = findConfig;

            const obj: any = {};
            if (paymentMethod) {
                obj.paymentMethod = paymentMethod;
            }
            if (paymentMethodDetail) {
                obj.paymentMethodDetail = paymentMethodDetail;
            }
            if (gatewayId) {
                obj.gatewayId = gatewayId;
            }

            return obj;
        }
        return {};
    }

    returnPaymentStatus(status: number): number {
        switch (status) {
            case 0:
                return 1;
            case 1:
                return 2;
            case -2:
                return 2;
            default:
                return 0;
        }
    }

    // booking: IBooking
    // booking.syncPatientIdV1
    async syncPatient(syncPatientIdV1: number, partnerId: string = 'umc'): Promise<any> {
        /* lấy thông tin hồ sơ bệnh nhân */
        let patient: any = null;
        let relativeInfo: any = null;
        switch (partnerId) {
            case PartnerOldHospital.UMC: {
                patient = await this.pkhPatientKnex(this.patientTable).where({
                    id: syncPatientIdV1,
                }).first();
                if (!patient) {
                    throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân.', HttpStatus.NOT_FOUND);
                }
                /* Lấy thông tin thân nhân */
                relativeInfo = await this.pkhPatientKnex(this.relativeTable).where({
                    patient_id: patient.id,
                }).first();
                break;
            }
            case PartnerOldHospital.SKIN: {
                patient = await this.pkhPatientKnex(this.patientSkinTable).where({
                    id: syncPatientIdV1,
                }).first();
                if (!patient) {
                    throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân.', HttpStatus.NOT_FOUND);
                }
                /* Lấy thông tin thân nhân */
                relativeInfo = await this.pkhPatientKnex(this.relativeSkinTable).where({
                    skin_patient_id: patient.id,
                }).first();
                break;
            }
            case PartnerOldHospital.NHIDONG1: {
                patient = await this.pkhPatientKnex(this.patientNd1Table).where({
                    id: syncPatientIdV1,
                }).first();
                if (!patient) {
                    throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân.', HttpStatus.NOT_FOUND);
                }
                /* Lấy thông tin thân nhân */
                relativeInfo = await this.pkhPatientKnex(this.relativeNd1Table).where({
                    nd1_patient_id: patient.id,
                }).first();
                break;
            }
            default:
                break;
        }
        /* Kiểm tra xem hồ sơ này đã có mã số hồ sơ hay ko */
        let keyCheckMsbn = '';
        const bojaa: any = {
            patientIdV1: 0,
            patientIdV1DaLieu: 0,
            patientIdV1CTCH: 0,
            patientIdV1ThuDuc: 0,
            patientIdV1UMC: 0,
            id: '',
        };
        switch (partnerId) {
            case PartnerOldHospital.UMC:
                keyCheckMsbn = patient.bvdhyd_msbn;
                bojaa.patientIdV1UMC = patient.id;
                bojaa.id = `umc_${patient.id}`;
                break;
            case PartnerOldHospital.SKIN:
                keyCheckMsbn = patient.bv_id;
                bojaa.patientIdV1DaLieu = patient.id;
                bojaa.id = `dalieuhcm_${patient.id}`;
                break;
            case PartnerOldHospital.NHIDONG1:
                keyCheckMsbn = patient.bv_id;
                bojaa.patientIdV1 = patient.id;
                bojaa.id = `nhidong1_${patient.id}`;
                break;
            default:
                break;
        }

        try {
            const baseUrl = this.urlConfigService.getBookingTreeUrl();
            const urlTransform = `${baseUrl}/his-connector/api/convertPartnerData`;

            const objRelative: any = {
                relative_name: '',
                relative_mobile: '',
                relative_type_id: '',
                relative_email: '',
            };

            if (relativeInfo) {
                objRelative.relative_name = relativeInfo.name;
                objRelative.relative_mobile = relativeInfo.mobile;
                objRelative.relative_email = relativeInfo.email;
                if (partnerId === 'nhidong1') {
                    objRelative.relative_type_id = Number(relativeInfo.nd1_relative_type_id) > 0 ? `${relativeInfo.nd1_relative_type_id}` : '';
                } else {
                    objRelative.relative_type_id = Number(relativeInfo.relative_type_id) > 0 ? `${relativeInfo.relative_type_id}` : '';
                }

            }

            const params: any = {
                fromPartnerId: partnerId,
                toPartnerId: 'medpro',
                countryId: '',
                cityId: `${patient.city_id}`,
                districtId: `${patient.district_id}`,
                wardId: `${patient.ward_id}`,
                nationId: `${patient.dantoc_id}`,
                professionId: `${patient.profession_id}`,
                relationTypeId: `${objRelative.relative_type_id}`,
                convertFrom: 'V1', // them convertFrom de tim ra phong gi do
            };
            let dataKeys: any = {};
            try {
                dataKeys = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
            } catch (error) {
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'transformDataPatientV1',
                    summary: 'Đồng bộ hồ sơ bệnh nhân',
                    nameParent: 'syncPatient',
                    params,
                    errorBody: this.utilService.errorHandler(error),
                    response: {},
                    message: error?.message,
                });
                throw error;
            }

            if (Object.keys(dataKeys).length === 0) {
                return null;
            }

            const objV1: any = {};

            if (dataKeys.relationTypeId) {
                objRelative.relative_type_id = dataKeys.relationTypeId;
            }
            const objRelation: any = {
                relation: {
                    ...objRelative,
                },
            };

            if (dataKeys.nationId) {
                const findNation = await this.nationModel.findOne({ id: dataKeys.nationId }).exec();
                if (findNation) {
                    const nationObj = findNation.toObject();
                    objV1.nation = nationObj._id;
                    objV1.dantoc_id = nationObj.id;
                }
            }

            if (dataKeys.professionId) {
                const findProfession = await this.professionModel.findOne({ id: dataKeys.professionId }).exec();
                if (findProfession) {
                    const professionObj = findProfession.toObject();
                    objV1.profession_id = professionObj.id;
                    objV1.profession = professionObj._id;
                }
            }

            if (dataKeys.cityId) {
                const findCity = await this.cityModel.findOne({ id: dataKeys.cityId }).exec();
                if (findCity) {
                    const cityObject = findCity.toObject();
                    objV1.city = cityObject._id;
                    objV1.city_id = cityObject.id;
                }
            }

            if (dataKeys.districtId) {
                const findDistrict = await this.districtModel.findOne({ id: dataKeys.districtId }).exec();
                if (findDistrict) {
                    const districtObj = findDistrict.toObject();
                    objV1.district = districtObj._id;
                    objV1.district_id = districtObj.id;
                }
            }

            if (dataKeys.wardId) {
                const findWard = await this.wardModel.findOne({ id: dataKeys.wardId }).exec();
                if (findWard) {
                    const wardObj = findWard.toObject();
                    objV1.ward = wardObj._id;
                    objV1.ward_id = wardObj.id;
                }
            }

            const findCountry = await this.countryModel.findOne({ code: 'VIE', partnerId: 'medpro' }).exec();
            const objCountry: any = {};
            if (findCountry) {
                objCountry.country = findCountry._id;
                objCountry.country_id = findCountry.id;
            }

            /* Tạo hồ sơ mới */
            const vIPatient: any = {
                address: patient.address,
                birthdate: moment(patient.birthdate).isValid() ? moment(patient.birthdate).format('YYYY-MM-DD') : '',
                birthyear: patient.birthyear,
                mobile: patient.mobile,
                name: patient.name,
                surname: patient.surname,
                email: patient.email,
                cmnd: patient.cmnd,
                sex: patient.sex,
                country_code: 'VIE',
                ...objV1,
                ...objCountry,
                partnerId,
                sourceId: partnerId,
                ...objRelation,
                relative: {},
                code: patient.medpro_id,
                ...bojaa,
            };
            /* Kiểm tra V2 đã có hồ sơ này hay chưa */
            const checkCode = await this.patientModel.findOne({
                code: patient.medpro_id,
            }).exec();
            let resultPatient;
            if (!checkCode) {
                const patientInsert = new this.patientModel({ ...vIPatient }); // , id: uuid.v4().replace(/-/g, '')
                resultPatient = await patientInsert.save();
            } else {
                resultPatient =
                    await this.patientModel.findByIdAndUpdate({
                        _id: checkCode._id,
                    }, {
                        ...vIPatient,
                    }, { new: true })
                        .exec();
            }
            if (keyCheckMsbn) {
                /* Kiểm tra xem có patientCode hay chưa */
                const checkPatientCode = await this.patientCodeModel.findOne({
                    patientId: resultPatient.id,
                    partnerId,
                    patientCode: `${keyCheckMsbn}`.trim(),
                }).exec();

                if (!checkPatientCode) {
                    /* tiến hành tạo patientCode */
                    const patientCodeInfo = new this.patientCodeModel({
                        id: `${partnerId}_${resultPatient.id}`,
                        createTime: moment().toDate(),
                        patientId: resultPatient.id,
                        patientCode: `${keyCheckMsbn}`.trim(),
                        partnerId,
                        appId: partnerId,
                    });
                    await patientCodeInfo.save();
                }
            }
            return {
                idRef: resultPatient._id,
                id: resultPatient.id,
                patientCode: keyCheckMsbn,
                name: resultPatient.name,
                surname: resultPatient.surname,
                fullname: `${resultPatient.surname} ${resultPatient.name}`,
                mobile: resultPatient.mobile,
                origin: resultPatient,
            };
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'transformDataPatientV1',
                summary: 'Đồng bộ hồ sơ bệnh nhân',
                nameParent: 'syncPatient',
                params: {
                    patient,
                    partnerId,
                    syncPatientIdV1,
                    relativeInfo,
                    keyCheckMsbn,
                    bojaa,
                },
                errorBody: this.utilService.errorHandler(error),
                response: {},
                message: error?.message,
            });
            throw error;
        }

    }

    transformDataPatientV1(url: string, data: any): Observable<AxiosResponse<any>> {
        return this.httpService.postHttpRequest(url, {
            ...data,
        });
    }

    async syncOneSkinBooking(id: number, partnerId: string = 'dalieuhcm'): Promise<any> {
        const getPartner = await this.hospitalModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            return null;
        }
        try {
            const getBooking = await this.pkhPatientKnex(this.bookingSkinTable)
                .select(
                    `${this.bookingSkinTable}.id as syncBookingIdV1`,
                    `${this.bookingSkinTable}.app as syncMethod`,
                    `${this.bookingSkinTable}.user_id as syncUserIdV1`,
                    `${this.bookingSkinTable}.status as bookingStatus`,
                    `${this.bookingSkinTable}.bhyt_accept as bhytAccept`,
                    `${this.bookingSkinTable}.platform as bookingPlatform`,
                    `${this.bookingSkinTable}.booking_date as bookingDate`,
                    `${this.bookingSkinTable}.transaction_code_gd as bookingCode`,
                    `${this.bookingSkinTable}.email as bookingEmail`,
                    `${this.bookingSkinTable}.skin_payment_id as paymentId`,
                    `${this.bookingSkinTable}.skin_schedule_id as scheduleId`,
                    `${this.bookingSkinTable}.skin_patient_id as syncPatientIdV1`,
                    `${this.bookingSkinTable}.bv_time as bvTime`,
                    `booking_phone as bookingPhone`,
                    `${this.bookingSkinTable}.date_create as dateCreate`,
                    /*     ROOM TABLE          */
                    // `${this.scheduleSkinTable}.room_id as roomId`,
                    // `${this.scheduleSkinTable}.doctor_id as doctorId`,
                    `${this.scheduleSkinTable}.skin_subject_id as subjectId`,
                    /*     PAYMENT TABLE        */
                    `${this.paymentSkinTable}.method_id as methodIdV1`,
                    `${this.paymentSkinTable}.transaction_code_tt as transactionId`,
                    `${this.paymentSkinTable}.amount as amount`,
                    `${this.paymentSkinTable}.amount_original as subTotal`,
                    `${this.paymentSkinTable}.amount_service as medproFee`,
                    `${this.paymentSkinTable}.amount_gate as transferFee`,
                    `${this.paymentSkinTable}.result as jsonData`,
                    `${this.paymentSkinTable}.status as paymentStatus`,
                    `${this.paymentSkinTable}.date_create as paymentDateUpdate`,
                    /*     PATIENT       */
                    `${this.patientSkinTable}.surname as patientSur`,
                    `${this.patientSkinTable}.name as patientName`,
                    `${this.patientSkinTable}.sex as patientGender`,
                    `${this.patientSkinTable}.bv_id as patientCode`,
                    `${this.patientSkinTable}.mobile as patientMobile`,
                    `${this.patientSkinTable}.email`,
                )
                .innerJoin(this.scheduleSkinTable, `${this.scheduleSkinTable}.id`, `${this.bookingSkinTable}.skin_schedule_id`)
                .innerJoin(this.patientSkinTable, `${this.patientSkinTable}.id`, `${this.bookingSkinTable}.skin_patient_id`)
                .leftJoin(this.paymentSkinTable, `${this.paymentSkinTable}.id`, `${this.bookingSkinTable}.skin_payment_id`)
                .where(`${this.bookingSkinTable}.id`, id).first();
            /* tiến hành transform info */
            if (getBooking) {
                const { roomId, doctorId, subjectId, bookingCode, bookingNumber, bookingEmail, syncBookingIdV1, syncUserIdV1, syncMethod,
                    bookingStatus, bookingPlatform, bhytAccept, bookingDate, patientCode, patientMobile, patientSur, patientName, syncPatientIdV1,
                    methodIdV1, transactionId, amount, subTotal, medproFee, transferFee, jsonData, paymentStatus, paymentDateUpdate,
                    patientGender } = getBooking;
                const fullname = `${patientSur} ${patientName}`;
                if (syncMethod === 'medpro') {
                    return null;
                }
                /* tiến hành sync user */
                // console.log('syncUserIdV1', syncUserIdV1);
                const user: IUser = await this.syncUserService.syncOneUser(syncUserIdV1);
                // console.log('user ne ', user);
                /* Tiến hành đồng bộ hồ sơ bệnh nhân */
                const patient = await this.syncPatient(syncPatientIdV1, 'dalieuhcm');
                /* Nếu tồn tại user dùng số điện thoại đăng nhập thì add thông tin patient vào user */
                const objGetUserV2: any = {};
                if (user && patient) {
                    objGetUserV2.userId = user._id;
                    const originPatient: IPatient = patient.origin;
                    await this.syncUserService.addPatientToUser(user, originPatient);
                }
                // console.log('objGetUserV2', objGetUserV2);
                // console.log('patient sau khi insert', patient);
                const objParams: any = {};
                if (subjectId) {
                    objParams.subjectId = `${subjectId}`;
                }

                if (roomId) {
                    objParams.roomId = `${roomId}`;
                }

                if (doctorId) {
                    objParams.doctorId = `${doctorId}`;
                }

                const params = {
                    fromPartnerId: partnerId,
                    toPartnerId: 'medpro',
                    price: subTotal,
                    ...objParams,
                    gender: Number(patientGender), // them gender de tim ra phong gi do
                    convertFrom: 'V1', // them convertFrom de tim ra phong gi do
                };
                // console.log('params dalieuhcm', params);
                try {
                    const dataKeys = (await this.transformDataBookingV1(this.urlTransform, params).toPromise()).data;
                    // console.log('result key dalieuhcm', dataKeys);
                    const keysObject = [
                        {
                            key: 'serviceId',
                            collectioName: 'services',
                            keyReturn: 'service',
                        },
                        {
                            key: 'subjectId',
                            collectioName: 'subjects',
                            keyReturn: 'subject',
                        },
                        {
                            key: 'roomId',
                            collectioName: 'rooms',
                            keyReturn: 'room',
                        },
                        {
                            key: 'doctorId',
                            collectioName: 'doctors',
                            keyReturn: 'doctor',
                        },
                    ];
                    /* tiếp tục tìm lại trong collection để get data */
                    let listKeysReturn: any = {
                        // service: {},
                        // subject: {},
                        // room: {},
                        // doctor: {},
                    };
                    for await (const obj of keysObject) {
                        const collectioName = obj.collectioName;
                        const key = obj.key;
                        const value = dataKeys[key];
                        const keyReturn = obj.keyReturn;
                        if (value) {
                            const objectInfo = await this.getObjectInfo(collectioName, value);
                            if (objectInfo) {
                                const { _id, id: idObj } = objectInfo.toObject();
                                const resuultObj = { [key]: idObj, [keyReturn]: _id };
                                listKeysReturn = { ...listKeysReturn, ...resuultObj };
                            }
                        }
                    }
                    /* Sau khi có thông tin id của medpro */
                    let bhytObj: any = {};
                    switch (bhytAccept) {
                        case 1:
                            bhytObj = { ...bhytObj, insuranceChoice: 'DUNG_TUYEN', insuranceCode: 'CO_BAO_HIEM' };
                            break;
                        case 2:
                            bhytObj = { ...bhytObj, insuranceChoice: 'TAI_KHAM', insuranceCode: 'CO_BAO_HIEM' };
                            break;
                        default:
                            break;
                    }
                    const formatDate = moment(bookingDate).format('YYYY-MM-DD');
                    const defaultValue = get(getBooking, 'bvTime', '00:00');
                    const bookingDateFormat = moment(`${formatDate} ${defaultValue}:00`, 'YYYY-MM-DD HH:mm:ss').subtract(7, 'hours').toDate();
                    const bookingId = `dalieuhcm_${syncBookingIdV1}`;
                    const visible = Number(bookingStatus) === 0 ? false : true;
                    const vIBooking: any = {
                        id: uuid.v4().replace(/-/g, ''),
                        bookingId,
                        bookingCode,
                        transactionId,
                        partnerId,
                        appId: partnerId,
                        partner: getPartner._id,
                        /* thông tin service, subject, room, doctor */
                        ...listKeysReturn,
                        sequenceNumber: bookingNumber,
                        status: bookingStatus,
                        paymentStatus: this.returnPaymentStatus(paymentStatus),
                        visible,
                        platform: bookingPlatform,
                        ...bhytObj,
                        date: bookingDateFormat,
                        bookingSlotId: 'NOT_SYNC_V1_YET',
                        /* thêm key để phân biệt v1 <=> v2 */
                        syncBookingType: 1, // v1: 1  v2: 2
                        syncBookingIdV1,
                        syncUserIdV1,
                        syncPatientIdV1,
                        patientNameV1: fullname,
                        patientPhoneV1: patientMobile,
                        patientMSBNV1: patientCode,
                        /* patient id */
                        patientId: patient.id,
                        patient: patient.idRef,
                        /* user v2 */
                        ...objGetUserV2,
                    };
                    // console.log('parms insert sync booking', vIBooking);
                    const newBooking = new this.bookingModel(vIBooking);
                    let booking;
                    try {
                        const newConstraintsBookingCode = new this.constraintsBookingCodeModel({
                            bookingCode: `${bookingCode}_${partnerId}`,
                        });
                        await newConstraintsBookingCode.save();
                        booking = await newBooking.save();
                    } catch (error) {
                        const { bookingCode: bookCode, ...rest } = vIBooking;
                        // console.log('update sync booking', rest);
                        booking = await this.bookingModel.findOneAndUpdate({
                            bookingCode: bookCode,
                            partnerId,
                            status: { $ne: 2 },
                        }, { ...rest }, { new: true }).exec();

                        if (!booking) {
                            /* trường hợp update ko phải trạng thái */
                            try {
                                const { status, ...rest2 } = rest;
                                booking = await this.bookingModel.findOneAndUpdate({
                                    bookingCode: bookCode,
                                    partnerId,
                                }, { ...rest2 }, { new: true }).exec();
                            } catch (error222) {
                                console.log(error222);
                            }
                        } else {
                            try {
                                const { status, paymentStatus: pStatus, patientId: patientIDDDD, patient: patientOOO, ...rest2 } = rest;
                                booking = await this.bookingModel.findOneAndUpdate({
                                    bookingCode: bookCode,
                                    partnerId,
                                }, {
                                    paymentStatus: pStatus,
                                    patient: patientOOO,
                                    patientId: patientIDDDD,
                                }, { new: true }).exec();
                            } catch (error222) {
                                console.log(error222);
                            }
                        }
                    }
                    /* tiến hành insert vào trong payment */
                    const paymentMethodObj = this.getPaymentMethod(partnerId, methodIdV1, jsonData);
                    const returnObjMethod = this.transformPaymentMethod(paymentMethodObj);
                    const totalFee = medproFee + transferFee;
                    const paymentTime = moment(paymentDateUpdate, 'YYYY-MM-DD HH:mm:ss').subtract(7, 'hours').format('YYYY-MM-DD HH:mm:ss');
                    const vIPayment: any = {
                        id: uuid.v4().replace(/-/g, ''),
                        amount,
                        subTotal,
                        medproFee,
                        totalFee,
                        transferFee,
                        transactionId,
                        status: this.returnPaymentStatus(paymentStatus),
                        appId: partnerId,
                        partnerId,
                        partner: getPartner._id,
                        date: bookingDateFormat,
                        bookingId,
                        email: bookingEmail,
                        ...returnObjMethod,
                        patientId: patient.id,
                        patient: patient.idRef,
                        paymentTime,
                    };
                    const newPayment = new this.paymentModel(vIPayment);
                    let payment;
                    try {
                        const newConstraintsPaymentTransaction = new this.constraintsPaymentTransactionModel({
                            transactionId: `${transactionId}_${partnerId}`,
                        });
                        await newConstraintsPaymentTransaction.save();
                        payment = await newPayment.save();
                    } catch (error) {
                        const { transactionId: transId, ...rest } = vIPayment;
                        payment = await this.paymentModel.findOneAndUpdate({
                            transactionId: transId,
                            partnerId,
                        }, { ...rest }, { new: true }).exec();
                    }
                    /* tiến hành tạo booking search */
                    const objBookingSearch = {
                        msbn: patientCode,
                        fullname,
                        mobile: patientMobile,
                    };
                    const vIBookingSearch: any = {
                        idBooking: booking.id,
                        idPatient: `${booking?.patientId || ''}`,
                        booking: booking._id,
                        bookingCode: booking.bookingCode,
                        ...objBookingSearch,
                        transactionId,
                        username: '',
                        partnerId,
                        /* dành cho v1 */
                        bookingType: 1,
                    };
                    const newBookingSearch = new this.bookingSearchModel(vIBookingSearch);
                    try {
                        await newBookingSearch.save();
                    } catch (error) {
                        this.logger.error(error);
                    }
                    /* END: tiến hành tạo booking search */
                    return {
                        booking,
                        payment,
                    };
                } catch (error) {
                    const { response } = error;
                    if (response) {
                        const { status = HttpStatus.BAD_REQUEST } = response;
                        throw new HttpException(error, status);
                    } else {
                        throw new HttpException(error, HttpStatus.BAD_REQUEST);
                    }
                }
            } else {
                return null;
            }
        } catch (error) {
            console.log(error);
            const { response } = error.response;
            if (response) {
                this.logger.error(response);
            } else {
                this.logger.error(error);
            }
            return null;
        }
    }

    async syncOneNd1Booking(id: number, partnerId: string = 'nhidong1'): Promise<any> {
        const getPartner = await this.hospitalModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            return null;
        }
        try {
            const getBooking = await this.pkhPatientKnex(this.bookingNd1Table)
                .select(
                    `${this.bookingNd1Table}.id as syncBookingIdV1`,
                    `${this.bookingNd1Table}.app as syncMethod`,
                    `${this.bookingNd1Table}.user_id as syncUserIdV1`,
                    `${this.bookingNd1Table}.status as bookingStatus`,
                    `${this.bookingNd1Table}.bhyt_accept as bhytAccept`,
                    `${this.bookingNd1Table}.platform as bookingPlatform`,
                    `${this.bookingNd1Table}.booking_date as bookingDate`,
                    `${this.bookingNd1Table}.transaction_code_gd as bookingCode`,
                    `${this.bookingNd1Table}.email as bookingEmail`,
                    `${this.bookingNd1Table}.nd1_payment_id as paymentId`,
                    `${this.bookingNd1Table}.nd1_schedule_id as scheduleId`,
                    `${this.bookingNd1Table}.nd1_patient_id as syncPatientIdV1`,
                    `${this.bookingNd1Table}.bv_time as bvTime`,
                    `booking_phone as bookingPhone`,
                    `${this.bookingNd1Table}.date_create as dateCreate`,
                    /*     ROOM TABLE          */
                    `${this.scheduleNd1Table}.nd1_room_id as roomId`,
                    `${this.scheduleNd1Table}.nd1_doctor_id as doctorId`,
                    `${this.scheduleNd1Table}.nd1_section_id as sectionId`,
                    `${this.scheduleNd1Table}.nd1_subject_id as subjectId`,
                    /*     PAYMENT TABLE        */
                    `${this.paymentNd1Table}.method_id as methodIdV1`,
                    `${this.paymentNd1Table}.transaction_code_tt as transactionId`,
                    `${this.paymentNd1Table}.amount as amount`,
                    `${this.paymentNd1Table}.amount_original as subTotal`,
                    `${this.paymentNd1Table}.amount_service as medproFee`,
                    `${this.paymentNd1Table}.amount_gate as transferFee`,
                    `${this.paymentNd1Table}.result as jsonData`,
                    `${this.paymentNd1Table}.status as paymentStatus`,
                    `${this.paymentNd1Table}.date_create as paymentDateUpdate`,
                    /*     PATIENT       */
                    `${this.patientNd1Table}.surname as patientSur`,
                    `${this.patientNd1Table}.name as patientName`,
                    `${this.patientNd1Table}.sex as patientGender`,
                    `${this.patientNd1Table}.bv_id as patientCode`,
                    `${this.patientNd1Table}.mobile as patientMobile`,
                    `${this.patientNd1Table}.email`,
                )
                .innerJoin(this.scheduleNd1Table, `${this.scheduleNd1Table}.id`, `${this.bookingNd1Table}.nd1_schedule_id`)
                .innerJoin(this.patientNd1Table, `${this.patientNd1Table}.id`, `${this.bookingNd1Table}.nd1_patient_id`)
                .leftJoin(this.paymentNd1Table, `${this.paymentNd1Table}.id`, `${this.bookingNd1Table}.nd1_payment_id`)
                .where(`${this.bookingNd1Table}.id`, id).first();
            /* tiến hành transform info */
            if (getBooking) {
                const { roomId, sectionId, doctorId, subjectId, bookingCode, bookingNumber, bookingEmail, syncBookingIdV1, syncUserIdV1, syncMethod,
                    bookingStatus, bookingPlatform, bhytAccept, bookingDate, patientCode, patientMobile, patientSur, patientName, syncPatientIdV1,
                    methodIdV1, transactionId, amount, subTotal, medproFee, transferFee, jsonData, paymentStatus, paymentDateUpdate,
                    patientGender } = getBooking;
                const fullname = `${patientSur} ${patientName}`;
                if (syncMethod === 'medpro') {
                    return null;
                }
                /* tiến hành sync user */
                // console.log('syncUserIdV1', syncUserIdV1);
                const user: IUser = await this.syncUserService.syncOneUser(syncUserIdV1);
                // console.log('user ne ', user);
                /* Tiến hành đồng bộ hồ sơ bệnh nhân */
                const patient = await this.syncPatient(syncPatientIdV1, 'nhidong1');
                /* Nếu tồn tại user dùng số điện thoại đăng nhập thì add thông tin patient vào user */
                const objGetUserV2: any = {};
                if (user && patient) {
                    objGetUserV2.userId = user._id;
                    const originPatient: IPatient = patient.origin;
                    await this.syncUserService.addPatientToUser(user, originPatient);
                }
                // console.log('objGetUserV2', objGetUserV2);
                // console.log('patient sau khi insert', patient);
                const objParams: any = {};
                if (subjectId) {
                    objParams.subjectId = `${subjectId}`;
                }

                if (roomId) {
                    objParams.roomId = `${roomId}`;
                }

                if (doctorId) {
                    objParams.doctorId = `${doctorId}`;
                }

                if (sectionId) {
                    objParams.sectionId = `${sectionId}`;
                }

                const params = {
                    fromPartnerId: partnerId,
                    toPartnerId: 'medpro',
                    price: subTotal,
                    ...objParams,
                    gender: Number(patientGender), // them gender de tim ra phong gi do
                    convertFrom: 'V1', // them convertFrom de tim ra phong gi do
                };
                // console.log('params dalieuhcm', params);
                try {
                    const dataKeys = (await this.transformDataBookingV1(this.urlTransform, params).toPromise()).data;
                    // console.log('result key dalieuhcm', dataKeys);
                    const keysObject = [
                        {
                            key: 'serviceId',
                            collectioName: 'services',
                            keyReturn: 'service',
                        },
                        {
                            key: 'subjectId',
                            collectioName: 'subjects',
                            keyReturn: 'subject',
                        },
                        {
                            key: 'roomId',
                            collectioName: 'rooms',
                            keyReturn: 'room',
                        },
                        {
                            key: 'doctorId',
                            collectioName: 'doctors',
                            keyReturn: 'doctor',
                        },
                        {
                            key: 'sectionId',
                            collectioName: 'sections',
                            keyReturn: 'section',
                        },
                    ];
                    /* tiếp tục tìm lại trong collection để get data */
                    let listKeysReturn: any = {
                        // service: {},
                        // subject: {},
                        // room: {},
                        // doctor: {},
                    };
                    for await (const obj of keysObject) {
                        const collectioName = obj.collectioName;
                        const key = obj.key;
                        const value = dataKeys[key];
                        const keyReturn = obj.keyReturn;
                        if (value) {
                            const objectInfo = await this.getObjectInfo(collectioName, value);
                            if (objectInfo) {
                                const { _id, id: idObj } = objectInfo.toObject();
                                const resuultObj = { [key]: idObj, [keyReturn]: _id };
                                listKeysReturn = { ...listKeysReturn, ...resuultObj };
                            }
                        }
                    }
                    /* Sau khi có thông tin id của medpro */
                    let bhytObj: any = {};
                    switch (bhytAccept) {
                        case 1:
                            bhytObj = { ...bhytObj, insuranceChoice: 'DUNG_TUYEN', insuranceCode: 'CO_BAO_HIEM' };
                            break;
                        case 2:
                            bhytObj = { ...bhytObj, insuranceChoice: 'TAI_KHAM', insuranceCode: 'CO_BAO_HIEM' };
                            break;
                        default:
                            break;
                    }
                    const formatDate = moment(bookingDate).format('YYYY-MM-DD');
                    const defaultValue = get(getBooking, 'bvTime', '00:00');
                    const bookingDateFormat = moment(`${formatDate} ${defaultValue}:00`, 'YYYY-MM-DD HH:mm:ss').subtract(7, 'hours').toDate();
                    const bookingId = `dalieuhcm_${syncBookingIdV1}`;
                    const visible = Number(bookingStatus) === 0 ? false : true;
                    const vIBooking: any = {
                        id: uuid.v4().replace(/-/g, ''),
                        bookingId,
                        bookingCode,
                        transactionId,
                        partnerId,
                        appId: partnerId,
                        partner: getPartner._id,
                        /* thông tin service, subject, room, doctor */
                        ...listKeysReturn,
                        sequenceNumber: bookingNumber,
                        status: bookingStatus,
                        paymentStatus: this.returnPaymentStatus(paymentStatus),
                        visible,
                        platform: bookingPlatform,
                        ...bhytObj,
                        date: bookingDateFormat,
                        bookingSlotId: 'NOT_SYNC_V1_YET',
                        /* thêm key để phân biệt v1 <=> v2 */
                        syncBookingType: 1, // v1: 1  v2: 2
                        syncBookingIdV1,
                        syncUserIdV1,
                        syncPatientIdV1,
                        patientNameV1: fullname,
                        patientPhoneV1: patientMobile,
                        patientMSBNV1: patientCode,
                        /* patient id */
                        patientId: patient.id,
                        patient: patient.idRef,
                        /* user v2 */
                        ...objGetUserV2,
                    };
                    // console.log('parms insert sync booking', vIBooking);
                    const newBooking = new this.bookingModel(vIBooking);
                    let booking;
                    try {
                        const newConstraintsBookingCode = new this.constraintsBookingCodeModel({
                            bookingCode: `${bookingCode}_${partnerId}`,
                        });
                        await newConstraintsBookingCode.save();
                        booking = await newBooking.save();
                    } catch (error) {
                        const { bookingCode: bookCode, ...rest } = vIBooking;
                        // console.log('update sync booking', rest);
                        booking = await this.bookingModel.findOneAndUpdate({
                            bookingCode: bookCode,
                            partnerId,
                            status: { $ne: 2 },
                        }, { ...rest }, { new: true }).exec();

                        if (!booking) {
                            /* trường hợp update ko phải trạng thái */
                            try {
                                const { status, ...rest2 } = rest;
                                booking = await this.bookingModel.findOneAndUpdate({
                                    bookingCode: bookCode,
                                    partnerId,
                                }, { ...rest2 }, { new: true }).exec();
                            } catch (error222) {
                                console.log(error222);
                            }
                        } else {
                            try {
                                const { status, paymentStatus: pStatus, patientId: patientIDDDD, patient: patientOOO, ...rest2 } = rest;
                                booking = await this.bookingModel.findOneAndUpdate({
                                    bookingCode: bookCode,
                                    partnerId,
                                }, {
                                    paymentStatus: pStatus,
                                    patient: patientOOO,
                                    patientId: patientIDDDD,
                                }, { new: true }).exec();
                            } catch (error222) {
                                console.log(error222);
                            }
                        }
                    }
                    /* tiến hành insert vào trong payment */
                    const paymentMethodObj = this.getPaymentMethod(partnerId, methodIdV1, jsonData);
                    const returnObjMethod = this.transformPaymentMethod(paymentMethodObj);
                    const totalFee = medproFee + transferFee;
                    const paymentTime = moment(paymentDateUpdate, 'YYYY-MM-DD HH:mm:ss').subtract(7, 'hours').format('YYYY-MM-DD HH:mm:ss');
                    const vIPayment: any = {
                        id: uuid.v4().replace(/-/g, ''),
                        amount,
                        subTotal,
                        medproFee,
                        totalFee,
                        transferFee,
                        transactionId,
                        status: this.returnPaymentStatus(paymentStatus),
                        appId: partnerId,
                        partnerId,
                        partner: getPartner._id,
                        date: bookingDateFormat,
                        bookingId,
                        email: bookingEmail,
                        ...returnObjMethod,
                        patientId: patient.id,
                        patient: patient.idRef,
                        paymentTime,
                    };
                    const newPayment = new this.paymentModel(vIPayment);
                    let payment;
                    try {
                        const newConstraintsPaymentTransaction = new this.constraintsPaymentTransactionModel({
                            transactionId: `${transactionId}_${partnerId}`,
                        });
                        await newConstraintsPaymentTransaction.save();
                        payment = await newPayment.save();
                    } catch (error) {
                        const { transactionId: transId, ...rest } = vIPayment;
                        payment = await this.paymentModel.findOneAndUpdate({
                            transactionId: transId,
                            partnerId,
                        }, { ...rest }, { new: true }).exec();
                    }
                    /* tiến hành tạo booking search */
                    const objBookingSearch = {
                        msbn: patientCode,
                        fullname,
                        mobile: patientMobile,
                    };
                    const vIBookingSearch: any = {
                        idBooking: booking.id,
                        idPatient: `${booking?.patientId || ''}`,
                        booking: booking._id,
                        bookingCode: booking.bookingCode,
                        ...objBookingSearch,
                        transactionId,
                        username: '',
                        partnerId,
                        /* dành cho v1 */
                        bookingType: 1,
                    };
                    const newBookingSearch = new this.bookingSearchModel(vIBookingSearch);
                    try {
                        await newBookingSearch.save();
                    } catch (error) {
                        this.logger.error(error);
                    }
                    /* END: tiến hành tạo booking search */
                    return {
                        booking,
                        payment,
                    };
                } catch (error) {
                    const { response } = error;
                    if (response) {
                        const { status = HttpStatus.BAD_REQUEST } = response;
                        throw new HttpException(error, status);
                    } else {
                        throw new HttpException(error, HttpStatus.BAD_REQUEST);
                    }
                }
            } else {
                return null;
            }
        } catch (error) {
            console.log(error);
            const { response } = error.response;
            if (response) {
                this.logger.error(response);
            } else {
                this.logger.error(error);
            }
            return null;
        }
    }

    async syncUmcBooking(formData: { bookingCodeV1?: string }): Promise<any> {
        // kiem tra xem có gửi bookingCodeV1 hay ko
        let getBooking: any = null;
        if (formData?.bookingCodeV1) {
            getBooking = await this.pkhPatientKnex(this.bookingTable)
                .select(`${this.bookingTable}.id as id`)
                .where(`${this.bookingTable}.transaction_code_gd`, formData.bookingCodeV1)
                .first();
        } else {
            throw new HttpException('Vui lòng kiểm tra lại thông tin.', HttpStatus.BAD_REQUEST);
        }
    }

    // 1445
    async syncOneBooking(id: number, partnerId: string = 'umc'): Promise<any> {
        const getPartner = await this.hospitalModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'syncOneBooking',
                summary: 'Đồng bộ booking',
                nameParent: 'syncOneBooking',
                params : {
                    id,
                    partnerId
                },
                errorBody: {},
                response: getPartner,
                message: `Không tìm thấy thông tin partner cho id: ${id}`,
            });
            throw new Error(`Không tìm thấy thông tin partner ${partnerId} cho id: ${id}`);
        }
        try {
            const getBooking = await this.pkhPatientKnex(this.bookingTable)
                .select(
                    `${this.bookingTable}.id as syncBookingIdV1`,
                    `${this.bookingTable}.app as syncMethod`,
                    `${this.bookingTable}.user_id as syncUserIdV1`,
                    `${this.bookingTable}.status as bookingStatus`,
                    `${this.bookingTable}.bhyt_accept as bhytAccept`,
                    `${this.bookingTable}.platform as bookingPlatform`,
                    `${this.bookingTable}.booking_date as bookingDate`,
                    `${this.bookingTable}.transaction_code_gd as bookingCode`,
                    `${this.bookingTable}.email as bookingEmail`,
                    `payment_id as paymentId`,
                    `schedule_id as scheduleId`,
                    `${this.bookingTable}.patient_id as syncPatientIdV1`,
                    `booking_time_id as bookingTimeId`,
                    `booking_number as bookingNumber`,
                    `booking_phone as bookingPhone`,
                    `${this.bookingTable}.date_create as dateCreate`,
                    `${this.bookingTable}.date_update as dateUpdate`,
                    /*     ROOM TABLE          */
                    `${this.scheduleTable}.room_id as roomId`,
                    `${this.scheduleTable}.doctor_id as doctorId`,
                    `${this.scheduleTable}.hospital_subject_id as subjectId`,
                    /*     PAYMENT TABLE        */
                    `${this.paymentTable}.method_id as methodIdV1`,
                    `${this.paymentTable}.transaction_code_tt as transactionId`,
                    `${this.paymentTable}.amount as amount`,
                    `${this.paymentTable}.amount_original as subTotal`,
                    `${this.paymentTable}.amount_service as medproFee`,
                    `${this.paymentTable}.amount_gate as transferFee`,
                    `${this.paymentTable}.result as jsonData`,
                    `${this.paymentTable}.status as paymentStatus`,
                    `${this.paymentTable}.date_create as paymentDateCreate`,
                    `${this.paymentTable}.date_update as paymentDateUpdate`,
                    /*     PATIENT       */
                    `${this.patientTable}.surname as patientSur`,
                    `${this.patientTable}.name as patientName`,
                    `${this.patientTable}.sex as patientGender`,
                    `${this.patientTable}.bvdhyd_msbn as patientCode`,
                    `${this.patientTable}.mobile as patientMobile`,
                    `${this.patientTable}.email`,
                )
                .innerJoin(this.scheduleTable, `${this.scheduleTable}.id`, `${this.bookingTable}.schedule_id`)
                .innerJoin(this.patientTable, `${this.patientTable}.id`, `${this.bookingTable}.patient_id`)
                .leftJoin(this.paymentTable, `${this.paymentTable}.id`, `${this.bookingTable}.payment_id`)
                .where(`${this.bookingTable}.id`, id).first();
            /* tiến hành transform info */
            // console.log('getBooking', getBooking);
            if (getBooking) {
                const { roomId, doctorId, subjectId, bookingCode, bookingNumber, bookingEmail, syncBookingIdV1, syncUserIdV1, syncMethod,
                    bookingStatus, bookingPlatform, bhytAccept, bookingDate, patientCode, patientMobile, patientSur, patientName, syncPatientIdV1,
                    methodIdV1, transactionId, amount, subTotal, medproFee, transferFee, jsonData, paymentStatus, paymentDateUpdate,
                    paymentDateCreate, dateCreate, dateUpdate } = getBooking;
                const fullname = `${patientSur} ${patientName}`;
                if (syncMethod === 'medpro') {
                    throw new Error(`Id booking ${id}, Mã phiếu: ${bookingCode}, appId: ${syncMethod} này từ v2 => v1. Không cần SYNC`);
                }
                /* tiến hành sync user */
                // console.log('syncUserIdV1', syncUserIdV1);
                const user: IUser = await this.syncUserService.syncOneUser(syncUserIdV1);
                // console.log('user ne ', user);
                // return user;
                /* Tiến hành đồng bộ hồ sơ bệnh nhân */
                const patient = await this.syncPatient(syncPatientIdV1);
                /* Nếu tồn tại user dùng số điện thoại đăng nhập thì add thông tin patient vào user */
                const objGetUserV2: any = {};
                if (user && patient) {
                    objGetUserV2.userId = user._id;
                    const originPatient: IPatient = patient.origin;
                    // this.clientSentry.instance()
                    //     .captureMessage(`tiến hành add patient to user. user_id: ${user._id} patient_id: ${originPatient._id}`);
                    await this.syncUserService.addPatientToUser(user, originPatient);
                }
                // console.log('objGetUserV2', objGetUserV2);
                // console.log('patient sau khi insert', patient);
                const objParams: any = {};
                if (subjectId) {
                    objParams.subjectId = `${subjectId}`;
                }

                if (roomId) {
                    objParams.roomId = `${roomId}`;
                }

                if (doctorId) {
                    objParams.doctorId = `${doctorId}`;
                }

                const params = {
                    fromPartnerId: partnerId,
                    toPartnerId: 'medpro',
                    ...objParams,
                    price: subTotal,
                    convertFrom: 'V1',
                };
                // console.log('params booking', params);
                try {
                    const dataKeys = (await this.transformDataBookingV1(this.urlTransform, params).toPromise()).data;
                    // console.log('result params booking', dataKeys);
                    const keysObject = [
                        {
                            key: 'serviceId',
                            collectioName: 'services',
                            keyReturn: 'service',
                        },
                        {
                            key: 'subjectId',
                            collectioName: 'subjects',
                            keyReturn: 'subject',
                        },
                        {
                            key: 'roomId',
                            collectioName: 'rooms',
                            keyReturn: 'room',
                        },
                        {
                            key: 'doctorId',
                            collectioName: 'doctors',
                            keyReturn: 'doctor',
                        },
                    ];
                    /* tiếp tục tìm lại trong collection để get data */
                    let listKeysReturn: any = {
                        // service: {},
                        // subject: {},
                        // room: {},
                        // doctor: {},
                    };
                    for await (const obj of keysObject) {
                        const collectioName = obj.collectioName;
                        const key = obj.key;
                        const value = dataKeys[key];
                        const keyReturn = obj.keyReturn;
                        if (value) {
                            const objectInfo = await this.getObjectInfo(collectioName, value);
                            if (objectInfo) {
                                const { _id, id: idObj } = objectInfo.toObject();
                                const resuultObj = { [key]: idObj, [keyReturn]: _id };
                                listKeysReturn = { ...listKeysReturn, ...resuultObj };
                            }
                        }
                    }
                    /* Sau khi có thông tin id của medpro */
                    let bhytObj: any = {};
                    switch (bhytAccept) {
                        case 1:
                            bhytObj = { ...bhytObj, insuranceChoice: 'DUNG_TUYEN', insuranceCode: 'CO_BAO_HIEM' };
                            break;
                        case 2:
                            bhytObj = { ...bhytObj, insuranceChoice: 'TAI_KHAM', insuranceCode: 'CO_BAO_HIEM' };
                            break;
                        default:
                            break;
                    }
                    let getThoiGianDuKien = '23:59';
                    const formatDate = moment(bookingDate).format('YYYY-MM-DD');
                    let bookingDateFormat;
                    if (this.getEnv === 'DEVELOPMENT') {
                        getThoiGianDuKien = '23:59';
                        bookingDateFormat = moment(`${formatDate} ${getThoiGianDuKien}:00`, 'YYYY-MM-DD HH:mm:ss').toDate();
                    } else {
                        getThoiGianDuKien = '23:59';
                        bookingDateFormat = moment(`${formatDate} ${getThoiGianDuKien}:00`, 'YYYY-MM-DD HH:mm:ss').subtract(7, 'hours').toDate();
                    }
                    const bookingId = `umc_${syncBookingIdV1}`;
                    const visible = Number(bookingStatus) === 0 ? false : true;
                    const vIBooking: any = {
                        id: uuid.v4().replace(/-/g, ''),
                        bookingId,
                        bookingCode,
                        transactionId,
                        partnerId,
                        appId: partnerId,
                        partner: getPartner._id,
                        /* thông tin service, subject, room, doctor */
                        ...listKeysReturn,
                        sequenceNumber: bookingNumber,
                        status: bookingStatus,
                        paymentStatus: this.returnPaymentStatus(paymentStatus),
                        visible,
                        platform: bookingPlatform,
                        ...bhytObj,
                        date: bookingDateFormat,
                        bookingSlotId: 'NOT_SYNC_V1_YET',
                        /* thêm key để phân biệt v1 <=> v2 */
                        syncBookingType: 1, // v1: 1  v2: 2
                        syncBookingIdV1,
                        syncUserIdV1,
                        syncPatientIdV1,
                        patientNameV1: fullname,
                        patientPhoneV1: patientMobile,
                        patientMSBNV1: patientCode,
                        /* patient id */
                        patientId: patient.id,
                        patient: patient.idRef,
                        /* user v2 */
                        ...objGetUserV2,
                        createdAt: moment(dateCreate).toDate(),
                        updateAt: moment(dateUpdate).toDate(),
                        syncAt: moment().toDate(),
                    };
                    // console.log('parms insert booking', vIBooking);
                    const newBooking = new this.bookingModel(vIBooking);
                    let booking;
                    try {
                        const newConstraintsBookingCode = new this.constraintsBookingCodeModel({
                            bookingCode: `${bookingCode}_${partnerId}`,
                        });
                        await newConstraintsBookingCode.save();
                        booking = await newBooking.save();
                    } catch (error) {
                        this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                            name: 'constraintsBookingCodeModel',
                            summary: 'newConstraintsBookingCode',
                            nameParent: 'syncOneBooking',
                            params : {
                                id, partnerId,bookingCode: `${bookingCode}_${partnerId}`,
                            },
                            errorBody: this.utilService.errorHandler(error),
                            response: {},
                            message: error?.message,
                        });
                        const { bookingCode: bookCode, ...rest } = vIBooking;
                        // console.log('update sync booking', rest);
                        booking = await this.bookingModel.findOneAndUpdate({
                            bookingCode: bookCode,
                            partnerId,
                            status: { $ne: 2 },
                        }, { ...rest }, { new: true }).exec();

                        if (!booking) {
                            /* trường hợp update ko phải trạng thái */
                            try {
                                const { status, ...rest2 } = rest;
                                booking = await this.bookingModel.findOneAndUpdate({
                                    bookingCode: bookCode,
                                    partnerId,
                                }, { ...rest2 }, { new: true }).exec();
                            } catch (error222) {
                                // console.log(error222);
                                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                                    name: 'bookingModel.findOneAndUpdate',
                                    summary: 'trường hợp update ko phải trạng thái',
                                    nameParent: 'syncOneBooking',
                                    params : {
                                        id, rest, bookCode, partnerId
                                    },
                                    errorBody: this.utilService.errorHandler(error222),
                                    response: {},
                                    message: error222?.message,
                                });
                            }
                        } else {
                            try {
                                const { status, paymentStatus: pStatus, patientId: patientIDDDD, patient: patientOOO, ...rest2 } = rest;
                                booking = await this.bookingModel.findOneAndUpdate({
                                    bookingCode: bookCode,
                                    partnerId,
                                }, {
                                    paymentStatus: pStatus,
                                    patient: patientOOO,
                                    patientId: patientIDDDD,
                                }, { new: true }).exec();
                            } catch (error222) {
                                // console.log(error222);
                                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                                    name: 'bookingModel.findOneAndUpdate',
                                    summary: 'trường hợp update là trạng thái',
                                    nameParent: 'syncOneBooking',
                                    params : {
                                        id, rest, bookCode, partnerId
                                    },
                                    errorBody: this.utilService.errorHandler(error222),
                                    response: {},
                                    message: error222?.message,
                                });
                            }
                        }
                    }
                    /* Lấy trạng thái booking để tìm lại giờ khám dự kiến */
                    if (booking.status === 1) {
                        const getTime = await this.getBookingV1(booking.partnerId, booking.syncUserIdV1, booking.syncBookingIdV1);
                        // console.log('getTime Boking detail uMC: ', getTime);
                        if (getTime) {
                            await this.bookingModel.findByIdAndUpdate({
                                _id: booking._id,
                            }, {
                                date: getTime,
                            }).exec();
                        }
                    }
                    /* tiến hành insert vào trong payment */
                    // console.log('json data ne', jsonData);
                    const paymentMethodObj = this.getPaymentMethod(partnerId, methodIdV1, jsonData);
                    // console.log('paymentMethodObj', paymentMethodObj);
                    const returnObjMethod = this.transformPaymentMethod(paymentMethodObj);
                    // console.log('returnObjMethod', returnObjMethod);
                    const totalFee = medproFee + transferFee;
                    const paymentTime = moment(paymentDateCreate, 'YYYY-MM-DD HH:mm:ss').subtract(7, 'hours').format('YYYY-MM-DD HH:mm:ss');
                    const vIPayment: any = {
                        id: uuid.v4().replace(/-/g, ''),
                        amount,
                        subTotal,
                        medproFee,
                        totalFee,
                        transferFee,
                        transactionId,
                        status: this.returnPaymentStatus(paymentStatus),
                        appId: partnerId,
                        partnerId,
                        partner: getPartner._id,
                        date: bookingDateFormat,
                        // bookingId,
                        email: bookingEmail,
                        ...returnObjMethod,
                        patientId: patient.id,
                        patient: patient.idRef,
                        paymentTime,
                        syncAt: moment().toDate(),
                        createdAt: moment(paymentDateCreate).toDate(),
                        updateAt: moment(paymentDateUpdate).toDate(),
                    };
                    // console.log('prams insert payment', vIPayment);
                    const newPayment = new this.paymentModel(vIPayment);
                    let payment;
                    try {
                        const newConstraintsPaymentTransaction = new this.constraintsPaymentTransactionModel({
                            transactionId: `${transactionId}_${partnerId}`,
                        });
                        await newConstraintsPaymentTransaction.save();
                        payment = await newPayment.save();
                    } catch (error) {
                        this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                            name: 'newConstraintsPaymentTransaction',
                            summary: 'newConstraintsPaymentTransaction',
                            nameParent: 'syncOneBooking',
                            params : {
                                id,partnerId,transactionId: `${transactionId}_${partnerId}`,
                            },
                            errorBody: this.utilService.errorHandler(error),
                            response: {},
                            message: error?.message,
                        });
                        const { transactionId: transId, ...rest } = vIPayment;
                        // console.log('transId', transId);
                        payment = await this.paymentModel.findOneAndUpdate({
                            transactionId: transId,
                            partnerId,
                        }, { ...rest }, { new: true }).exec();
                    }
                    // console.log('payment info', payment);
                    /* tiến hành tạo booking search */
                    const objBookingSearch = {
                        msbn: patientCode,
                        fullname,
                        mobile: patientMobile,
                    };
                    const vIBookingSearch: any = {
                        idBooking: booking.id,
                        idPatient: `${booking?.patientId || ''}`,
                        booking: booking._id,
                        bookingCode: booking.bookingCode,
                        ...objBookingSearch,
                        transactionId,
                        username: '',
                        partnerId,
                        /* dành cho v1 */
                        bookingType: 1,
                    };
                    const newBookingSearch = new this.bookingSearchModel(vIBookingSearch);
                    try {
                        await newBookingSearch.save();
                    } catch (error) {
                        this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                            name: 'newBookingSearch',
                            summary: 'newBookingSearch',
                            nameParent: 'syncOneBooking',
                            params : {
                                id,partnerId,vIBookingSearch,
                            },
                            errorBody: this.utilService.errorHandler(error),
                            response: {},
                            message: error?.message,
                        });
                    }
                    /* END: tiến hành tạo booking search */
                    return {
                        booking,
                        payment,
                    };
                } catch (error) {
                    this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                        name: 'syncOneBooking',
                        summary: '',
                        nameParent: 'syncOneBooking',
                        params : {
                            id, partnerId
                        },
                        errorBody: this.utilService.errorHandler(error),
                        response: getPartner,
                        message: error?.message,
                    });
                    throw error;
                    // const { response } = error;
                    // if (response) {
                    //     const { status = HttpStatus.BAD_REQUEST } = response;
                    //     throw new HttpException(error, status);
                    // } else {
                    //     throw new HttpException(error, HttpStatus.BAD_REQUEST);
                    // }
                }
            } else {
                // throw new HttpException('Không tìm thấy thông tin phiếu khám.', HttpStatus.NOT_FOUND);
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'syncOneBooking',
                    summary: '',
                    nameParent: 'syncOneBooking',
                    params : {
                        id, partnerId
                    },
                    errorBody: {},
                    response: getPartner,
                    message:`Không tìm thấy thông in phiếu khám id: ${id}`,
                });
                // return null;
                throw new Error(`Không tìm thấy thông in phiếu khám id: ${id}`);
            }
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'syncOneBooking',
                summary: '',
                nameParent: 'syncOneBooking',
                params : {id, partnerId},
                errorBody: this.utilService.errorHandler(error),
                response: getPartner,
                message: error?.message,
            });
            // console.log(error);
            // const { response } = error.response;
            // if (response) {
            //     this.clientSentry.instance().captureException(response);
            //     // const { status = HttpStatus.BAD_REQUEST, data = {} } = response;
            //     // throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng thử lại sau.', status);
            // } else {
            //     this.clientSentry.instance().captureException(error);
            //     // throw new HttpException('Hệ thống không xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
            // }
            throw error;
        }
    }

    async getBookingV1(partnerId: string, syncUserIdV1: number, syncBookingIdV1: number): Promise<any> {
        /* gọi qua v1 */
        const urlGetBookingV1 = this.restApiMapping.NhiDong1GetBookingInfo();
        try {
            /* tìm lại user_id, access_token */
            let session: any = {};
            /* tìm lại thông tin session theo partnerid */
            switch (partnerId) {
                case 'nhidong1':
                    session = await this.sessionService.checkExistsNhiDong1SessionByUserID(syncUserIdV1);
                    break;
                case 'dalieuhcm':
                    session = await this.sessionService.checkExistsSkinSessionByUserID(syncUserIdV1);
                    break;
                case 'ctchhcm':
                    session = await this.sessionService.checkExistsCTCHSessionByUserID(syncUserIdV1);
                    break;
                case 'thuduc':
                    session = await this.sessionService.checkExistsThuDucSessionByUserID(syncUserIdV1);
                    break;
                case 'umc':
                    session = await this.sessionService.checkExistsUMCSessionByUserID(syncUserIdV1);
                    break;
                default:
                    break;
            }
            const { user_id, access_token } = session;
            const resultBookingV1 = (await this.getBookingInfoV1(
                urlGetBookingV1, user_id, access_token, syncBookingIdV1, partnerId).toPromise()).data;
            // console.log('response resultBookingV1 v1: ', resultBookingV1);

            const defaultValue = get(resultBookingV1, 'bv_time', '00:00');
            const getThoiGianDuKien = get(resultBookingV1, 'bv_booking_time', defaultValue);
            /* override lại cái date */
            // const bookingObj = getBooking.toObject();
            // console.log(resultBookingV1.booking_date);
            // console.log(getThoiGianDuKien);
            const resultValue = moment(`${resultBookingV1.booking_date} ${getThoiGianDuKien}:00`, 'YYYY-MM-DD HH:mm:ss')
                .subtract(7, 'hours').toDate();
            return resultValue;
        } catch (error) {
            throw error;
            // console.log(error)
            // this.clientSentry.instance().captureException(error);
            // this.clientSentry.instance().captureMessage(`Lỗi phần lấy thông tin phiếu khám ${partnerId} ${syncUserIdV1} ${syncBookingIdV1}`)
            // return null;
        }
    }

    getBookingInfoV1(
        url: string, userId: string, accessToken: string, bookingidV1: number, partnerId: string): Observable<AxiosResponse<any>> {
        const urlBase = `${url}?user_id=${userId}&access_token=${accessToken}&booking_id=${bookingidV1}`;
        // console.log('urlBase', urlBase);
        return this.httpService.getHttpRequestWithHeaders(urlBase, partnerId);
    }

    async getObjectInfo(collectioName: string, id: string): Promise<any> {
        switch (collectioName) {
            case 'services':
                return this.serviceModel.findOne({ id }).exec();
            case 'subjects':
                return this.subjectModel.findOne({ id }).exec();
            case 'rooms':
                return this.roomModel.findOne({ id }).exec();
            case 'doctors':
                return this.doctorModel.findOne({ id }).exec();
            case 'sections':
                return this.sectionModel.findOne({ id }).exec();
            default:
                return null;
        }
    }

    transformDataBookingV1(url: string, data: any): Observable<AxiosResponse<any>> {
        // console.log(url);
        return this.httpService.postHttpRequest(url, {
            ...data,
        });
    }

    async getSyncBookingSuccess(fromData: SyncBookingDTO): Promise<BaseResponse<ISyncBookingSuccess>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.syncBookingSuccessModel
                .find({})
                .sort({ createdAt: 'desc' })
                .skip(pageIndex*pageSize)
                .limit(pageSize)
                .exec(),
                this.syncBookingSuccessModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            }
        } catch (error) {
            this.logger.error(`Error when exec getSyncBookingSuccess()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getSyncBookingProcess(fromData: SyncBookingDTO): Promise<BaseResponse<ISyncBookingProcess>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.syncBookingProcessModel
                .find({})
                .sort({ createdAt: 'desc' })
                .skip(pageIndex*pageSize)
                .limit(pageSize)
                .exec(),
                this.syncBookingProcessModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            }
        } catch (error) {
            this.logger.error(`Error when exec getSyncBookingProcess()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getSyncBookingProcessFailed(fromData: SyncBookingDTO): Promise<BaseResponse<ISyncBookingProcessFailed>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.syncBookingProcessFailModel
                .find({})
                .sort({ createdAt: 'desc' })
                .skip(pageIndex*pageSize)
                .limit(pageSize)
                .exec(),
                this.syncBookingProcessFailModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            }
        } catch (error) {
            this.logger.error(`Error when exec getSyncBookingProcessFailed()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async rollBackSyncBookingFailService(processId: string): Promise<any> {
        try {
            const syncBookingFail = await this.syncBookingProcessFailModel.findOne({ processId }).lean().exec();
            if (!syncBookingFail) {
                throw new HttpException(`Không tìm thấy dữ liệu với processId là ${processId} `, 404)
            }
            const { errorBody, syncStatus, ...data } = syncBookingFail
            await this.syncBookingProcessModel.create({ ...data, syncStatus: SyncProcessStatus.PENDING })
            await this.syncBookingProcessFailModel.deleteOne({ processId }).exec();
        } catch (error) {
            this.logger.error(`Error when exec rollBackSyncBookingFailService()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

}
