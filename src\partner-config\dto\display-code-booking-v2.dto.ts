import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class DisplayCodeBookingV2Dto {
    @ApiProperty({
        description: 'title',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    title: string;

    @ApiProperty({
        description: 'type',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    type: string;

    @ApiProperty({
        description: 'value',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    value: string;

    @ApiProperty({
        description: 'treeId',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    treeId: string;

}
