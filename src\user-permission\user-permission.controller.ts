import { <PERSON>pi<PERSON>ody, ApiTags } from '@nestjs/swagger';
import { Controller, Put, Body, Query, Get, UseGuards, Req, UseInterceptors } from '@nestjs/common';
import { PermissionAddUserDto } from './dto/permission-add-user.dto';
import { UserPermissionService } from './user-permission.service';
import { PermissionPageOptions } from './dto/permission-page-options.dto';
import { IAddIcsPermissionToUser } from './dto/add-ics-permission-user.dto';
import { CskhGuard } from 'src/common/guards/cskh.guard';
import { CskhCare247SalaryGuard } from '../common/guards/cskh-care247-salary.guard';
import { AppCskhActivitiesInterceptor } from '../middleware/app-cskh-activities.interceptor';
import { ActionData } from '../cskh/decorator/cskh-action.decorator';
import { ActionEnum, CategoryEnum } from '../cskh/decorator/action-data.enum';

@Controller('user-permission')
@ApiTags('User Permission CSKH')
export class UserPermissionController {
    constructor(private readonly service: UserPermissionService) {}

    @Get('permissions')
    getPermissions(@Query() options?: PermissionPageOptions): Promise<any> {
        return this.service.getPermissions(options);
    }

    @Get()
    getPermissionByUser(@Query('usermongoid') userMongoId: string): Promise<any> {
        return this.service.getPermissionByUser(userMongoId);
    }

    @Put('permission-provide-user')
    @ApiBody({
        type: PermissionAddUserDto,
    })
    addPermissionToUser(@Body() formData: PermissionAddUserDto): Promise<any> {
        return this.service.addPermissionToUser(formData);
    }

    @Put('add-ics-permission-user')
    @ApiBody({
        type: PermissionAddUserDto,
    })
    @UseGuards(CskhGuard, CskhCare247SalaryGuard)
    @UseInterceptors(AppCskhActivitiesInterceptor)
    @ActionData({ category: CategoryEnum.QUAN_LY_NHAN_VIEN, action: ActionEnum.UPDATE_USER_CSKH_CARE247 })
    addIcsPermissionToUser(@Req() req: any, @Body() formData: IAddIcsPermissionToUser): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.addIcsPermissionToUser(userMongoId, formData);
    }
}
