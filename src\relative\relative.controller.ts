import { Controller, Get, Headers } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { RelativeService } from './relative.service';

@Controller('relative')
@ApiTags('<PERSON>uan hệ Thân nhân trên MongoDB')
export class RelativeController {
    constructor(
        private readonly relativeService: RelativeService,
    ) { }

    @Get('list')
    async getRelativeList(@Headers('appid') appid: string, @Headers('partnerid') partnerid: string): Promise<any> {
        return this.relativeService.getAll(appid, partnerid);
    }
}
