import {
    Controller,
    Post,
    UseGuards,
    Req,
    Query,
    Headers,
    Get,
    HttpException,
    HttpStatus,
    Body,
    Delete,
    HttpCode,
    UseInterceptors,
    HttpService
} from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiHeader } from '@nestjs/swagger';
import { PatientMongoService } from './patient-mongo.service';
import { AuthGuard } from '@nestjs/passport';
import { PatientFormMongoDTO } from './dto/patient-form-mongo.dto';
import { SearchPatientDTO } from 'src/patient/dto/search-patient.dto';
import { SearchPatientExtraInfoDTO } from 'src/patient/dto/search-patient-extra-info.dto';
import { VerifyPhonePatientDTO } from 'src/patient/dto/verify-phone-patient.dto';
import { VerifyPhonePatientMongoDTO } from 'src/patient/dto/verify-phone-patient-mongo.dto';
import { SearchPatientExtraInfoMongoDTO } from 'src/patient/dto/search-patient-extra-info-mongo.dto';
import { AddPatientToUserDTO } from 'src/patient/dto/add-patient-to-user.dto';
import * as moment from 'moment';
import { UpdatePatientFormDataDTO } from 'src/patient/dto/update-patient-form-data.dto';
import { UpdatePatientMongoDTO } from './dto/update-patient-mongo.dto';
import { PatientFormMongoUpdateDTO } from './dto/patient-form-update-mongo.dto';
import { find, get } from 'lodash';
import { GetInsuranceInfoDTO } from './dto/get-insurance-info.dto';
import { GetInsuranceDateDTO } from './dto/get-insurance-date.dto';
import { GetInsuranceParseAddressDTO } from './dto/get-insurance-parse-address.dto';
import { IsSelectedPatientDTO } from './dto/is-selected-patient.dto';
import { VerifyInsuranceCodePatientMongoDTO } from 'src/patient/dto/verify-insurance-code-patient-mongo.dto';
import { PatientProfileDTO } from './dto/patient-profile.dto';
import { GetPatientProfileDTO } from './dto/get-patient-profile.dto';
import { ConstraintSearchLogDTO } from './dto/constraint-search-log.dto';
import { YearOldValidationDto } from './dto/year-old-validation.dto';
import { AppCskhInterceptor } from 'src/middleware/app-cskh.interceptor';
import { ValidateBookingRuleDto } from './dto/validate-booking-rule.dto';
import { CskhGuard } from '../common/guards/cskh.guard';
import { PatientTrackingDto } from "./dto/patient-tracking.dto";
import { PatientBasicInfoDto } from './dto/patient-basic-info.dto';
import { ErrorMessage } from '../common/enums/message-error.enum';
import { UrlConfigService } from '../config/config.url.service';
import { ConfigRepoService } from '../config/config.repo.service';
import { FindPatientHisDto } from './dto/find-patient-his.dto';
import { ViettelPayInterceptor } from 'src/middleware/viettelpay.interceptor';
import { AppCskhActivitiesInterceptor } from '../middleware/app-cskh-activities.interceptor';
import { PartnerCSKHGuard } from '../common/guards/partner-cskh.guard';

@Controller('mongo/patient')
@ApiTags('Patient - Quản lý thông tin Bệnh nhân')
export class PatientMongoController {

    constructor(
        private readonly patientMongoService: PatientMongoService,
        private httpService: HttpService,
        private urlConfigService: UrlConfigService,
        private repoConfigService: ConfigRepoService,
    ) { }

    // @Post('test-duplicate-msbn')
    // async testDuplicateMsbn(): Promise<any> {
    //     return this.patientMongoService.checkExistsUMCPatientBelongsToUser2();
    // }

    @Post('create-patient-profile')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async createPatientProfile(
        @Body() formData: PatientProfileDTO,
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException({
                message: 'Token không hợp lệ.',
                error: 401,
            }, HttpStatus.UNAUTHORIZED);
        }
        const userId = user.userMongoId;
        return this.patientMongoService.createPatientProfile(appId, partnerid, userId, formData);
    }

    @Post('get-patient-profiles')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getPatientProfile(
        @Body() formData: GetPatientProfileDTO,
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException({
                message: 'Token không hợp lệ.',
                error: 401,
            }, HttpStatus.UNAUTHORIZED);
        }
        const userId = user.userMongoId;
        return this.patientMongoService.getPatientProfile(appId, partnerid, userId, formData);
    }

    @Get('test-find-phone-cmnd-his')
    async testFindPhoneCmndHIS(
        @Query('partnerId') partnerId: string,
        // @Query('phone') phone?: string,
        // @Query('cmnd') cmnd?: string,
    ): Promise<any> {
        return this.patientMongoService.testFindPhoneCmndHIS(partnerId, '**********', '020455991');
    }

    @UseGuards(CskhGuard)
    @Post('find-patient-by-cmnd-his')
    async findPatientByCmndHis(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string, 
        @Headers('cskhtoken') cskhToken: string,
        @Body() body: any): Promise<any> {
        if (!!partnerid === false) {
            partnerid = 'medpro';
        }
        return this.patientMongoService.findPatientByCmndHis(partnerid, appid, body);
    }

    @Get('test-transform-data-v1')
    async testTransformDataPatientV1(): Promise<any> {
        return this.patientMongoService.testTransformDataPatientV1();
    }

    @Get('auto-generate-medpro-id')
    async autoGenerateMedproId(): Promise<any> {
        return this.patientMongoService.autoGenerateMedproId();
    }

    @Post('insurance/getInfo')
    async getInsuranceInfo(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string, @Body() insuranceForm: GetInsuranceInfoDTO): Promise<any> {
        return this.patientMongoService.getInsuranceInfo(partnerid, appid, insuranceForm);
    }

    @Post('insurance/getDate')
    @UseInterceptors(ViettelPayInterceptor)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getInsuranceDate(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
        @Body() insuranceForm: GetInsuranceDateDTO): Promise<any> {
        const user = req.user;
        
        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const userId = objUser.userMongoId;
        return this.patientMongoService.getInsuranceDate(partnerid, appid, insuranceForm, req, userId, cskhInfo);
    }

    // @Post('insurance/test-insurance')
    // async testInsurance(): Promise<any> {
    //     return this.patientMongoService.testInsurance();
    // }

    @Post('insurance/parse-address')
    async getInsuranceParseAddress(@Body() formData: GetInsuranceParseAddressDTO): Promise<any> {
        return this.patientMongoService.getInsuranceParseAddress(formData);
    }

    @Post('insert')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseInterceptors(ViettelPayInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async insertUMCPatient(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req, @Body() patientFormData: PatientFormMongoDTO): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException({
                message: 'Token không hợp lệ.',
                error: 401,
            }, HttpStatus.UNAUTHORIZED);
        }

        if (!appId) {
            appId = partnerid;
        }

        if (!partnerid) {
            partnerid = appId;
        }
        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
            patientFormData.cskhUserId = cskhInfo.cskhUserId;
        }
        return this.patientMongoService.insertPatient(partnerid, appId, objUser, patientFormData, cskhInfo);
    }

    @Post('insert-basic-info')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async insertBasicInfo(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('platform') platform: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req, @Body() patientFormData: PatientBasicInfoDto): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException({
                message: 'Token không hợp lệ.',
                error: 401,
            }, HttpStatus.UNAUTHORIZED);
        }

        if (!appId) {
            appId = partnerid;
        }

        if (!partnerid) {
            partnerid = appId;
        }

        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
            // patientFormData.cskhUserId = cskhInfo.cskhUserId;
        }

        return this.patientMongoService.insertBasicInfo(patientFormData, objUser.userMongoId, partnerid, appId, platform);
    }

    @Post('update-msbn')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'), PartnerCSKHGuard)
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async updatePatientHasPatientCode(
        @Req() req,
        @Body() updatePatientMongoDTO: PatientFormMongoUpdateDTO,
        @Headers('cskhtoken') cskhToken: string,
        @Headers('partnerid') partnerId: string,
        @Headers('appid') appId: string,
        @Headers('locale') locale: string,
    ): Promise<any> {
        const user = req.user;

        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        return this.patientMongoService.upatePatientWithoutPatientCode(partnerId, appId, objUser, updatePatientMongoDTO, cskhInfo, locale);
    }

    // @Post('update-msbn')
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    // async updatePatientHasPatientCode(@Req() req, @Body() updatePatientMongoDTO: UpdatePatientMongoDTO): Promise<any> {
    //     const user = req.user;
    //     return this.patientMongoService.updatePatientHasPatientCode(updatePatientMongoDTO, user.userMongoId);
    // }

    @Post('update-without-msbn')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'), PartnerCSKHGuard)
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async upatePatientWithoutPatientCode(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
        @Body() patientFormData: PatientFormMongoUpdateDTO,
    ): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException({
                message: 'Token không hợp lệ.',
                error: 401,
            }, HttpStatus.UNAUTHORIZED);
        }
        if (!appId) {
            appId = partnerid;
        }

        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        return this.patientMongoService.upatePatientWithoutPatientCode(partnerid, appId, objUser, patientFormData, cskhInfo);
    }

    @Get('getbyuserid')
    @UseInterceptors(AppCskhInterceptor)
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async getAllPatientsByUserId(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException({
                message: 'Token không hợp lệ.',
                error: 401,
            }, HttpStatus.UNAUTHORIZED);
        }

        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        return this.patientMongoService.getPatientsByUserIdMedpro(appid, partnerid, objUser);
    }

    @Post('getbyuserid')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor, ViettelPayInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async getAllValidPatientsByUserId(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
        @Body() formData: YearOldValidationDto,
    ): Promise<any> {

        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException(
                {
                    message: 'Token không hợp lệ.',
                    error: 401,
                },
                HttpStatus.UNAUTHORIZED,
            );
        }
        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);
        const objUser = { ...user };
        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        formData.version = 2;
        return this.patientMongoService.getPatientsByUserIdMedpro(appid, partnerid, objUser, formData);
    }

    @Post('getbyuserid-v2')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async getAllPatientsByUserIdV2(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Body() formData: IsSelectedPatientDTO,
        @Req() req): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException({
                message: 'Token không hợp lệ.',
                error: 401,
            }, HttpStatus.UNAUTHORIZED);
        }

        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const result = await this.patientMongoService.getPatientsByUserIdMedpro(appid, partnerid, objUser, formData);
        return {
            list: result,
            cskhPatientIdSelected: formData.isSelectedPatient ? (cskhInfo?.patientId || '') : '',
        };
    }

    @Get('search')
    async searchExtraInfo(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        // @Body() searchPatientDTO: SearchPatientDTO
    ): Promise<any> {
        // throw new HttpException('Chức năng này đang được nâng cấp, chúng tôi sẽ cập nhật lại trong thời gian sớm nhất!', HttpStatus.FORBIDDEN);
        return this.patientMongoService.searchExtraInfo(partnerid, appid);
    }

    @UseGuards(CskhGuard)
    @Get('remove-limit-patient-search')
    async removeLimitPatientSearch(@Query('phone') phone: string): Promise<any> {
        return this.patientMongoService.removeLimitPatientSearch(phone);
    }

    @Post('getbymsbn')
    @ApiOperation({
        summary: 'Tìm hồ sơ bệnh nhân theo mã số bệnh nhân.',
        description: 'Tìm hồ sơ bệnh nhân theo mã số bệnh nhân.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async searchPatientByMsbn(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Body() searchPatientDTO: SearchPatientDTO, @Req() req): Promise<any> {
        if (!!partnerid === false) {
            partnerid = 'medpro';
        }
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException({
                message: 'Token không hợp lệ.',
                error: 401,
            }, HttpStatus.UNAUTHORIZED);
        }

        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        // throw new HttpException('Chức năng này đang được nâng cấp, chúng tôi sẽ cập nhật lại trong thời gian sớm nhất!', HttpStatus.FORBIDDEN);
        return this.patientMongoService.searchPatientByMsbn(objUser.userMongoId, appid, partnerid, searchPatientDTO, true, cskhInfo, req);
    }

    @Post('find-patient-by-extra-info')
    @ApiOperation({
        summary: 'Tìm hồ sơ bệnh nhân theo họ, tên, giới tính, năm sinh, tỉnh thành.',
        description: 'Tìm hồ sơ bệnh nhân theo họ, tên, giới tính, năm sinh, tỉnh thành.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async findUMCPatientByExtraInfo(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Body() searchPatientExtraInfoDTO: SearchPatientExtraInfoMongoDTO, @Req() req): Promise<any> {
        if (!!partnerid === false) {
            partnerid = 'medpro';
        }
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException({
                message: 'Token không hợp lệ.',
                error: 401,
            }, HttpStatus.UNAUTHORIZED);
        }

        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        // throw new HttpException('Chức năng này đang được nâng cấp, chúng tôi sẽ cập nhật lại trong thời gian sớm nhất!', HttpStatus.FORBIDDEN);
        return this.patientMongoService.findUMCPatientByExtraInfo(objUser.userMongoId, appid, partnerid, searchPatientExtraInfoDTO, cskhInfo);
    }

    @Post('find-patient-his')
    @ApiOperation({
        summary: 'Tìm hồ sơ bệnh nhân theo họ, tên, giới tính, năm sinh, tỉnh thành.',
        description: 'Tìm hồ sơ bệnh nhân theo họ, tên, giới tính, năm sinh, tỉnh thành.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @UseInterceptors(ViettelPayInterceptor)
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async findPatientHis(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Body() body: FindPatientHisDto,
        @Req() req): Promise<any> {
        if (!!partnerid === false) {
            partnerid = 'medpro';
        }
        const user = req.user;

        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        return this.patientMongoService.findPatientHis(objUser.userMongoId, appid, partnerid, body, cskhInfo);
    }

    @Post('verify-phone')
    @ApiOperation({
        summary: 'Xác thực xem số điện thoại có trùng khớp với hồ sơ bệnh nhân.',
        description: 'Xác thực xem số điện thoại có trùng khớp với hồ sơ bệnh nhân.',
    })
    @UseInterceptors(ViettelPayInterceptor)
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    async verifyUMCPatientByPhone(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string, @Body() verifyPhonePatientDTO: VerifyPhonePatientMongoDTO): Promise<any> {
        return this.patientMongoService.verifyUMCPatientByPhone(partnerid, appid, verifyPhonePatientDTO);
    }

    @Post('verify-insurance-code')
    @ApiOperation({
        summary: 'Xác thực xem số điện thoại có trùng khớp với hồ sơ bệnh nhân.',
        description: 'Xác thực xem số điện thoại có trùng khớp với hồ sơ bệnh nhân.',
    })
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    async verifyPatientByInsuranceCode(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string, @Body() verifyInsuranceCodePatientDTO: VerifyInsuranceCodePatientMongoDTO): Promise<any> {
        return this.patientMongoService.verifyPatientByInsuranceCode(partnerid, appid, verifyInsuranceCodePatientDTO);
    }

    @Post('add-patient-to-user')
    @ApiOperation({
        summary: 'Gửi secret key lên để decode xem patientId thuộc UserId.',
        description: 'Gửi secret key lên để decode xem patientId thuộc UserId.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async addPatientToUserUMCPatient(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req, @Body() addPatientToUserDTO: AddPatientToUserDTO): Promise<any> {
        const user = req.user;

        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        return this.patientMongoService.addPatientToUserUMCPatient(partnerid, appId, addPatientToUserDTO, objUser.userMongoId, cskhInfo);
    }

    @Get('detail-for-update')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async getUMCPatientDetailForUpdate(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req, @Query('id') id: string): Promise<any> {
        const user = req.user;
        if (!!partnerId === false) {
            if (appId !== 'medpro') {
                partnerId = appId;
            }
        }
        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        return this.patientMongoService.getPatientDetailForUpdate(id, objUser.userMongoId, partnerId, appId, cskhInfo);
    }

    @Get('unlink-patient')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'), PartnerCSKHGuard)
    async unLinkPatient(@Req() req, @Query('id') id: string, @Headers('cskhtoken') cskhToken: string): Promise<any> {
        const user = req.user;
        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        let userMongoId = user.userMongoId;
        if (cskhInfo && cskhInfo.userIdPatient) {
            userMongoId = cskhInfo.userIdPatient;
        }
        return this.patientMongoService.unLinkPatient(id, userMongoId, cskhInfo);
    }

    @Get('tracking')
    @ApiBearerAuth()
    @UseGuards(CskhGuard)
    async getPatientTracking(@Headers('cskhtoken') cskhToken: string, @Query() query: PatientTrackingDto, @Req() req: any): Promise<any> {
        return this.patientMongoService.getPatientTracking(req.user, query);
    }

    @Get('info')
    async getPatientInfo(@Query('patientId') patientId: string, @Headers('partnerid') partnerId: string,): Promise<any> {
        return this.patientMongoService.getPatientInfoById(patientId, partnerId);
    }

    @Get('check-list-patient')
    async getListPatient(@Query('userid') userid: string): Promise<any> {
        return this.patientMongoService.getListPatient(userid);
    }

    @Post('constraint-search-log')
    @HttpCode(200)
    @ApiOperation({ summary: 'Lấy thông tin constraint search log bằng userPhone và partnerId (optional)' })
    async getConstraintSearchLog(@Body() fromData: ConstraintSearchLogDTO): Promise<any> {
        return this.patientMongoService.getConstraintSearchLog(fromData);
    }

    @Delete('constraint-search-log')
    @ApiOperation({ summary: 'Xóa thông tin constraint search log' })
    async deleteConstraintSearchLog(@Query('id') id: []): Promise<any> {
        return this.patientMongoService.deleteConstraintSearchLog(id);
    }

    @Post('cskh/insert-patient-in-user')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @UseInterceptors(AppCskhActivitiesInterceptor)
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async createPatientIntoUserCskh(
        @Headers('cskhtoken') cskhToken: string,
        @Headers('partnerid') partnerId: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;

        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        const { userMongoId } = objUser;

        return this.patientMongoService.createPatientIntoUserCskh(cskhInfo.patientId, userMongoId, partnerId, cskhInfo);
    }

    @Post('validate-booking-rule')
    async validateBookingRule(
        @Headers('partnerid') partnerId: string,
        @Body() dto: ValidateBookingRuleDto): Promise<void> {
        return this.patientMongoService.validateBookingRule(dto, partnerId);
    }

    @Post('config-btn')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor, ViettelPayInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async getConfigBtnUser(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
        @Body() formData: YearOldValidationDto,
    ): Promise<any> {

        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException(
                {
                    message: 'Token không hợp lệ.',
                    error: 401,
                },
                HttpStatus.UNAUTHORIZED,
            );
        }
        const cskhInfo = await this.patientMongoService.verifyCskhToken(cskhToken);
        const objUser = { ...user };
        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        formData.version = 2;
        return this.patientMongoService.getConfigBtnUser(appid, partnerid, objUser, formData);
    }
}
