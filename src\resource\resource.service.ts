import { Injectable, Inject, HttpService, HttpException, HttpStatus } from '@nestjs/common';
import { THU_DUC_HOSPITAL_CONNECTION } from '../config/thuDucHospitalConnection';
import { PKH_PATIENT_CONNECTION } from '../config/pkhPatientConnection';
import { CheckVersionDTO } from './dto/checkVersionDto';
import { GetConfigDTO } from './dto/getConfigDto';
import { GetArticleBySubIdDTO } from './dto/getArticleBySubIdDto';
import { GetCityDTO } from './dto/getCityDto';
import { GetDistrictDTO } from './dto/getDistrictDto';
import { GetWardDTO } from './dto/getWardDto';
import { GetAllWebBannerDTO } from './dto/getAllWebBannersDto';
import { GetPricesDTO } from './dto/getPricesDto';
import { GetTypePricesDTO } from './dto/getTypePricesDto';
import { GetGroupPricesDTO } from './dto/getGroupPricesDto';
import { CHO_RAY_HOSPITAL_CONNECTION } from 'src/config/choRayHospitalConnection';
import { UrlConfigService } from 'src/config/config.url.service';
import { GetTranslateDTO } from './dto/getTranslateDto';
import { GlobalSettingLocaleService } from 'src/global-setting-locale/global-setting-locale.service';

@Injectable()
export class ResourceService {
    private readonly apiTranslate: string;
    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @Inject(THU_DUC_HOSPITAL_CONNECTION) private readonly thuDucHospitalKnex,
        @Inject(CHO_RAY_HOSPITAL_CONNECTION) private readonly choRayHospitalKnex,
        private readonly urlConfigService: UrlConfigService,
        private readonly http: HttpService,
        private readonly globalSettingLocaleService: GlobalSettingLocaleService,
    ) {
        this.apiTranslate = this.urlConfigService.getTranslateUrl();
     }
    

    async getLatestVerionByPlatform(checkVersionDTO: CheckVersionDTO): Promise<any> {
        return this.thuDucHospitalKnex('app_version')
            .where('platform', checkVersionDTO.platform)
            .where('status', 1)
            .orderBy(`version_code`, 'desc')
            .first();
    }

    async getListConfig(getConfigDTO: GetConfigDTO): Promise<any> {
        return this.thuDucHospitalKnex('app_config')
            .where('platform', getConfigDTO.platform)
            .where('status', 1);
    }

    async getArticleBySubId(getArticleBySubIdDTO: GetArticleBySubIdDTO): Promise<any> {
        return this.thuDucHospitalKnex('article')
            .where('sub_id', getArticleBySubIdDTO.sub_id)
            .first();
    }

    async getAllCountry(): Promise<any> {
        return this.thuDucHospitalKnex('dm_country')
            .select('id', 'code', 'name');
    }

    async getUMCAllCountry(): Promise<any> {
        return this.pkhPatientKnex('dm_country')
            .select('id', 'code', 'name');
    }

    async getCitiesByCountryCode(getCityDTO: GetCityDTO): Promise<any> {
        return this.thuDucHospitalKnex('dm_city')
            .select('id', 'name')
            .where('country_code', getCityDTO.country_code)
            .where('status', 1)
            .orderBy([
                {
                    column: 'priority',
                    order: 'desc',
                }, {
                    column: 'name',
                    order: 'asc',
                },
            ]);
    }
    async getTranslate(translate: GetTranslateDTO): Promise<any> {
        try {
            const { language = 'vi' } = translate;
            const dataTranslateLocale = await this.globalSettingLocaleService.findByKeyAndRepoName('TRANSLATE_TUTORIAL_PAYMENT', null, language);
            const response = await this.http.get(`${this.apiTranslate}/api/translations-app?bundleId=medpro&language=${language}`).toPromise();
            if (!response.data && response.status < 200 && response.status >= 400) {
                throw new HttpException(`Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`, HttpStatus.BAD_REQUEST);
            }
            const dataTranslates = dataTranslateLocale ? JSON.parse(dataTranslateLocale) : {};

            return { ...response.data, ...dataTranslates };
        } catch (error) {
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getUMCCitiesByCountryCode(getCityDTO: GetCityDTO): Promise<any> {
        return this.pkhPatientKnex('dm_city')
            .select('id', 'name')
            .where('country_code', getCityDTO.country_code)
            .where('status', 1)
            .orderBy([
                {
                    column: 'priority',
                    order: 'desc',
                }, {
                    column: 'name',
                    order: 'asc',
                },
            ]);
    }

    async getDistrictsByCityId(getDistrictDTO: GetDistrictDTO): Promise<any> {
        return this.thuDucHospitalKnex('dm_district')
            .select('id', 'name')
            .where('city_id', getDistrictDTO.city_id)
            .where('status', 1);
    }

    async getUMCDistrictsByCityId(getDistrictDTO: GetDistrictDTO): Promise<any> {
        return this.pkhPatientKnex('dm_district')
            .select('id', 'name')
            .where('city_id', getDistrictDTO.city_id)
            .where('status', 1);
    }

    async getWardsByDistrictId(getWardDTO: GetWardDTO): Promise<any> {
        return this.thuDucHospitalKnex('dm_ward')
            .select('id', 'name')
            .where('district_id', getWardDTO.district_id)
            .where('status', 1);
    }

    async getUMCWardsByDistrictId(getWardDTO: GetWardDTO): Promise<any> {
        return this.pkhPatientKnex('dm_ward')
            .select('id', 'name')
            .where('district_id', getWardDTO.district_id)
            .where('status', 1);
    }

    async getAllNation(): Promise<any> {
        return this.thuDucHospitalKnex('dm_nation')
            .select('id', 'name');
    }

    async getUMCAllNation(): Promise<any> {
        return this.pkhPatientKnex('dm_dantoc')
            .select('id', 'name');
    }

    async getAllBanners(): Promise<any> {
        return this.thuDucHospitalKnex('banner')
            .select('image');
    }

    async getAllWebBannersByType(getAllWebBannerDTO: GetAllWebBannerDTO): Promise<any> {
        const tableName = 'banner_web';
        const getConnectionQuery = this.getConnection(CHO_RAY_HOSPITAL_CONNECTION, tableName);
        const data = await getConnectionQuery
            .where('type', getAllWebBannerDTO.type)
            .where('status', 1);
        const baseUrl = this.urlConfigService.getBaseUrl();
        return data.map(item => {
            return {
                ...item,
                image_url: `${baseUrl}/st/banner/${item.image}`,
            };
        });
    }

    async getOccupations(): Promise<any> {
        return this.thuDucHospitalKnex('dm_profession')
            .select('id', 'name')
            .orderBy(`name`, 'asc');
    }

    async getUMCOccupations(): Promise<any> {
        return this.pkhPatientKnex('profession')
            .select('id', 'name')
            .orderBy(`name`, 'asc');
    }

    // tslint:disable-next-line: variable-name
    async getHoliday(hospital_id: number): Promise<any> {
        return this.pkhPatientKnex('holiday')
            .select('date', 'description', 'description_html')
            .where('hospital_id', hospital_id);
    }

    async getBranch(): Promise<any> {
        return this.thuDucHospitalKnex('branch')
            .select('id', 'name');
    }

    async getRelativeType(): Promise<any> {
        return this.thuDucHospitalKnex('relative_type')
            .select('id', 'name')
            .orderBy([
                {
                    column: 'priority',
                    order: 'asc',
                }, {
                    column: 'name',
                    order: 'asc',
                },
            ]);
    }

    async getUMCRelativeType(): Promise<any> {
        return this.pkhPatientKnex('relative_type')
            .select('id', 'name')
            .orderBy([
                {
                    column: 'priority',
                    order: 'asc',
                }, {
                    column: 'name',
                    order: 'asc',
                },
            ]);
    }

    fullTextWildcards(term: any) {
        // removing symbols used by MySQL
        const myTerm = term.replace(/[-+<>@()~]/g, '');

        const words = term.split(' ');
        let arrTemp = [];

        words.forEach(item => {
            if (item.length >= 1) {
                arrTemp = [...arrTemp, `+${item}*`];
            }
        });

        return arrTemp.join(' ');

    }

    async getPrices(getPricesDTO: GetPricesDTO): Promise<any> {
        let query = this.thuDucHospitalKnex('dm_price');
        if (getPricesDTO.keyword) {
            query = query.whereRaw(`MATCH (??) AGAINST (? IN BOOLEAN MODE)`, ['name', this.fullTextWildcards(getPricesDTO.keyword)]);
        }
        if (getPricesDTO.type_id) {
            query = query.where('type_id', getPricesDTO.type_id);
        }
        if (getPricesDTO.group_id) {
            query = query.where('group_id', getPricesDTO.group_id);
        }
        return query;
    }

    async getTypePrices(getTypePricesDTO: GetTypePricesDTO): Promise<any> {
        let query = this.thuDucHospitalKnex('dm_price').select('type_id', 'type_name').groupBy('type_id').groupBy('type_name');

        if (getTypePricesDTO.type_id) {
            query = query.where('type_id', getTypePricesDTO.type_id);
        }

        return query;
    }

    async getGroupPrices(getGroupPricesDTO: GetGroupPricesDTO): Promise<any> {
        let query = this.thuDucHospitalKnex('dm_price').select('group_id', 'group_name').groupBy('group_id').groupBy('group_name');

        if (getGroupPricesDTO.groupId) {
            query = query.where('group_id', getGroupPricesDTO.groupId);
        }

        return query;
    }

    getConnection(hospitalConnection = PKH_PATIENT_CONNECTION, tableName = 'banner_web') {
        let connectionQuery = null;
        switch (hospitalConnection) {
            case THU_DUC_HOSPITAL_CONNECTION:
                connectionQuery = this.thuDucHospitalKnex(tableName);
                break;
            case CHO_RAY_HOSPITAL_CONNECTION:
                connectionQuery = this.choRayHospitalKnex(tableName);
                break;
            default:
                connectionQuery = this.pkhPatientKnex(tableName);
        }
        return connectionQuery;
    }

}
