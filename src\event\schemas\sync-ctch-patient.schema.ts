import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SYNC_CTCH_PATIENT } from './constants';

const Schema = mongoose.Schema;

export const SyncCTCHPatientSchema = new Schema({
    id: { type: Number, required: true },
    userId: { type: Number },
    medproId: { type: String },
    date_create: { type: Date },
}, {
    collection: SYNC_CTCH_PATIENT,
    timestamps: true,
}).plugin(jsonMongo);
