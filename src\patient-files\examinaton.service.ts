import { BadRequestException, HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import * as jwt from 'jsonwebtoken';
import { first, get, groupBy } from 'lodash';
import { LOG_SERVICE_EVENT } from '../audit-log/constant';
import { ClientUtilService } from '../config/client-util.service';
// import * as queryString from 'query-string';
import { JwtModuleOptions } from '@nestjs/jwt';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import { Model } from 'mongoose';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { UrlConfigService } from 'src/config/config.url.service';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { ConfigsS3Service } from 'src/config/configs.S3.service';
import { UtilService } from 'src/config/util.service';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { CSKHTokenVerifyDTO } from 'src/patient-mongo/dto/cskh-token-verify.dto';
import { IPatientCodes } from 'src/patient-mongo/intefaces/patient-codes.inteface';
import { IPatient } from 'src/patient-mongo/intefaces/patient.inteface';
import { IUser } from 'src/user/interfaces/user.interface';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { createExamNoAuthDTO } from './dto/create-exam-no-auth.dto';
import { createExaminationResultDTO } from './dto/create-examination-result.dto';
import { GetPatientExamNoAuthDTO } from './dto/get-patient-exam-no-auth.dto';
import { GetPatientExaminationDTO } from './dto/get-patient-examination.dto';
import { updateExaminationResultDTO } from './dto/update-examination-result.dto';
import { UserInfoVerifyTokenDTO } from './dto/userInfo';
import { ICdnImagePatient } from './intefaces/cdn-image-patient.interface';
import { IExamination } from './intefaces/examination.interface';
import { IHealthIndex } from './intefaces/health-index.interface';
import {
    HEALTH_INDEX_COLLECTION_NAME,
    LIST_CDN_IMAGE_COLLECTION_NAME,
    PATIENT_CODE_COLLECTION_NAME,
    PATIENT_COLLECTION_NAME,
    PATIENT_EXAMINATION_COLLECTION_NAME,
    PENDING_PATINET_HEALTH_INDEX,
} from './schemas/constants';
import * as Excel from 'exceljs';
import * as path from 'path';
import { IPendingPatient } from './intefaces/pending-patients.interface';
import moment = require('moment');

@Injectable()
export class ExaminatonService {
    constructor(
        @InjectModel(PATIENT_COLLECTION_NAME) private patientModel: Model<IPatient>,
        @InjectModel(PATIENT_EXAMINATION_COLLECTION_NAME) private examinationModel: Model<IExamination>,
        @InjectModel(PATIENT_CODE_COLLECTION_NAME) private patientCodeModel: Model<IPatientCodes>,
        @InjectModel(LIST_CDN_IMAGE_COLLECTION_NAME) private uploadCdnImageModel: Model<ICdnImagePatient>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(HEALTH_INDEX_COLLECTION_NAME) private healthIndexModel: Model<IHealthIndex>,
        // @InjectModel(PATIENT_XC_COLLECTION_NAME) private readonly patientXncModel: Model<IPatientXnc>,
        @InjectModel(PENDING_PATINET_HEALTH_INDEX) private pendingPatientModel: Model<IPendingPatient>,
        @InjectSentry() private readonly clientSentry: SentryService,
        private utilService: UtilService,
        private readonly jwtUserConfigService: JwtUserConfigService,
        private readonly configS3: ConfigsS3Service,
        private readonly urlConfigService: UrlConfigService,
        private emitService: EventEmitter2,
        private readonly client: ClientUtilService,
        private globalSettingService: GlobalSettingService,
    ) {}

    async getExaminationResult(userId: string, formData?: GetPatientExaminationDTO): Promise<any[]> {
        const query: any = {
            userId,
            isDeleted: false,
        };

        if (formData?.patientId) {
            query.patientId = formData.patientId;
        } else {
            query.$or = [{ patientId: { $exists: false } }, { patientId: null }, { patientId: '' }];
        }

        const results = await this.examinationModel
            .find(query)
            .sort({ createdAt: -1 })
            .read('primary')
            .lean();

        const examinationIds = results.map(item => item._id);

        const healthIndexes = await this.healthIndexModel.find({ examinationId: { $in: examinationIds }, userId }).lean();

        const healthIndexMap = new Map(healthIndexes.map(index => [String(index.examinationId), index]));

        return results.map(item => {
            const health = healthIndexMap.get(String(item._id)) || null;

            const generalHealthIndex = health
                ? {
                      glucoseU: health.glucoseU,
                      bp: health.bp,
                      height: health.height,
                      weight: health.weight,
                      bmi: health.bmi,
                      waist: health.waist,
                      bloodType: health.bloodType,
                      pulse: health.pulse,
                      temperature: health.temperature,
                      respiratoryRate: health.respiratoryRate,
                  }
                : null;

            const allergyHistory = health
                ? {
                      drugAllergyStatus: health.drugAllergyStatus,
                      drugAllergyNote: health.drugAllergyNote,
                      ingredientAllergyStatus: health.ingredientAllergyStatus,
                      ingredientAllergyNote: health.ingredientAllergyNote,
                      foodAllergyStatus: health.foodAllergyStatus,
                      foodAllergyNote: health.foodAllergyNote,
                  }
                : null;

            const familyMedicalHistory = health
                ? {
                      familyHistoryPulse: health.familyHistoryPulse,
                      familyHistoryHypertension: health.familyHistoryHypertension,
                      familyHistoryMental: health.familyHistoryMental,
                      familyHistoryCancer: health.familyHistoryCancer,
                      familyHistoryAsthma: health.familyHistoryAsthma,
                      familyHistoryEpilepsy: health.familyHistoryEpilepsy,
                      familyHistoryTuberculosis: health.familyHistoryTuberculosis,
                  }
                : null;

            return {
                ...item,
                generalHealthIndex,
                allergyHistory,
                familyMedicalHistory,
            };
        });
    }

    async getExamByPatientId(formData: GetPatientExamNoAuthDTO): Promise<any> {
        const query: any = { isDeleted: false };

        if (formData?.patientId) {
            query.patientId = formData.patientId;
        }

        const results = await this.examinationModel
            .find(query)
            .sort({ createdAt: -1 })
            .read('primary')
            .lean()
            .exec();

        const examinationIds = results.map(item => item._id);
        const healthIndexList = await this.healthIndexModel
            .find({ examinationId: { $in: examinationIds } })
            .lean()
            .exec();

        const healthIndexMap = new Map<string, any>();
        for (const health of healthIndexList) {
            healthIndexMap.set(String(health.examinationId), health);
        }

        return results.map(item => {
            const health = healthIndexMap.get(String(item._id)) || null;

            const generalHealthIndex = health
                ? {
                      glucoseU: health.glucoseU,
                      bp: health.bp,
                      height: health.height,
                      weight: health.weight,
                      bmi: health.bmi,
                      waist: health.waist,
                      bloodType: health.bloodType,
                      pulse: health.pulse,
                      temperature: health.temperature,
                      respiratoryRate: health.respiratoryRate,
                  }
                : null;

            const allergyHistory = health
                ? {
                      drugAllergyStatus: health.drugAllergyStatus,
                      drugAllergyNote: health.drugAllergyNote,
                      ingredientAllergyStatus: health.ingredientAllergyStatus,
                      ingredientAllergyNote: health.ingredientAllergyNote,
                      foodAllergyStatus: health.foodAllergyStatus,
                      foodAllergyNote: health.foodAllergyNote,
                  }
                : null;

            const familyMedicalHistory = health
                ? {
                      familyHistoryPulse: health.familyHistoryPulse,
                      familyHistoryHypertension: health.familyHistoryHypertension,
                      familyHistoryMental: health.familyHistoryMental,
                      familyHistoryCancer: health.familyHistoryCancer,
                      familyHistoryAsthma: health.familyHistoryAsthma,
                      familyHistoryEpilepsy: health.familyHistoryEpilepsy,
                      familyHistoryTuberculosis: health.familyHistoryTuberculosis,
                  }
                : null;

            return {
                ...item,
                generalHealthIndex,
                allergyHistory,
                familyMedicalHistory,
            };
        });
    }

    async deleteExaminationResult(id: string): Promise<any> {
        const isExistExamination = await this.examinationModel.exists({ _id: id });
        if (!isExistExamination) {
            throw new HttpException(`Không tìm thấy hồ sơ sức khỏe: ${id}`, HttpStatus.NOT_FOUND);
        }
        try {
            await this.examinationModel.findByIdAndUpdate(id, { isDeleted: true }, { new: true }).exec();
            return { status: HttpStatus.OK, message: 'Xóa thành công' };
        } catch (error) {
            throw new HttpException(`Hệ thống chưa xử lý được thao tác này, vui lòng thử lại sau`, HttpStatus.BAD_REQUEST);
        }
    }

    async updateExaminationResult(
        partnerId: string,
        appId: string,
        user: UserInfoVerifyTokenDTO,
        examinationResult: updateExaminationResultDTO,
        cskhInfo?: CSKHTokenVerifyDTO,
        locale = 'vi',
    ): Promise<any> {
        let patientObj: any = {};
        const { files = [], patientId, name, dateExamination, description, hospitalName, source, id, ...rest } = examinationResult;

        try {
            // 1. Kiểm tra bệnh nhân nếu có
            if (patientId) {
                const findPatient = await this.patientModel.findOne({ id: patientId }).exec();
                if (!findPatient) {
                    throw new HttpException({ key: 'PATIENT_MESSAGE_ERROR_NOT_FOUND', locale }, HttpStatus.NOT_FOUND);
                }

                patientObj = findPatient.toObject();
                const checkValue = await this.checkUMCPatientBelongsToUser(user.userMongoId, patientObj._id);
                if (!checkValue) {
                    throw new HttpException('Thao tác không thành công. Vui lòng liên hệ 19002115 để được hỗ trợ.', HttpStatus.NOT_FOUND);
                }
            }

            // 2. Kiểm tra kết quả khám
            const checkExam = await this.examinationModel.findOne({ _id: id }).exec();
            if (!checkExam) {
                throw new HttpException('Cập nhật không thành công. Không tồn tại kết quả khám này!', HttpStatus.BAD_REQUEST);
            }

            const existingFiles: string[] = Array.isArray(checkExam.listImage) ? (checkExam.listImage as string[]) : [];
            const updatedFiles: string[] = files;

            // 3. Xử lý cập nhật file ảnh
            const removedFiles = existingFiles.filter(file => !updatedFiles.includes(file));
            if (removedFiles.length > 0) {
                await this.uploadCdnImageModel.updateMany({ fileUrl: { $in: removedFiles } }, { $unset: { examResultId: '' } });
            }

            const newFiles = updatedFiles.filter(file => !existingFiles.includes(file));
            if (newFiles.length > 0) {
                await this.uploadCdnImageModel.updateMany({ fileUrl: { $in: newFiles } }, { $set: { examResultId: checkExam._id } });
            }

            // 4. Cập nhật nội dung kết quả khám
            const updateData = {
                name,
                listImage: updatedFiles,
                dateExamination,
                description,
                patientId,
                hospitalName,
                source,
            };

            await this.updateExamination(checkExam._id, updateData);

            // 5. Nếu có dữ liệu chỉ số sức khỏe thì tạo mới (không update)
            if (Object.keys(rest).length > 0) {
                const healthIndexData = {
                    userId: user.userMongoId,
                    patientId,
                    examinationId: checkExam._id,
                    examination: checkExam._id,
                    ...rest,
                };
                // Tìm kiếm health index theo examinationId
                const existingHealthIndex = await this.healthIndexModel.findOne({ examinationId: checkExam._id });
                if (existingHealthIndex) {
                    await this.healthIndexModel.updateOne({ _id: existingHealthIndex._id }, { $set: healthIndexData });
                } else {
                    await this.healthIndexModel.create(healthIndexData);
                }
            }

            // 6. Trả kết quả mới nhất
            return await this.getExaminationResult(user.userMongoId, { patientId });
        } catch (error) {
            // Ghi log lỗi
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'trackingUpdatePatientError',
                summary: 'Lỗi khi cập nhật kết quả khám.',
                nameParent: 'updateExaminationResult',
                params: { appId, partnerId, user, cskhInfo },
                errorBody: this.utilService.errorHandler(error),
                response: null,
                message:
                    locale === 'vi'
                        ? 'Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.'
                        : 'Operation failed. Please contact support.',
            });

            const status = error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;
            const message = locale === 'vi' ? 'Lỗi hệ thống. Vui lòng thử lại sau.' : 'System error. Please try again later.';
            throw new HttpException({ key: 'BAD_REQUEST_MESSAGE_ERROR', message, locale }, status);
        }
    }

    async createQuickExaminationResult(
        partnerId: string,
        appId: string,
        user: UserInfoVerifyTokenDTO,
        examinationResult: createExaminationResultDTO,
        cskhInfo?: CSKHTokenVerifyDTO,
        locale = 'vi',
    ): Promise<{ status: number; message: string }> {
        const { files = [], patientId, name, dateExamination, description, hospitalName, source, ...rest } = examinationResult;

        if (patientId) {
            const findPatient = await this.patientModel.findOne({ id: patientId }).exec();
            if (!findPatient) {
                throw new HttpException({ key: 'PATIENT_MESSAGE_ERROR_NOT_FOUND', locale }, HttpStatus.NOT_FOUND);
            }

            const patientObj = findPatient.toObject();
            const checkValue = await this.checkUMCPatientBelongsToUser(user.userMongoId, patientObj._id);
            if (!checkValue) {
                throw new HttpException('Hồ sơ không thuộc user này!', HttpStatus.NOT_FOUND);
            }
        }
        // Dữ liệu tạo kết quả khám
        const createData = {
            userId: user.userMongoId,
            patientId: patientId || null,
            name,
            listImage: files,
            dateExamination,
            description,
            hospitalName,
            source,
        };

        try {
            // Tạo bản ghi kết quả khám
            const dataUpdate = await this.examinationModel.create(createData);
            if (!dataUpdate) {
                throw new Error('Failed to create examination result');
            }

            // Xử lý file upload nếu có
            if (files.length > 0) {
                // Kiểm tra file tồn tại trên CDN
                const listFiles = await this.uploadCdnImageModel.find({
                    fileUrl: { $in: files },
                });
                // Lấy danh sách fileUrl từ dữ liệu tìm được, chỉ lấy fileUrl chưa có examResultId
                const uploadedFiles = listFiles.filter(file => file.fileUrl && !file.examResultId).map(file => file.fileUrl);
                // Nếu có patientId, cập nhật vào các file đã upload
                await this.uploadCdnImageModel.updateMany({ fileUrl: { $in: uploadedFiles } }, { $set: { examResultId: dataUpdate._id } });
            }
            if (Object.keys(rest).length > 0) {
                // Tạo dữ liệu chỉ số sức khỏe
                const healthIndexData = {
                    userId: user.userMongoId,
                    patientId,
                    examinationId: dataUpdate._id,
                    examination: dataUpdate._id,
                    ...rest,
                };
                await this.healthIndexModel.create(healthIndexData);
            }

            return {
                status: HttpStatus.OK,
                message: locale === 'vi' ? 'Tạo kết quả khám thành công' : 'Examination result created successfully',
            };
        } catch (error) {
            // Ghi log lỗi
            this.emitService.emit('LOG_SERVICE_EVENT', {
                name: 'trackingUpdatePatientError',
                summary: 'Lỗi tạo kết quả khám',
                nameParent: 'createQuickExamination',
                params: { appId, partnerId, user, cskhInfo },
                errorBody: this.utilService.errorHandler(error),
                response: null,
                message:
                    locale === 'vi'
                        ? 'Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.'
                        : 'Operation failed. Please contact support.',
            });

            // Phân loại lỗi
            const status = error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;
            const message = locale === 'vi' ? 'Lỗi hệ thống. Vui lòng thử lại sau.' : 'System error. Please try again later.';
            console.log('error', error);
            throw new HttpException({ key: 'BAD_REQUEST_MESSAGE_ERROR', message, locale }, status);
        }
    }

    async uploadFileExamResults(userId: string, file: Express.Multer.File): Promise<any> {
        try {
            // Upload file lên S3
            const fileUrl = await this.configS3.uploadPatientImages(file);

            // Lưu thông tin vào MongoDB
            const newFile = new this.uploadCdnImageModel({
                userId,
                fileUrl,
                fileName: file.originalname,
                fileType: file.mimetype,
            });

            await newFile.save();
            return fileUrl;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    async uploadFileExamNoAuth(file: Express.Multer.File): Promise<any> {
        try {
            // Upload file lên S3
            const fileUrl = await this.configS3.uploadPatientImages(file);
            return fileUrl;
        } catch (error) {
            console.log('error', error);
            throw new Error(error.message);
        }
    }

    async checkUMCPatientBelongsToUser(userId: string, patientId: string): Promise<any> {
        /* Kiểm tra xem patient này đã thuộc về user hay chua */
        const userInfoWithPatients = await this.getCountPatientInUserPatientUMC(userId);
        const findIndexValue = userInfoWithPatients.patients.indexOf(patientId);
        if (findIndexValue > -1) {
            return true;
        } else {
            return false;
        }
    }
    async getCountPatientInUserPatientUMC(userId: string): Promise<any> {
        return this.userModel.findById({ _id: userId }, { patients: true }).exec();
    }
    async insertExamination(patientInsert): Promise<any> {
        const examinationModel = new this.examinationModel(patientInsert);
        return await examinationModel.save();
    }

    async updateExamination(id: string, patientInfo: any): Promise<any> {
        const { id: idHehe, ...rest } = patientInfo;
        return this.examinationModel.findByIdAndUpdate({ _id: id }, { ...rest }, { new: true });
    }
    async verifyCskhToken(cskhToken: string): Promise<CSKHTokenVerifyDTO> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyCSKHJwtOptions();
        let cskhInfo: CSKHTokenVerifyDTO;
        if (typeof cskhToken !== typeof undefined && `${cskhToken}`.trim() !== '') {
            try {
                const jwtVerify: any = jwt.verify(cskhToken, jwtOptions.secret);
                cskhInfo = jwtVerify;
                /* Kiểm tra lại thông tin cskhInfo */
                const { userIdPatient, cskhUserId } = cskhInfo;
                if (cskhUserId) {
                    const cskhUser = await this.findUserByRefId(cskhUserId);
                    if (!cskhUser) {
                        throw new HttpException('Vui lòng kiểm tra lại thông tin user cskh', HttpStatus.UNAUTHORIZED);
                    }
                }

                if (userIdPatient) {
                    const patientUser = await this.findUserByRefId(userIdPatient);
                    if (!patientUser) {
                        throw new HttpException('Vui lòng kiểm tra lại thông tin patient user', HttpStatus.UNAUTHORIZED);
                    }
                }

                return cskhInfo;
            } catch (error) {
                this.clientSentry.instance().captureException(error);
                const nameJWTError = !!error.name ? error.name : '';
                if (nameJWTError === 'TokenExpiredError') {
                    throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
                } else if (nameJWTError === 'JsonWebTokenError') {
                    throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
                } else if (nameJWTError === 'NotBeforeError') {
                    throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
                } else {
                    throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
                }
            }
        } else {
            return cskhInfo;
        }
    }
    async findUserByRefId(userId: string): Promise<any> {
        return this.userModel
            .findById({
                _id: userId,
            })
            .exec();
    }
    getFullAddress(patient: IPatient): string {
        let fullAddress = '';
        if (!!patient.address) {
            fullAddress = `${patient.address}`.trim();
        }
        const getCountryObjId = get(patient, 'country._id', '');
        if (`5ecb3b014ae1165edc747c5b` !== `${getCountryObjId}`) {
            return fullAddress;
        }
        const getWardName = get(patient, 'ward.name', '');
        if (getWardName) {
            fullAddress = fullAddress ? `${fullAddress}, ${getWardName}` : getWardName;
        }
        const getDistrict = get(patient, 'district.name', '');
        if (getDistrict) {
            fullAddress = fullAddress ? `${fullAddress}, ${getDistrict}` : getDistrict;
        }
        const getCity = get(patient, 'city.name', '');
        if (getCity) {
            fullAddress = fullAddress ? `${fullAddress}, ${getCity}` : getCity;
        }

        return fullAddress;
    }

    async limitSizeFile(): Promise<any> {
        const fileSizeObj = await this.globalSettingService.findByKeyAndRepoName('LIMIT_UPLOAD_FILE_SIZE');
        let fileSize: {
            image: number;
            document: number;
        } = { image: 10, document: 10 };
        if (fileSizeObj) {
            fileSize = JSON.parse(fileSizeObj);
        }
        return fileSize;
    }

    async getInsuranCodeByPatientId(patientIds: any[], partnerId: string, appId: string = 'medpro', treeId?: string): Promise<any> {
        let patientCodes = await this.patientCodeModel
            .find({
                partnerId,
                patientId: { $in: patientIds },
            })
            .exec();

        if (patientCodes.length === 0) {
            patientCodes = await this.patientCodeModel
                .find({
                    partnerId: appId,
                    patientId: { $in: patientIds },
                })
                .exec();
        }

        if (treeId && treeId === 'CLS') {
            const patientCodeCls = await this.patientCodeModel
                .find({
                    partnerId,
                    patientId: { $in: patientIds },
                })
                .exec();

            if (patientCodeCls.length === 0) {
                patientCodes = await this.patientCodeModel
                    .find({
                        partnerId: appId,
                        patientId: { $in: patientIds },
                    })
                    .exec();
            }

            return patientCodeCls || patientCodes;
        }

        return patientCodes;
    }
    async checkPatientPartner(patients: any, formData: any, partnerid: string, appid: string, locale = 'vi'): Promise<any> {
        const baseUrl = await this.getBookingRuleUrl();
        const api = `${baseUrl}/booking-rules/patient/exam`;
        const patientRules = await this.client.post(
            api,
            {
                data: formData,
                patientIds: patients.map(patient => patient?.id),
            },
            {
                partnerid,
                appid,
                locale,
            },
        );

        const groupPatientRules = groupBy(patientRules, 'id');
        return patients.map(patient => {
            const warningMessage = first<any>(groupPatientRules[patient?.id])?.warningMessage || '';
            return {
                ...patient,
                ...(warningMessage && { warningMessage }),
            };
        });
    }
    async getBookingRuleUrl() {
        return this.urlConfigService.getUrlCheckBookingRules();
    }

    async createExamNoAuth(partnerId: string, appId: string, examinationResult: createExamNoAuthDTO, locale?: string): Promise<any> {
        let stepInfo: any = {};

        try {
            let patientObj: any = {};

            if (examinationResult?.userId) {
                const findPatient = await this.patientModel.findOne({ id: examinationResult?.patientId }).exec();
                if (!findPatient) {
                    throw new HttpException({ key: 'PATIENT_MESSAGE_ERROR_NOT_FOUND', locale }, HttpStatus.NOT_FOUND);
                }

                patientObj = findPatient.toObject();
                const checkValue = await this.checkUMCPatientBelongsToUser(examinationResult?.userId, patientObj._id);
                if (!checkValue) {
                    throw new HttpException('Hồ sơ không thuộc user này !', HttpStatus.NOT_FOUND);
                }
            }

            // Tách phần dữ liệu healthIndex (nếu có) khỏi createData
            const { name, userId, patientId, files, dateExamination, description, hospitalName, source, ...rest } = examinationResult;

            const createData = {
                userId,
                patientId: patientId || null,
                name,
                listImage: files,
                dateExamination,
                description,
                hospitalName,
                source,
            };

            const dataUpdate = await this.insertExamination(createData);

            // Gắn ID của kết quả khám vào ảnh nếu có
            if (files.length > 0) {
                // Kiểm tra file tồn tại trên CDN
                const listFiles = await this.uploadCdnImageModel.find({
                    fileUrl: { $in: files },
                });
                // Lấy danh sách fileUrl từ dữ liệu tìm được, chỉ lấy fileUrl chưa có examResultId
                const uploadedFiles = listFiles.filter(file => file.fileUrl && !file.examResultId).map(file => file.fileUrl);
                // Nếu có patientId, cập nhật vào các file đã upload
                await this.uploadCdnImageModel.updateMany({ fileUrl: { $in: uploadedFiles } }, { $set: { examResultId: dataUpdate._id } });
            }

            // Nếu còn dư field ngoài DTO chính => tạo chỉ số sức khỏe
            if (Object.keys(rest).length > 0 && dataUpdate) {
                const healthIndexData = {
                    userId: userId,
                    patientId: patientId,
                    examinationId: dataUpdate._id,
                    examination: dataUpdate._id,
                    ...rest,
                };

                await this.healthIndexModel.create(healthIndexData);
            }

            if (dataUpdate) {
                return { status: HttpStatus.OK, message: 'Tạo kết quả khám thành công' };
            }
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'trackingUpdatePatientError',
                summary: 'Lỗi tạo kết quả khám.',
                nameParent: 'createQuickExamination',
                params: {
                    stepInfo,
                    appId,
                    partnerId,
                },
                errorBody: this.utilService.errorHandler(error),
                response: null,
                message: 'Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.',
            });

            throw new HttpException(error.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async getBloodType(): Promise<any[]> {
        try {
            const bloodTypeObj = await this.globalSettingService.findByKeyAndRepoName('LIST_BLOOD_TYPE');
            let bloodType = [];
            if (bloodTypeObj) {
                bloodType = JSON.parse(bloodTypeObj);
            }
            return bloodType;
        } catch (error) {
            throw new HttpException(error.message || 'Lỗi lấy danh sách loại máu!', error.status || HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async processHealthData(excelData: any[]) {
        const results = [];

        for (const [index, row] of excelData.entries()) {
            const rowNumber = index + 2; // Excel row number (starting from 2, as row 1 is header)

            try {
                // Validate required fields
                const validationResult = this.validateHealthDataRow(row, rowNumber);

                if (!validationResult.isValid) {
                    console.log(`Row ${rowNumber} failed validation. Skipping.`);
                    results.push({
                        status: 'error',
                        error: validationResult.errors.join('; '),
                        row: { ...row, rowNumber },
                        rowNumber,
                    });
                    continue;
                }

                let cmnd = '';
                if (!row['Căn cước công dân']) {
                    console.error(`Row ${rowNumber} is missing 'Căn cước công dân':`, row);
                } else {
                    cmnd = row['Căn cước công dân'].toString().trim();
                    console.log(`Extracted CMND for row ${rowNumber}: ${cmnd}`);
                }

                // Parse and validate health data
                const healthData = this.parseHealthDataFromRow(row);

                await this.savePendingHealthData(cmnd, healthData, row);

                results.push({
                    status: 'success',
                    message: `Đã lưu dữ liệu cho CMND: ${cmnd}`,
                    cmnd,
                    rowNumber,
                    healthData,
                });

            } catch (error) {
                results.push({
                    status: 'error',
                    error: `Lỗi xử lý dữ liệu: ${error.message}`,
                    message: `Lỗi tại dòng ${rowNumber}: ${error.message}`,
                    rowNumber,
                    row: { ...row, rowNumber },
                    errorType: 'processing_error',
                });
            }
        }

        return results;
    }

    /**
     * Đồng bộ các patient có CCCD trùng với CCCD trong pendingPatient
     * Tạo examination và health index cho các patient đã tìm thấy
     * @param cmnds - Danh sách CMND cụ thể cần đồng bộ (tùy chọn)
     */
    async syncPendingPatientsWithExistingPatients(cmnds?: string[]): Promise<any> {
        const results = [];
        let totalProcessed = 0;
        let totalSynced = 0;
        let totalErrors = 0;

        try {
            // Tạo query filter
            const filter: any = { status: 'pending' };
            if (cmnds && cmnds.length > 0) {
                filter.cmnd = { $in: cmnds };
            }

            // Lấy pending patients theo filter
            const pendingPatients = await this.pendingPatientModel.find(filter).exec();

            console.log(`Found ${pendingPatients.length} pending patients to process`);

            for (const pendingPatient of pendingPatients) {
                totalProcessed++;

                try {
                    const cmnd = pendingPatient.cmnd;
                    console.log(`Processing pending patient with CMND: ${cmnd}`);

                    // Tìm tất cả patients có cùng CMND
                    const patients = await this.patientModel.find({ cmnd }).exec();

                    if (patients.length === 0) {
                        console.log(`No patients found for CMND: ${cmnd}. Keeping as pending.`);
                        results.push({
                            status: 'no_match',
                            message: `Không tìm thấy bệnh nhân cho CMND: ${cmnd}`,
                            cmnd,
                            pendingPatientId: pendingPatient._id,
                        });
                        continue;
                    }

                    console.log(`Found ${patients.length} patients for CMND: ${cmnd}`);

                    // Tạo examination và health index cho tất cả patients có cùng CMND
                    const syncedPatients = [];
                    for (const patient of patients) {
                        try {
                            const savedData = await this.createExaminationWithHealthIndex(
                                patient,
                                pendingPatient.healthData
                            );

                            syncedPatients.push({
                                patientId: patient._id,
                                examinationId: savedData.examinationId,
                                healthIndexId: savedData.healthIndexId,
                            });

                            console.log(`Synced patient ID: ${patient._id}, Examination ID: ${savedData.examinationId}`);
                        } catch (error) {
                            console.error(`Error creating examination for patient ${patient._id}:`, error);
                            results.push({
                                status: 'partial_error',
                                error: `Lỗi tạo examination cho patient ${patient._id}: ${error.message}`,
                                cmnd,
                                patientId: patient._id,
                                pendingPatientId: pendingPatient._id,
                            });
                        }
                    }

                    if (syncedPatients.length > 0) {
                        // Cập nhật trạng thái pending patient thành 'synced'
                        await this.pendingPatientModel.findByIdAndUpdate(
                            pendingPatient._id,
                            {
                                status: 'synced',
                                linkedAt: new Date(),
                                linkedPatientIds: syncedPatients.map(p => p.patientId)
                            }
                        );

                        totalSynced++;
                        results.push({
                            status: 'success',
                            message: `Đã đồng bộ thành công cho CMND: ${cmnd}`,
                            cmnd,
                            pendingPatientId: pendingPatient._id,
                            syncedPatients,
                            totalPatientsLinked: syncedPatients.length,
                        });
                    }

                } catch (error) {
                    totalErrors++;
                    console.error(`Error processing pending patient ${pendingPatient._id}:`, error);
                    results.push({
                        status: 'error',
                        error: `Lỗi xử lý pending patient: ${error.message}`,
                        cmnd: pendingPatient.cmnd,
                        pendingPatientId: pendingPatient._id,
                    });
                }
            }

            const summary = {
                totalProcessed,
                totalSynced,
                totalErrors,
                totalNoMatch: results.filter(r => r.status === 'no_match').length,
                processedAt: new Date(),
            };

            console.log('Sync summary:', summary);

            return {
                success: true,
                message: `Đã xử lý ${totalProcessed} pending patients. Đồng bộ thành công: ${totalSynced}, Lỗi: ${totalErrors}`,
                summary,
                details: results,
            };

        } catch (error) {
            console.error('Error in syncPendingPatientsWithExistingPatients:', error);
            throw new HttpException(
                `Lỗi đồng bộ dữ liệu: ${error.message}`,
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    private validateHealthDataRow(row: any, rowNumber: number): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        // Required fields validation
        if (!row['Căn cước công dân']?.toString()?.trim()) {
            errors.push(`Dòng ${rowNumber}: Căn cước công dân là bắt buộc`);
        }

        if (!row['Tiêu đề']?.toString()?.trim()) {
            errors.push(`Dòng ${rowNumber}: Tiêu đề khám là bắt buộc`);
        }

        if (!row['Tên cơ sở y tế']?.toString()?.trim()) {
            errors.push(`Dòng ${rowNumber}: Tên cơ sở y tế là bắt buộc`);
        }

        // Date validation
        const examDate = row['Dấu thời gian'];
        if (examDate && !this.isValidDate(examDate)) {
            errors.push(`Dòng ${rowNumber}: Dấu thời gian không hợp lệ`);
        }

        // Numeric fields validation
        const numericFields = [
            { field: 'Chiều Cao', min: 50, max: 250 },
            { field: 'Cân nặng', min: 10, max: 300 },
            { field: 'Vòng bụng', min: 40, max: 200 },
            { field: 'Nhịp tim', min: 30, max: 200 },
            { field: 'Nhiệt độ', min: 35, max: 45 },
        ];

        for (const { field, min, max } of numericFields) {
            const value = row[field];
            if (value !== null && value !== undefined && value !== '') {
                const numValue = Number(value);
                if (isNaN(numValue) || numValue < min || numValue > max) {
                    errors.push(`Dòng ${rowNumber}: ${field} không hợp lệ (phải từ ${min} đến ${max})`);
                }
            }
        }

        return { isValid: errors.length === 0, errors };
    }

    private parseHealthDataFromRow(row: any) {
        const height = this.parseNumber(row['Chiều Cao']);
        const weight = this.parseNumber(row['Cân nặng']);

        // Calculate BMI if both height and weight are available
        let bmi = null;
        if (height && weight) {
            const heightInMeters = height / 100;
            bmi = Math.round((weight / (heightInMeters * heightInMeters)) * 100) / 100;
        }

        // Check for hypertension history
        const hypertensionValue = row['Tăng Huyết Áp(THA)']
            ?.toString()
            ?.trim()
            ?.toLowerCase();
        const hasHypertension = hypertensionValue === 'x' || hypertensionValue === 'X';

        return {
            title: row['Tiêu đề']?.toString()?.trim() || '',
            hospitalName: row['Tên cơ sở y tế']?.toString()?.trim() || '',
            dateExamination: this.parseDate(row['Dấu thời gian']),
            height,
            weight,
            bmi,
            waist: this.parseNumber(row['Vòng bụng']),
            bp: this.parseBloodPressure(row['Huyết Áp']),
            pulse: this.parseNumber(row['Nhịp tim']),
            temperature: this.parseNumber(row['Nhiệt độ']),
            // Family history based on hypertension column
            familyHistoryHypertension: hasHypertension ? 'YES' : 'UNKNOWN',
            familyHistoryPulse: hasHypertension ? 'Tăng huyết áp' : '',
        };
    }

    private async savePendingHealthData(cmnd: string, healthData: any, originalRow: any) {
        const pendingData = {
            cmnd,
            healthData: {
                ...healthData,
                originalRow, // Keep original row for reference
            },
            status: 'pending',
            createdAt: new Date(),
        };

        // Check if pending data already exists for this CMND
        await this.pendingPatientModel.create(pendingData);
    }

    private async createExaminationWithHealthIndex(patient: any, healthData: any) {
        // Create examination record
        const examinationData = {
            userId: patient.userId?.toString() || '',
            patientId: patient.id,
            name: healthData.title,
            hospitalName: healthData.hospitalName,
            dateExamination: healthData.dateExamination || new Date(),
            description: `Nhập từ Excel - ${healthData.title}`,
            source: 'EXCEL_IMPORT',
            listImage: [],
            isDeleted: false,
            createdAt: new Date(),
        };

        const examination = await this.examinationModel.create(examinationData);

        // Create health index record
        const healthIndexData = {
            userId: patient.userId?.toString() || '',
            patientId: patient.id,
            examinationId: examination._id,
            examination: examination._id,
            height: healthData.height,
            weight: healthData.weight,
            bmi: healthData.bmi,
            waist: healthData.waist,
            bp: healthData.bp,
            pulse: healthData.pulse,
            temperature: healthData.temperature,
            // Set default values for fields not in Excel
            glucoseU: '',
            respiratoryRate: '',
            bloodType: '',
            drugAllergyStatus: 'UNKNOWN',
            drugAllergyNote: '',
            ingredientAllergyStatus: 'UNKNOWN',
            ingredientAllergyNote: '',
            foodAllergyStatus: 'UNKNOWN',
            foodAllergyNote: '',
            // Use family history data from parsed health data
            familyHistoryPulse: healthData.familyHistoryPulse || '',
            familyHistoryHypertension: healthData.familyHistoryHypertension || 'UNKNOWN',
            familyHistoryMental: 'UNKNOWN',
            familyHistoryCancer: 'UNKNOWN',
            familyHistoryAsthma: 'UNKNOWN',
            familyHistoryEpilepsy: 'UNKNOWN',
            familyHistoryTuberculosis: 'UNKNOWN',
        };

        const healthIndex = await this.healthIndexModel.create(healthIndexData);

        return {
            examinationId: examination._id,
            healthIndexId: healthIndex._id,
        };
    }

    private parseNumber(value: any): number | null {
        if (value === null || value === undefined || value === '') {
            return null;
        }
        const num = Number(value);
        return isNaN(num) ? null : num;
    }

    private parseDate(value: any): Date | null {
        if (!value) return null;

        try {
            // Handle Excel date formats
            if (typeof value === 'number') {
                // Excel serial date
                const excelEpoch = new Date(1900, 0, 1);
                const date = new Date(excelEpoch.getTime() + (value - 2) * 24 * 60 * 60 * 1000);
                return date;
            }

            if (typeof value === 'string') {
                const date = new Date(value);
                return isNaN(date.getTime()) ? null : date;
            }

            if (value instanceof Date) {
                return value;
            }

            return null;
        } catch {
            return null;
        }
    }

    private parseBloodPressure(bloodPressureValue: any): string {
        if (!bloodPressureValue) {
            return '';
        }

        const bpString = bloodPressureValue.toString().trim();

        // Check if it's already in format "120/80"
        if (bpString.includes('/')) {
            const parts = bpString.split('/');
            const sys = this.parseNumber(parts[0]);
            const dia = this.parseNumber(parts[1]);

            if (sys && dia) {
                return `${sys}/${dia}`;
            } else if (sys) {
                return `${sys}/`;
            } else if (dia) {
                return `/${dia}`;
            }
        } else {
            // If it's just a single number, treat it as systolic
            const sys = this.parseNumber(bpString);
            if (sys) {
                return `${sys}/`;
            }
        }

        return '';
    }

    private isValidDate(dateStr: string): boolean {
        if (!dateStr) return false;
        return moment(dateStr, ['D/M/YYYY', 'DD/MM/YYYY'], true).isValid();
    }

    async getPendingHealthData() {
        try {
            const pendingData = await this.pendingPatientModel
                .find({ status: 'pending' })
                .sort({ createdAt: -1 })
                .lean()
                .exec();

            return pendingData.map(item => ({
                id: item._id,
                cmnd: item.cmnd,
                healthData: item.healthData,
                createdAt: item.createdAt,
                status: item.status,
            }));
        } catch (error) {
            throw new HttpException(`Lỗi lấy danh sách dữ liệu chờ liên kết: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async linkPendingData(cmnd: string, patientId: string) {
        // Find all pending data for the given CMND
        const pendingDataList = await this.pendingPatientModel.find({ cmnd, status: 'pending' }).exec();
        if (!pendingDataList || pendingDataList.length === 0) {
            throw new HttpException(`Không tìm thấy dữ liệu chờ liên kết cho CMND: ${cmnd}`, HttpStatus.NOT_FOUND);
        }

        // Verify patient exists
        const patient = await this.patientModel.findOne({ id: patientId }).exec();
        if (!patient) {
            throw new HttpException(`Không tìm thấy bệnh nhân với ID: ${patientId}`, HttpStatus.NOT_FOUND);
        }

        const results = [];
        for (const pendingData of pendingDataList) {
            try {
                // Create examination and health index from pending data
                const savedData = await this.createExaminationWithHealthIndex(patient, pendingData.healthData);

                // Delete pending data after successful linking
                await this.pendingPatientModel.deleteOne({ _id: pendingData._id });

                results.push({
                    message: 'Liên kết dữ liệu thành công và đã xóa dữ liệu chờ',
                    cmnd,
                    patientId,
                    examinationId: savedData.examinationId,
                    healthIndexId: savedData.healthIndexId,
                    pendingId: pendingData._id,
                });
            } catch (error) {
                results.push({
                    message: `Lỗi liên kết dữ liệu: ${error.message}`,
                    cmnd,
                    patientId,
                    pendingId: pendingData._id,
                    error: true,
                });
            }
        }

        return results;
    }

    async generateErrorExcelFile(errors: any[]): Promise<Buffer> {
        const workbook = new Excel.Workbook();
        const worksheet = workbook.addWorksheet('Dữ liệu lỗi');

        // Define headers (same as original file + error column)
        const headers = [
            'Căn cước công dân',
            'Dấu thời gian',
            'Tiêu đề',
            'Tên cơ sở y tế',
            'Chiều Cao',
            'Cân nặng',
            'Vòng bụng',
            'Huyết Áp',
            'Nhịp tim',
            'Nhiệt độ',
            'Tăng Huyết Áp(THA)',
            'Lỗi',
        ];

        // Add header row with styling
        const headerRow = worksheet.addRow(headers);
        headerRow.eachCell(cell => {
            cell.font = { bold: true };
            cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE0E0E0' },
            };
            cell.border = {
                top: { style: 'thin' },
                left: { style: 'thin' },
                bottom: { style: 'thin' },
                right: { style: 'thin' },
            };
        });

        // Add error data rows
        errors.forEach((errorData, index) => {
            try {
                const rowData = [
                    errorData.row?.['Căn cước công dân'] || 'N/A',
                    errorData.row?.['Dấu thời gian'] || 'N/A',
                    errorData.row?.['Tiêu đề'] || 'N/A',
                    errorData.row?.['Tên cơ sở y tế'] || 'N/A',
                    errorData.row?.['Chiều Cao'] || 'N/A',
                    errorData.row?.['Cân nặng'] || 'N/A',
                    errorData.row?.['Vòng bụng'] || 'N/A',
                    errorData.row?.['Huyết Áp'] || 'N/A',
                    errorData.row?.['Nhịp tim'] || 'N/A',
                    errorData.row?.['Nhiệt độ'] || 'N/A',
                    errorData.row?.['Tăng Huyết Áp(THA)'] || 'N/A',
                    errorData.error || 'Unknown error',
                ];
                const row = worksheet.addRow(rowData);

                // Highlight error rows with light red background
                row.eachCell((cell, colNumber) => {
                    cell.fill = {
                        type: 'pattern',
                        pattern: 'solid',
                        fgColor: { argb: 'FFFFD0D0' }, // Light red background
                    };
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' },
                    };

                    // Make error column text red and bold
                    if (colNumber === 12) {
                        // Error column
                        cell.font = { color: { argb: 'FFFF0000' }, bold: true };
                    }
                });
            } catch (error) {
                console.error(`Error processing row ${index + 1}:`, error);
            }
        });

        // Auto-fit columns
        worksheet.columns.forEach(column => {
            const header = column.header || 'Unknown';
            column.width = header.length < 12 ? 12 : header.length + 2;
        });

        // Return buffer
        const buffer = await workbook.xlsx.writeBuffer();
        return buffer as Buffer;
    }
}
