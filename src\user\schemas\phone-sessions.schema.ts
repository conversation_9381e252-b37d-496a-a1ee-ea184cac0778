import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PHONE_SESSION_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const PhoneSessionSchema = new Schema({
    phone: { type: String, unique: true },
    session: { type: String },
    capcha: { type: String },
}, {
    collection: PHONE_SESSION_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
