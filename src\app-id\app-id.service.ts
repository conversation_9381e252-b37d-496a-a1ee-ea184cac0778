import { UrlConfigService } from './../config/config.url.service';
import { ClientUtilService } from './../config/client-util.service';
import { FeatureChildResponseEnum, FeatureResponseEnum } from './enum/feature-response.enum';
import { HttpException, HttpService, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { chain, find, first, orderBy, pick, groupBy, get, map, findIndex } from 'lodash';
import { Model } from 'mongoose';
import { IFeature } from 'src/feature-mongo/interfaces/feature.interface';
import { FEATURE_COLLECTION_NAME } from 'src/feature-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';

import { AddChildrenFeatureDTO } from './dto/add-child-feature-into-feature.dto';
import { AddFeatureIntoPartnerDTO } from './dto/add-feature-into-partner.dto';
import { AddPartnerIntoAppIdDTO } from './dto/add-partner-into-appId.dto';
import { CopyFeatureFromPartnerDTO } from './dto/copy-feature-from-partner.dto';
import { CreateAppIdDTO } from './dto/create-app-id.dto';
import { HospitalResponseDTO } from './dto/hospital-response.dto';
import { HospitalDTO } from './dto/hospital.dto';
import { PartnerResponseEnum } from './enum/partner-response.enum';
import { UpdateAppIdDTO } from './dto/update-app-id.dto';
import { UpdateChildrenInFeatureOfPartnerDTO } from './dto/update-children-feature.dto';
import { UpdateFeatureInPartnerDTO } from './dto/update-feature-in-partner.dto';
import { IAppId } from './interfaces/app-id.inteface';
import { APP_ID_COLLECTION_NAME } from './schemas/constants';
import { UpdatePartnerFeatureDto } from './dto/update-partner-feature.dto';
import { HeadersDto } from '../common/base/headers.dto';

@Injectable()
export class AppIdService {
    private logger = new Logger(AppIdService.name);

    constructor(
        @InjectModel(APP_ID_COLLECTION_NAME) private appIdModel: Model<IAppId>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(FEATURE_COLLECTION_NAME) private featureModel: Model<IFeature>,
        private readonly client: ClientUtilService,
        private readonly config: UrlConfigService,
        private readonly httpService: HttpService,
    ) {}

    async hospitalResponse(fromData: HospitalDTO): Promise<HospitalResponseDTO> {
        const response: HospitalResponseDTO = {
            partnerId: fromData.partnerId,
            name: fromData.name,
            address: fromData.address,
            hotline: fromData.hotline,
            website: fromData.website,
            workingTime: fromData.workingTime,
            workingDate: fromData.workingDate,
        };
        return response;
    }

    async isExistAppId(appId: string): Promise<boolean> {
        const appIdInfo = await this.appIdModel.findOne({ appId }).exec();
        if (appIdInfo) {
            return true;
        }
        return false;
    }

    async isExistPartnerId(partnerId: string): Promise<boolean> {
        const partnerInfo = await this.hospitalModel.findOne({ partnerId }).exec();
        if (partnerInfo) {
            return true;
        }
        return false;
    }

    async isExistPartnerInAppId(appId: string, partnerId: string): Promise<boolean> {
        try {
            const partner = await this.appIdModel.findOne({ appId, 'detail.partnerId': partnerId }).exec();
            if (partner) {
                return true;
            }
        } catch (err) {
            this.logger.error(`Error handle get partner in appId: ${appId}, Error message: ${err.message}`);
            throw err;
        }
        return false;
    }

    async isExistFeature(featureId: string): Promise<any> {
        try {
            const feature = await this.featureModel.findById({ _id: featureId }).exec();
            if (feature) {
                return true;
            }
            return false;
        } catch (err) {
            this.logger.error(`Error when exec isExistFeature() with featureId: ${featureId}\nError: ${err.message}`);
            throw err;
        }
    }

    async isExistFeatureInPartner(partnerId: string, featureId: string): Promise<any> {
        try {
            const hospital = await this.hospitalModel.findOne({ partnerId }).exec();
            // find feature in partner
            const feature = find(hospital.features, item => {
                return `${item._id}` === featureId;
            });
            // validate feature
            if (feature) {
                return true;
            }
            return false;
        } catch (err) {
            this.logger.error(
                `Error when exec isExistFeatureInPartner() with featureId: ${featureId} and partnerId: ${partnerId}\nError: ${err.message}`,
            );
            throw err;
        }
    }

    async isExistChildFeatureInFeatureOfPartner(partnerId: string, featureId: string, childFeatureId: string): Promise<any> {
        try {
            const hospital = await this.hospitalModel.findOne({ partnerId }).exec();
            // find feature in partner
            const feature = find(hospital.features, item => {
                return `${item._id}` === featureId;
            });
            // validate feature
            if (feature) {
                // find children feature in feature
                const childFeature = find(feature.children, item => {
                    return `${item._id}` === childFeatureId;
                });
                if (childFeature) {
                    return true;
                }
            }
            return false;
        } catch (err) {
            this.logger.error(
                `Error when exec isExistChildFeatureInFeatureOfPartner() with childrenFeatureId: ${childFeatureId},
                featureId: ${featureId} and partnerId: ${partnerId}\nError: ${err.message}`,
            );
            throw err;
        }
    }

    // check feature is already exist in partner in appId
    async isAlreadyExistFeatureInPartner(appId: string, partnerId: string, featureId: string): Promise<boolean> {
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            // get partner in appId
            const partner = find(appIdData.detail, item => {
                return `${item.partnerId}` === partnerId;
            });
            const feature = find(partner.features, item => {
                return `${item._id}` === featureId;
            });
            if (feature) {
                return true;
            }
            return false;
        } catch (error) {
            this.logger.error(`Error when exec isAlreadyExistFeatureInPartner() with appId: ${appId}\nError: ${error.message}`);
            throw error;
        }
    }

    // check children feature is already exist in feature in partner of appId
    async isAlreadyExistChildFeatureInParentOfPartner(appId: string, partnerId: string, featureId: string, childFeatureId: string): Promise<boolean> {
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            // get partner in appId
            const partner = find(appIdData.detail, item => {
                return `${item.partnerId}` === partnerId;
            });
            const feature = find(partner.features, item => {
                return `${item._id}` === featureId;
            });
            const childFeature = find(feature.children, item => {
                return `${item._id}` === childFeature;
            });
            if (childFeature) {
                return true;
            }
            return false;
        } catch (error) {
            this.logger.error(`Error when exec isAlreadyExistChildFeatureInParentOfPartner() with appId: ${appId}\nError: ${error.message}`);
            throw error;
        }
    }

    async findAll(): Promise<any> {
        return this.appIdModel.find({}).exec();
    }

    async getDataByAppId(appId: string): Promise<any> {
        const appIdInfo = await this.appIdModel.findOne({ appId }).exec();
        if (!appIdInfo) {
            throw new HttpException('appId not found', HttpStatus.BAD_REQUEST);
        }
        return appIdInfo;
    }

    async createAppId(createAppIdDTO: CreateAppIdDTO): Promise<IAppId> {
        if (await this.isExistAppId(createAppIdDTO.appId)) {
            throw new HttpException('appId is exitst', HttpStatus.BAD_REQUEST);
        }
        try {
            await this.appIdModel.syncIndexes();
            const appId = new this.appIdModel(createAppIdDTO);
            return await appId.save();
        } catch (err) {
            this.logger.error(`Error create AppId: ${createAppIdDTO.appId}, Error message: ${err.message}`);
            throw err;
        }
    }

    async validationAddFeatureIntoPartnerOfAppId(fromData: AddFeatureIntoPartnerDTO): Promise<any> {
        const { appId, partnerId, featureId } = fromData;
        const isExistAppId = await this.isExistAppId(appId);
        const isExistPartnerId = await this.isExistPartnerId(partnerId);
        const isExistFeature = await this.isExistFeature(featureId);
        const isAlreadyExistFeatureInPartner = await this.isAlreadyExistFeatureInPartner(appId, partnerId, featureId);
        // validate base feature in partner
        const isExistFeatureInPartner = await this.isExistFeatureInPartner(partnerId, featureId);
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerId) {
            throw new HttpException(`partner with partnerId: ${partnerId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistFeature) {
            throw new HttpException(`feature with featureId: ${featureId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistFeatureInPartner) {
            throw new HttpException(`featureId: ${featureId} does not exist in partnerId: ${partnerId}`, HttpStatus.BAD_REQUEST);
        }
        if (isAlreadyExistFeatureInPartner) {
            throw new HttpException(`featureId: ${featureId} already exist in partnerId: ${partnerId}`, HttpStatus.BAD_REQUEST);
        }
    }

    async addFeatureIntoPartnerOfAppId(fromData: AddFeatureIntoPartnerDTO): Promise<any> {
        await this.validationAddFeatureIntoPartnerOfAppId(fromData);
        const { appId, partnerId, featureId } = fromData;
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            // find partner in appId
            const partner = find(appIdData.detail, { partnerId });
            // validation partner
            if (partner !== undefined) {
                // get feature
                const feature = await this.featureModel.findById({ _id: featureId }).exec();
                // add feature into partner
                partner.features.push(feature);
            }
            return (await appIdData.save()).toJSON();
        } catch (err) {
            this.logger.error(`Error when exec addFeatureIntoPartnerOfAppId() with appId: ${appId}\nError: ${err.message}`);
            throw err;
        }
    }

    async updatePartnerFeature(formData: UpdatePartnerFeatureDto): Promise<any> {
        const { appId, partnerId, id } = formData;
        const appIdData = await this.appIdModel.findOne({ appId }).exec();
        const partner = find(appIdData.detail, { partnerId });

        if (partner) {
            // add feature into partner
            const features = partner.features;
            const index = findIndex(features, { id });
            if (index >= 0) {
                const oldFeature = features[index];
                features.splice(index, 1, { ...oldFeature.toObject(), ...formData });
                let updatedAppId = appIdData.save();
                return features[index];
            } else {
                throw new HttpException({ message: `Không tìm thấy feature ${id}` }, HttpStatus.BAD_REQUEST);
            }
        } else {
            throw new HttpException({ message: `Không tìm thấy partnerId ${partnerId} trong appId ${appId}` }, HttpStatus.BAD_REQUEST);
        }
    }

    async validationAddChildFeatureIntoFeatureOfPartner(fromData: AddChildrenFeatureDTO): Promise<any> {
        const { appId, partnerId, featureId, childFeatureId } = fromData;
        const isExistAppId = await this.isExistAppId(appId);
        const isExistPartnerId = await this.isExistPartnerId(partnerId);
        const isExistFeature = await this.isExistFeature(featureId);
        const isExistChildFeatureInFeature = await this.isExistChildFeatureInFeatureOfPartner(partnerId, featureId, childFeatureId);
        const isAlreadyExistChildFeatureInPartner = await this.isAlreadyExistChildFeatureInParentOfPartner(
            appId,
            partnerId,
            featureId,
            childFeatureId,
        );
        // validate base feature in partner
        const isExistFeatureInPartner = await this.isExistFeatureInPartner(partnerId, featureId);
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerId) {
            throw new HttpException(`partner with partnerId: ${partnerId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistFeature) {
            throw new HttpException(`feature with featureId: ${featureId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistFeatureInPartner) {
            throw new HttpException(`featureId: ${featureId} does not exist in partnerId: ${partnerId}`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistChildFeatureInFeature) {
            throw new HttpException(`childFeatureId: ${childFeatureId} does not exist in featureId: ${featureId}`, HttpStatus.BAD_REQUEST);
        }
        if (isAlreadyExistChildFeatureInPartner) {
            throw new HttpException(
                `childFeatureId: ${childFeatureId} already exist in featureID: ${featureId} of partnerId: ${partnerId}`,
                HttpStatus.BAD_REQUEST,
            );
        }
    }

    async addChildFeatureIntoFeatureOfPartner(fromData: AddChildrenFeatureDTO): Promise<any> {
        await this.validationAddChildFeatureIntoFeatureOfPartner(fromData);
        const { appId, partnerId, featureId, childFeatureId } = fromData;
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            // find partner in appId
            const partner = find(appIdData.detail, { partnerId });
            // validation partner
            if (typeof partner !== undefined) {
                // find feature
                const feature = find(partner.features, item => {
                    return `${item._id}` === featureId;
                });
                // validate feature
                if (typeof feature !== undefined) {
                    // get feature data
                    const featureData = await this.featureModel.findById({ _id: featureId }).exec();
                    // add children feature into feature
                    feature.children.push(featureData);
                }
            }
            return (await appIdData.save()).toJSON();
        } catch (err) {
            this.logger.error(`Error when exec addFeatureIntoPartnerOfAppId() with appId: ${appId}\nError: ${err.message}`);
            throw err;
        }
    }

    async getPartnerInfo(partnerId: string): Promise<IHospital> {
        if (!(await this.isExistPartnerId(partnerId))) {
            throw new HttpException('partnerId not found', HttpStatus.BAD_REQUEST);
        }
        try {
            const partnerInfo = await this.hospitalModel.findOne({ partnerId }).exec();
            return partnerInfo;
        } catch (err) {
            this.logger.error(`Error get partnerId: ${partnerId}, Error message: ${err.message}`);
            throw err;
        }
    }

    async updateAppId(fromData: UpdateAppIdDTO): Promise<IAppId> {
        const { appId, ...updateData } = fromData;
        if (!(await this.isExistAppId(appId))) {
            throw new HttpException('appId not found', HttpStatus.BAD_REQUEST);
        }
        try {
            const res = await this.appIdModel.findOneAndUpdate({ appId }, { ...updateData }, { new: true }).exec();
            return res;
        } catch (err) {
            this.logger.error(`Error updateAppId: ${err.message}`);
            throw err;
        }
    }

    async deleteAppId(appId: string): Promise<any> {
        if (!(await this.isExistAppId(appId))) {
            throw new HttpException('appId not found', HttpStatus.BAD_REQUEST);
        }
        try {
            const res = await this.appIdModel.findOneAndDelete({ appId }).exec();
            return res;
        } catch (err) {
            this.logger.error(`Error deleteAppId: ${err.message}`);
            throw err;
        }
    }

    async addPartnerIntoAppId(fromData: AddPartnerIntoAppIdDTO): Promise<HospitalResponseDTO> {
        const { appId, partnerId } = fromData;
        if (!(await this.isExistAppId(appId))) {
            throw new HttpException('appId not found', HttpStatus.BAD_REQUEST);
        }
        if (!(await this.isExistPartnerId(partnerId))) {
            throw new HttpException('partnerId not found', HttpStatus.BAD_REQUEST);
        }
        if (await this.isExistPartnerInAppId(appId, partnerId)) {
            throw new HttpException(`partnerId: ${partnerId} already exits in appId: ${appId}`, HttpStatus.BAD_REQUEST);
        }
        try {
            // Get AppId
            const appIdInfo = await this.appIdModel.findOne({ appId }).exec();
            // Find partnerInfo
            const partner = await this.hospitalModel.findOne({ partnerId }).exec();
            // Add partner into appId detail
            const appIdUpdate = appIdInfo.detail.push(partner);
            await appIdInfo.save();
            const response = await this.hospitalResponse(partner);
            return response;
        } catch (err) {
            this.logger.error(`Error add Partner into AppId: ${appId}, Error: ${err.message}`);
            throw err;
        }
    }

    async deletePartnerInAppId(appId: string, partnerId: string): Promise<any> {
        if (!(await this.isExistAppId(appId))) {
            throw new HttpException('appId not found', HttpStatus.BAD_REQUEST);
        }
        if (!(await this.isExistPartnerId(partnerId))) {
            throw new HttpException('partnerId not found', HttpStatus.BAD_REQUEST);
        }
        if (!(await this.isExistPartnerInAppId(appId, partnerId))) {
            throw new HttpException(`partnerId: ${partnerId} does not exist in appId: ${appId}`, HttpStatus.BAD_REQUEST);
        }
        try {
            return await this.appIdModel.findOneAndUpdate({ appId }, { $pull: { detail: { partnerId } } }).exec();
        } catch (err) {
            this.logger.error(`Error delete Partner into AppId: ${appId}, Error: ${err.message}`);
            throw err;
        }
    }

    async deleteFeatureInPartnerOfAppId(appId: string, partnerId: string, featureId: string): Promise<any> {
        // check is exist appId
        const isExistAppId = await this.isExistAppId(appId);
        // check is exist partner in appId
        const isExistPartnerInAppId = await this.isExistPartnerInAppId(appId, partnerId);
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerInAppId) {
            throw new HttpException(`partnerId: ${partnerId} does not exist in appId: ${appId}`, HttpStatus.BAD_REQUEST);
        }
        try {
            // find appId
            const appIds = await this.appIdModel.findOne({ appId }).exec();
            // find hospital in appId
            const hospital = find(appIds.detail, { partnerId });
            // find feature in hospital by filter options
            const feature = find(hospital.features, item => {
                return `${item._id}` === featureId;
            });
            const featureIndex = hospital.features.indexOf(feature);
            // delete features with index in partner of appId
            hospital.features.splice(featureIndex, 1);
            return appIds.save();
        } catch (err) {
            this.logger.error(
                `Error when exec deleteFeatureInPartnerOfAppId() with appId: ${appId} and partnerId: ${partnerId}\nError: ${err.message}`,
            );
            throw err;
        }
    }

    async deleteChildFeatureInPartnerOfAppId(appId: string, partnerId: string, featureId: string, childFeatureId: string): Promise<any> {
        // check exist appId
        const isExistAppId = await this.isExistAppId(appId);
        // check exist partner in appId
        const isExistPartnerInAppId = await this.isExistPartnerInAppId(appId, partnerId);
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerInAppId) {
            throw new HttpException(`partnerId: ${partnerId} does not exist in appId: ${appId}`, HttpStatus.BAD_REQUEST);
        }
        try {
            // find appId
            const appIds = await this.appIdModel.findOne({ appId }).exec();
            // find hospital in appId
            const hospital = find(appIds.detail, { partnerId });
            // find feature in hospital by filter options
            const feature = find(hospital.features, item => {
                return `${item._id}` === featureId;
            });
            // find child feature in feature
            const childFeature = find(feature.children, item => {
                return `${item._id}` === childFeatureId;
            });
            const childFeatureIndex = feature.children.indexOf(childFeature);
            // delete children feature in feature with index in partner of appId
            feature.children.splice(childFeatureIndex, 1);
            return appIds.save();
        } catch (err) {
            this.logger.error(
                `Error when exec deleteChildFeatureInPartnerOfAppId() with appId: ${appId} and partnerId: ${partnerId}\nError: ${err.message}`,
            );
            throw err;
        }
    }

    async getFeatureOfPartnerInAppId(appId: string, partnerId: string): Promise<any> {
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            const partner = find(appIdData.detail, item => {
                return `${item.partnerId}` === partnerId;
            });
            return partner.features;
        } catch (err) {
            this.logger.error(`Error when exec getFeatureOfPartnerInAppId() with partnerId: ${partnerId} and appId: ${appId}\nError: ${err.message}`);
        }
    }

    async handleInputDataForCopyFeature(appId: string, partnerId: string): Promise<any> {
        const isExistAppId = await this.isExistAppId(appId);
        const isExistPartnerInAppId = await this.isExistPartnerInAppId(appId, partnerId);
        const isExistPartnerId = await this.isExistPartnerId(partnerId);

        // validation for input data
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerInAppId) {
            throw new HttpException(`partnerId: ${partnerId} does not exist in appId: ${appId}`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerId) {
            throw new HttpException(`partnerId: ${partnerId} does not exist`, HttpStatus.BAD_REQUEST);
        }
    }

    async getFeatureFromPartner(partnerId: string): Promise<any> {
        try {
            const hospital = await this.hospitalModel.findOne({ partnerId }).exec();
            return hospital.features;
        } catch (err) {
            this.logger.error(`Error when exec getFeatureFromPartner() with partnerId: ${partnerId}\nError: ${err.message}`);
            throw err;
        }
    }

    async preHandleCopyFeatureFormPartner(partnerId: string, appId: string): Promise<any> {
        try {
            // find appId
            const appIds = await this.appIdModel.findOne({ appId }).exec();
            // find hospital in appId
            const hospital = find(appIds.detail, { partnerId });
            // find feature in hospital by filter options
            let feature = hospital.features;
            if (feature) {
                feature = [];
                return appIds.save();
            }
        } catch (err) {
            this.logger.error(
                `Error when exec checkFeatureInPartnerIntoAppId() with partnerId: ${partnerId} and appId: ${appId}\nError: ${err.message}`,
            );
            throw err;
        }
    }

    async copyFeatureFromBasePartnerToPartnerOfAppId(fromData: CopyFeatureFromPartnerDTO): Promise<any> {
        const { appId, partnerId } = fromData;
        // validation for input data
        await this.handleInputDataForCopyFeature(appId, partnerId);
        try {
            // Get feature from partner with partnerId
            const features = await this.getFeatureFromPartner(partnerId);
            // Pre handler
            await this.preHandleCopyFeatureFormPartner(partnerId, appId);
            // find appId
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            // find hospital in appId
            const hospital = find(appIdData.detail, { partnerId });
            hospital.features = features;
            return (await appIdData.save()).toJSON();
        } catch (err) {
            this.logger.error(`Error when exec copyFeatureFromBasePartnerToPartnerOfAppId() with appId: ${appId}\nError: ${err.message}`);
            throw err;
        }
    }

    async validationForUpdateFeature(fromData: UpdateFeatureInPartnerDTO): Promise<any> {
        // declare variable
        const { appId, partnerId, featureId } = fromData;
        const isExistAppId = await this.isExistAppId(appId);
        const isExistPartnerInAppId = await this.isExistPartnerInAppId(appId, partnerId);
        const isExistFeatureInPartner = await this.isAlreadyExistFeatureInPartner(appId, partnerId, featureId);
        // validation process
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerInAppId) {
            throw new HttpException(`partner with partnerId: ${partnerId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistFeatureInPartner) {
            throw new HttpException(`featureId: ${featureId} does not exist in partnerId: ${partnerId}`, HttpStatus.BAD_REQUEST);
        }
    }

    async updateFeatureInPartner(fromData: UpdateFeatureInPartnerDTO): Promise<any> {
        // pre handler
        await this.validationForUpdateFeature(fromData);
        // declare variable
        const { appId, partnerId, featureId, ...updateData } = fromData;
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            const partner = find(appIdData.detail, item => {
                return `${item.partnerId}` === partnerId;
            });
            const feature = find(partner.features, item => {
                return `${item._id}` === featureId;
            });
            // update data
            feature.set(updateData);
            return (await partner.save()).toJSON();
        } catch (error) {
            this.logger.error(`Error when exec updateFeatureInPartner() with appId: ${appId} and partnerId: ${partnerId}\nError: ${error.message}`);
            throw error;
        }
    }

    async validationForUpdateChildFeature(fromData: UpdateChildrenInFeatureOfPartnerDTO): Promise<any> {
        // declare variable
        const { appId, partnerId, featureId, childFeatureId } = fromData;
        const isExistAppId = await this.isExistAppId(appId);
        const isExistPartnerInAppId = await this.isExistPartnerInAppId(appId, partnerId);
        const isExistFeatureInPartner = await this.isAlreadyExistFeatureInPartner(appId, partnerId, featureId);
        const isExistChildFeatureInFeature = await this.isAlreadyExistChildFeatureInParentOfPartner(appId, partnerId, featureId, childFeatureId);
        // validation process
        if (!isExistAppId) {
            throw new HttpException(`appId: ${appId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistPartnerInAppId) {
            throw new HttpException(`partner with partnerId: ${partnerId} does not exist`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistFeatureInPartner) {
            throw new HttpException(`featureId: ${featureId} does not exist in partnerId: ${partnerId}`, HttpStatus.BAD_REQUEST);
        }
        if (!isExistChildFeatureInFeature) {
            throw new HttpException(
                `childFeatureId: ${childFeatureId} does not exist in featureId: ${featureId} in partnerId: ${partnerId}`,
                HttpStatus.BAD_REQUEST,
            );
        }
    }

    async updateChildFeatureInFeatureOfPartner(fromData: UpdateChildrenInFeatureOfPartnerDTO): Promise<any> {
        // pre handler
        await this.validationForUpdateChildFeature(fromData);
        // declare variable
        const { appId, partnerId, featureId, ...updateData } = fromData;
        try {
            const appIdData = await this.appIdModel.findOne({ appId }).exec();
            const partner = find(appIdData.detail, item => {
                return `${item.partnerId}` === partnerId;
            });
            const feature = find(partner.features, item => {
                return `${item._id}` === featureId;
            });
            const childFeature = find(feature.children, item => {
                return `${item._id}` === childFeature;
            });
            // update data
            childFeature.set(updateData);
            return (await partner.save()).toJSON();
        } catch (error) {
            this.logger.error(
                `Error when exec updateChildFeatureInFeatureOfPartner() with appId: ${appId} and partnerId: ${partnerId}\nError: ${error.message}`,
            );
            throw error;
        }
    }

    async getListPartnerByAppId(appId: string): Promise<any[]> {
        try {
            const query : any = {
                partnerId: { $nin: ['medpro', 'umcmono', 'digimed', 'canthozone'] },
                status: 1,
                hospitalType: 1,
            };
            const projection = {
                partnerId: 1,
                city: 1,
                city_id: 1,
                name: 1,
                address: 1,
                packageImgDefault: 1,
                circleLogo: 1,
                slug: 1,
                newHospitalTypes: 1,
                isCashBack: 1,
                listingPackagePaid: 1,
                message: 1
            };
            const data = await this.hospitalModel
                .find(query, projection)
                .populate({ path: 'city', select: { name: true, id: true } })
                .sort({ deliveryStatus: 'desc', sortOrder: 'asc' })
                .exec();
            return data
        } catch (error) {
            throw error;
        }
    }

    async getListPartnerByAppIdAndFeatureType(appId: string, featureType?: string): Promise<any[]> {
        const app = await this.appIdModel.findOne({ appId });
        if (!app) {
            throw new HttpException(`appId: ${appId} không tồn tại!`, HttpStatus.NOT_FOUND);
        }
        const { detail } = app;
        if (!detail) {
            throw new HttpException('appId không có partner nào!', HttpStatus.BAD_REQUEST);
        }

        let partnerIds = [];
        if (featureType) {
            partnerIds = detail
                .filter(hospital => {
                    const { features = [] } = hospital;
                    const foundFeature = features.find(f => f.type === featureType);
                    return !!foundFeature;
                })
                .map(({ partnerId }) => partnerId);
        }

        // Get lại từ collection hospitals
        const projection = ['partnerId', 'phone', 'email', 'name', 'image', 'circleLogo', 'address', 'disabled', 'status', 'message'];
        return await Promise.all(
            [...new Set(partnerIds)].map(async partnerId => {
                const hospital = await this.hospitalModel.findOne({ partnerId }, projection);
                return pick(hospital, projection);
            }),
        );
    }

    async getFeatureInAppId(appId: string): Promise<any> {
        try {
            const detail = await this.getListPartnerByAppId(appId);
            let featureHandlers = [];
            let featureRes = [];
            for (const partner of detail) {
                const { features } = partner;
                featureHandlers = [...featureHandlers, ...features.map(feature => pick<any>(feature, Object.values(FeatureResponseEnum)))];
            }
            const featureGroup = groupBy(featureHandlers, 'type');
            for (const [key, value] of Object.entries(featureGroup)) {
                const feature = first(value);
                value.map(element => {
                    feature.children = [...feature.children, ...element.children];
                });
                if (feature.children && feature.children.length) {
                    const childrenGroup = groupBy(feature.children, 'type');
                    feature.children = Object.entries(childrenGroup).map(([keyChild, valueChild]) => {
                        return pick<any>(first(valueChild), Object.values(FeatureChildResponseEnum));
                    });
                }
                featureRes = [...featureRes, feature];
            }
            return featureRes;
        } catch (error) {
            throw error;
        }
    }

    async getPartnerByAppIdAndFeatureId(appId: string, featureId: string): Promise<any[]> {
        try {
            let partnerResponse = [];
            const detail = await this.getListPartnerByAppId(appId);
            for (const partner of detail) {
                let featureHandlers: any = [];
                const { features } = partner;
                if (features) {
                    for (const feature of features) {
                        const { children } = feature;
                        if (children) {
                            featureHandlers = [...featureHandlers, ...children];
                        }
                    }
                    featureHandlers = [...featureHandlers, ...features];
                }
                const isHaveFeature = featureHandlers.find(item => item.id === featureId);
                if (isHaveFeature) {
                    partnerResponse = [...partnerResponse, partner];
                }
            }
            return partnerResponse;
        } catch (error) {
            throw error;
        }
    }

    async getPartnerIdsInAppGroup(appId: string): Promise<string[]> {
        const appIdConfig = await this.appIdModel.findOne({ appId }).exec();

        if (!appIdConfig) {
            return [];
        }

        return map(get(appIdConfig, 'detail', []), 'partnerId');
    }

    async getFeaturesDomain(appId: string): Promise<any> {
        let listFeature: any[];
        const app = await this.appIdModel.findOne({ appId }).exec();
        if (app?.detail && app.detail.length) {
            const appObj = app.toObject();
            const { detail: partnersApp } = appObj;
            let features: any[] = [];
            let featureTypes: string[] = [];
            let partners: string[] = [];

            for (const partner of partnersApp) {
                features = [
                    ...features,
                    ...partner.features.map(feature => {
                        return {
                            ...feature,
                            partnerId: partner.partnerId,
                            partnerName: partner.name,
                            logoPartner: partner.image,
                        };
                    }),
                ];
                featureTypes = [...featureTypes, ...partner.features.map(feature => feature.type)];
                partners = [...partners, partner.partnerId];
            }

            const setFeturesTypes = new Set(featureTypes);
            const groupFeaturePartner = groupBy(features, 'partnerId');

            listFeature = [...setFeturesTypes].map(featureType => {
                let partnerFeatures: any[] = [];
                for (const partner of partners) {
                    const findFeature = groupFeaturePartner[partner]
                        ? groupFeaturePartner[partner].find(feature => feature.type === featureType)
                        : null;
                    if (findFeature) {
                        partnerFeatures = [
                            ...partnerFeatures,
                            {
                                partnerId: findFeature.partnerId,
                                disabled: findFeature.disabled,
                                message: findFeature.message,
                                name: findFeature.partnerName,
                                logoPartner: findFeature.logoPartner,
                                webRoute: findFeature?.webRoute,
                                mobileRoute: findFeature?.mobileRoute,
                                status: findFeature?.status,
                                mobileStatus: findFeature?.mobileStatus,
                            },
                        ];
                    }
                }
                const getInfoFeature = features.find(feature => feature.type === featureType);
                return {
                    type: featureType,
                    name: getInfoFeature.name,
                    iconFeature: getInfoFeature.image,
                    displayIcon: getInfoFeature?.displayIcon || '',
                    partners: partnerFeatures,
                };
            });
        } else {
            const hospital = await this.hospitalModel
                .findOne({
                    partnerId: appId,
                })
                .exec();
            if (hospital) {
                const { features = [] } = hospital.toObject();
                /* kiểm tra trước khi response về */
                let resultData: any = [];
                for (const feature of features) {
                    const getChildren = get(feature, 'children', []);
                    if (getChildren.length > 0) {
                        resultData = [...resultData, ...getChildren];
                    } else {
                        resultData = [...resultData, feature];
                    }
                }
                const setFeturesTypes = new Set(resultData.map(feature => feature.type));
                listFeature = [...setFeturesTypes].map(featureType => {
                    let partnerFeatures: any[] = [];

                    const findFeature = resultData.find(feature => feature.type === featureType);
                    if (findFeature) {
                        partnerFeatures = [
                            ...partnerFeatures,
                            {
                                partnerId: hospital.partnerId,
                                disabled: findFeature.disabled,
                                message: findFeature.message,
                                name: hospital.name,
                                logoPartner: hospital.image,
                                webRoute: findFeature?.webRoute,
                                mobileRoute: findFeature?.mobileRoute,
                                status: findFeature?.status,
                                mobileStatus: findFeature?.mobileStatus,
                            },
                        ];
                    }
                    return {
                        type: featureType,
                        name: findFeature?.name || '',
                        iconFeature: findFeature?.image || '',
                        displayIcon: findFeature?.displayIcon || '',
                        partners: partnerFeatures,
                    };
                });
            }
        }
        return listFeature;
    }

    async getByDomain(domain: string): Promise<any> {
        const data = await this.client.get(`${this.config.getBoUrl()}/partner-domain/single?domain=${domain}`);

        if (!data || !data?.partnerId) {
            throw new HttpException('Partner domain chưa được cấu hình cho bệnh viện này!', HttpStatus.BAD_REQUEST);
        }

        const features = await this.getFeaturesDomain(data.partnerId);

        let partners = [];
        for (const feature of features) {
            partners = [...partners, ...feature.partners.map(partner => partner.partnerId)];
        }
        partners = await Promise.all(
            [...new Set(partners)].map(async partnerId => {
                const hospital = await this.hospitalModel.findOne(
                    { partnerId },
                    {
                        partnerId: true,
                        phone: true,
                        email: true,
                        name: true,
                        logoPartner: true,
                        circleLogo: true,
                        address: true,
                    },
                );
                return {
                    partnerId: hospital?.partnerId,
                    disabled: hospital?.phone,
                    email: hospital?.email,
                    name: hospital?.name,
                    logoPartner: hospital?.image,
                    circleLogo: hospital?.circleLogo,
                    address: hospital?.address,
                };
            }),
        );

        return {
            ...data,
            appId: data.partnerId,
            features,
            partners,
        };
    }

    async getAppIdByDomain(domain: string) {
        const data = await this.client.get(`${this.config.getBoUrl()}/partner-domain/single?domain=${domain}`);

        if (!data || !data?.partnerId) {
            throw new HttpException('Partner domain chưa được cấu hình cho bệnh viện này!', HttpStatus.BAD_REQUEST);
        }

        return {
            appId: data.partnerId,
            ...data,
        };
    }

    public async getQuickNotifBookingPages(formData: any, appId: string, partnerId: string, platform: string, version?: string, locale?: string): Promise<any> {
        const url = `${this.config.getUrlApiWeb}/app-id/quick-notif`;
        try {
            const { data } = await this.httpService.post(
                url,
                formData,
                { headers: { appid: appId, partnerid: partnerId, platform, version: version || '', locale: locale || 'vi'} },
            ).toPromise();
            return data;
        } catch (error) {
            const message = error?.message || error?.response?.data?.message
            this.logger.error(`Error when exec getQuickNotifBookingPages. Cause: ${message}`);
            throw new HttpException(message, HttpStatus.BAD_REQUEST)
        }
    }

    public async getFeatureInApp(headers: HeadersDto): Promise<any> {
        const url = `${this.config.getUrlApiWeb}/app-id/quick-notif`;
        try {
            const { data } = await this.httpService.get(url, { headers }).toPromise();
            return data;
        } catch (error) {
            const message = error?.message || error?.response?.data?.message
            this.logger.error(`Error when exec getFeatureInApp. Cause: ${message}`);
            throw new HttpException(message, HttpStatus.BAD_REQUEST)
        }
    }
}
