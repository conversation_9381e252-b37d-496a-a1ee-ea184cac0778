import { Body, Controller, Get, HttpCode, Patch, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { RollBackBookingProcessDto } from './dto/rollback-process-patient.dto';
import { SyncBookingDTO } from './dto/sync-booking.dto';
import { SyncBookingService } from './sync-booking.service';

@Controller('sync-booking')
@ApiTags('sync booking')
export class SyncBookingController {

    constructor(
        private readonly service: SyncBookingService,
    ) { }

    @Post('one-booking-umc')
    async syncOneBooking(
        @Body() formData :  { bookingCodeV1?: string }
    ): Promise<any> {
        return this.service.syncUmcBooking(formData);
    }

    @Post('one-skin-booking')
    async syncOneSkinBooking(
        @Query('id') id: number,
    ): Promise<any> {
        return this.service.syncOneSkinBooking(id);
    }

    @Post('sync-patient')
    async syncPatient(
        @Query('id') id: number,
        @Query('partnerId') partnerId: string,
    ): Promise<any> {
        return this.service.syncPatient(id, partnerId);
    }

    @Post('sync-skin-patient')
    async syncSkinPatient(
        @Query('id') id: number,
    ): Promise<any> {
        return this.service.syncPatient(id, 'dalieuhcm');
    }

    @Get('process')
    @ApiOperation({ summary: 'Lấy danh sách đồng bộ Booking process v1 -> v2' })
    async getSyncBookingProcess(@Query() fromData: SyncBookingDTO): Promise<any> {
        return this.service.getSyncBookingProcess(fromData);
    }

    @Get('process-failed')
    @ApiOperation({ summary: 'Lấy danh sách đồng bộ Booking process failed v1 -> v2' })
    async getSyncBookingProcessFailed(@Query() fromData: SyncBookingDTO): Promise<any> {
        return this.service.getSyncBookingProcessFailed(fromData);
    }

    @Get('success')
    @ApiOperation({ summary: 'Lấy danh sách đồng bộ Booking success v1 -> v2' })
    async getSyncBookingSuccess(@Query() fromData: SyncBookingDTO): Promise<any> {
        return this.service.getSyncBookingSuccess(fromData);
    }

    @Patch('rollback-process-failed')
    @HttpCode(204)
    @ApiOperation({ summary: 'Rollback sync booking fail' })
    async rollBackSyncBookingFail(
        @Body() formData: RollBackBookingProcessDto,
    ): Promise<any> {
        return this.service.rollBackSyncBookingFailService(formData.processId);
    }

}
