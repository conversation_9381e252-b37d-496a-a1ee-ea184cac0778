import {
    BadRequestException,
    Body,
    Controller,
    Delete,
    Get,
    Headers,
    HttpException,
    HttpStatus,
    Param,
    Post,
    Req,
    Res,
    UploadedFile,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiHeader, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { createExaminationResultDTO } from './dto/create-examination-result.dto';
import { GetPatientExaminationDTO } from './dto/get-patient-examination.dto';
import { updateExaminationResultDTO } from './dto/update-examination-result.dto';
import { ExaminatonService } from './examinaton.service';
import multer = require('multer');
import { createExamNoAuthDTO } from './dto/create-exam-no-auth.dto';
import { GetPatientExamNoAuthDTO } from './dto/get-patient-exam-no-auth.dto';
import { SyncPendingPatientsDTO } from './dto/sync-pending-patients.dto';
import { extname } from 'path';
import * as Excel from 'exceljs';

@Controller('patient-files')
@ApiTags('Patient - Upload file kết quả khám')
export class ExaminatonController {
    constructor(private readonly examinatonService: ExaminatonService) {}

    @Post('update-examination-result')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    @UseInterceptors(FilesInterceptor('files'))
    async updateExaminationResult(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
        @Body() examinationResult: updateExaminationResultDTO,
        @Headers('locale') locale: string,
    ): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException(
                {
                    message: 'Token không hợp lệ.',
                    error: 401,
                },
                HttpStatus.UNAUTHORIZED,
            );
        }
        if (!appId) {
            appId = partnerid;
        }

        const cskhInfo = await this.examinatonService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        return this.examinatonService.updateExaminationResult(partnerid, appId, objUser, examinationResult, cskhInfo, locale);
    }

    @Post('create-quick-examination')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    @UseInterceptors(FilesInterceptor('files'))
    async createQuickExaminationResult(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
        @Body() examinationResult: createExaminationResultDTO,
        @Headers('locale') locale: string,
    ): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException(
                {
                    message: 'Token không hợp lệ.',
                    error: 401,
                },
                HttpStatus.UNAUTHORIZED,
            );
        }
        if (!appId) {
            appId = partnerid;
        }

        const cskhInfo = await this.examinatonService.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        return this.examinatonService.createQuickExaminationResult(partnerid, appId, objUser, examinationResult, cskhInfo, locale);
    }

    @Post('create-exam')
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    @UseInterceptors(FilesInterceptor('files'))
    async createExamNoAuth(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Body() examinationResult: createExamNoAuthDTO,
        @Headers('locale') locale: string,
    ): Promise<any> {
        if (!appId) {
            appId = partnerid;
        }

        return this.examinatonService.createExamNoAuth(partnerid, appId, examinationResult, locale);
    }

    @Post('get-examination-result')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getExaminationResult(
        @Body() formData: GetPatientExaminationDTO,
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException(
                {
                    message: 'Token không hợp lệ.',
                    error: 401,
                },
                HttpStatus.UNAUTHORIZED,
            );
        }
        const userId = user.userMongoId;
        return this.examinatonService.getExaminationResult(userId, formData);
    }

    @Delete('/:id')
    @ApiParam({
        name: 'id',
        type: String,
        required: true,
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({ summary: 'Xóa Examination Result' })
    async delete(@Param('id') id: string): Promise<any> {
        return this.examinatonService.deleteExaminationResult(id);
    }

    @Get('/size-limit')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({ summary: 'Lấy setting limit size file' })
    async limitSizeFile(): Promise<any> {
        return this.examinatonService.limitSizeFile();
    }

    @Post('upload')
    @ApiConsumes('multipart/form-data')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @UseInterceptors(FileInterceptor('file'))
    async uploadFileExamResults(@Req() req, @UploadedFile() file): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException(
                {
                    message: 'Token không hợp lệ.',
                    error: 401,
                },
                HttpStatus.UNAUTHORIZED,
            );
        }
        const userId = user.userMongoId;
        return this.examinatonService.uploadFileExamResults(userId, file);
    }

    @Post('upload-s3')
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @UseInterceptors(FileInterceptor('file'))
    async uploadFileExamNoAuth(@UploadedFile() file): Promise<any> {
        return this.examinatonService.uploadFileExamNoAuth(file);
    }

    @Post('get-exam')
    async getExamByPatientId(@Body() formData: GetPatientExamNoAuthDTO): Promise<any> {
        return this.examinatonService.getExamByPatientId(formData);
    }

    @Get('blood-type')
    async getBloodType(): Promise<any> {
        return this.examinatonService.getBloodType();
    }

    @Post('link-pending-data')
    @ApiOperation({ summary: 'Liên kết dữ liệu sức khỏe đang chờ với bệnh nhân' })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                cmnd: {
                    type: 'string',
                    description: 'Căn cước công dân',
                    example: '123456789012',
                },
                patientId: {
                    type: 'string',
                    description: 'ID của bệnh nhân',
                    example: 'patient_123',
                },
            },
            required: ['cmnd', 'patientId'],
        },
    })
    async linkPendingHealthData(@Body() body: { cmnd: string; patientId: string }, @Req() req): Promise<any> {
        const { cmnd, patientId } = body;
        if (!cmnd || !patientId) {
            throw new BadRequestException('CMND và Patient ID là bắt buộc');
        }

        return this.examinatonService.linkPendingData(cmnd, patientId);
    }

    @Get('pending-data')
    @ApiOperation({ summary: 'Lấy danh sách dữ liệu sức khỏe đang chờ liên kết' })
    async getPendingHealthData(@Req() req): Promise<any> {
        return this.examinatonService.getPendingHealthData();
    }

    @Post('health-data')
    @ApiConsumes('multipart/form-data')
    @ApiOperation({ summary: 'Import dữ liệu sức khỏe từ file Excel' })
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                    description: 'File Excel chứa dữ liệu sức khỏe (.xlsx hoặc .xls)',
                },
            },
        },
    })
    @UseInterceptors(
        FileInterceptor('file', {
            storage: multer.memoryStorage(),
            fileFilter: (req, file, cb) => {
                // Validate file type by extension and mime type
                const allowedExtensions = /\.(xlsx|xls)$/i;
                const allowedMimeTypes = [
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'application/vnd.ms-excel',
                    'application/octet-stream'
                ];
                
                const isValidExtension = allowedExtensions.test(file.originalname);
                const isValidMimeType = allowedMimeTypes.includes(file.mimetype);
                
                if (!isValidExtension && !isValidMimeType) {
                    return cb(new BadRequestException('Chỉ chấp nhận file Excel (.xlsx hoặc .xls)!'), false);
                }
                cb(null, true);
            },
            limits: {
                fileSize: 10 * 1024 * 1024, // 10MB limit
            },
        }),
    )
    async importHealthData(@UploadedFile() file: Express.Multer.File, @Res() res) {
        if (!file || !file.buffer) {
            throw new BadRequestException('Vui lòng chọn file Excel để upload');
        }

        if (file.size === 0) {
            throw new BadRequestException('File Excel không được để trống');
        }

        try {
            // Parse Excel file
            const excelData = await this.parseExcelFile(file);
            // Process health data
            const results = await this.examinatonService.processHealthData(excelData);

            // Categorize results
            const successResults = results.filter(r => r.status === 'success');
            const pendingResults = results.filter(r => r.status === 'pending');
            const errorResults = results.filter(r => r.status === 'error' || r.error);
            // Check if there are any errors
            if (errorResults?.length > 0) {
                // Generate error Excel file
                try {
                    const errorFilePath: Buffer = await this.examinatonService.generateErrorExcelFile(errorResults);
                    const filename = `examination-error-data-${new Date().toISOString().slice(0, 10)}.xlsx`;
                    
                    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
                    res.setHeader('Content-Length', errorFilePath.length);
                    
                    return res.send(errorFilePath);
                } catch (reportError) {
                    console.error('Error generating error Excel file:', reportError);
                    throw new BadRequestException({
                        success: false,
                        message: `Import thất bại. Có ${errorResults.length} dòng bị lỗi.`,
                        errorCount: errorResults.length,
                        successCount: successResults.length,
                        pendingCount: pendingResults.length,
                        totalRows: results.length,
                        error: 'Không thể tạo file Excel báo cáo lỗi',
                        errorItems: errorResults.slice(0, 10).map(err => ({
                            rowNumber: err.row?.rowNumber || 'Unknown',
                            cmnd: err.row?.['Căn cước công dân'] || 'N/A',
                            error: err.error,
                        })),
                    });
                }
            } else {
                // All successful - return success message
                return res.json({
                    success: true,
                    message: `Import thành công ${successResults.length} hồ sơ khám bệnh.`,
                    summary: {
                        total: results.length,
                        success: successResults.length,
                        pending: pendingResults.length,
                        errors: errorResults.length,
                    },
                    details: {
                        successItems: successResults.length > 0 ? successResults.slice(0, 5) : [],
                        pendingItems: pendingResults.length > 0 ? pendingResults.slice(0, 5) : [],
                    },
                });
            }
        } catch (error) {
            const message = error?.response?.message || error?.message || 'Lỗi không xác định';
            throw new BadRequestException(`Lỗi import dữ liệu sức khỏe: ${message}`);
        }
    }

    private async parseExcelFile(file: Express.Multer.File): Promise<any[]> {
        if (!file.buffer || file.buffer.length === 0) {
            throw new BadRequestException('File Excel không hợp lệ hoặc bị trống');
        }

        const workbook = new Excel.Workbook();
        
        try {
            await workbook.xlsx.load(file.buffer);
        } catch (error) {
            throw new BadRequestException(`Không thể đọc file Excel: ${error.message}. Vui lòng kiểm tra file có đúng định dạng Excel không.`);
        }

        // Get first worksheet
        let worksheet = workbook.getWorksheet(1);
        if (!worksheet) {
            worksheet = workbook.worksheets[0];
            if (!worksheet) {
                throw new BadRequestException('Không tìm thấy worksheet hợp lệ trong file Excel');
            }
        }

        const maxRows = 5000;
        const meaningfulRows = [];

        for (let i = 0; i <= worksheet.rowCount; i++) {
            const row = worksheet.getRow(i);
            const rowValues = Array.isArray(row.values) ? row.values : [];
            if (rowValues.some(value => value !== null && value !== undefined && value !== '')) {
                meaningfulRows.push(row);
            }
        }
        if (meaningfulRows.length - 1 > maxRows) {
            throw new BadRequestException(`File Excel vượt quá giới hạn ${maxRows} dòng. Vui lòng kiểm tra lại.`);
        }

        // Define expected headers
        const expectedHeaders = [
            'Căn cước công dân',
            'Dấu thời gian',
            'Tiêu đề',
            'Tên cơ sở y tế',
            'Chiều Cao',
            'Cân nặng',
            'Vòng bụng',
            'Huyết Áp',
            'Nhịp tim',
            'Nhiệt độ',
            'Tăng Huyết Áp(THA)',
        ];

        const data = [];
        let headerRow: string[] = [];

        // Process rows
        let rowIndex = 0;
        worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
            if (rowIndex === 0) {
                // Process header row
                headerRow = [];
                row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
                    const cellValue = cell.value?.toString()?.trim() || '';
                    headerRow[colNumber - 1] = cellValue;
                });

            } else {
                // Process data rows
                const rowData: any = { rowNumber }; // Add rowNumber for error tracking
                let hasData = false;

                row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
                    const header = headerRow[colNumber - 1];
                    if (header && expectedHeaders.includes(header)) {
                        let cellValue = cell.value;
                        
                        // Handle different cell value types
                        if (cellValue !== null && cellValue !== undefined) {
                            // Handle Excel date values
                            if (header === 'Dấu thời gian' && cellValue) {
                                if (typeof cellValue === 'number') {
                                    // Excel serial date - convert to JavaScript Date
                                    const excelEpoch = new Date(1900, 0, 1);
                                    cellValue = new Date(excelEpoch.getTime() + (cellValue - 2) * 24 * 60 * 60 * 1000);
                                } else if (cellValue instanceof Date) {
                                    // Already a Date object
                                    cellValue = cellValue;
                                } else if (typeof cellValue === 'string') {
                                    // Try to parse as date string
                                    const parsedDate = new Date(cellValue);
                                    cellValue = isNaN(parsedDate.getTime()) ? cellValue : parsedDate;
                                }
                            }


                            // Handle text values
                            if (['Căn cước công dân'].includes(header)) {
                                cellValue = cellValue.toString().trim();
                            }
                        }

                        // Store the processed value
                        rowData[header] = cellValue;
                        
                        // Check if this cell has meaningful data
                        if (cellValue !== null && cellValue !== undefined && cellValue !== '') {
                            hasData = true;
                        }
                    }
                });
                
                // Only add rows that have at least some meaningful data
                if (hasData) {
                    data.push(rowData);
                }
            }
            rowIndex++;
        });

        if (data.length === 0) {
            throw new BadRequestException('Không tìm thấy dữ liệu hợp lệ trong file Excel. Vui lòng kiểm tra lại format và nội dung file.');
        }
        return data;
    }

    @Post('sync-pending-patients')
    @ApiOperation({
        summary: 'Đồng bộ pending patients với existing patients',
        description: 'API để đồng bộ các pending patient có CCCD trùng với patient hiện có trong hệ thống. Sẽ tạo examination và health index cho các patient đã tìm thấy.'
    })
    @ApiBody({
        type: SyncPendingPatientsDTO,
        description: 'Tham số tùy chọn để chỉ định CMND cụ thể cần đồng bộ',
        required: false
    })
    async syncPendingPatientsWithExistingPatients(
        @Body() syncData: SyncPendingPatientsDTO = {}
    ): Promise<any> {
        try {
            const { cmnds } = syncData;
            const result = await this.examinatonService.syncPendingPatientsWithExistingPatients(cmnds);
            return {
                statusCode: HttpStatus.OK,
                message: 'Đồng bộ dữ liệu thành công',
                data: result
            };
        } catch (error) {
            throw new HttpException(
                error.message || 'Lỗi đồng bộ dữ liệu',
                error.status || HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }
}
