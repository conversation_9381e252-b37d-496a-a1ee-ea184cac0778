import { USER_APP_COLLECTION_NAME, USER_COLLECTION_NAME, USER_REQUEST_COLLECTION_NAME } from 'src/user/schemas/constants';
import { BookingSchema } from './../booking-gateway/schemas/booking.schema';
import { BOOKING_COLLECTION_NAME } from './../booking-gateway/schemas/constants';
import { ConfigCacheManagerService } from './../config/config.cache-manager.service';
import { CacheModule, Module } from '@nestjs/common';
import { ReportService } from './report.service';
import { ReportController } from './report.controller';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { UserSchema } from 'src/user/schemas/user.schema';
import { UserAppSchema } from 'src/user/schemas/user-app.schema';
import { UserRequestsSchema } from 'src/user/schemas/user-requests.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
      { name: USER_COLLECTION_NAME, schema: UserSchema },
      { name: USER_APP_COLLECTION_NAME, schema: UserAppSchema },
      { name: USER_REQUEST_COLLECTION_NAME, schema: UserRequestsSchema },
    ]),
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigCacheManagerService],
      useFactory: (config: ConfigCacheManagerService) => config.createCacheOptions,
    }),
  ],
  providers: [ReportService],
  controllers: [ReportController],
})
export class ReportModule { }
