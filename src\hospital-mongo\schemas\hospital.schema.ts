import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { HOSPITAL_COLLECTION_NAME } from './constants';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { FEATURE_COLLECTION_NAME } from 'src/feature-mongo/schemas/constants';
import { FeatureSchema } from 'src/feature-mongo/schemas/feature.schema';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { ROOM_COLLECTION_NAME } from 'src/room-mongo/schemas/constants';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { DeliveryStatusEnum } from '../enums/delivery-status.enums';

const Schema = mongoose.Schema;

const HospitalSchema = new Schema({
    partnerId: { type: String, index: { unique: true } },
    name: String,
    short_name: String,
    sms_name: { type: String, uppercase: true },
    image: String,
    banner: { type: String, default: '' },
    status: { type: Number, default: 0 },
    city_id: String,
    city: { type: Schema.Types.ObjectId, ref: CITY_COLLECTION_NAME },
    address: String,
    base_url: String,
    lat: Number,
    long: Number,
    hotline: String,
    subjects: [{ type: Schema.Types.ObjectId, ref: SUBJECT_COLLECTION_NAME }],
    services: [{ type: Schema.Types.ObjectId, ref: SERVICE_COLLECTION_NAME }],
    doctors: [{ type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME }],
    rooms: [{ type: Schema.Types.ObjectId, ref: ROOM_COLLECTION_NAME }],
    notifAppId: { type: String },
    notifApiKey: { type: String },
    features: [FeatureSchema],
    message: { type: String, default: '' },
    sortOrder: { type: Number, default: 100 },
    sortOrderKM: { type: Number, default: 100 },
    website: String,
    phone: String,
    email: String,
    hotlinePartner: String,
    hotlineMedpro: String,
    workingTime: String,
    workingDate: String,
    googleMap: String,
    ios: String,
    android: String,
    bienLai: Boolean,
    deliveryStatus: { type: Number, default: 0 },
    circleLogo: { type: String },
    hospitalType: { type: Number, default: 1 }, // 1 hospital | 2 clinic
    oldVersionFeature: [FeatureSchema],
    homeBannerAction: {type: [Schema.Types.Mixed], default: []},
    id: { type: String },
    contact: { type: String },
    sharePaymentConfigUrl: { type: String },
    isCashBack: { type: Boolean, default: false },
    isContractSigned: { type: Boolean, default: false },
    addressCensored: { type: String }
}, {
    collection: HOSPITAL_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);

export { HospitalSchema };
