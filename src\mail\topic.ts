
// tslint:disable-next-line: no-namespace
export namespace TopicMailer {
    export const enum User {
        USER_REGISTER_SUCCESS = 'user_register_success',
        USER_CREATE_PATIENT_SUCCESS = 'user_create_patient_success',
        USER_UPDATE_BOOKING_SUCCESS = 'user_update_booking_success',
        USER_BOOKING_RESERVE_SUCCESS = 'user_booking_reserve_success',
        USER_PAYMENT_TRANSACTION_FAILED = 'user_payment_transaction_failed',
        USER_BOOKING_CANCEL_SUCCESS = 'user_booking_cancel_success',
        USER_RE_EXAM = 'user_re_exam',
    }

    export const enum Patient {
        PATIENT_CREATE_SUCCESS = 'patient_create_success',
        PATIENT_UPDATE_SUCCESS = 'patient_update_success',
        PATIENT_BOOKING_RESERVE_SUCCESS = 'patient_reserve_success',
        PATIENT_BOOKING_CANCEL_SUCCESS = 'patient_cancel_success',
    }
}
