import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
// import { MailerService } from 'src/mailer/mailer.service';
// import { InjectModel } from '@nestjs/mongoose';
// import { EVENT_COLLECTION_NAME } from 'src/event/schemas/constants';
// import { Model } from 'mongoose';
// import { IEvent } from 'src/event/intefaces/event.inteface';
// import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
// import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { EventService } from 'src/event/event.service';
import { UrlConfigService } from 'src/config/config.url.service';
import { BookingGatewayService } from 'src/booking-gateway/booking-gateway.service';
import moment = require('moment');
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { ConfigRepoService } from 'src/config/config.repo.service';
// import { CronJob } from 'cron';
// import { THU_DUC_HOSPITAL_CONNECTION } from 'src/config/thuDucHospitalConnection';
@Injectable()
export class TaskService {
    private readonly logger = new Logger(TaskService.name);
    private readonly REPO_NAME: string;
    private env: string;
    private runOneEnv: string;
    private runUMCSyncBooking: boolean;
    private syncDaLieuLogin: boolean;
    private syncNhiDong1Login: boolean;
    private cskhSendMail: boolean;
    private syncDHYDLogin: boolean;
    private allowTelemedReminder: boolean;
    private readonly REPO_RUN_UMC_JOB: string = 'REPO_RUN_UMC_JOB';
    constructor(
        private readonly eventService: EventService,
        private readonly bookingGateWayService: BookingGatewayService,
        private readonly urlConfigService: UrlConfigService,
        private readonly globalSetting: GlobalSettingService,
        private readonly configRepoService: ConfigRepoService,
    ) {
        this.env = this.urlConfigService.getEnv();
        this.runOneEnv = this.urlConfigService.getRunOneEnv();
        this.runUMCSyncBooking = this.urlConfigService.getRunUMCSyncBooking();
        this.syncDaLieuLogin = this.urlConfigService.getSyncDaLieuLogin();
        this.syncDHYDLogin = this.urlConfigService.getSyncDHYDLogin();
        this.syncNhiDong1Login = this.urlConfigService.getSyncNhiDong1Login();
        this.cskhSendMail = this.urlConfigService.getCSKHSendMail();
        this.allowTelemedReminder = this.urlConfigService.getAllowTelemedReminder();
        this.REPO_NAME = this.configRepoService.getRepoName();
    }

    /* START: DHYD */
    @Cron('*/2 * * * * *')
    async handleDHYDPatientPushSyncProcess() {
        if (this.env !== 'DEVELOPMENT' && this.syncDHYDLogin) {
            return this.eventService.handleDHYDPatientPushSyncProcess(); /* sync hồ sơ khi login DHYD - Bệnh viện đại học y dược */
        }
    }

    @Cron('*/2 * * * * *')
    async handleDHYDPatientSyncProcess() {
        if (this.env !== 'DEVELOPMENT' && this.syncDHYDLogin) {
            return this.eventService.handleDHYDPatientSyncProcess(); /* sync hồ sơ khi login DHYD - Bệnh viện đại học y dược */
        }
    }

    @Cron('*/2 * * * * *')
    async handleDHYDBookingPushSyncProcess() {
        if (this.env !== 'DEVELOPMENT' && this.syncDHYDLogin) {
            return this.eventService.handleDHYDBookingPushSyncProcess(); /* Tự đông sync booking v1 => v2 */
        }
    }

    @Cron('*/2 * * * * *')
    async handleDHYDBookingSyncProcess() {
        const repoName = await this.globalSetting.findByKeyAndRepoName(this.REPO_RUN_UMC_JOB);
        const validate = repoName === this.REPO_NAME;
        if (this.env !== 'DEVELOPMENT' && this.syncDHYDLogin && validate) {
            return this.eventService.handleDHYDBookingSyncProcess(); /* Tự đông sync booking v1 => v2 */
        }
    }

    /* END: DHYD */

    @Cron('*/3 * * * * *')
    async handleCSKHSendMail() {
        if (this.env !== 'DEVELOPMENT' && this.cskhSendMail) {
            return this.eventService.getEventsForSendMailCSKH(); /* phần gửi mail booking vs thanh toan vien phi*/
        }
    }

    /* Nhi đồng */
    @Cron('*/2 * * * * *')
    async handleNhiDong1BookingPushSyncProcess() {
        if (this.env !== 'DEVELOPMENT' && this.syncNhiDong1Login) {
            return this.eventService.handleNhiDong1BookingPushSyncProcess(); /* Tự đông sync booking v1 => v2 */
        }
    }

    @Cron('*/2 * * * * *')
    async handleNhiDong1BookingSyncProcess() {
        if (this.env !== 'DEVELOPMENT' && this.syncNhiDong1Login) {
            return this.eventService.handleNhiDong1BookingSyncProcess(); /* Tự đông sync booking v1 => v2 */
        }
    }

    @Cron('*/2 * * * * *')
    async handleNhiDong1PatientPushSyncProcess() {
        if (this.env !== 'DEVELOPMENT' && this.syncNhiDong1Login) {
            return this.eventService.handleNhiDong1PatientPushSyncProcess(); /* sync hồ sơ khi login nhi dong 1 */
        }
    }

    @Cron('*/2 * * * * *')
    async handleNhiDong1PatientSyncProcess() {
        if (this.env !== 'DEVELOPMENT' && this.syncNhiDong1Login) {
            return this.eventService.handleNhiDong1PatientSyncProcess(); /* sync hồ sơ khi login nhi dong 1 */
        }
    }

    /* Dành cho Bv Nhi Đồng 1 chạy đồng bộ trước ngày khám */
    @Cron('*/2 * * * * *')
    async handleND1BeforeBookingPushSyncProcess() {
        const repoName = await this.globalSetting.findByKeyAndRepoName(this.REPO_RUN_UMC_JOB);
        const validate = repoName === this.REPO_NAME;
        if (this.env !== 'DEVELOPMENT' && this.runUMCSyncBooking && validate) {
            return this.eventService.handleND1BeforeBookingPushSyncProcess(); /* Tự đông sync booking v1 => v2 */
        }
    }

    @Cron('*/2 * * * * *')
    async handleNd1BeforeBookingSyncProcess() {
        const repoName = await this.globalSetting.findByKeyAndRepoName(this.REPO_RUN_UMC_JOB);
        const validate = repoName === this.REPO_NAME;
        if (this.env !== 'DEVELOPMENT' && this.runUMCSyncBooking && validate) {
            return this.eventService.handleNd1BeforeBookingSyncProcess(); /* Tự đông sync booking v1 => v2 */
        }
    }

    @Cron('*/2 * * * * *')
    async handleDaLieuPatientPushSyncProcess() {
        if (this.env !== 'DEVELOPMENT' && this.syncDaLieuLogin) {
            return this.eventService.handleDaLieuPatientPushSyncProcess(); /* sync hồ sơ khi login da liễu */
        }
    }

    @Cron('*/2 * * * * *')
    async handleDaLieuPatientSyncProcess() {
        if (this.env !== 'DEVELOPMENT' && this.syncDaLieuLogin) {
            return this.eventService.handleDaLieuPatientSyncProcess(); /* Tự đông sync patient v1 => v2 */
        }
    }

    @Cron('*/2 * * * * *')
    async handleDaLieuBookingPushSyncProcess() {
        if (this.env !== 'DEVELOPMENT' && this.syncDaLieuLogin) {
            return this.eventService.handleDaLieuBookingPushSyncProcess(); /* Tự đông sync booking v1 => v2 */
        }
    }

    @Cron('*/2 * * * * *')
    async handleDaLieuBookingSyncProcess() {
        if (this.env !== 'DEVELOPMENT' && this.syncDaLieuLogin) {
            return this.eventService.handleDaLieuBookingSyncProcess(); /* Tự đông sync booking v1 => v2 */
        }
    }

    // CronExpression.EVERY_5_SECONDS
    @Cron('*/15 * * * * *')
    /* dành cho booking, thanh toán viện phí, hủy phiếu */
    async handlePushNotif() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.getEventsForPushNotif();
        }
    }

    @Cron(CronExpression.EVERY_DAY_AT_1PM)
    // @Cron('*/5 * * * * *')
    /* lúc 8h tối. push notif nhắc nhở bệnh nhân đi khám */
    async handlePushRemindBooking() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.getEventsForPushNotifRemindBooking();
        }
    }

    // CronExpression.EVERY_5_SECONDS
    @Cron('*/15 * * * * *')
    /* dành cho booking, thanh toán viện phí, hủy phiếu */
    async handlePushNotifChangeSTT() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.getEventsForPushNotifChangeSTT();
        }
    }

    @Cron('*/15 * * * * *')
    /* dành cho UNC Push notif */
    async handlePushNotifUMC_Schedule() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.handlePushNotifUMC_Schedule();
        }
    }

    @Cron('*/15 * * * * *')
    /* dành cho UNC Push notif */
    async handlePushNotifNewHospital_Schedule() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.handlePushNotifNewHospital_Schedule();
        }
    }

    @Cron('*/15 * * * * *')
    async handleSendMailBooking() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.getEventsForSendMail(); /* phần gửi mail booking vs thanh toan vien phi*/
        }
    }

    @Cron('*/30 * * * * *')
    async handleSendMailInvoice() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.getEventsForSendMailInvoice(); /* phần gửi mail thông tin hóa đơn*/
        }
    }

    @Cron('*/30 * * * * *')
    async handleSendMailReExam() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.getEventsForSendMailReExam(); /* phần gửi mail tái khám */
        }
    }

    @Cron('*/30 * * * * *')
    async handleSendMailFeeders() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.getEventsForSendMailFeeders(); /* phần gửi mail booking vs thanh toan vien phi*/
        }
    }

    /* Dành cho bệnh viện Da Liễu  - Sync 1 lần User */
    // @Cron('*/1 * * * * *', {
    //     name: 'sync_user_da_lieu_upgrade',
    // })
    // async handleSyncUserDaLieuUpgrade() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
    //         return this.eventService.handleSyncUserDaLieuUpgrade(); /* chạy lấy dữ liệu */
    //     }
    // }

    // @Cron('*/1 * * * * *', {
    //     name: 'sync_user_da_lieu_push_sync_process',
    // })
    // async handleUserDaLieuPushSyncProcess() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
    //         return this.eventService.handleUserDaLieuPushSyncProcess(); /* sync user của trung vuong */
    //     }
    // }

    // @Cron('*/4 * * * * *', {
    //     name: 'sync_user_da_lieu_sync_process',
    // })
    // async handleUserDaLieuSyncProcess() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
    //         return this.eventService.handleUserDaLieuSyncProcess(); /* sync user của trung vuong */
    //     }
    // }

    // /* Dành cho bệnh viện Trưng Vương */
    // @Cron('*/2 * * * * *')
    // async handleTrungVuongPushSyncProcess() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
    //         return this.eventService.handleTrungVuongPushSyncProcess(); /* sync user của trung vuong */
    //     }
    // }

    // @Cron('*/2 * * * * *')
    // async handleTrungVuongSyncProcess() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
    //         return this.eventService.handleTrungVuongSyncProcess(); /* sync user của trung vuong */
    //     }
    // }

    /* Dành cho Bv Da Liễu */
    @Cron('*/4 * * * * *')
    async handleSkinBookingPushSyncProcess() {
        const repoName = await this.globalSetting.findByKeyAndRepoName(this.REPO_RUN_UMC_JOB);
        const validate = repoName === this.REPO_NAME;
        if (this.env !== 'DEVELOPMENT' && this.runUMCSyncBooking && validate) {
            return this.eventService.handleDaLieuPushSyncProcess(); /* Tự đông sync booking v1 => v2 */
        }
    }

    @Cron('*/4 * * * * *')
    async handleSkinBookingSyncProcess() {
        const repoName = await this.globalSetting.findByKeyAndRepoName(this.REPO_RUN_UMC_JOB);
        const validate = repoName === this.REPO_NAME;
        if (this.env !== 'DEVELOPMENT' && this.runUMCSyncBooking && validate) {
            return this.eventService.handleDaLieuSyncProcess(); /* Tự đông sync booking v1 => v2 */
        }
    }

    // @Cron('*/8 * * * * *')
    // async handleSkinPatientPushSyncProcess() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
    //         return this.eventService.handleSkinPatientPushSyncProcess(); /* Tự đông sync patient v1 => v2 */
    //     }
    // }

    // @Cron('*/8 * * * * *')
    // async handleSkinPatientSyncProcess() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
    //         return this.eventService.handleSkinPatientSyncProcess(); /* Tự đông sync patient v1 => v2 */
    //     }
    // }

    // /* END BV Da Liễu */

    /* Phần đồng bộ User */
    @Cron('*/4 * * * * *')
    async handleUserPushSyncProcess() {
        const repoName = await this.globalSetting.findByKeyAndRepoName(this.REPO_RUN_UMC_JOB);
        const validate = repoName === this.REPO_NAME;
        if (this.env !== 'DEVELOPMENT' && this.runUMCSyncBooking && validate) {
            return this.eventService.handleUserPushSyncProcess(); /* Tự đông sync booking v1 => v2 */
        }
    }

    @Cron('*/4 * * * * *')
    async handleUserSyncProcess() {
        const repoName = await this.globalSetting.findByKeyAndRepoName(this.REPO_RUN_UMC_JOB);
        const validate = repoName === this.REPO_NAME;
        if (this.env !== 'DEVELOPMENT' && this.runUMCSyncBooking && validate) {
            return this.eventService.handleUserSyncProcess(); /* Tự đông sync booking v1 => v2 */
        }
    }

    /* Phần đồng bộ User */

    @Cron('*/2 * * * * *')
    async handlePushSyncProcess() {
        const repoName = await this.globalSetting.findByKeyAndRepoName(this.REPO_RUN_UMC_JOB);
        const validate = repoName === this.REPO_NAME;
        if (this.env !== 'DEVELOPMENT' && this.runUMCSyncBooking && validate) {
            return this.eventService.handlePushSyncProcess(); /* Trigger Đồng bộ Booking UMC  */
        }
    }

    @Cron('*/2 * * * * *')
    async handleSyncProcess() {
        const repoName = await this.globalSetting.findByKeyAndRepoName(this.REPO_RUN_UMC_JOB);
        const validate = repoName === this.REPO_NAME;
        if (this.env !== 'DEVELOPMENT' && this.runUMCSyncBooking && validate) {
            return this.eventService.handleSyncProcess(); /* Trigger Đồng bộ Booking UMC  */
        }
    }

    @Cron('*/4 * * * * *')
    async handlePatientPushSyncProcess() {
        const repoName = await this.globalSetting.findByKeyAndRepoName(this.REPO_RUN_UMC_JOB);
        const validate = repoName === this.REPO_NAME;
        if (this.env !== 'DEVELOPMENT' && this.runUMCSyncBooking && validate) {
            return this.eventService.handlePatientPushSyncProcess(); /* Trigger Đồng bộ Patient UMC  */
        }
    }

    @Cron('*/4 * * * * *')
    async handlePatientSyncProcess() {
        const repoName = await this.globalSetting.findByKeyAndRepoName(this.REPO_RUN_UMC_JOB);
        const validate = repoName === this.REPO_NAME;
        if (this.env !== 'DEVELOPMENT' && this.runUMCSyncBooking && validate) {
            return this.eventService.handlePatientSyncProcess(); /* Trigger Đồng bộ Patient UMC  */
        }
    }

    // @Cron('*/3 * * * * *')
    // async handleBookingDatePushSyncProcess() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
    //         return this.eventService.handleBookingDatePushSyncProcess(); /* Tự đông sync patient v1 => v2 */
    //     }
    // }

    // @Cron('*/3 * * * * *')
    // async handleBookingDateSyncProcess() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
    //         return this.eventService.handleBookingDateSyncProcess(); /* Tự đông sync patient v1 => v2 */
    //     }
    // }

    // @Cron('*/4 * * * * *')
    // async  handleAutoSendNotifBookingUMC() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
    //         return this.eventService.autoNotifBooking();
    //     }
    // }

    @Cron('*/1 * * * * *')
    async handleSyncPushDevicesUMC() {
        // console.log('syncPushDevicesUMC');
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.syncPushDevicesUMC();
        }
    }

    @Cron('*/30 * * * * *')
    async handleGetEventForPushNotifUMC() {
        // console.log('getEventForPushNotifUMC');
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.getEventForPushNotifUMC();
        }
    }

    @Cron('*/30 * * * * *')
    async handleGetEventForPushNotifNewHospital() {
        // console.log('getEventForPushNotifUMC');
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.getEventForPushNotifNewHospital();
        }
    }

    @Cron(CronExpression.EVERY_5_MINUTES)
    async handleSummaryEventForPushNotifUMC() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.summaryEventForPushNotifUMC();
        }
    }

    @Cron(CronExpression.EVERY_DAY_AT_11PM)
    async handleProcessQuaNgayKham() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.handleProcessQuaNgayKham();
        }
    }

    /* Phần dành cho Feeder */
    @Cron('*/3 * * * * *')
    /* EventChannel.PUSH_NOTIF_APP  */
    async handlePushNotifFeeders() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.eventService.getEventsForPushNotifFeeders();
        }
    }

    // @Cron('* 30 * * * *')
    // async  handleSendMailToSupporter() {
    //     // this.logger.debug('Called when the second is 10');
    //     const systemExceptionTable = 'system_exception';
    //     const { countValue } = await this.hospitalKnex(systemExceptionTable)
    //         .count('id', { as: 'countValue' })
    //         .whereRaw(`date_create BETWEEN timestamp(DATE_SUB(NOW(), INTERVAL 30 MINUTE)) AND timestamp(NOW())`).first();
    //     // this.logger.debug(`-----${countValue}-----`);
    //     if (countValue > 0) {
    //         /* Xử lý lấy dữ liệu từ trong bảng system_exception */
    //         this.mailerService.sendMailToSupporter(countValue);
    //     }
    // }

    @Cron(CronExpression.EVERY_HOUR)
    async rollBackSTT() {
        // Test for BV Le Loi BRVT -> Hard Code
        const partnerId = 'leloi';
        const serviceId = 'leloi_VRSARS';
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
            return this.bookingGateWayService.remindExpiredBookings(partnerId, serviceId);
        }
    }

    // @Cron(CronExpression.EVERY_5_MINUTES)
    // async cancelExpiredBookings() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
    //         return this.bookingGateWayService.cancelExpiredBookings();
    //     }
    // }

    @Cron('*/10 * * * * *')
    async handlePushNotifForTelemedReminder() {
        if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON' && this.allowTelemedReminder) {
            return this.eventService.getEventsForPushNotifyTelemed();
        }
    }

    // @Cron('*/2 * * * * *')
    // async handleMessageEvent() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON' ) {
    //         return this.eventService.handleMessageEvent();
    //     }
    // }

    // @Cron('*/2 * * * * *')
    // async handleMessageProccessEvent() {
    //     if (this.env !== 'DEVELOPMENT' && this.runOneEnv === 'ON') {
    //         return this.eventService.handleMessageEventProcess();
    //     }
    // }

    @Cron('*/10 * * * * *')
    async retryUpdateBookingV1Fail(): Promise<void> {
        const retryConfig = await this.globalSetting.findByKeyAndRepoName('RETRY_UPDATE_BOOKING_STATUS');
        if (this.REPO_NAME === retryConfig) {
            return this.bookingGateWayService.retryUpdateBookingFailV1();
        }
    }

    // @Cron(CronExpression.EVERY_DAY_AT_11PM)
    // async pushBookingNotif(): Promise<void> {
    //     if (this.REPO_NAME === 'api-v2-beta') {
    //         return this.eventService.pushBookingNotif({ vdate: '', bookingCode: '' })
    //     }
    // }

}
