import { Injectable, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { IProfession } from './interfaces/profession.interface';
import { PROFESSION_COLLECTION_NAME } from './schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';

@Injectable()
export class ProfessionMongoService {
    constructor(
        @InjectModel(PROFESSION_COLLECTION_NAME) private professionModel: Model<IProfession>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitaltModel: Model<IHospital>,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
    ) { }

    async find(partnerId: string): Promise<any> {
        const profession = await this.professionModel
            .find({ partnerId: 'medpro' }, { name: 1, id: 1 })
            .exec();
        return profession;
    }

    async seed(partnerId: string): Promise<any> {
        const hospital = await this.hospitaltModel.findOne({ partnerId }).exec();
        const profession = await this.pkhPatientKnex('profession');
        for await (const pro of profession) {
            const professionM = new this.professionModel({
                id: `${partnerId}_${pro.id}`,
                code: pro.id,
                name: pro.name,
                partnerId,
                hospitalId: hospital._id,
            });
            await professionM.save();
        }
        return true;
    }
}
