import { Module } from '@nestjs/common';
import { PushDeviceController } from './push-device.controller';
import { PushDeviceService } from './push-device.service';
import { MongooseModule } from '@nestjs/mongoose';
import { PUSH_DEVICE_COLLECTION_NAME } from './schemas/constants';
import { PushDeviceSchema } from './schemas/push-device.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { SentryToken } from 'src/sentry/sentry.module';

@Module({
  imports: [
    SentryToken,
    MongooseModule.forFeature([
      { name: PUSH_DEVICE_COLLECTION_NAME, schema: PushDeviceSchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
      { name: USER_COLLECTION_NAME, schema: UserSchema },
    ]),
  ],
  controllers: [PushDeviceController],
  providers: [PushDeviceService],
})
export class PushDeviceModule { }
