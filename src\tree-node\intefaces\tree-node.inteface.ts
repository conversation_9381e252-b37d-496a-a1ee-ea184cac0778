import { Document } from 'mongoose';

export interface ITreeNode extends Document {
    parentId: string;
    type: string;
    cta: {
        text: string;
        link: string;
        screenLink: string;
    };
    bgImg: string;
    order: string;
    children: [
        {
            id: string;
            parentId: string;
            category: string;
            cardType: string;
            cardTitle: string;
            cardDescription: string;
            cardSubject: string;
            cardHospital: string;
            doctorTitle: string;
            doctorName: string;
            bgImg: string;
            avtImg: string;
            order: string;
        },
    ];
    status: boolean;
}
