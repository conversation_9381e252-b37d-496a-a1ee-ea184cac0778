import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { get } from 'lodash';
import { Observable } from 'rxjs';

@Injectable()
export class CanthoZoneInterceptor implements NestInterceptor {
    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const appId = get(request.headers, 'appid');
    request.headers.appid = appId === 'canthozone' ? 'medpro' : appId;
    return next.handle();
  }
}