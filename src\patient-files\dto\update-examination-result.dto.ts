import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDateString, IsNotEmpty, IsOptional } from 'class-validator';


export class updateExaminationResultDTO {
    @ApiProperty()
    @IsOptional()
    name: string;

    @ApiProperty()
    @IsDateString(
        { strict: true },
        {
            message: 'Thông tin ngày khám. ISOString',
        },
    )
    @IsNotEmpty()
    dateExamination: string;

    @IsOptional()
    @Transform(value => `${value}`.trim())
    readonly description: string;

    @IsNotEmpty()
    id: string;
    
    @IsOptional()
    patientId: string;

    @IsOptional()
    files:[string]

    @ApiProperty()
    @IsOptional()
    hospitalName: string;

    @IsOptional()
    source: string;
}