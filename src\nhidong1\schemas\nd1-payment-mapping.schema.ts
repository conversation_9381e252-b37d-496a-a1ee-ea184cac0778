import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PAYMENT_METHOD_MAPPINGS_COLLECTION } from './constants';

const Schema = mongoose.Schema;

const PaymentMethodMappingSchema = new Schema({
    v1: Number,
    v2: String,
}, {
    collection: PAYMENT_METHOD_MAPPINGS_COLLECTION,
    timestamps: true,
}).plugin(jsonMongo);

export { PaymentMethodMappingSchema };
