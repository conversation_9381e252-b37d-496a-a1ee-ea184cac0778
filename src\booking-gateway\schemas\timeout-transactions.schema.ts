import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { TIMEOUT_TRANSACTION_LOGS_COLLECTION_NAME, BOOKING_COLLECTION_NAME } from './constants';
import { TimeoutTransactionEnum } from 'src/common/enums/timeout-transaction.enum';

const Schema = mongoose.Schema;

export const TimeoutTransactionLogsSchema = new Schema({
    transactionId: { type: String, unique: true },
    booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
    action: { type: String, default: TimeoutTransactionEnum.PENDING },
}, {
    collection: TIMEOUT_TRANSACTION_LOGS_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
