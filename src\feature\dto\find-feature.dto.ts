import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';

export class FindFeatureDto {

    @ApiProperty({
        description: 'type',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    readonly type: string;

}
