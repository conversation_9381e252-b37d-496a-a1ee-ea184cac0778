import { Injectable, Inject, HttpService, HttpException, HttpStatus } from '@nestjs/common';
import * as moment from 'moment';
import * as queryString from 'query-string';
import { THU_DUC_HOSPITAL_CONNECTION } from '../config/thuDucHospitalConnection';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { PatientDTO } from './dto/patient.dto';
import { CHO_RAY_HOSPITAL_CONNECTION } from 'src/config/choRayHospitalConnection';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { UMCPartnerConfigService } from 'src/config/config.umc.partner.service';
import { SearchPatientDTO } from './dto/search-patient.dto';
import { first } from 'lodash';
import { UMCPatientResponseDTO } from './dto/umc-patient-response.dto';
import { VerifyPhonePatientDTO } from './dto/verify-phone-patient.dto';
import { AddPatientToUserDTO } from './dto/add-patient-to-user.dto';
import * as jwt from 'jsonwebtoken';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { JwtModuleOptions } from '@nestjs/jwt';
import { SearchPatientExtraInfoDTO } from './dto/search-patient-extra-info.dto';
import { UMCPatientExtraInfoResponseDTO } from './dto/umc-patient-extra-info-response.dto';
import { PatientFormDataDTO } from './dto/patient-form-data.dto';
import { UpdatePatientFormDataDTO } from './dto/update-patient-form-data.dto';
import { UtilService } from 'src/config/util.service';
import { VerifyPhoneWithoutMSBNPatientDTO } from './dto/verify-phone-patient-without-msbn.dto';
import { UpdatePatientFormDataWithoutMsbnDTO } from './dto/update-patient-form-data-without-msbn.dto';

@Injectable()
export class PatientService {

    private tableName = 'patient';
    private countPatient = 10;
    constructor(
        @Inject(THU_DUC_HOSPITAL_CONNECTION) private readonly thuDucHospitalKnex,
        @Inject(CHO_RAY_HOSPITAL_CONNECTION) private readonly choRayHospitalKnex,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        private readonly httpService: HttpService,
        private readonly uMCPartnerConfigService: UMCPartnerConfigService,
        private readonly jwtUserConfigService: JwtUserConfigService,
        private readonly utilService: UtilService,
    ) { }

    async getAllPatients(): Promise<any> {
        const dmNationTable = 'dm_nation';
        const dmCountryTable = 'dm_country';
        const patientTable = 'patient';
        return this.thuDucHospitalKnex
            .select(
                `${patientTable}.id as idPatient`,
                `${patientTable}.surname as surname`,
                `${patientTable}.name as name`,
                `${patientTable}.bv_id as bv_id`,
                `${patientTable}.medpro_id as medpro_id`,
                `${patientTable}.sex as sex`,
                `${patientTable}.birthdate as birthdate`,
                `${patientTable}.birthyear as birthyear`,
                `${patientTable}.address as address`,
                `${dmNationTable}.name as dantoc`,
                `${dmCountryTable}.name as quocgia`,
            )
            .from('patient')
            .innerJoin(dmNationTable, `${dmNationTable}.id`, `${patientTable}.dantoc_id`)
            .innerJoin(dmCountryTable, `${dmCountryTable}.code`, `${patientTable}.country_code`)
            ;

    }

    async getAllUMCPatientsByUserId(userId: number): Promise<any> {
        const dmNationTable = 'dm_dantoc';
        const dmCity = 'dm_city';
        const dmDistrict = 'dm_district';
        const dmWard = 'dm_ward';
        const dmCountry = 'dm_country';
        const patientTable = 'patient';
        const userPatientTable = 'user_patient';
        return this.pkhPatientKnex(patientTable)
            .select(
                `${patientTable}.id as id`,
                `bvdhyd_msbn as patientCode`,
                `medpro_id as medProId`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.surname,' ', ${patientTable}.name) as fullname`),
                `cmnd`,
                `sex as gender`,
                `birthdate as birthDate`,
                `birthyear as  birthYear`,
                `${dmNationTable}.name as nationName`,
                `${dmCountry}.name as countryName`,
                `mobile`,
                `email`,
                `${userPatientTable}.is_default as isDefault`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.address,', ', ${dmWard}.name,', ', ${dmDistrict}.name,', ', ${dmCity}.name) as address`),
            )
            .innerJoin(userPatientTable, `${userPatientTable}.patient_id`, `${patientTable}.id`)
            .innerJoin(dmCountry, `${dmCountry}.code`, `${patientTable}.country_code`)
            .innerJoin(dmNationTable, `${dmNationTable}.id`, `${patientTable}.dantoc_id`)
            .innerJoin(dmCity, `${dmCity}.id`, `${patientTable}.city_id`)
            .innerJoin(dmDistrict, `${dmDistrict}.id`, `${patientTable}.district_id`)
            .innerJoin(dmWard, `${dmWard}.id`, `${patientTable}.ward_id`)
            .where(`${userPatientTable}.user_id`, userId);
    }

    async getCountPatientInUserPatientUMC(userId: number): Promise<any> {
        const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION, 'user_patient');
        return getConnectionQuery
            .count('id', { as: 'countValue' })
            .where('user_id', userId)
            .first();
    }

    async getAllChoRayPatientsByUserId(userId: number): Promise<any> {
        const dmNationTable = 'dm_nation';
        const dmCity = 'dm_city';
        const dmDistrict = 'dm_district';
        const dmWard = 'dm_ward';
        const dmCountry = 'dm_country';
        const patientTable = 'patient';
        const userPatientTable = 'user_patient';
        return this.choRayHospitalKnex(patientTable)
            .select(
                `${patientTable}.id as id`,
                `bv_id as patientCode`,
                `medpro_id as medProId`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.surname,' ', ${patientTable}.name) as fullname`),
                `cmnd`,
                `sex as gender`,
                `birthdate as birthDate`,
                `birthyear as  birthYear`,
                `${dmNationTable}.name as nationName`,
                `${dmCountry}.name as countryName`,
                `mobile`,
                `email`,
                `${userPatientTable}.is_default as isDefault`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.address,', ', ${dmWard}.name,', ', ${dmDistrict}.name,', ', ${dmCity}.name) as address`),
            )
            .innerJoin(userPatientTable, `${userPatientTable}.patient_id`, `${patientTable}.id`)
            .innerJoin(dmCountry, `${dmCountry}.code`, `${patientTable}.country_code`)
            .innerJoin(dmNationTable, `${dmNationTable}.id`, `${patientTable}.dantoc_id`)
            .innerJoin(dmCity, `${dmCity}.id`, `${patientTable}.city_id`)
            .innerJoin(dmDistrict, `${dmDistrict}.id`, `${patientTable}.district_id`)
            .innerJoin(dmWard, `${dmWard}.id`, `${patientTable}.ward_id`)
            .where(`${userPatientTable}.user_id`, userId);
    }

    async getAllThuDucPatientsByUserId(userId: number): Promise<any> {
        const dmNationTable = 'dm_nation';
        const dmCity = 'dm_city';
        const dmDistrict = 'dm_district';
        const dmWard = 'dm_ward';
        const dmCountry = 'dm_country';
        const patientTable = 'patient';
        const userPatientTable = 'user_patient';
        return this.thuDucHospitalKnex(patientTable)
            .select(
                `${patientTable}.id as id`,
                `bv_id as patientCode`,
                `medpro_id as medProId`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.surname,' ', ${patientTable}.name) as fullname`),
                `cmnd`,
                `sex as gender`,
                `birthdate as birthDate`,
                `birthyear as  birthYear`,
                `${dmNationTable}.name as nationName`,
                `${dmCountry}.name as countryName`,
                `mobile`,
                `email`,
                `${userPatientTable}.is_default as isDefault`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.address,', ', ${dmWard}.name,', ', ${dmDistrict}.name,', ', ${dmCity}.name) as address`),
            )
            .innerJoin(userPatientTable, `${userPatientTable}.patient_id`, `${patientTable}.id`)
            .innerJoin(dmCountry, `${dmCountry}.code`, `${patientTable}.country_code`)
            .innerJoin(dmNationTable, `${dmNationTable}.id`, `${patientTable}.dantoc_id`)
            .innerJoin(dmCity, `${dmCity}.id`, `${patientTable}.city_id`)
            .innerJoin(dmDistrict, `${dmDistrict}.id`, `${patientTable}.district_id`)
            .innerJoin(dmWard, `${dmWard}.id`, `${patientTable}.ward_id`)
            .where(`${userPatientTable}.user_id`, userId);
    }

    async insertPatient(data): Promise<any> {
        return this.thuDucHospitalKnex('patient').insert(data);
    }

    async insertPatientInfo(hospital = PKH_PATIENT_CONNECTION, data): Promise<any> {
        const getConnectionQuery = this.getConnection(hospital, 'patient');
        return getConnectionQuery.insert(data);
    }

    async updatePatientInfo(hospital = PKH_PATIENT_CONNECTION, data, id: number): Promise<any> {
        const getConnectionQuery = this.getConnection(hospital, 'patient');
        return getConnectionQuery.update(data).where({ id });
    }

    async insertRelativeInfo(hospital = PKH_PATIENT_CONNECTION, data): Promise<any> {
        const getConnectionQuery = this.getConnection(hospital, 'relative');
        return getConnectionQuery.insert(data);
    }

    async updateRelativeInfo(hospital = PKH_PATIENT_CONNECTION, data, id: number): Promise<any> {
        const getConnectionQuery = this.getConnection(hospital, 'relative');
        return getConnectionQuery.update(data).where({ patient_id: id });
    }

    async getRelativeInfo(hospital = PKH_PATIENT_CONNECTION, id: number): Promise<any> {
        const getConnectionQuery = this.getConnection(hospital, 'relative');
        return getConnectionQuery
            .where({ patient_id: id })
            .first();
    }

    async getUMCPatientById(id: number): Promise<PatientDTO> {
        const dmNationTable = 'dm_dantoc';
        const dmCity = 'dm_city';
        const dmDistrict = 'dm_district';
        const dmWard = 'dm_ward';
        const patientTable = 'patient';
        const info = await this.pkhPatientKnex(patientTable)
            .select(
                `${patientTable}.id as id`,
                `bvdhyd_msbn as patientCode`,
                `medpro_id as medProId`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.surname,' ', ${patientTable}.name) as fullname`),
                `cmnd`,
                `sex as gender`,
                `birthdate as birthDate`,
                `birthyear as  birthYear`,
                `${dmNationTable}.name as nationName`,
                `${dmCity}.name as cityName`,
                `mobile`,
                `email`,
                `address as addressPatient`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.address,', ', ${dmWard}.name,', ', ${dmDistrict}.name,', ', ${dmCity}.name) as address`),
            )
            .leftJoin(dmNationTable, `${dmNationTable}.id`, `${patientTable}.dantoc_id`)
            .leftJoin(dmCity, `${dmCity}.id`, `${patientTable}.city_id`)
            .leftJoin(dmDistrict, `${dmDistrict}.id`, `${patientTable}.district_id`)
            .leftJoin(dmWard, `${dmWard}.id`, `${patientTable}.ward_id`)
            .where(`${patientTable}.id`, id)
            .first();
        if (info) {
            return {
                ...info,
                mobile: this.secretMobile(info.mobile),
            };
        }
        return null;

    }

    async getUMC_MSBN_PatientByIdForUpdate(id: number): Promise<any> {
        const patientTable = 'patient';
        return this.pkhPatientKnex(patientTable)
            .select(
                `bvdhyd_msbn as msbn`,
            )
            .where(`${patientTable}.id`, id)
            .first();
    }

    async getUMCPatientByIdForUpdate(id: number): Promise<PatientDTO> {
        const dmRelative = 'relative';
        const dmCity = 'dm_city';
        const dmDistrict = 'dm_district';
        const dmWard = 'dm_ward';
        const dmCountry = 'dm_country';
        const patientTable = 'patient';
        const info = await this.pkhPatientKnex(patientTable)
            .select(
                /* Thông tin thân nhân */
                `${dmRelative}.name as relative_name`,
                `${dmRelative}.email as relative_email`,
                `${dmRelative}.mobile as relative_mobile`,
                `${dmRelative}.relative_type_id as relative_type_id`,
                /* Thông tin bệnh nhân */
                `${patientTable}.id as id`,
                `bvdhyd_msbn as msbn`,
                `medpro_id as medpro_id`,
                `${patientTable}.surname`,
                `${patientTable}.name`,
                `cmnd`,
                `sex`,
                `birthdate`,
                `birthyear`,
                `${patientTable}.mobile`,
                `${patientTable}.email`,
                `${patientTable}.dantoc_id`,
                `${patientTable}.profession_id`,
                `${patientTable}.country_code`,
                `${dmCountry}.id as country_id`,
                `${patientTable}.city_id`,
                `${patientTable}.district_id`,
                `${patientTable}.ward_id`,
                `address as addressPatient`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.address,', ', ${dmWard}.name,', ', ${dmDistrict}.name,', ', ${dmCity}.name) as address`),
            )
            .innerJoin(dmRelative, `${dmRelative}.patient_id`, `${patientTable}.id`)
            .innerJoin(dmCountry, `${dmCountry}.code`, `${patientTable}.country_code`)
            .leftJoin(dmCity, `${dmCity}.id`, `${patientTable}.city_id`)
            .leftJoin(dmDistrict, `${dmDistrict}.id`, `${patientTable}.district_id`)
            .leftJoin(dmWard, `${dmWard}.id`, `${patientTable}.ward_id`)
            .where(`${patientTable}.id`, id)
            .first();
        if (info) {
            return {
                ...info,
                mobile: this.secretMobile(info.mobile),
            };
        }
        return null;

    }

    async getUMCPatientByIdVerify(id: number): Promise<PatientDTO> {
        const patientTable = 'patient';
        const info = await this.pkhPatientKnex(patientTable)
            .select(
                `${patientTable}.id as id`,
                `bvdhyd_msbn as patientCode`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.surname,' ', ${patientTable}.name) as fullname`),
                `mobile`,
            )
            .where('id', id)
            .first();
        if (info) {
            return {
                ...info,
                mobile: this.secretMobile(info.mobile),
            };
        }
        return null;

    }

    async getUMCPatientShortInfoVerifyPhoneByPatientId(id: number): Promise<any> {
        const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION, 'patient');
        return getConnectionQuery
            .select(
                'mobile',
                'bvdhyd_msbn',
            )
            .where('id', id)
            .first();

    }

    async getUMCPatientByMsbn(msbn: string): Promise<PatientDTO> {
        const dmNationTable = 'dm_dantoc';
        const dmCity = 'dm_city';
        const dmDistrict = 'dm_district';
        const dmWard = 'dm_ward';
        const patientTable = 'patient';
        const info = await this.pkhPatientKnex(patientTable)
            .select(
                `${patientTable}.id as id`,
                `bvdhyd_msbn as patientCode`,
                `medpro_id as medProId`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.surname,' ', ${patientTable}.name) as fullname`),
                `cmnd`,
                `sex as gender`,
                `birthdate as birthDate`,
                `birthyear as  birthYear`,
                `${dmNationTable}.name as nationName`,
                `${dmCity}.name as cityName`,
                `mobile`,
                `email`,
                `address as addressPatient`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.address,', ', ${dmWard}.name,', ', ${dmDistrict}.name,', ', ${dmCity}.name) as address`),
            )
            .leftJoin(dmNationTable, `${dmNationTable}.id`, `${patientTable}.dantoc_id`)
            .leftJoin(dmCity, `${dmCity}.id`, `${patientTable}.city_id`)
            .leftJoin(dmDistrict, `${dmDistrict}.id`, `${patientTable}.district_id`)
            .leftJoin(dmWard, `${dmWard}.id`, `${patientTable}.ward_id`)
            .where(`${patientTable}.bvdhyd_msbn`, msbn)
            .first();
        if (info) {
            return {
                ...info,
                mobile: this.secretMobile(info.mobile),
            };
        }
        return null;
    }

    async getUMCPatientByMsbnVerify(msbn: string): Promise<PatientDTO> {
        const patientTable = 'patient';
        const info = await this.pkhPatientKnex(patientTable)
            .select(
                `${patientTable}.id as id`,
                `bvdhyd_msbn as patientCode`,
                this.pkhPatientKnex.raw(`CONCAT(${patientTable}.surname,' ', ${patientTable}.name) as fullname`),
                `mobile`,
            )
            .where(`${patientTable}.bvdhyd_msbn`, msbn)
            .first();
        if (info) {
            return {
                ...info,
                mobile: this.secretMobile(info.mobile),
            };
        }
        return null;
    }

    async checkPatient(patientCode): Promise<any> {
        return this.thuDucHospitalKnex('patient')
            .where('bv_id', patientCode)
            .first();
    }

    async deleteUMCPatient(userId: number, patientId: number): Promise<any> {
        return this.deletePatientInUserPatient(PKH_PATIENT_CONNECTION, userId, patientId);
    }

    async deleteThuDucPatient(userId: number, patientId: number): Promise<any> {
        return this.deletePatientInUserPatient(THU_DUC_HOSPITAL_CONNECTION, userId, patientId);
    }

    async deleteChoRayPatient(userId: number, patientId: number): Promise<any> {
        return this.deletePatientInUserPatient(CHO_RAY_HOSPITAL_CONNECTION, userId, patientId);
    }

    async deletePatientInUserPatient(hospitalConnection = PKH_PATIENT_CONNECTION, userId: number, patientId: number): Promise<any> {
        const getConnectionQuery = this.getConnection(hospitalConnection, 'user_patient');
        return getConnectionQuery.where('user_id', userId).where('patient_id', patientId).del();
    }

    async searchUMCPatientByMsbn(searchPatientDTO: SearchPatientDTO): Promise<any> {
        const params = { sohs: searchPatientDTO.msbn }; // 'A12-0253088'
        const url = this.uMCPartnerConfigService.getPatientBySoHoSo();
        const urlSMSBrandName = queryString.stringifyUrl({
            url,
            query: { ...params },
        });

        // Kiểm tra xem search mấy lần rồi
        /* TOOD LIST
        */
        let resultData = [];
        try {
            const { data } = (await this.getPatientByHIS(urlSMSBrandName).toPromise()).data;
            resultData = data;
        } catch (error) {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.BAD_REQUEST);
        }

        if (resultData.length === 0) {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra xem bệnh nhân này ngưng sử dụng hay ko */
        const patient: UMCPatientResponseDTO = first(resultData);
        const NgungSD = patient.NgungSD;
        if (NgungSD) {
            throw new HttpException('Bệnh nhân này ngưng dùng.', HttpStatus.BAD_REQUEST);
        }
        try {
            /* kiểm tra xem thông tin ngày có chưa. nếu có rồi thì update */
            const checkExists = await this.checkExistsPatientByMsbnMedproId(searchPatientDTO.msbn);
            if (checkExists) {
                await this.updateUMCSyncPatient(searchPatientDTO.msbn, {
                    DiDong: patient.DiDong,
                    DienThoai: patient.DienThoai,
                    SoCMND: patient.SoCMND,
                    NgaySinh: patient.NgaySinh,
                    NamSinh: patient.NamSinh,
                });
                return this.getUMCPatientByMsbnVerify(searchPatientDTO.msbn);
            } else {
                const [patientId] = await this.insertUMCSyncPatient(patient);
                return this.getUMCPatientByIdVerify(patientId);
            }
        } catch (error) {
            throw new HttpException('Có lỗi xảy ra', HttpStatus.BAD_REQUEST);
        }

    }

    async findUMCPatientByExtraInfo(searchPatientExtraInfoDTO: SearchPatientExtraInfoDTO): Promise<any> {
        const params = {
            Ho: searchPatientExtraInfoDTO.surName,
            Ten: searchPatientExtraInfoDTO.firstName,
            NamSinh: searchPatientExtraInfoDTO.birthYear,
            GioiTinh: searchPatientExtraInfoDTO.gender,
            IDTinh: searchPatientExtraInfoDTO.cityId,
        };
        const url = this.uMCPartnerConfigService.getPatientByExtraInfo();
        try {
            const { data } = (await this.postPatientByHIS(url, params).toPromise()).data;
            if (data.length === 0) {
                throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
            }
            // tiếp tục xử lý
            const filterData: UMCPatientExtraInfoResponseDTO[] = data.filter((item: UMCPatientExtraInfoResponseDTO) => {
                return !item.NgungSD;
            });
            let resultData = [];
            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
            for await (const item of filterData) {
                const pInfo: UMCPatientResponseDTO = item;
                const checkExists = await this.checkExistsPatientByMsbnMedproId(pInfo.SoHS);
                if (checkExists) {
                    await this.updateUMCSyncPatient(pInfo.SoHS, {
                        DiDong: pInfo.DiDong,
                        DienThoai: pInfo.DienThoai,
                        SoCMND: pInfo.SoCMND,
                        NgaySinh: pInfo.NgaySinh,
                        NamSinh: pInfo.NamSinh,
                    });
                    const pdetail = await this.getUMCPatientByMsbn(pInfo.SoHS);
                    resultData = [...resultData, pdetail];
                } else {
                    const [patientId] = await this.insertUMCSyncPatient(pInfo);
                    const pdetail = await this.getUMCPatientById(patientId);
                    resultData = [...resultData, pdetail];
                }
            }

            return resultData.map((item) => {
                const secretKey = jwt.sign({ patientId: item.id }, jwtOptions.secret, jwtOptions.signOptions);
                return {
                    ...item,
                    secretKey,
                };
            });
        } catch (error) {
            throw new HttpException('Không tìm thấy thông tin', HttpStatus.NOT_FOUND);
        }
    }

    async verifyUMCPatientByPhone(verifyPhonePatientDTO: VerifyPhonePatientDTO): Promise<any> {
        const shortinfo = await this.getUMCPatientShortInfoVerifyPhoneByPatientId(verifyPhonePatientDTO.patientId);
        if (!shortinfo) {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra */
        if (verifyPhonePatientDTO.msbn === shortinfo.bvdhyd_msbn && verifyPhonePatientDTO.phone === shortinfo.mobile) {
            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
            return {
                patient: await this.getUMCPatientById(verifyPhonePatientDTO.patientId),
                secretKey: jwt.sign({ patientId: verifyPhonePatientDTO.patientId }, jwtOptions.secret, jwtOptions.signOptions),
            };
        }
        throw new HttpException('Thông tin gửi lên không chính xác. Vui lòng kiểm tra lại!', HttpStatus.NOT_FOUND);
    }

    async verifyUMCPatientByPhoneWithoutMsbn(verifyPhoneWithoutMSBNPatientDTO: VerifyPhoneWithoutMSBNPatientDTO, userId: number): Promise<any> {
        const shortinfo = await this.getUMCPatientShortInfoVerifyPhoneByPatientId(verifyPhoneWithoutMSBNPatientDTO.patientId);
        if (!shortinfo) {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }
        /* cần kiểm tra thêm patient id này có thuộc user hay ko */
        const { countValue } = await this.checkUMCPatientBelongsToUser(userId, verifyPhoneWithoutMSBNPatientDTO.patientId);
        if (countValue === 0) {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra */
        if (verifyPhoneWithoutMSBNPatientDTO.phone === shortinfo.mobile) {
            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
            return {
                patient: await this.getUMCPatientById(verifyPhoneWithoutMSBNPatientDTO.patientId),
                secretKey: jwt.sign({ patientId: verifyPhoneWithoutMSBNPatientDTO.patientId }, jwtOptions.secret, jwtOptions.signOptions),
            };
        }
        throw new HttpException('Thông tin gửi lên không chính xác. Vui lòng kiểm tra lại!', HttpStatus.NOT_FOUND);
    }

    async checkExistsPatientByMsbnMedproId(msbn: string): Promise<any> {
        const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION, 'patient');
        const { countValue } = await getConnectionQuery.count('id', { as: 'countValue' }).where('bvdhyd_msbn', msbn).first();
        if (countValue > 0) {
            return true;
        }
        return false;
    }

    async updateUMCSyncPatient(msbn, patient): Promise<any> {
        const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION, 'patient');
        return getConnectionQuery.where({ bvdhyd_msbn: msbn }).update({
            mobile: (patient.DiDong ? patient.DiDong : patient.DienThoai),
            birthdate: patient.NgaySinh,
            birthyear: patient.NamSinh,
            cmnd: patient.SoCMND,
        });
    }

    async insertUMCSyncPatient(patient): Promise<any> {
        const {
            Ten, Ho, DiDong, DienThoai, DiaChi, GioiTinh, IDDanToc, IDNgheNghiep, IDPhuongXa,
            IDQuanHuyen, IDTinh, MaQuocGia, NamSinh, NgaySinh, SoCMND, SoHS,
        } = patient;
        const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION, 'patient');
        const medproId = await this.generateUMCMedproID();
        return getConnectionQuery.insert({
            name: Ten,
            surname: Ho,
            cmnd: SoCMND,
            sex: GioiTinh,
            mobile: (DiDong ? DiDong : DienThoai),
            dantoc_id: IDDanToc,
            profession_id: IDNgheNghiep,
            country_code: MaQuocGia,
            city_id: IDTinh,
            district_id: IDQuanHuyen,
            ward_id: IDPhuongXa,
            address: DiaChi,
            birthdate: NgaySinh,
            birthyear: NamSinh,
            bvdhyd_msbn: SoHS,
            medpro_id: medproId,
            is_medpro: 0,
        });
    }

    async addPatientToUserUMCPatient(addPatientToUserDTO: AddPatientToUserDTO, userId: number): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        let patientId = 0;
        try {
            const jwtVerify: any = jwt.verify(addPatientToUserDTO.secretKey, jwtOptions.secret);
            patientId = jwtVerify.patientId;
        } catch (error) {
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }
        /* xử lý quá trình add patient to user */
        const { countValue } = await this.getCountPatientInUserPatientUMC(userId);
        if (countValue >= this.countPatient) {
            throw new HttpException('Số lượng hồ sơ bệnh nhân đạt tối đa!', HttpStatus.FORBIDDEN);
        }
        /* kiểm tra xem đã có insert rồi hay chưa */
        const { countValue: countExistsPatient } = await this.checkUMCPatientBelongsToUser(userId, patientId);
        if (countExistsPatient > 0) {
            throw new HttpException('Thông tin bệnh nhân này đã được thêm vào user rồi.', HttpStatus.CONFLICT);
        }
        /* tiến hành apply hồ sơ bệnh nhân vào trong user id */
        try {
            await this.addUserPatient(userId, patientId);
            return this.getUMCPatientById(patientId);
        } catch (error) {
            throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
        }
    }

    async getUMCPatientDetailForUpdate(patientId: number, userId: number): Promise<any> {
        const { countValue } = await this.checkUMCPatientBelongsToUser(userId, patientId);
        if (countValue === 0) {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }
        /* lấy thông tin đầy đủ bệnh nhân để cập nhật */
        return this.getUMCPatientByIdForUpdate(patientId);
    }

    async insertUMCPatient(patientFormData: PatientFormDataDTO, userId: number): Promise<any> {
        /* kiểm tra xem nghề nghiệp đúng ko */
        if (patientFormData.profession_id) {
            const { countValue: countProfession } = await this.checkProfessionUMC(patientFormData.profession_id);
            if (countProfession === 0) {
                throw new HttpException('Nghề nghiệp không chính xác.', HttpStatus.NOT_FOUND);
            }
        }
        if (patientFormData.country_code) {
            /* Kiểm tra xem quốc gia đúng ko */
            const { countValue: countCountry } = await this.checkCountryUMC(patientFormData.country_code);
            if (countCountry === 0) {
                throw new HttpException('Quốc gia không chính xác.', HttpStatus.NOT_FOUND);
            }
        }
        if (patientFormData.dantoc_id) {
            /* Kiểm tra xem dân tộc đúng ko */
            const { countValue: countNation } = await this.checkNationUMC(patientFormData.dantoc_id);
            if (countNation === 0) {
                throw new HttpException('Dân tộc không chính xác.', HttpStatus.NOT_FOUND);
            }
        }
        if (patientFormData.relative_type_id) {
            /* Kiểm tra xem quan hệ thân nhân đúng ko */
            if (patientFormData.relative_type_id) {
                const { countValue: countRelation } = await this.checkRelativeTypeId(patientFormData.relative_type_id);
                if (countRelation === 0) {
                    throw new HttpException('Mối quan hệ không chính xác.', HttpStatus.NOT_FOUND);
                }
            }
        }
        /* kiểm tra xem đã có bao nhiêu hồ sơ bệnh nhân */
        const { countValue: countPatient } = await this.getCountPatientInUserPatientUMC(userId);
        if (countPatient >= this.countPatient) {
            throw new HttpException('Số lượng hồ sơ bệnh nhân đạt tối đa!', HttpStatus.FORBIDDEN);
        }
        try {
            const { relative_name, relative_email, relative_mobile, relative_type_id, ...patientInfo } = patientFormData;
            const medproId = await this.generateUMCMedproID();
            const patientInsert = {
                ...patientInfo,
                birthdate: moment(patientInfo.birthdate).format('YYYY-MM-DD'),
                medpro_id: medproId,
            };
            const [returnId] = await this.insertPatientInfo(PKH_PATIENT_CONNECTION, patientInsert);
            /* Kiểm tra thông tin người thân */
            const relativeData = {
                name: relative_name ?? '',
                email: relative_email ?? '',
                mobile: relative_mobile ?? '',
                relative_type_id: !!relative_type_id ? Number(relative_type_id) : 0,
                patient_id: returnId,
            };
            await this.insertRelativeInfo(PKH_PATIENT_CONNECTION, relativeData);
            await this.addUserPatient(userId, returnId);
            return this.getUMCPatientById(returnId);
        } catch (error) {
            throw new HttpException('Có lỗi xảy ra.', HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    async updateUMCPatient(updatePatientFormDataDTO: UpdatePatientFormDataDTO, userId: number): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        let patientId = 0;
        try {
            const jwtVerify: any = jwt.verify(updatePatientFormDataDTO.secretKey, jwtOptions.secret);
            patientId = jwtVerify.patientId;
        } catch (error) {
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }

        const { countValue } = await this.checkUMCPatientBelongsToUser(userId, patientId);
        if (countValue === 0) {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }
        if (updatePatientFormDataDTO.relative_type_id) {
            /* Kiểm tra xem quan hệ thân nhân đúng ko */
            if (updatePatientFormDataDTO.relative_type_id) {
                const { countValue: countRelation } = await this.checkRelativeTypeId(updatePatientFormDataDTO.relative_type_id);
                if (countRelation === 0) {
                    throw new HttpException('Mối quan hệ không chính xác.', HttpStatus.NOT_FOUND);
                }
            }
        }

        const { relative_name, relative_email, relative_mobile, relative_type_id, ...patientInfo } = updatePatientFormDataDTO;

        /* tiến hành kiểm tra thông tin relative */
        const relativeInfo = await this.getRelativeInfo(PKH_PATIENT_CONNECTION, patientId);
        const {
            name: relativeNameCheck,
            // email: relativeEmailCheck,
            // mobile: relativeMobileCheck,
            relative_type_id: relativeTypeCheck,
        } = relativeInfo;
        // duyệt qua xem những thông tin nào đã cập nhật
        if ((!!relativeNameCheck && !!relative_name === false) || (!!relativeTypeCheck && !!relative_type_id === false)) {
            throw new HttpException('Vui lòng kiểm tra lại họ và tên nhân thân và mối quan hệ.', HttpStatus.BAD_REQUEST);
        }
        const { secretKey, ...updateFields } = patientInfo;
        const patientUpdate = {
            ...updateFields,
        };

        const relativeData = {
            name: relative_name ?? '',
            email: relative_email ?? '',
            mobile: relative_mobile ?? '',
            relative_type_id: !!relative_type_id ? Number(relative_type_id) : 0,
        };

        try {
            // kiểm tra trường hợp có số hồ sơ bệnh nhân hay không
            let updateInfo = {};
            const msbnById = await this.getUMC_MSBN_PatientByIdForUpdate(patientId);
            if (!!msbnById.msbn) {
                // tồn tại mã số bệnh nhân
                const { mobile, email } = patientUpdate;
                updateInfo = { mobile, email };
                // tiến hành update patient
                await this.updatePatientInfo(PKH_PATIENT_CONNECTION, updateInfo, patientId);
                // tiến hành cập nhật relative
                await this.updateRelativeInfo(PKH_PATIENT_CONNECTION, relativeData, patientId);
                return this.getUMCPatientByIdForUpdate(patientId);
            }
            throw new HttpException('Thông tin bệnh nhân này chưa có số hồ sơ bệnh nhân.', HttpStatus.BAD_REQUEST);
        } catch (error) {
            throw new HttpException('Có lỗi xảy ra', HttpStatus.BAD_REQUEST);
        }
    }

    async updateUMCPatientWithoutMSBN(updatePatientFormDataDTO: UpdatePatientFormDataWithoutMsbnDTO, userId: number): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        let patientId = 0;
        try {
            const jwtVerify: any = jwt.verify(updatePatientFormDataDTO.secretKey, jwtOptions.secret);
            patientId = jwtVerify.patientId;
        } catch (error) {
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }

        const { countValue } = await this.checkUMCPatientBelongsToUser(userId, patientId);
        if (countValue === 0) {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra xem nghề nghiệp đúng ko */
        if (updatePatientFormDataDTO.profession_id) {
            const { countValue: countProfession } = await this.checkProfessionUMC(updatePatientFormDataDTO.profession_id);
            if (countProfession === 0) {
                throw new HttpException('Nghề nghiệp không chính xác.', HttpStatus.NOT_FOUND);
            }
        }
        if (updatePatientFormDataDTO.country_code) {
            /* Kiểm tra xem quốc gia đúng ko */
            const { countValue: countCountry } = await this.checkCountryUMC(updatePatientFormDataDTO.country_code);
            if (countCountry === 0) {
                throw new HttpException('Quốc gia không chính xác.', HttpStatus.NOT_FOUND);
            }
        }
        if (updatePatientFormDataDTO.dantoc_id) {
            /* Kiểm tra xem dân tộc đúng ko */
            const { countValue: countNation } = await this.checkNationUMC(updatePatientFormDataDTO.dantoc_id);
            if (countNation === 0) {
                throw new HttpException('Dân tộc không chính xác.', HttpStatus.NOT_FOUND);
            }
        }
        if (updatePatientFormDataDTO.relative_type_id) {
            /* Kiểm tra xem quan hệ thân nhân đúng ko */
            if (updatePatientFormDataDTO.relative_type_id) {
                const { countValue: countRelation } = await this.checkRelativeTypeId(updatePatientFormDataDTO.relative_type_id);
                if (countRelation === 0) {
                    throw new HttpException('Mối quan hệ không chính xác.', HttpStatus.NOT_FOUND);
                }
            }
        }
        const { relative_name, relative_email, relative_mobile, relative_type_id, ...patientInfo } = updatePatientFormDataDTO;
        const { secretKey, ...updateFields } = patientInfo;
        const patientUpdate = {
            ...updateFields,
            birthdate: moment(updateFields.birthdate).format('YYYY-MM-DD'),
        };

        const relativeData = {
            name: relative_name ?? '',
            email: relative_email ?? '',
            mobile: relative_mobile ?? '',
            relative_type_id: !!relative_type_id ? Number(relative_type_id) : 0,
        };

        try {
            // kiểm tra trường hợp có số hồ sơ bệnh nhân hay không
            let updateInfo = {};
            const msbnById = await this.getUMC_MSBN_PatientByIdForUpdate(patientId);
            if (!!msbnById.msbn) {
                // tồn tại mã số bệnh nhân
                const { mobile, email } = patientUpdate;
                updateInfo = { mobile, email };
            } else {
                updateInfo = { ...patientUpdate };
            }
            // tiến hành update patient
            await this.updatePatientInfo(PKH_PATIENT_CONNECTION, updateInfo, patientId);
            // tiến hành cập nhật relative
            await this.updateRelativeInfo(PKH_PATIENT_CONNECTION, relativeData, patientId);
            return this.getUMCPatientByIdForUpdate(patientId);
        } catch (error) {
            throw new HttpException('Có lỗi xảy ra', HttpStatus.BAD_REQUEST);
        }
    }

    // tslint:disable-next-line: variable-name
    async checkRelativeTypeId(relative_type_id: number): Promise<any> {
        return this.pkhPatientKnex('relative_type')
            .count('id', { as: 'countValue' })
            .where('id', relative_type_id)
            .first();
    }

    async checkNationUMC(nationId: number): Promise<any> {
        return this.pkhPatientKnex('dm_dantoc')
            .count('id', { as: 'countValue' })
            .where('id', nationId)
            .first();
    }

    async checkCountryUMC(countryCode: string): Promise<any> {
        return this.pkhPatientKnex('dm_country')
            .count('id', { as: 'countValue' })
            .where('code', countryCode)
            .first();
    }

    async checkProfessionUMC(professionId: number): Promise<any> {
        return this.pkhPatientKnex('profession')
            .count('id', { as: 'countValue' })
            .where('id', professionId)
            .first();
    }

    async checkUMCPatientBelongsToUser(userId: number, patientId: number): Promise<any> {
        const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION, 'user_patient');
        return getConnectionQuery.count('id', { as: 'countValue' })
            .where('user_id', userId)
            .where('patient_id', patientId)
            .first();
    }

    async addUserPatient(userId: number, patientId: number): Promise<any> {
        const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION, 'user_patient');
        return getConnectionQuery.insert({
            user_id: userId,
            patient_id: patientId,
        });
    }

    getPatientByHIS(url): Observable<AxiosResponse<any>> {
        return this.httpService.get(url);
    }

    postPatientByHIS(url, params): Observable<AxiosResponse<any>> {
        return this.httpService.post(url, params);
    }

    getConnection(hospitalConnection = PKH_PATIENT_CONNECTION, tableName = this.tableName) {
        let connectionQuery = null;
        switch (hospitalConnection) {
            case THU_DUC_HOSPITAL_CONNECTION:
                connectionQuery = this.thuDucHospitalKnex(tableName);
                break;
            case CHO_RAY_HOSPITAL_CONNECTION:
                connectionQuery = this.choRayHospitalKnex(tableName);
                break;
            default:
                connectionQuery = this.pkhPatientKnex(tableName);
        }
        return connectionQuery;
    }

    generateMedproID(vLength = 6) {
        const dateFormat = moment().format('YYMMDD');
        const pattern = '**********';
        let pass = [];
        const alphaLength = pattern.length - 1;
        for (let i = 0; i < vLength; i++) {
            const n = this.utilService.getRandomInt(0, alphaLength);
            pass = [...pass, pattern[n]];
        }
        const uuid = `MP-${dateFormat}${pass.join('')}`; // dành cho UMC
        /* kiểm tra xem có trùng hay ko. nếu trùng thì tạo lại. */

    }

    async generateUMCMedproID(vLength = 6) {
        const dateFormat = moment().format('YYMMDD');
        const pattern = '**********';
        let pass = [];
        const alphaLength = pattern.length - 1;
        for (let i = 0; i < vLength; i++) {
            const n = this.utilService.getRandomInt(0, alphaLength);
            pass = [...pass, pattern[n]];
        }
        const uuid = `MP-${dateFormat}${pass.join('')}`; // dành cho UMC
        /* kiểm tra xem có trùng hay ko. nếu trùng thì tạo lại. */
        const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION, 'patient');
        const { countValue } = await getConnectionQuery
            .count('id', { as: 'countValue' })
            .where('medpro_id', uuid)
            .first();
        if (countValue > 0) {
            return this.generateUMCMedproID();
        }
        return uuid;
    }

    secretMobile(mobile: string) {
        const get2charLast = mobile ? mobile.slice(-3) : '';
        const secretMobile = mobile ? `${mobile.slice(0, -7)}xxxx${get2charLast}` : '';
        return secretMobile;
    }

}
