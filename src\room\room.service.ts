import { Injectable, Inject, HttpException, HttpStatus, HttpService } from '@nestjs/common';
import { THU_DUC_HOSPITAL_CONNECTION } from 'src/config/thuDucHospitalConnection';
import { CHO_RAY_HOSPITAL_CONNECTION } from 'src/config/choRayHospitalConnection';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { FilterDateAndSubjectDTO } from './dto/filter-date-subject.dto';
import * as moment from 'moment';
import * as queryString from 'query-string';
import { FilterChangeDoctorDTO } from './dto/filter-change-doctor.dto copy';
import { UMCPartnerConfigService } from 'src/config/config.umc.partner.service';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
@Injectable()
export class RoomService {

    private hospitalSubjectTableName = 'hospital_subject';
    private subjectTableName = 'subject';
    private scheduleTableName = 'schedule';
    private roomTableName = 'room';
    private ptimeTableName = 'ptime';
    private doctorTableName = 'doctor';
    private changeDoctorTableName = 'bvdhyd_lichbsthaythe';
    private bookingTimeTableName = 'booking_time';
    private bookingTableName = 'booking';

    constructor(
        @Inject(THU_DUC_HOSPITAL_CONNECTION) private readonly thuDucHospitalKnex,
        @Inject(CHO_RAY_HOSPITAL_CONNECTION) private readonly choRayHospitalKnex,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        private readonly httpService: HttpService,
        private readonly uMCPartnerConfigService: UMCPartnerConfigService,
    ) { }

    async getListByDateAndSubject(filterDateAndSubjectDTO: FilterDateAndSubjectDTO): Promise<any> {
        try {
            const bookingDate = moment(filterDateAndSubjectDTO.bookingDate, 'YYYY-MM-DD');
            const dayOfWeek = bookingDate.day();
            const date = Number(bookingDate.format('D'));
            const month = Number(bookingDate.format('M'));
            const hospitalSubjectInfo =
                await this.pkhPatientKnex(this.hospitalSubjectTableName)
                    .select(
                        /* Thông tin phòng khám */
                        `${this.hospitalSubjectTableName}.id as id`,
                        `${this.subjectTableName}.code as subjectCode`,
                    )
                    .innerJoin(this.subjectTableName, `${this.subjectTableName}.id`, `${this.hospitalSubjectTableName}.subject_id`)
                    .where(`${this.hospitalSubjectTableName}.hospital_id`, filterDateAndSubjectDTO.hospitalId)
                    .where(`${this.hospitalSubjectTableName}.subject_id`, filterDateAndSubjectDTO.subjectId)
                    .where(`${this.hospitalSubjectTableName}.status`, 1)
                    .first();
    
            if (!hospitalSubjectInfo) {
                throw new HttpException('Không tìm thấy thông tin lịch khám', HttpStatus.NOT_FOUND);
            }
            const schedule = await this.pkhPatientKnex(this.scheduleTableName)
                .select(
                    /* Thông tin phòng khám */
                    `${this.roomTableName}.id as roomId`,
                    `${this.roomTableName}.name as roomName`,
                    /* Thông tin bác sĩ */
                    `${this.doctorTableName}.id as doctorId`,
                    `${this.doctorTableName}.name as doctorFullName`,
                    `${this.doctorTableName}.name_only as doctorOnlyName`,
                    `${this.doctorTableName}.role as doctorRole`,
                    `${this.doctorTableName}.sex as doctorGender`,
                    /* Thông tin schedule */
                    // `${this.scheduleTableName}.*`,
                    /* Thông tin ptime */
                    `${this.ptimeTableName}.buoi as buoiKham`,
                    `${this.ptimeTableName}.hour_from as hour_from`,
                    `${this.ptimeTableName}.hour_to as hour_to`,
                )
                .innerJoin(this.roomTableName, `${this.roomTableName}.id`, `${this.scheduleTableName}.room_id`)
                .innerJoin(this.ptimeTableName, `${this.ptimeTableName}.id`, `${this.scheduleTableName}.ptime_id`)
                .innerJoin(this.doctorTableName, `${this.doctorTableName}.id`, `${this.scheduleTableName}.doctor_id`)
                .where(`${this.scheduleTableName}.hospital_subject_id`, hospitalSubjectInfo.id)
                .where(`${this.scheduleTableName}.status`, 1)
                .where(`${this.scheduleTableName}.is_old`, 0)
                .whereRaw(`? between ${this.ptimeTableName}.day_from AND ${this.ptimeTableName}.day_to`, [date])
                .whereRaw(`? between ${this.ptimeTableName}.month_from AND ${this.ptimeTableName}.month_to`, [month])
                .where(`${this.ptimeTableName}.weekday`, dayOfWeek);
            /* Kiểm tra xem có đổi thông tin bác sĩ khám hay không */
            const Ngay = bookingDate.format('YYYY-MM-DD');
            let resultData = [];
            for await (const item of schedule) {
                /* gửi sang HIS kiểm tra */
                const params = {
                    Ngay,
                    IDBS: item.doctorId,
                    MaDonVi: hospitalSubjectInfo.subjectCode,
                    RIDBuoiKham: item.buoiKham,
                    RIDThu: (dayOfWeek + 1), // tăng thêm 1 ngày.
                };
                const changeDoctor = await this.findDoctorChange(params);
                let isChanged = false;
                let doctorChangeObj = {};
                if (changeDoctor) {
                    isChanged = true;
                    doctorChangeObj = {
                        doctorChangedId: changeDoctor.doctorId,
                        doctorChangedFullName: changeDoctor.doctorFullName,
                        doctorChangedOnlyName: changeDoctor.doctorOnlyName,
                        doctorChangedRole: changeDoctor.doctorRole,
                        doctorChangedGender: changeDoctor.doctorGender,
                    };
                }
                /* tiếp tục kiểm tra xem cái booking time */
                const slotTimes = await this.getUMCBookingTime(item.hour_from, item.hour_to, item.roomId);
                /* Lấy thông tin lịch khám bên bệnh viện */
                const url = this.uMCPartnerConfigService.getBookedIn();
                const paramsGetBookedIn = {
                    MaDonVi: hospitalSubjectInfo.subjectCode,
                    RIDBuoiKham: item.buoiKham,
                    IDBS: isChanged ? changeDoctor.doctorId : item.doctorId,
                    NgayKham: Ngay,
                };
                const urlBookedIn = queryString.stringifyUrl({
                    url,
                    query: { ...paramsGetBookedIn },
                });
                const { data: bookedIn } = (await this.getBookedIn(urlBookedIn).toPromise()).data;
                /* join lại */
                resultData = [...resultData, {
                    ...item,
                    ...filterDateAndSubjectDTO,
                    bookingDate: Ngay,
                    isDoctorChanged: isChanged,
                    ...doctorChangeObj,
                    bookedIn,
                    slotTimes,
                }];
            }
            /* Tiến hành kiểm tra cái phần đã sử dụng bao nhiêu */
            for await (const item of resultData) {
                const dataSlotTimes = item.slotTimes;
                const dataBookedIn = item.bookedIn;
                for await (const slotTimeDetail of dataSlotTimes) {
                    const usedSlot = 0;
                    if (slotTimeDetail.max_slot > 0 && dataBookedIn.length > 0) {
                        const { countValue: isUsed } = await this.countIsUseTimeSlot(slotTimeDetail.id, item.bookingDate);
                    }
                }
            }
            return resultData;
        } catch (error) {
            //TO DO HANDLE ERROR
        }
    }

    parseFloatToTime(value: number) {
        const minute = (value * 60) % 60;
        const hour = value;
        const hourString = hour < 10 ? `0${hour}` : hour;
        const minuteString = minute < 10 ? `0${minute}` : minute;
        return `${hourString}:${minuteString}:00`;
    }

    async countIsUseTimeSlot(bookingTimeId: number, bookingDate: string): Promise<any> {
        return this.pkhPatientKnex(this.bookingTableName)
            .count('id', { as: 'countValue' })
            .where('status', '>=', 0)
            .where('is_deleted', '!=', 1)
            .where('booking_time_id', bookingTimeId)
            .where('booking_date', bookingDate);

    }

    getBookedIn(url): Observable<AxiosResponse<any>> {
        return this.httpService.get(url);
    }

    async getUMCBookingTime(hourFrom: number, hourTo: number, roomId: number): Promise<any> {
        return this.pkhPatientKnex(this.bookingTimeTableName)
            .whereRaw(`((${this.bookingTimeTableName}.from <= ? AND ${this.bookingTimeTableName}.to > ?) OR (${this.bookingTimeTableName}.from >= ? AND ${this.bookingTimeTableName}.to <= ?) OR (${this.bookingTimeTableName}.from < ? AND ${this.bookingTimeTableName}.to >= ?))`,
                [hourFrom, hourFrom, hourFrom, hourTo, hourTo, hourTo])
            .where(`${this.bookingTimeTableName}.is_old`, 0)
            .where(`${this.bookingTimeTableName}.room_id`, roomId);

    }

    async findDoctorChange(filterChangeDoctorDTO: FilterChangeDoctorDTO): Promise<any> {
        return this.pkhPatientKnex(this.changeDoctorTableName)
            .select(
                `${this.doctorTableName}.id as doctorId`,
                `${this.doctorTableName}.name as doctorFullName`,
                `${this.doctorTableName}.name_only as doctorOnlyName`,
                `${this.doctorTableName}.role as doctorRole`,
                `${this.doctorTableName}.sex as doctorGender`,
            )
            .innerJoin(this.doctorTableName, `${this.doctorTableName}.id`, `${this.changeDoctorTableName}.IDBS`)
            .where({ ...filterChangeDoctorDTO })
            .first();
    }

}
