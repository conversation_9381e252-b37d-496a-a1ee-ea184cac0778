
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class UMCPatientResponseDTO {

    @ApiProperty({
        description: 'Tên bệnh nhân',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim().toUpperCase())
    readonly Ten: string;

    @ApiProperty({
        description: 'Họ bệnh nhân',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim().toUpperCase())
    readonly Ho: string;

    @ApiProperty({
        description: 'Số CMND',
        required: true,
        type: String,
    })
    readonly SoCMND: string;

    @ApiProperty({
        description: 'Giới tính',
        required: true,
        type: Boolean,
    })
    readonly GioiTinh: boolean;

    @ApiProperty({
        description: 'Điện thoại hoặc điện thoại di động',
        required: true,
        type: String,
    })
    readonly DiDong: string;

    @ApiProperty({
        description: '<PERSON><PERSON><PERSON><PERSON> thoại hoặc điện thoại di động',
        required: true,
        type: String,
    })
    readonly DienThoai: string;

    @ApiProperty({
        description: 'Id Dân tộc',
        required: true,
        type: Number,
    })
    readonly IDDanToc: number;

    @ApiProperty({
        description: 'Id Nghề nghiệp',
        required: true,
        type: Number,
    })
    readonly IDNgheNghiep: number;

    @ApiProperty({
        description: 'Mã quốc gia',
        required: true,
        type: String,
    })
    readonly MaQuocGia: string;

    @ApiProperty({
        description: 'Id Tỉnh/Thành',
        required: true,
        type: Number,
    })
    readonly IDTinh: number;

    @ApiProperty({
        description: 'Id Quận/Huyện',
        required: true,
        type: Number,
    })
    readonly IDQuanHuyen: number;

    @ApiProperty({
        description: 'Id Phường/Xã',
        required: true,
        type: Number,
    })
    readonly IDPhuongXa: number;

    @ApiProperty({
        description: 'Địa chỉ',
        required: true,
        type: String,
    })
    readonly DiaChi: string;

    @ApiProperty({
        description: 'ISO string',
        required: true,
        type: String,
    })
    readonly NgaySinh: string;

    @ApiProperty({
        description: 'Năm sinh',
        required: true,
        type: Number,
    })
    readonly NamSinh: number;

    @ApiProperty({
        description: 'Số hồ sơ',
        required: true,
        type: String,
    })
    readonly SoHS: string;

    @ApiProperty({
        description: 'Số hồ sơ',
        required: true,
        type: Boolean,
    })
    readonly NgungSD: boolean;

    partnerId?: string;
    /* hồ sơ từ thông tin tra cứu kết quả xét nghiệm */
    isExam?: boolean;

    relative?: {
        fullname?: string;
        phone?: string | null;
        email?: string | null;
        relativeType: string;
        relativeName: string;
    };

}
