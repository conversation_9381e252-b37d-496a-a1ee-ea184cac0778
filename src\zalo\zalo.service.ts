import { get } from 'lodash';
import { GlobalSettingService } from './../global-setting/global-setting.service';
import { ErrorMessage } from 'src/common/enums/message-error.enum';
import { HttpException, HttpStatus, Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import * as crypto from 'crypto';
import * as querystring from 'query-string';
import { ClientUtilService } from 'src/config/client-util.service';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UtilService } from 'src/config/util.service';
import * as uuid from 'uuid';
import { UserAccessTokenDto } from './dto/user-access-token.dto';

@Injectable()
export class ZaloService {
    private readonly logger = new Logger(ZaloService.name);

    @Inject(ClientUtilService)
    private readonly client: ClientUtilService;

    @Inject(GlobalSettingService)
    private readonly global: GlobalSettingService;

    @Inject(EventEmitter2)
    private readonly event: EventEmitter2;

    @Inject(UtilService)
    private readonly util: UtilService

    async getConfigZaloV4(appid: string) {
        let config = await this.global.findByKeyAndRepoName(`ZALO_V4_${appid.toUpperCase()}`);

        if (!config) {
            config = await this.global.findByKeyAndRepoName(`ZALO_V4`)
        }

        let configZaloV4: any;
        try {
            configZaloV4 = JSON.parse(config);
        } catch (error) {
            this.logger.error(error?.message);
            throw new HttpException(ErrorMessage.BAD_REQUEST, HttpStatus.BAD_REQUEST);
        }

        return configZaloV4;
    }

    async getAuthorizationCodeGrant(appid: string, platform: string): Promise<any> {
        const code_verifier = `${this.util.randomText(11).toUpperCase()}${uuid.v4().replace(/-/g,'')}`;
        const configZaloV4 = await this.getConfigZaloV4(appid);
        let params: any = {
            app_id: configZaloV4?.app_id,
            code_challenge: crypto
                .createHash('sha256')
                .update(Buffer.from(code_verifier).toString('ascii'))
                .digest('base64')
                .replace(/=/g, '')
                .replace(/\+/g, '-')
                .replace(/\//g, '_'),
            state: configZaloV4?.state,
        };

        switch (platform) {
            case 'pc':
            case 'web':
                params = {
                    ...params,
                    redirect_uri: configZaloV4?.redirect_uri,
                };
                break;
            case 'android':
                params = {
                    ...params,
                    pkg_name: configZaloV4?.pkg_name,
                    sign_key: configZaloV4?.sign_key,
                    os: platform,
                };
                break;
            case 'ios':
                params = {
                    ...params,
                    bndl_id: configZaloV4?.bndl_id,
                    os: platform,
                };
                break;
        }

        const url = querystring.stringifyUrl({
            url: `${configZaloV4?.zalo_v4_url}/permission`,
            query: params,
        });

        return { url, code_verifier };
    }

    async getZaloInfoAfterVerified(token: string, appid: string = ''): Promise<any> {
        const configZaloV4 = await this.getConfigZaloV4(appid);
        const { urlZaloInfo } = configZaloV4;

        const queryParamsGetZaloInfo = {
            fields: 'id,name',
        };
        const urlGetZaloInfo = querystring.stringifyUrl({
            url: urlZaloInfo,
            query: queryParamsGetZaloInfo,
        });
        try {
            const zaloInfo = await this.client.get(urlGetZaloInfo, {
                access_token: token
            });
            console.log('zaloInfo sau khi decode', zaloInfo);
            // this.clientSentry.instance().captureMessage(JSON.stringify(zaloInfo, null, 2));
            const getError = get(zaloInfo, 'error', 0);
            if (getError > 0) {
                this.event.emit(LOG_SERVICE_EVENT, {
                    name: 'getZaloInfo',
                    summary: 'Lấy thông tin zalo',
                    nameParent: 'getZaloInfoAfterVerified',
                    params: {
                        urlGetZaloInfo,
                    },
                    errorBody: getError,
                    response: { soucre: zaloInfo },
                    message: `Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại`,
                });
                throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
            }
            return zaloInfo;
        } catch (error) {
            console.log(error);
            this.event.emit(LOG_SERVICE_EVENT, {
                name: 'getZaloInfo',
                summary: 'Lấy thông tin zalo',
                nameParent: 'getZaloInfoAfterVerified',
                params: {
                    urlGetZaloInfo,
                },
                errorBody: this.util.errorHandler(error),
                message: `Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại`,
            });
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async getUserAccessToken(appid: string, formdata: UserAccessTokenDto): Promise<any> {
        const configZaloV4 = await this.getConfigZaloV4(appid);
        const res = await this.client.post(
            `${configZaloV4?.zalo_v4_url}/access_token`,
            querystring.stringify({
                code_verifier: formdata?.code_verifier,
                code: formdata?.authorization_code,
                app_id: configZaloV4?.app_id,
                grant_type: 'authorization_code',
            }),
            {
                'Content-Type': 'application/x-www-form-urlencoded',
                secret_key: configZaloV4?.app_secret,
            }
        );

        if (res?.error || !res?.access_token) {
            throw new HttpException(res?.error_description || res?.error_name || 'Không thể thực hiện được đăng nhập! Xin thử lại!', HttpStatus.BAD_REQUEST);
        }

        return res;
    }
}
