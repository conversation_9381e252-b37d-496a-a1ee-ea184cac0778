import * as mongoose from 'mongoose';
import { MAIL_TEMPLATE_COLLECTION } from './constant';

const Schema = mongoose.Schema;

export const MailTemplateSchema = new Schema({
    template: { type: String , unique: true },
    html: { type: String },
    description: { type: String },
    subject: { type: String },
    propertyMaps: { type: String },
    propertySubjectMaps: { type: String },
    propertyMedproMaps: { type: String },
}, {
    collection: MAIL_TEMPLATE_COLLECTION,
    timestamps: true,
});
