import { HttpModule, Module } from '@nestjs/common';
import { ResourceController } from './resource.controller';
import { ResourceService } from './resource.service';
import { GlobalSettingLocaleModule } from 'src/global-setting-locale/global-setting-locale.module';

@Module({
  imports: [
    GlobalSettingLocaleModule,
    HttpModule,
  ],
  controllers: [ResourceController],
  providers: [ResourceService],
})
export class ResourceModule { }
