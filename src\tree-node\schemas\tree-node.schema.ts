import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { TREE_NODE_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

const TreeNodeSchema = new Schema(
    {
        parentId: String,
        type: String,
        cta: {
            text: String,
            link: String,
            screenLink: String,
        },
        bgImg: String,
        order: String,
        children: [
            {
                id: String,
                parentId: String,
                category: String,
                cardType: String,
                cardTitle: String,
                cardDescription: String,
                cardSubject: String,
                cardHospital: String,
                doctorTitle: String,
                doctorName: String,
                bgImg: String,
                avtImg: String,
                order: String,
                status: { type: Number, default: 1 },
            },
        ],
        status: { type: Number, default: 0 },
    },
    {
        collection: TREE_NODE_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);

export {TreeNodeSchema};
