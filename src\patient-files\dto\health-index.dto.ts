import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, ValidateIf } from 'class-validator';

export enum BloodTypeEnum {
    A = 'A',
    B = 'B',
    AB = 'AB',
    O = 'O',
}

export class createHealthIndexDTO {
    // Chỉ số sức khỏe
    @IsOptional()
    glucoseU?: string;

    @IsOptional()
    bp?: string;

    @IsOptional()
    height?: number;

    @IsOptional()
    weight?: number;

    @IsOptional()
    bmi?: number;

    @IsOptional()
    waist?: string;

    @IsEnum(BloodTypeEnum, { message: 'Vui lòng gửi đúng dữ liệu [A, B, AB, O]' })
    @IsOptional()
    bloodType?: BloodTypeEnum;

    @IsOptional()
    pulse?: string;

    @IsOptional()
    temperature?: string;

    @IsOptional()
    respiratoryRate?: string;

    // Dị ứng
    @IsOptional()
    @ApiProperty({ enum: ['YES', 'NO', 'UNKNOWN'] })
    drugAllergyStatus?: 'YES' | 'NO' | 'UNKNOWN';

    @ValidateIf(o => o.drugAllergyStatus?.toUpperCase() === 'YES')
    @IsNotEmpty({ message: 'Vui lòng nhập tên thuốc dị ứng nếu có dị ứng thuốc.' })
    drugAllergyNote?: string;

    @IsOptional()
    @ApiProperty({ enum: ['YES', 'NO', 'UNKNOWN'] })
    ingredientAllergyStatus?: 'YES' | 'NO' | 'UNKNOWN';

    @ValidateIf(o => o.ingredientAllergyStatus?.toUpperCase() === 'YES')
    @IsNotEmpty({ message: 'Vui lòng nhập tên hóa chất dị ứng nếu có dị ứng.' })
    ingredientAllergyNote?: string;

    @IsOptional()
    @ApiProperty({ enum: ['YES', 'NO', 'UNKNOWN'] })
    foodAllergyStatus?: 'YES' | 'NO' | 'UNKNOWN';

    @ValidateIf(o => o.foodAllergyStatus?.toUpperCase() === 'YES')
    @IsNotEmpty({ message: 'Vui lòng nhập tên thực phẩm dị ứng nếu có dị ứng.' })
    foodAllergyNote?: string;

    // Tiền sử bệnh lý gia đình
    @IsOptional()
    familyHistoryPulse?: string;

    @IsOptional()
    @ApiProperty({ enum: ['YES', 'NO', 'UNKNOWN'] })
    familyHistoryHypertension?: 'YES' | 'NO' | 'UNKNOWN';

    @IsOptional()
    @ApiProperty({ enum: ['YES', 'NO', 'UNKNOWN'] })
    familyHistoryMental?: 'YES' | 'NO' | 'UNKNOWN';

    @IsOptional()
    @ApiProperty({ enum: ['YES', 'NO', 'UNKNOWN'] })
    familyHistoryCancer?: 'YES' | 'NO' | 'UNKNOWN';

    @IsOptional()
    @ApiProperty({ enum: ['YES', 'NO', 'UNKNOWN'] })
    familyHistoryAsthma?: 'YES' | 'NO' | 'UNKNOWN';

    @IsOptional()
    @ApiProperty({ enum: ['YES', 'NO', 'UNKNOWN'] })
    familyHistoryEpilepsy?: 'YES' | 'NO' | 'UNKNOWN';

    @IsOptional()
    @ApiProperty({ enum: ['YES', 'NO', 'UNKNOWN'] })
    familyHistoryTuberculosis?: 'YES' | 'NO' | 'UNKNOWN';
}
