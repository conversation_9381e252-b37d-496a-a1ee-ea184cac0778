import { Module, HttpModule } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { UserService } from './user.service';
import { UserController } from './user.controller';
import { ConfigManagerModule } from '@nestjsplus/config';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { UserJwtStrategy } from './jwt.strategy';
import { LocalUserStrategy } from './local.user.strategy';
import { SessionService } from 'src/session/session.service';
import { PhoneLoginService } from 'src/phone-login/phone-login.service';
import { SmsService } from 'src/sms/sms.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
    USER_COLLECTION_NAME,
    SIGNIN_PROVIDER_COLLECTION_NAME,
    CONSTRAINTS_USER_COLLECTION_NAME,
    CONSTRAINTS_PROVIDER_COLLECTION_NAME,
    USER_PROFILE_COLLECTION_NAME,
    HOC_VI_COLLECTION_NAME,
    VI_TRI_CONG_VIEC_COLLECTION_NAME,
    ORG_PROFILE_COLLECTION_NAME,
    REFERRAL_CODE_REGISTER_COLLECTION_NAME,
    USER_APP_COLLECTION_NAME,
    USER_REQUEST_COLLECTION_NAME,
    CONTACT_US_COLLECTION_NAME,
    USER_DOCTOR_COLLECTION_NAME,
    USER_LOGIN_LOG_COLLECTION_NAME,
    USER_LOCKED_COLLECTION_NAME,
    VISA_USER_COLLECTION_NAME,
} from './schemas/constants';
import { UserSchema } from './schemas/user.schema';
import { SignInProviderSchema } from './schemas/signin-provider.schema';
import { UserConstraintSchema } from './schemas/user-constraints.schema';
import { ProviderConstraintSchema } from './schemas/provider-constraints.schema';
import { UserProfileSchema } from './schemas/user-profile.schema';
import { USER_ACCOUNT_COLLECTION_NAME } from 'src/user-account/schemas/constants';
import { UserAccountSchema } from 'src/user-account/schemas/user-account.schema';
import { FilesService } from 'src/files/files.service';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigGridFSMullterService } from 'src/config/config.gridfs.multer.service';
import { HocViSchema } from './schemas/hoc-vi.schema';
import { ViTriCongViecSchema } from './schemas/vi-tri-cong-viec.schema';
import { BOOKING_COLLECTION_NAME, BOOKING_ORDER_COLLECTION_NAME, CONVERT_USER_CSKH, NEW_BILL_LOG_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { ReferralCodeService } from 'src/referral-code/referral-code.service';
import { REFERRAL_CODE_COLLECTION_NAME } from 'src/referral-code/schemas/constants';
import { ReferralCodeSchema } from 'src/referral-code/schemas/referral-code.schema';
import {
    SYNC_DALIEU_PATIENT,
    SYNC_DALIEU_BOOKING,
    SYNC_NHI_DONG_1_PATIENT,
    SYNC_NHI_DONG_1_BOOKING,
    SYNC_DHYD_PATIENT,
    SYNC_DHYD_BOOKING,
} from 'src/event/schemas/constants';
import { SyncDaLieuPatientSchema } from 'src/event/schemas/sync-dalieu-patient.schema';
import { SyncDaLieuBookingSchema } from 'src/event/schemas/sync-dalieu-booking.schema';
import { OrgProfileSchema } from './schemas/org-profile.schema';
import { SyncNhiDong1PatientSchema } from 'src/event/schemas/sync-nhidong1-patient.schema';
import { SyncNhiDong1BookingSchema } from 'src/event/schemas/sync-nhidong1-booking.schema';
import { ReferralCodeRegisterSchema } from './schemas/referral-code-register';
import { PATIENT_PROFILE_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { PatientProfileSchema } from 'src/patient-mongo/schemas/patient-profile.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { SyncDHYDPatientSchema } from 'src/event/schemas/sync-dhyd-patient.schema';
import { SyncDHYDBookingSchema } from 'src/event/schemas/sync-dhyd-booking.schema';
import { BookingOrderSchema } from 'src/booking-gateway/schemas/booking-order';
import { NewBillLogSchema } from 'src/booking-gateway/schemas/new-bill-log.schema';
import { FirebaseConfigModule } from 'src/firebase-config/firebase-config.module';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { UserAppSchema } from './schemas/user-app.schema';
import { UserRequestsSchema } from './schemas/user-requests.schema';
import { ContactUsSchema } from './schemas/contact-us.schema';
import { UserDoctorSchema } from './schemas/user-doctor.schema';
import { DOCTOR_COLLECTION_NAME } from '../doctor-mongo/schemas/constants';
import { DoctorSchema } from '../doctor-mongo/schemas/doctor.schema';
import { PushDeviceSchema } from '../push-device/schemas/push-device.schema';
import { PUSH_DEVICE_COLLECTION_NAME } from '../push-device/schemas/constants';
import { UserLoginLogSchema } from './schemas/user-login-logs.schema';
import { UserLockedLogSchema } from './schemas/user-locked.schema';
import { VisaUserSchema } from './schemas/visa-users.schema';
import { ConvertUserCskhSchema } from '../booking-gateway/schemas/convert-user-cskh.schema';
import { CountrySchema } from 'src/country-mongo/schemas/country.schema';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { NationSchema } from 'src/nation-mongo/schemas/nation.schema';
import { NATION_COLLECTION_NAME } from 'src/nation-mongo/schemas/constants';
import { PROFESSION_COLLECTION_NAME } from 'src/profession-mongo/schemas/constants';
import { ProfessionSchema } from 'src/profession-mongo/schemas/profession.schema';

@Module({
    imports: [
        HttpModule,
        PassportModule.register({ defaultStrategy: 'user-jwt' }),
        JwtModule.registerAsync({
            useExisting: JwtUserConfigService,
        }),
        MulterModule.registerAsync({
            useExisting: ConfigGridFSMullterService,
        }),
        MongooseModule.forFeature([
            { name: USER_COLLECTION_NAME, schema: UserSchema },
            { name: USER_PROFILE_COLLECTION_NAME, schema: UserProfileSchema },
            { name: ORG_PROFILE_COLLECTION_NAME, schema: OrgProfileSchema },
            { name: SIGNIN_PROVIDER_COLLECTION_NAME, schema: SignInProviderSchema },
            { name: CONSTRAINTS_USER_COLLECTION_NAME, schema: UserConstraintSchema },
            { name: CONSTRAINTS_PROVIDER_COLLECTION_NAME, schema: ProviderConstraintSchema },
            { name: USER_ACCOUNT_COLLECTION_NAME, schema: UserAccountSchema },
            { name: HOC_VI_COLLECTION_NAME, schema: HocViSchema },
            { name: VI_TRI_CONG_VIEC_COLLECTION_NAME, schema: ViTriCongViecSchema },
            { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
            { name: REFERRAL_CODE_COLLECTION_NAME, schema: ReferralCodeSchema },
            { name: SYNC_DALIEU_PATIENT, schema: SyncDaLieuPatientSchema },
            { name: SYNC_DALIEU_BOOKING, schema: SyncDaLieuBookingSchema },
            { name: SYNC_NHI_DONG_1_PATIENT, schema: SyncNhiDong1PatientSchema },
            { name: SYNC_NHI_DONG_1_BOOKING, schema: SyncNhiDong1BookingSchema },
            { name: SYNC_DHYD_PATIENT, schema: SyncDHYDPatientSchema },
            { name: SYNC_DHYD_BOOKING, schema: SyncDHYDBookingSchema },
            { name: REFERRAL_CODE_REGISTER_COLLECTION_NAME, schema: ReferralCodeRegisterSchema },
            { name: PATIENT_PROFILE_COLLECTION_NAME, schema: PatientProfileSchema },
            { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
            { name: BOOKING_ORDER_COLLECTION_NAME, schema: BookingOrderSchema },
            { name: NEW_BILL_LOG_COLLECTION_NAME, schema: NewBillLogSchema },
            { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
            { name: USER_APP_COLLECTION_NAME, schema: UserAppSchema },
            { name: USER_REQUEST_COLLECTION_NAME, schema: UserRequestsSchema },
            { name: CONTACT_US_COLLECTION_NAME, schema: ContactUsSchema },
            { name: USER_DOCTOR_COLLECTION_NAME, schema: UserDoctorSchema },
            { name: DOCTOR_COLLECTION_NAME, schema: DoctorSchema },
            { name: PUSH_DEVICE_COLLECTION_NAME, schema: PushDeviceSchema },
            { name: USER_LOGIN_LOG_COLLECTION_NAME, schema: UserLoginLogSchema },
            { name: USER_LOCKED_COLLECTION_NAME, schema: UserLockedLogSchema },
            { name: VISA_USER_COLLECTION_NAME, schema: VisaUserSchema },
            { name: CONVERT_USER_CSKH, schema: ConvertUserCskhSchema },
            { name: COUNTRY_COLLECTION_NAME, schema: CountrySchema},
            { name: NATION_COLLECTION_NAME, schema: NationSchema},
            { name: PROFESSION_COLLECTION_NAME, schema: ProfessionSchema},
        ]),
        FirebaseConfigModule,
    ],
    providers: [
        UserService,
        LocalUserStrategy,
        UserJwtStrategy,
        SessionService,
        PhoneLoginService,
        SmsService,
        FilesService,
        ReferralCodeService,
        GlobalSettingService,
    ],
    controllers: [UserController],
    exports: [UserService, LocalUserStrategy, UserJwtStrategy, SessionService, PhoneLoginService, FilesService, ReferralCodeService],
})
export class UserModule {}
