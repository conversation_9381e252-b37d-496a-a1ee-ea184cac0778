import { ArgumentMetadata, Injectable, PipeTransform } from '@nestjs/common';

@Injectable()
export class SearchDtoPipe implements PipeTransform<any> {
    async transform(value: any, { metatype }: ArgumentMetadata) {
        if (!metatype || !this.toValidate(metatype)) {
            return value;
        }

        value.hospital_ids = value.hospital_ids || value.hospitalIds;
        value.subject_ids = value.subject_ids || value.subjectIds;
        value.service_ids = value.service_ids || value.serviceIds;
        value.doctor_ids = value.doctor_ids || value.doctorIds;
        value.service_type_ids = value.service_type_ids || value.serviceTypeIds;
        value.tree_ids = value.tree_ids || value.treeIds;
        value.role_ids = value.role_ids || value.roleIds;
        value.tag_ids = value.tag_ids || value.tagIds;
        value.city_ids = value.city_ids || value.cityIds;

        delete value.hospitalIds;
        delete value.subjectIds;
        delete value.serviceIds;
        delete value.doctorIds;
        delete value.serviceTypeIds;
        delete value.treeIds;
        delete value.roleIds;
        delete value.tagIds;
        delete value.cityIds;

        return value;
    }

    private toValidate(metatype: Function): boolean {
        const types: Function[] = [String, Boolean, Number, Array, Object];
        return !types.includes(metatype);
    }
}
