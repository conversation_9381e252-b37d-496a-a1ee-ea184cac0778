import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { BOOKING_ORDER_COLLECTION_NAME } from './constants';
import { PATIENT_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const BookingOrderSchema = new Schema({
    id: { type: String },
    orderCode: { type: String },
    transactionId: { type: String },
    status: { type: Number, default: 0 },
    paymentStatus: { type: Number },
    paymentMessage: { type: String },
    patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
    patientId: { type: String },
    /* patient version */
    patientVersionId: { type: String },
    patientVersion: { type: Schema.Types.ObjectId, ref: PATIENT_VERSION_COLLECTION_NAME },
    userId: { type: String },
    partnerId: { type: String },
    partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
    appId: { type: String },
    platform: { type: String },
    invoiceId: { type: String },
    invoiceCode: { type: String },
    visible: { type: Boolean, default: true }, /* hiển thị phiếu khám hay ko */
    checkInRoom: { type: Object, default: {} },
    syncStatus: { type: String },
    syncDate: { type: Date },
    content: { type: String },
    note: { type: String, default: '' },
    cskhUserId: { type: String },
}, {
    collection: BOOKING_ORDER_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
