import { Module, HttpModule } from '@nestjs/common';
import { SyncBookingService } from './sync-booking.service';
import { SyncBookingController } from './sync-booking.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
    BOOKING_COLLECTION_NAME,
    PAYMENT_COLLECTION_NAME,
    BOOKING_SEARCH_COLLECTION_NAME,
    SECTION_COLLECTION_NAME,
    BOOKING_ORDER_COLLECTION_NAME,
    NEW_BILL_LOG_COLLECTION_NAME,
} from 'src/booking-gateway/schemas/constants';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { ROOM_COLLECTION_NAME } from 'src/room-mongo/schemas/constants';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { ServiceSchema } from 'src/service-mongo/schemas/service.schema';
import { SubjectSchema } from 'src/subject-mongo/schemas/subject.schema';
import { RoomSchema } from 'src/room-mongo/schemas/room.schema';
import { DoctorSchema } from 'src/doctor-mongo/schemas/doctor.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { PaymentSchema } from 'src/booking-gateway/schemas/payment.schema';
import { CONSTRAINTS_BOOKING_CODE_COLLECTION_NAME, CONSTRAINTS_PAYMENT_TRANSACTION_COLLECTION_NAME } from './schemas/constants';
import { ConstraintsBookingCodeSchema } from './schemas/constraints-booking-code.schema';
import { ConstraintsPaymentTransactionSchema } from './schemas/constraints-payment-transaction.schema';
import {
    SYNC_BOOKING_PROCESS,
    SYNC_BOOKING_SUCCESS,
    SYNC_BOOKING_PROCESS_FAILED,
    SYNC_SKIN_BOOKING_PROCESS,
    SYNC_SKIN_BOOKING_PROCESS_FAILED,
    SYNC_SKIN_BOOKING_SUCCESS,
    SYNC_USER_PROCESS,
    SYNC_USER_PROCESS_FAILED,
    SYNC_USER_SUCCESS,
    SYNC_DALIEU_PATIENT,
    SYNC_DALIEU_BOOKING,
    SYNC_ND1_BOOKING_PROCESS,
    SYNC_ND1_BOOKING_PROCESS_FAILED,
    SYNC_ND1_BOOKING_SUCCESS,
    SEND_MAIL_OR_SMS,
    ADDRESS_TRACKING_COLLECTION_NAME,
    SYNC_NHI_DONG_1_PATIENT,
    SYNC_NHI_DONG_1_BOOKING,
    SYNC_DHYD_PATIENT,
    SYNC_DHYD_BOOKING,
} from 'src/event/schemas/constants';
import { SyncBookingProcessSchema } from 'src/event/schemas/sync-booking-process.schema';
import { SyncBookingSuccessSchema } from 'src/event/schemas/sync-booking-success.schema';
import { SyncBookingProcessFailedSchema } from 'src/event/schemas/sync-booking-process-failed.schema';
import { BookingSearchSchema } from 'src/booking-gateway/schemas/search-booking.schema';
import {
    PATIENT_COLLECTION_NAME,
    PATIENT_CODE_COLLECTION_NAME,
    PATIENT_VERSION_COLLECTION_NAME,
    RELATIVE_TYPE_COLLECTION_NAME,
    PATIENT_SEARCH_LOG_COLLECTION_NAME,
    PATIENT_PROFILE_COLLECTION_NAME,
} from 'src/patient-mongo/schemas/constants';
import { PatientSchema } from 'src/patient-mongo/schemas/patient.schema';
import { PatientCodeSchema } from 'src/patient-mongo/schemas/patient-codes.schema';
import { PatientVersionSchema } from 'src/patient-mongo/schemas/patient-version.schema';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { CountrySchema } from 'src/country-mongo/schemas/country.schema';
import { NATION_COLLECTION_NAME } from 'src/nation-mongo/schemas/constants';
import { NationSchema } from 'src/nation-mongo/schemas/nation.schema';
import { PROFESSION_COLLECTION_NAME } from 'src/profession-mongo/schemas/constants';
import { ProfessionSchema } from 'src/profession-mongo/schemas/profession.schema';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { CitySchema } from 'src/city-mongo/schemas/city.schema';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { DistrictSchema } from 'src/district-mongo/schemas/district.schema';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { WardSchema } from 'src/ward-mongo/schemas/ward.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { RelativeSchema } from 'src/patient-mongo/schemas/relative-mongo.schema';
import { SessionService } from 'src/session/session.service';
import { SyncSkinBookingProcessSchema } from 'src/event/schemas/sync-skin-booking-process.schema';
import { SyncSkinBookingProcessFailedSchema } from 'src/event/schemas/sync-skin-booking-process-failed.schema';
import { SyncSkinBookingSuccessSchema } from 'src/event/schemas/sync-skin-booking-success.schema';
import { SyncUserProcessSchema } from 'src/event/schemas/sync-user-process.schema';
import { SyncUserProcessFailedSchema } from 'src/event/schemas/sync-user-process-failed.schema';
import { SyncUserSuccessSchema } from 'src/event/schemas/sync-user-success.schema';
import { SyncUserService } from 'src/sync-user/sync-user.service';
import { UserService } from 'src/user/user.service';
import {
    USER_COLLECTION_NAME,
    USER_PROFILE_COLLECTION_NAME,
    CONSTRAINTS_USER_COLLECTION_NAME,
    CONSTRAINTS_PROVIDER_COLLECTION_NAME,
    SIGNIN_PROVIDER_COLLECTION_NAME,
    HOC_VI_COLLECTION_NAME,
    VI_TRI_CONG_VIEC_COLLECTION_NAME,
    ORG_PROFILE_COLLECTION_NAME,
    REFERRAL_CODE_REGISTER_COLLECTION_NAME,
    USER_APP_COLLECTION_NAME,
    USER_REQUEST_COLLECTION_NAME,
} from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { UserProfileSchema } from 'src/user/schemas/user-profile.schema';
import { UserConstraintSchema } from 'src/user/schemas/user-constraints.schema';
import { ProviderConstraintSchema } from 'src/user/schemas/provider-constraints.schema';
import { SignInProviderSchema } from 'src/user/schemas/signin-provider.schema';
import { USER_ACCOUNT_COLLECTION_NAME } from 'src/user-account/schemas/constants';
import { UserAccountSchema } from 'src/user-account/schemas/user-account.schema';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { PhoneLoginService } from 'src/phone-login/phone-login.service';
import { SmsService } from 'src/sms/sms.service';
import { PatientMongoService } from 'src/patient-mongo/patient-mongo.service';
import { PatientSearchLogSchema } from 'src/patient-mongo/schemas/patient-search-log.schema';
import { PatientService } from 'src/patient/patient.service';
import { FilesService } from 'src/files/files.service';
import { HocViSchema } from 'src/user/schemas/hoc-vi.schema';
import { ViTriCongViecSchema } from 'src/user/schemas/vi-tri-cong-viec.schema';
import {
  SYNC_USER_TRUNGVUONG_PROCESS_COLLECTION_NAME, SYNC_USER_TRUNGVUONG_PROCESS_FAILED_COLLECTION_NAME,
  SYNC_USER_TRUNGVUONG_SUCCESS_COLLECTION_NAME,
  SYNC_USER_TRUNGVUONG_UPGRADE_COLLECTION_NAME,
} from 'src/sync-trung-vuong-medpro/schemas/constants';
import { SyncUserTrungVuongProcessSchema } from 'src/sync-trung-vuong-medpro/schemas/sync-user-trungvuong-process.schema';
import { SyncUserTrungVuongProcessFailedSchema } from 'src/sync-trung-vuong-medpro/schemas/sync-booking-process-failed.schema';
import { SyncUserTrungVuongSuccessSchema } from 'src/sync-trung-vuong-medpro/schemas/sync-user-trungvuong-success.schema';
import { SyncUserTrungVuongUpgradeSchema } from 'src/sync-trung-vuong-medpro/schemas/sync-user-trungvuong-upgrade.schema';
import {
    SYNC_USER_DALIEU_UPGRADE_COLLECTION_NAME,
    SYNC_USER_DALIEU_PROCESS_COLLECTION_NAME,
    SYNC_USER_DALIEU_PROCESS_FAILED_COLLECTION_NAME,
    SYNC_USER_DALIEU_SUCCESS_COLLECTION_NAME,
} from 'src/sync-da-lieu-medpro/schemas/constants';
import { SyncUserDaLieuUpgradeSchema } from 'src/sync-da-lieu-medpro/schemas/sync-user-dalieu-upgrade.schema';
import { SyncUserDaLieuProcessSchema } from 'src/sync-da-lieu-medpro/schemas/sync-user-dalieu-process.schema';
import { SyncUserDaLieuProcessFailedSchema } from 'src/sync-da-lieu-medpro/schemas/sync-user-dalieu-process-failed.schema';
import { SyncUserDaLieuSuccessSchema } from 'src/sync-da-lieu-medpro/schemas/sync-user-dalieu-success.schema';
import { ReferralCodeSchema } from 'src/referral-code/schemas/referral-code.schema';
import { REFERRAL_CODE_COLLECTION_NAME } from 'src/referral-code/schemas/constants';
import { ReferralCodeService } from 'src/referral-code/referral-code.service';
import { SyncDaLieuPatientSchema } from 'src/event/schemas/sync-dalieu-patient.schema';
import { SyncDaLieuBookingSchema } from 'src/event/schemas/sync-dalieu-booking.schema';
import { OrgProfileSchema } from 'src/user/schemas/org-profile.schema';
import { SyncNd1BookingProcessSchema } from 'src/event/schemas/sync-nd1-booking-process.schema';
import { SyncNd1BookingProcessFailedSchema } from 'src/event/schemas/sync-nd1-booking-process-failed.schema';
import { SyncNd1BookingSuccessSchema } from 'src/event/schemas/sync-nd1-booking-success.schema';
import { SendMailOrSmsSchema } from 'src/event/schemas/send-mail-or-sms.schema';
import { AddressTrackingSchema } from 'src/event/schemas/address-tracking.schema';
import { SyncNhiDong1PatientSchema } from 'src/event/schemas/sync-nhidong1-patient.schema';
import { SyncNhiDong1BookingSchema } from 'src/event/schemas/sync-nhidong1-booking.schema';
import { SectionSchema } from 'src/booking-gateway/schemas/section.schema';
import { ReferralCodeRegisterSchema } from 'src/user/schemas/referral-code-register';
import { PatientProfileSchema } from 'src/patient-mongo/schemas/patient-profile.schema';
import { SyncDHYDPatientSchema } from 'src/event/schemas/sync-dhyd-patient.schema';
import { SyncDHYDBookingSchema } from 'src/event/schemas/sync-dhyd-booking.schema';
import { BookingOrderSchema } from 'src/booking-gateway/schemas/booking-order';
import { NewBillLogSchema } from 'src/booking-gateway/schemas/new-bill-log.schema';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { UserAppSchema } from 'src/user/schemas/user-app.schema';
import { UserRequestsSchema } from 'src/user/schemas/user-requests.schema';
import { PatientMongoModule } from '../patient-mongo/patient-mongo.module';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [
    HttpModule,
    PassportModule.register({ defaultStrategy: 'user-jwt' }),
    JwtModule.registerAsync({
      useExisting: JwtUserConfigService,
    }),
    MongooseModule.forFeature([
      { name: USER_COLLECTION_NAME, schema: UserSchema },
      { name: USER_PROFILE_COLLECTION_NAME, schema: UserProfileSchema },
      { name: ORG_PROFILE_COLLECTION_NAME, schema: OrgProfileSchema },
      { name: CONSTRAINTS_USER_COLLECTION_NAME, schema: UserConstraintSchema },
      { name: CONSTRAINTS_PROVIDER_COLLECTION_NAME, schema: ProviderConstraintSchema },
      { name: SIGNIN_PROVIDER_COLLECTION_NAME, schema: SignInProviderSchema },
      { name: USER_ACCOUNT_COLLECTION_NAME, schema: UserAccountSchema },
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
      { name: BOOKING_SEARCH_COLLECTION_NAME, schema: BookingSearchSchema },
      { name: PAYMENT_COLLECTION_NAME, schema: PaymentSchema },
      { name: SERVICE_COLLECTION_NAME, schema: ServiceSchema },
      { name: SECTION_COLLECTION_NAME, schema: SectionSchema },
      { name: SUBJECT_COLLECTION_NAME, schema: SubjectSchema },
      { name: ROOM_COLLECTION_NAME, schema: RoomSchema },
      { name: DOCTOR_COLLECTION_NAME, schema: DoctorSchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
      { name: CONSTRAINTS_BOOKING_CODE_COLLECTION_NAME, schema: ConstraintsBookingCodeSchema },
      { name: CONSTRAINTS_PAYMENT_TRANSACTION_COLLECTION_NAME, schema: ConstraintsPaymentTransactionSchema },
      { name: SYNC_BOOKING_PROCESS, schema: SyncBookingProcessSchema },
      { name: SYNC_BOOKING_PROCESS_FAILED, schema: SyncBookingProcessFailedSchema },
      { name: SYNC_BOOKING_SUCCESS, schema: SyncBookingSuccessSchema },
      { name: SYNC_SKIN_BOOKING_PROCESS, schema: SyncSkinBookingProcessSchema },
      { name: SYNC_SKIN_BOOKING_PROCESS_FAILED, schema: SyncSkinBookingProcessFailedSchema },
      { name: SYNC_SKIN_BOOKING_SUCCESS, schema: SyncSkinBookingSuccessSchema },
      { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
      { name: PATIENT_CODE_COLLECTION_NAME, schema: PatientCodeSchema },
      { name: PATIENT_VERSION_COLLECTION_NAME, schema: PatientVersionSchema },
      { name: PROFESSION_COLLECTION_NAME, schema: ProfessionSchema },
      { name: NATION_COLLECTION_NAME, schema: NationSchema },
      { name: COUNTRY_COLLECTION_NAME, schema: CountrySchema },
      { name: CITY_COLLECTION_NAME, schema: CitySchema },
      { name: DISTRICT_COLLECTION_NAME, schema: DistrictSchema },
      { name: WARD_COLLECTION_NAME, schema: WardSchema },
      { name: RELATIVE_TYPE_COLLECTION_NAME, schema: RelativeSchema },
      { name: SYNC_USER_PROCESS, schema: SyncUserProcessSchema },
      { name: SYNC_USER_PROCESS_FAILED, schema: SyncUserProcessFailedSchema },
      { name: SYNC_USER_SUCCESS, schema: SyncUserSuccessSchema },
      { name: PATIENT_SEARCH_LOG_COLLECTION_NAME, schema: PatientSearchLogSchema },
      { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
      { name: HOC_VI_COLLECTION_NAME, schema: HocViSchema },
      { name: VI_TRI_CONG_VIEC_COLLECTION_NAME, schema: ViTriCongViecSchema },
      { name: REFERRAL_CODE_REGISTER_COLLECTION_NAME, schema: ReferralCodeRegisterSchema },
      { name: SYNC_USER_TRUNGVUONG_PROCESS_COLLECTION_NAME, schema: SyncUserTrungVuongProcessSchema },
      { name: SYNC_USER_TRUNGVUONG_PROCESS_FAILED_COLLECTION_NAME, schema: SyncUserTrungVuongProcessFailedSchema },
      { name: SYNC_USER_TRUNGVUONG_SUCCESS_COLLECTION_NAME, schema: SyncUserTrungVuongSuccessSchema },
      { name: SYNC_USER_TRUNGVUONG_UPGRADE_COLLECTION_NAME, schema: SyncUserTrungVuongUpgradeSchema },
      { name: SYNC_USER_DALIEU_PROCESS_COLLECTION_NAME, schema: SyncUserDaLieuProcessSchema },
      { name: SYNC_USER_DALIEU_PROCESS_FAILED_COLLECTION_NAME, schema: SyncUserDaLieuProcessFailedSchema },
      { name: SYNC_USER_DALIEU_SUCCESS_COLLECTION_NAME, schema: SyncUserDaLieuSuccessSchema },
      { name: SYNC_USER_DALIEU_UPGRADE_COLLECTION_NAME, schema: SyncUserDaLieuUpgradeSchema },
      { name: REFERRAL_CODE_COLLECTION_NAME, schema: ReferralCodeSchema },
      { name: SYNC_DALIEU_PATIENT, schema: SyncDaLieuPatientSchema },
      { name: SYNC_DALIEU_BOOKING, schema: SyncDaLieuBookingSchema },
      { name: SYNC_ND1_BOOKING_PROCESS, schema: SyncNd1BookingProcessSchema },
      { name: SYNC_ND1_BOOKING_PROCESS_FAILED, schema: SyncNd1BookingProcessFailedSchema },
      { name: SYNC_ND1_BOOKING_SUCCESS, schema: SyncNd1BookingSuccessSchema },
      { name: SEND_MAIL_OR_SMS, schema: SendMailOrSmsSchema },
      { name: ADDRESS_TRACKING_COLLECTION_NAME, schema: AddressTrackingSchema },
      { name: SYNC_NHI_DONG_1_PATIENT, schema: SyncNhiDong1PatientSchema },
      { name: SYNC_NHI_DONG_1_BOOKING, schema: SyncNhiDong1BookingSchema },
      { name: SYNC_DHYD_PATIENT, schema: SyncDHYDPatientSchema },
      { name: SYNC_DHYD_BOOKING, schema: SyncDHYDBookingSchema },
      { name: PATIENT_PROFILE_COLLECTION_NAME, schema: PatientProfileSchema },
      { name: BOOKING_ORDER_COLLECTION_NAME, schema: BookingOrderSchema },
      { name: NEW_BILL_LOG_COLLECTION_NAME, schema: NewBillLogSchema },
      { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
      { name: USER_APP_COLLECTION_NAME, schema: UserAppSchema },
      { name: USER_REQUEST_COLLECTION_NAME, schema: UserRequestsSchema },
    ]),
    PatientMongoModule,
    UserModule,
  ],
  providers: [SyncBookingService, SessionService, SyncUserService, PhoneLoginService, FilesService,
    SmsService, PatientService, ReferralCodeService, GlobalSettingService],
  controllers: [SyncBookingController],
})
export class SyncBookingModule { }
