import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { USER_COLLECTION_NAME } from './constants';
import { PATIENT_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { PROFESSION_COLLECTION_NAME } from 'src/profession-mongo/schemas/constants';
import { NATION_COLLECTION_NAME } from 'src/nation-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const UserSchema = new Schema(
    {
        zaloUserResource: Object,
        username: String,
        password: String,
        salt: String,
        email: String,
        phone: String,
        fullname: String,
        patients: [{ type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME }],
        prevPatients: [{ type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME }],
        momoId: String,
        medproId: String,
        isCS: { type: Boolean, default: false },
        isCare247: { type: Boolean, default: false },
        userIdV1: { type: Number, default: 0 },
        tvUser: { type: Schema.Types.ObjectId },
        daLieuUser: { type: Number },
        nd1User: { type: Number },
        umcUser: { type: Number },
        prevUser: { type: Schema.Types.ObjectId },
        nextStep: { type: String },
        referralCode: { type: String },
        partners: [{ type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME }],
        viettelMoneyId: { type: String },
        profileImage: { type: String },
        zaloId: String,
        surname: String,
        name: String,
        isSaveEmail: { type: Boolean, default: false },
        defaultPatient: { type: Schema.Types.Mixed },
        country_code: { type: String },
        country: [{ type: Schema.Types.Mixed, ref: COUNTRY_COLLECTION_NAME }],
        dantoc_id: { type: String },
        nation: [{ type: Schema.Types.Mixed, ref: NATION_COLLECTION_NAME }],
        insuranceId: { type: String },
        profession_id: { type: String },
        profession: [{ type: Schema.Types.Mixed, ref: PROFESSION_COLLECTION_NAME }],
    },
    {
        collection: USER_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
