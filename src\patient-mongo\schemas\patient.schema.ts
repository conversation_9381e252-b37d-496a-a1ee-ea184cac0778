import * as mongoose from 'mongoose';
import * as json<PERSON><PERSON>o from '@meanie/mongoose-to-json';
import { PATIENT_COLLECTION_NAME, MEDPRO } from './constants';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { PROFESSION_COLLECTION_NAME } from 'src/profession-mongo/schemas/constants';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { NATION_COLLECTION_NAME } from 'src/nation-mongo/schemas/constants';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';

const Schema = mongoose.Schema;

const PatientSchema = new Schema({
    id: String,
    surname: String,
    name: String,
    mobile: String,
    mobileLocaleIso: String,
    birthdate: String,
    birthyear: Number,
    sex: Number,
    cmnd: String,
    email: String,
    code: String,
    patientCode: String,
    profession_id: String,
    profession: { type: Schema.Types.ObjectId, ref: PROFESSION_COLLECTION_NAME },
    country_code: String,
    country_id: String,
    country: { type: Schema.Types.ObjectId, ref: COUNTRY_COLLECTION_NAME },
    dantoc_id: String,
    nation: { type: Schema.Types.ObjectId, ref: NATION_COLLECTION_NAME },
    city_id: String,
    city: { type: Schema.Types.ObjectId, ref: CITY_COLLECTION_NAME },
    district_id: String,
    district: { type: Schema.Types.ObjectId, ref: DISTRICT_COLLECTION_NAME },
    ward_id: String,
    ward: { type: Schema.Types.ObjectId, ref: WARD_COLLECTION_NAME },
    address: String,
    partnerId: { type: String, default: MEDPRO },
    sourceId: String,
    userId: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    relation: {
        relative_name: { type: String, default: '' },
        relative_mobile: { type: String, default: '' },
        relativeMobileLocaleIso: { type: String, default: '' },
        relative_type_id: { type: String, default: '' },
        relative_email: { type: String, default: '' },
    },
    /* dành cho các bệnh viện cũ */
    patientIdV1: { type: Number, default: 0 },
    patientIdV1DaLieu: { type: Number, default: 0 },
    patientIdV1CTCH: { type: Number, default: 0 },
    patientIdV1ThuDuc: { type: Number, default: 0 },
    patientIdV1UMC: { type: Number, default: 0 },
    /* cskh */
    cskhUserId: { type: String },
    isExam: { type: Boolean, default: false },
    insuranceId: { type: String },
}, {
    collection: PATIENT_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);

PatientSchema.index({ surname: 'text', name: 'text' });

export { PatientSchema };
