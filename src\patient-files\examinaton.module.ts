import { HttpModule, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { PassportModule } from '@nestjs/passport';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { ConfigsS3Service } from 'src/config/configs.S3.service';
import { FilesService } from 'src/files/files.service';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { GLOBAL_SETTING_COLLECTION_NAME, GLOBAL_SETTING_LOCALE_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingLocaleSchema } from 'src/global-setting/schemas/global-setting-locale.schema';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { PatientCodeSchema } from 'src/patient-mongo/schemas/patient-codes.schema';
import { PatientSchema } from 'src/patient-mongo/schemas/patient.schema';
import { PatientService } from 'src/patient/patient.service';
import { PhoneLoginService } from 'src/phone-login/phone-login.service';
import { ReferralCodeService } from 'src/referral-code/referral-code.service';
import { REFERRAL_CODE_COLLECTION_NAME } from 'src/referral-code/schemas/constants';
import { ReferralCodeSchema } from 'src/referral-code/schemas/referral-code.schema';
import { SessionService } from 'src/session/session.service';
import { SmsService } from 'src/sms/sms.service';
import { UserJwtStrategy } from 'src/user/jwt.strategy';
import { LocalUserStrategy } from 'src/user/local.user.strategy';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { UserModule } from '../user/user.module';
import { ExaminatonController } from './examinaton.controller';
import { ExaminatonService } from './examinaton.service';
import { UploadCdnImagePatientSchema } from './schemas/cdn-image-patient';
import {
    HEALTH_INDEX_COLLECTION_NAME,
    LIST_CDN_IMAGE_COLLECTION_NAME,
    PATIENT_CODE_COLLECTION_NAME,
    PATIENT_COLLECTION_NAME,
    PATIENT_EXAMINATION_COLLECTION_NAME,
    PENDING_PATINET_HEALTH_INDEX,
} from './schemas/constants';
import { HealthIndexSchema } from './schemas/health-index.schema';
import { ExaminationResultSchema } from './schemas/update-examination';
import { PendingPatientSchema } from './schemas/pending-patient.schema';

@Module({
    imports: [
        HttpModule,
        ConfigModule,
        MongooseModule.forFeature([
            { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
            { name: LIST_CDN_IMAGE_COLLECTION_NAME, schema: UploadCdnImagePatientSchema },
            { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
            { name: PATIENT_EXAMINATION_COLLECTION_NAME, schema: ExaminationResultSchema },
            { name: PATIENT_CODE_COLLECTION_NAME, schema: PatientCodeSchema },
            { name: USER_COLLECTION_NAME, schema: UserSchema },
            { name: LIST_CDN_IMAGE_COLLECTION_NAME, schema: UploadCdnImagePatientSchema },
            { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
            { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
            { name: REFERRAL_CODE_COLLECTION_NAME, schema: ReferralCodeSchema },
            { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
            { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
            { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
            { name: HEALTH_INDEX_COLLECTION_NAME, schema: HealthIndexSchema },
            { name: GLOBAL_SETTING_LOCALE_COLLECTION_NAME, schema: GlobalSettingLocaleSchema },
            { name: PENDING_PATINET_HEALTH_INDEX, schema: PendingPatientSchema },
        ]),
        UserModule,
    ],
    controllers: [ExaminatonController],
    providers: [
        ExaminatonService,
        PatientService,
        SessionService,
        FilesService,
        LocalUserStrategy,
        UserJwtStrategy,
        PhoneLoginService,
        SmsService,
        ReferralCodeService,
        ConfigsS3Service,
        GlobalSettingService,
    ],
    exports: [ExaminatonService],
})
export class ExaminatonModule {}
