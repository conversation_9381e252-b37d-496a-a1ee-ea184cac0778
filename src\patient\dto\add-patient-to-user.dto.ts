import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsJWT } from 'class-validator';
import { Transform } from 'class-transformer';
export class AddPatientToUserDTO {

    @ApiProperty({
        description: 'Secret key để add patient to users',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @IsJWT({
        message: 'Vui lòng gửi lên đúng dịnh dạng JWT',
    })
    readonly secretKey: string;

    @ApiProperty({
        description: 'Kiểm tra xem phải trả kết quả chợ rẫy hay ko',
        required: false,
        type: Boolean,
        default: false,
    })
    @Transform(value => Boolean(value))
    readonly isExam?: boolean;

}
