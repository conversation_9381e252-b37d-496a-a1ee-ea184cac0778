import { Body, Controller, Get, HttpCode, Patch, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiQuery } from '@nestjs/swagger';
import { GetSyncPaymentFeeDto } from './dto/get-sync-payment-fee.dto';
import { RollBackPaymentFeeProcessDto } from './dto/rollback-process-payment-fee.dto';
import { SyncPaymentFeeService } from './sync-payment-fee.service';

@Controller('sync-payment-fee')
export class SyncPaymentFeeController {

    constructor(
        private service: SyncPaymentFeeService,
    ) {}

    @Get('process')
    @ApiOperation({ summary: 'L<PERSON>y danh sách đồng bộ PaymentFee process v1 -> v2' })
    async getSyncPaymentFeeProcess(@Query() formData: GetSyncPaymentFeeDto): Promise<any> {
        return this.service.getSyncPaymentFeeProcess(formData);
    }

    @Get('process-failed')
    @ApiOperation({ summary: 'L<PERSON>y danh sách đồng bộ PaymentFee process failed v1 -> v2' })
    async getSyncPaymentFeeProcessFailed(@Query() formData: GetSyncPaymentFeeDto): Promise<any> {
        return this.service.getSyncPaymentFeeProcessFailed(formData);
    }

    @Get('success')
    @ApiOperation({ summary: 'Lấy danh sách đồng bộ PaymentFee success v1 -> v2' })
    async getSyncPaymentFeeSuccess(@Query() formData: GetSyncPaymentFeeDto): Promise<any> {
        return this.service.getSyncPaymentFeeSuccess(formData);
    }

    @Patch('rollback-process-failed')
    @HttpCode(204)
    @ApiOperation({ summary: 'Rollback sync PaymentFee fail' })
    async rollBackSyncBookingFail(
        @Body() formData: RollBackPaymentFeeProcessDto,
    ): Promise<any> {
        return this.service.rollBackSyncPaymentFeeFailService(formData.processId);
    }

    @Get('sync-old-hostpital')
    @ApiQuery({
        name: 'transactionId',
    })
    async syncPaymentFeeToOldHospital(@Query('transactionId') transactionId: string): Promise<any> {
        return this.service.syncPaymentFeeToOldHospital({transactionId});
    }
}
