import { ApiProperty } from '@nestjs/swagger';
import { IsMobilePhone, IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';

export class ForgotPasswordDTO {

    @ApiProperty({
        description: 'Số điện thoại',
        required: true,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng nhập số điện thoại',
    })
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Số điện thoại không hợp lệ.',
    })
    readonly phone: string;

    @ApiProperty({
        description: 'Device Id',
        required: true,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng gửi lên deviceId',
    })
    readonly deviceId: string;

    captchaResponse: string
    captchaType?: string
}
