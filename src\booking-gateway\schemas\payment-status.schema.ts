import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PAYMENT_STATUS_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const PaymentStatusSchema = new Schema({
    statusCode: Number,
    statusText: String,
    priority: Number,
}, {
    collection: PAYMENT_STATUS_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
