import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDateString, ValidateIf } from 'class-validator';

export class OptionsDataReportDto {
    @ApiProperty({ required: false })
    @IsDateString({ strict: true }, {
        message: '<PERSON><PERSON><PERSON> bắt đầu',
    })
    @Transform(value => `${value}`.trim())
    @ValidateIf(o => o.startDate)
    startDate?: string;

    @ApiProperty({ required: false })
    @IsDateString({ strict: true }, {
        message: '<PERSON><PERSON><PERSON> kết thúc',
    })
    @Transform(value => `${value}`.trim())
    @ValidateIf(o => o.endDate)
    endDate?: string;
}
