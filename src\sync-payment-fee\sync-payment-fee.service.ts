import { UserService } from './../user/user.service';
import { ISyncPaymentFeeProcess } from './../event/intefaces/sync-payment-fee-process.interface';
import { HttpException, HttpService, HttpStatus, Inject, Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { UtilService } from 'src/config/util.service';
import { ISyncPaymentFeeProcessFailed } from 'src/event/intefaces/sync-payment-fee-process-failed.interface';
import { ISyncPaymentFeeSuccess } from 'src/event/intefaces/sync-payment-fee-success.interface';
import { SYNC_PAYMENT_FEE_PROCESS, SYNC_PAYMENT_FEE_PROCESS_FAILED, SYNC_PAYMENT_FEE_SUCCESS } from 'src/event/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { PAYMENT_FEE_TABLE_NAME, FEE_TABLE_NAME, SUBJECT_TABLE_NAME, PATIENT_TABLE_NAME, SYNC_PAYMENT_FEE_TO_OLD_HOSPITAL, USER_TABLE_NAME } from './constant';
import * as uuid from 'uuid';
import * as moment from 'moment';
import { IUser } from 'src/user/interfaces/user.interface';
import { SyncUserService } from 'src/sync-user/sync-user.service';
import { PaymentFeeGatewayService } from 'src/payment-fee-gateway/payment-fee-gateway.service';
import { PartnerOldHospital } from 'src/sync-booking/dto/old-hospital.dto';
import * as _ from 'lodash';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { HOSPITAL_FEE_COLLECTION_NAME, PAYMENT_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { IPayment } from 'src/booking-gateway/intefaces/payment.inteface';
import { BaseResponse } from './interfaces/base-response.interface';
import { SyncProcessStatus } from 'src/event/dto/sync-status.dto';
import { GetSyncPaymentFeeDto } from './dto/get-sync-payment-fee.dto';
import { RestfulAPIOldHospitalConfigService } from 'src/config/config.rest-api.old.hospital';
import { SessionService } from 'src/session/session.service';
import { first } from 'lodash';

@Injectable()
export class SyncPaymentFeeService implements OnModuleInit {
    private logger = new Logger(SyncPaymentFeeService.name);
    private readonly paymentFeeTable = PAYMENT_FEE_TABLE_NAME;
    private readonly feeTable = FEE_TABLE_NAME;
    private readonly patientTable = PATIENT_TABLE_NAME;
    private readonly subjectTable = SUBJECT_TABLE_NAME;
    private readonly userTable = USER_TABLE_NAME;
    private umcConfig: any[] = [];
    private skinConfig: any[] = [];
    private nd1Config: any[] = [];

    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(SYNC_PAYMENT_FEE_PROCESS_FAILED) private syncPaymentFeeProcessFailedModel: Model<ISyncPaymentFeeProcessFailed>,
        @InjectModel(SYNC_PAYMENT_FEE_PROCESS) private syncPaymentFeeProcessModel: Model<ISyncPaymentFeeProcess>,
        @InjectModel(SYNC_PAYMENT_FEE_SUCCESS) private syncPaymentFeeSuccessModel: Model<ISyncPaymentFeeSuccess>,
        @InjectModel(PAYMENT_COLLECTION_NAME) private paymentModel: Model<IPayment>,
        @InjectModel(HOSPITAL_FEE_COLLECTION_NAME) private hospitalFeeModel: Model<IPayment>,
        private restFulApiOldHosptal: RestfulAPIOldHospitalConfigService,
        private syncUserService: SyncUserService,
        private paymentFeeService: PaymentFeeGatewayService,
        private utilService: UtilService,
        private globalConfig: GlobalSettingService,
        private eventEmitter: EventEmitter2,
        private http: HttpService,
        private sessionService: SessionService,
        private userService: UserService,
    ) { }

    async onModuleInit() {
        const [
            umcConfig,
            skinConfig,
            nd1Config,
        ] = await Promise.all([
            this.globalConfig.findByKeyAndRepoName('PAYMENT_METHOD_CONFIG_UMC'),
            this.globalConfig.findByKeyAndRepoName('PAYMENT_METHOD_CONFIG_SKIN'),
            this.globalConfig.findByKeyAndRepoName('PAYMENT_METHOD_CONFIG_ND1'),
        ]);
        this.umcConfig = JSON.parse(umcConfig);
        this.skinConfig = JSON.parse(skinConfig);
        this.nd1Config = JSON.parse(nd1Config);
    }

    async syncOnePaymentFee(paymentFeeId: number, partnerId: string = 'umc'): Promise<any> {
        let dataFee: any;

        const getPartner = await this.hospitalModel.findOne({ partnerId }).exec();
        // this.logger.warn(`getPartner ${getPartner}`);
        if (!getPartner) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'getPartner',
                summary: 'Đồng bộ booking',
                nameParent: 'SyncOnePaymentFee',
                params: {
                    paymentFeeId,
                    partnerId,
                },
                errorBody: {},
                response: getPartner,
                message: `Không tìm thấy thông tin partner cho id: ${paymentFeeId}`,
            });
            throw new Error(`Không tìm thấy thông tin partner ${partnerId} cho paymentFeeId: ${paymentFeeId}`);
        }
        try {
            const [getPaymentFee, getFee] = await Promise.all([
                this.pkhPatientKnex(this.paymentFeeTable)
                    .select(
                        `${this.paymentFeeTable}.id as paymentIdV1`,
                        `${this.paymentFeeTable}.app as syncMethod`,
                        `${this.paymentFeeTable}.method_id as methodIdV1`,
                        `${this.paymentFeeTable}.transaction_code_tt as transactionId`,
                        `${this.paymentFeeTable}.amount as amount`,
                        `${this.paymentFeeTable}.amount_original as subTotal`,
                        `${this.paymentFeeTable}.amount_rate as amountRate`,
                        `${this.paymentFeeTable}.amount_add as amountAdd`,
                        `${this.paymentFeeTable}.amount_medpro as amountMedpro`,
                        `${this.paymentFeeTable}.fee_id as feeId`,
                        `${this.paymentFeeTable}.user_id as userIdV1`,
                        `${this.paymentFeeTable}.status as paymentStatus`,
                        `${this.paymentFeeTable}.status_process as statusProcess`,
                        `${this.paymentFeeTable}.result as jsonData`,
                        `${this.paymentFeeTable}.date_create as paymentDateCreate`,
                        `${this.paymentFeeTable}.date_update as paymentDateUpdate`,

                    )
                    .where(`${this.paymentFeeTable}.fee_id`, paymentFeeId)
                    .first(),
                this.pkhPatientKnex(this.feeTable)
                    .select(
                        `${this.feeTable}.sohoadon as feeCode`,
                        `${this.feeTable}.msbn as bvId`,

                        `${this.patientTable}.id as patientId`,
                        `${this.patientTable}.name as patientName`,
                        `${this.patientTable}.surname as patientSurname`,
                        `${this.patientTable}.cmnd as patientCmnd`,
                        `${this.patientTable}.sex as patientSex`,
                        `${this.patientTable}.mobile as patientMobile`,
                        `${this.patientTable}.email as patientEmail`,
                        `${this.patientTable}.address as patientAddress`,
                        `${this.patientTable}.birthyear as birthYear`,
                        `${this.patientTable}.birthdate as birthDate`,
                        `${this.patientTable}.bvdhyd_msbn as bvId`,
                    )
                    .innerJoin(`${this.patientTable}`, `${this.patientTable}.bvdhyd_msbn`, `${this.feeTable}.msbn`)
                    .where(`${this.feeTable}.id`, paymentFeeId)
                    .first(),
            ]);
            /* tiến hành transform info */
            // console.log('getBooking', getBooking);
            // this.logger.warn(`getPaymentFee ${getPaymentFee}`);
            if (getPaymentFee) {
                const {
                    paymentIdV1, syncMethod, methodIdV1, transactionId,
                    amount, subTotal, amountRate, amountAdd, amountMedpro,
                    feeId, userIdV1, paymentStatus, statusProcess, jsonData,
                    paymentDateCreate, paymentDateUpdate,
                } = getPaymentFee;
                const jsonDataObj = JSON.parse(jsonData);
                this.logger.warn(`jsonDataObj ${jsonDataObj}`);

                const {
                    patientId, patientName, patientSurname, patientCmnd,
                    patientSex, patientMobile, patientEmail, patientAddress, birthYear,
                    birthDate, bvId,
                } = getFee;

                if (syncMethod === 'medpro') {
                    throw new Error(`Id payment fee ${feeId}, appId: ${syncMethod} này từ v2 => v1. Không cần SYNC`);
                }

                /* tiến hành sync user */
                const user: IUser = await this.syncUserService.syncOneUser(userIdV1);
                // this.logger.warn(`sync user ${user}`);

                // this.logger.warn(`feeCode ${getFee.feeCode}`);
                if (getFee) {
                    try {
                        let session: any;
                        switch (partnerId) {
                            case 'nhidong1':
                                session = await this.sessionService.checkExistsNhiDong1SessionByUserID(userIdV1);
                                break;
                            case 'dalieuhcm':
                                session = await this.sessionService.checkExistsSkinSessionByUserID(userIdV1);
                                break;
                            case 'ctchhcm':
                                session = await this.sessionService.checkExistsCTCHSessionByUserID(userIdV1);
                                break;
                            case 'thuduc':
                                session = await this.sessionService.checkExistsThuDucSessionByUserID(userIdV1);
                                break;
                            case 'umc':
                                session = await this.sessionService.checkExistsUMCSessionByUserID(userIdV1);
                                break;
                            default:
                                session = {
                                    user_id: 0,
                                    access_token: '',
                                };
                                break;
                        }
                        const baseUrlSearchV1 = `${this.restFulApiOldHosptal.NhiDong1RestfulAPI()}/fee/search`;
                        // this.logger.warn(baseUrlSearchV1);
                        // dataFee = await this.paymentFeeService.search_v2(partnerId, getFee.feeCode);
                        const resDataFee = (await this.http.post(baseUrlSearchV1, {
                            userId: userIdV1, token: session.access_token, feeCode: getFee.feeCode,
                        }).toPromise()).data;
                        dataFee = first(resDataFee.history);
                        // this.logger.warn(dataFee);
                    } catch (error) {
                        this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                            name: 'search_v1',
                            summary: 'paymentFeeService search_v1',
                            nameParent: 'syncOnePaymentFee',
                            params: { partnerId, feeCode: getFee.feeCode },
                            errorBody: this.utilService.errorHandler(error),
                            response: {},
                            message: error?.message || `Exception khi tìm kiếm lại thông tin Fee: ${getFee.feeCode}`,
                        });
                        throw new Error(`Không tìm thấy dataFee từ search_v2 ${getFee.feeCode}`);
                    }
                }

                if (!dataFee) {
                    this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                        name: 'search_v1',
                        summary: 'paymentFeeService search_v1',
                        nameParent: 'syncOnePaymentFee',
                        params: { partnerId, feeCode: getFee.feeCode },
                        response: {},
                        message: `Không tìm thấy dataFee từ search_v2 ${getFee.feeCode}`,
                    });
                    throw new Error(`Không tìm thấy dataFee từ search_v2 ${getFee.feeCode}`);
                }

                try {
                    const newHospitalFee = new this.hospitalFeeModel({
                        id: dataFee.id,
                        // bill_id,
                        fee_code: dataFee.sohoadon,
                        amount: dataFee.amount,
                        bv_id: bvId,
                        // patientEMRNo,
                        fullname: `${patientSurname} ${patientName}`,
                        sex: `${patientSex}`,
                        birthdate: moment(birthDate).format('YYYY-MM-DD'),
                        subject_id: dataFee.subject.id,
                        subject_name: dataFee.subject.name,
                        // typeId
                        // mobile
                        email: patientEmail,
                        content: dataFee.content,
                        // place
                        // number
                        status: dataFee.status,
                        // is_sent
                        // message
                        // transactionId
                        // paymentStatus
                        // paymentMessage
                        userId: user._id,
                        // appId
                        partnerId,
                        // partner
                        // platform
                        // invoiceId
                        // invoiceCode
                        // invoiceCode
                        // responseLog
                        // isSyncPartner
                        // actionType
                    });
                    const hospitalFee = await newHospitalFee.save();
                    // this.logger.warn(`hospitalFee ${hospitalFee}`);

                    /* tiến hành insert vào trong payment */
                    const paymentMethodObj = this.getPaymentMethod(partnerId, methodIdV1, jsonData);
                    // this.logger.warn(`paymentMethodObj ${paymentMethodObj}`);

                    const returnObjMethod = this.transformPaymentMethod(paymentMethodObj);
                    // this.logger.warn(`returnObjMethod ${returnObjMethod}`);

                    const totalFee = (subTotal + amountMedpro) * amountRate + amountAdd;
                    const paymentTime = moment(paymentDateCreate, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD HH:mm:ss');

                    let type: number;
                    switch (partnerId) {
                        case 'umc':
                            type = 6;
                            break;
                        case 'choray':
                            type = 2;
                            break;
                        default:
                            type = 2;
                            break;
                    }

                    const vIPayment: any = {
                        id: uuid.v4().replace(/-/g, ''),
                        amount,
                        subTotal,
                        // medproFee,
                        totalFee,
                        feeCode: dataFee.sohoadon,
                        // transferFee,
                        transactionId,
                        status: this.returnPaymentStatus(paymentStatus),
                        appId: partnerId,
                        partnerId,
                        partner: getPartner._id,
                        date: moment(paymentDateCreate).toDate(),
                        // bookingId,
                        email: user.email,
                        ...returnObjMethod,
                        // patientId: patient.id,
                        // patient: patient.idRef,
                        paymentTime,
                        syncAt: moment().toDate(),
                        createdAt: moment(paymentDateCreate).toDate(),
                        updateAt: moment(paymentDateUpdate).toDate(),
                        hospitalFee: hospitalFee._id,
                        userId: user._id,
                        type,
                    };
                    const newPayment = new this.paymentModel(vIPayment);
                    const payment = await newPayment.save();
                    // this.logger.warn(`payment ${payment}`);

                    return {
                        payment,
                        hospitalFee,
                    };
                } catch (error) {
                    this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                        name: 'syncOnePaymentFee',
                        summary: '',
                        nameParent: 'syncOnePaymentFee',
                        params: {
                            paymentFeeId, partnerId,
                        },
                        errorBody: this.utilService.errorHandler(error),
                        response: getPartner,
                        message: error?.message,
                    });
                    // this.logger.warn(error);
                    throw error;
                }
            } else {
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'syncOnePaymentFee',
                    summary: '',
                    nameParent: 'syncOnePaymentFee',
                    params: {
                        paymentFeeId, partnerId,
                    },
                    errorBody: {},
                    response: getPartner,
                    message: `Không tìm thấy thông tin paymentFeeId: ${paymentFeeId}`,
                });
                throw new Error(`Không tìm thấy thông tin paymentFeeId: ${paymentFeeId}`);
            }
        } catch (error) {
            // console.log(error);
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'syncOnePaymentFee',
                summary: '',
                nameParent: 'syncOnePaymentFee',
                params: { paymentFeeId, partnerId },
                errorBody: this.utilService.errorHandler(error),
                response: getPartner,
                message: error?.message,
            });
            throw error;
        }
    }

    @OnEvent(SYNC_PAYMENT_FEE_TO_OLD_HOSPITAL)
    async syncPaymentFeeToOldHospital(data: any): Promise<any> {
        const { transactionId, partnerId = 'umc' } = data;
        try {
            const getPartner = await this.hospitalModel.findOne({ partnerId }).exec();
            // this.logger.warn(`getPartner ${getPartner}`);
            if (!getPartner) {
                this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                    name: 'getPartner',
                    summary: 'Đồng bộ booking',
                    nameParent: 'syncPaymentFeeToOldHospital',
                    params: {
                        transactionId,
                        partnerId,
                    },
                    errorBody: {},
                    response: getPartner,
                    message: `Không tìm thấy thông tin partner cho transaction: ${transactionId}`,
                });
                throw new Error(`Không tìm thấy thông tin partner ${partnerId} cho paymentFeeId: ${transactionId}`);
            }
            const payment = await this.paymentModel.findOne({ transactionId }).populate('hospitalFee').exec();
            const paymentObj = payment.toObject();
            const { hospitalFee } = paymentObj;
            if (paymentObj && hospitalFee) {
                const user: IUser = await this.userService.getUserByMongoUserId(paymentObj.userId);
                try {
                    await this.userService.getUserIdAndCreateSessionV1(paymentObj.appId, 'noConfig', user);
                } catch (error) {
                    this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                        name: 'getUserIdAndCreateSessionV1',
                        summary: '',
                        nameParent: 'syncPaymentFeeToOldHospital',
                        params: { transactionId, partnerId },
                        errorBody: this.utilService.errorHandler(error),
                        response: getPartner,
                        message: error?.message,
                    });
                    this.logger.warn('Kết nối tới database v1 bị lỗi. Không đồng bộ được user v2 -> v1');
                }
                const [userIdV1, subjectV1] = await Promise.all([
                    this.pkhPatientKnex(this.userTable)
                        .select(`${this.userTable}.id as id`)
                        .where(`${this.userTable}.username`, user.username)
                        .first(),
                    this.pkhPatientKnex(this.subjectTable)
                        .select(`${this.subjectTable}.code as subject_code`)
                        .where(`${this.subjectTable}.id`, hospitalFee.subject_id)
                        .first(),
                ]);

                this.logger.warn(userIdV1);
                this.logger.warn(subjectV1);

                const dataFeeV1 = {
                    msbn: hospitalFee.bv_id,
                    // msnv: ``,
                    sohoadon: hospitalFee.fee_code,
                    amount: hospitalFee.amount,
                    subject_code: subjectV1.subject_code,
                    content: hospitalFee.content,
                    // tenphong: ``,
                    // chandoan: ``,
                    // tengiuong: ``,
                    // ttxv: 0,
                    so_dt: hospitalFee?.mobile || ``,
                    status: paymentObj.status,
                    date_create: paymentObj.createdAt,
                    date_update: paymentObj.updatedAt,
                };
                const [idFee] = await this.pkhPatientKnex(this.feeTable).insert(dataFeeV1);
                this.logger.warn(idFee);

                const dataPaymentFeeV1 = {
                    // app,
                    // method_id,
                    // transaction_code_tt,
                    amount: paymentObj.amount,
                    amount_original: paymentObj.subTotal,
                    amount_rate: 1, // chua tim thay
                    amount_add: 0, // chua tim thay
                    amount_medpro: 0, // chua tim thay
                    fee_id: idFee,
                    user_id: userIdV1.id,
                    status: hospitalFee.status,
                    // status_process,
                    // result,
                    transaction_id: transactionId,
                    date_create: hospitalFee.createdAt,
                    date_update: hospitalFee.updatedAt,
                };
                const [idPaymentFee] = await this.pkhPatientKnex(this.paymentFeeTable).insert(dataPaymentFeeV1);
                this.logger.warn(idPaymentFee);
            }
        } catch (error) {
            throw error;
        }
    }

    getPaymentMethod(partnerId: string, methodId: number, jsonData: string): any {
        switch (partnerId) {
            case PartnerOldHospital.UMC:
                return this.getPaymentMethodDetail(methodId, jsonData);
            case PartnerOldHospital.SKIN:
                return this.getPaymentMethodDetailSkin(methodId, jsonData);
            case PartnerOldHospital.NHIDONG1:
                return this.getPaymentMethodDetailNd1(methodId, jsonData);
            default:
                break;
        }
    }

    getPaymentMethodDetailNd1(methodId: number, jsonData: string): any {
        const findConfig = _.find(this.nd1Config, { id: methodId });
        if (typeof findConfig !== typeof undefined) {
            /* kiểm tra jsonData có gì ko để tìm lại ngân hàng thanh toán */
            if (jsonData) {
                try {
                    const data = JSON.parse(jsonData);
                    const { vnp_BankCode } = data;
                    if (vnp_BankCode) {
                        return {
                            ...findConfig,
                            paymentMethod: vnp_BankCode,
                        };
                    }
                } catch (error) {
                    // this.clientSentry.instance().captureException(error);
                    this.logger.error(error?.message);
                }
            }
            return findConfig;
        }
        return false;
    }

    getPaymentMethodDetailSkin(methodId: number, jsonData: string): any {
        const findConfig = _.find(this.skinConfig, { id: methodId });
        if (typeof findConfig !== typeof undefined) {
            /* kiểm tra jsonData có gì ko để tìm lại ngân hàng thanh toán */
            if (jsonData) {
                try {
                    const data = JSON.parse(jsonData);
                    const { vnp_BankCode } = data;
                    if (vnp_BankCode) {
                        return {
                            ...findConfig,
                            paymentMethod: vnp_BankCode,
                        };
                    }
                } catch (error) {
                    // this.clientSentry.instance().captureException(error);
                    this.logger.error(error?.message);
                }
            }
            return findConfig;
        }
        return false;
    }

    getPaymentMethodDetail(methodId: number, jsonData: string): any {
        const findConfig = _.find(this.umcConfig, { id: methodId });
        if (typeof findConfig !== typeof undefined) {
            /* kiểm tra jsonData có gì ko để tìm lại ngân hàng thanh toán */
            if (!!jsonData) {
                try {
                    const data = JSON.parse(jsonData);
                    const { vnp_BankCode = null } = data;
                    if (vnp_BankCode) {
                        return {
                            ...findConfig,
                            paymentMethod: vnp_BankCode,
                        };
                    } else {
                        return findConfig;
                    }
                } catch (error) {
                    // this.clientSentry.instance().captureException(error);
                    return findConfig;
                }
            } else {
                return findConfig;
            }
        } else {
            // this.clientSentry.instance().captureMessage(`tim2 khong thay medthodiD ${methodId}`);
            this.logger.error(`tim khong thay medthodiD ${methodId}`);
            return false;
        }
    }

    transformPaymentMethod(findConfig: any): any {
        if (findConfig) {
            const { paymentMethod, paymentMethodDetail, gatewayId } = findConfig;

            const obj: any = {};
            if (paymentMethod) {
                obj.paymentMethod = paymentMethod;
            }
            if (paymentMethodDetail) {
                obj.paymentMethodDetail = paymentMethodDetail;
            }
            if (gatewayId) {
                obj.gatewayId = gatewayId;
            }
            return obj;
        }
        return {};
    }

    returnPaymentStatus(status: number): number {
        switch (status) {
            case 0:
                return 1;
            case 1:
                return 2;
            case -2:
                return 2;
            default:
                return 0;
        }
    }

    async getSyncPaymentFeeSuccess(formData: GetSyncPaymentFeeDto): Promise<BaseResponse<ISyncPaymentFeeSuccess>> {
        const { pageIndex, pageSize = 10 } = formData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.syncPaymentFeeSuccessModel
                    .find({})
                    .sort({ createdAt: 'desc' })
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .exec(),
                this.syncPaymentFeeSuccessModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            }
        } catch (error) {
            this.logger.error(`Error when exec getPaymentFeeSuccess()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getSyncPaymentFeeProcess(fromData: GetSyncPaymentFeeDto): Promise<BaseResponse<ISyncPaymentFeeProcess>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.syncPaymentFeeProcessModel
                    .find({})
                    .sort({ createdAt: 'desc' })
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .exec(),
                this.syncPaymentFeeProcessModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            }
        } catch (error) {
            this.logger.error(`Error when exec getPaymentFeeSuccess()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async getSyncPaymentFeeProcessFailed(fromData: GetSyncPaymentFeeDto): Promise<BaseResponse<ISyncPaymentFeeProcessFailed>> {
        const { pageIndex, pageSize = 10 } = fromData;
        try {
            const [rows, totalRows] = await Promise.all([
                this.syncPaymentFeeProcessFailedModel
                    .find({})
                    .sort({ createdAt: 'desc' })
                    .skip(pageIndex * pageSize)
                    .limit(pageSize)
                    .exec(),
                this.syncPaymentFeeProcessFailedModel.countDocuments(),
            ]);
            return {
                pageIndex,
                pageSize,
                rows,
                totalRows,
            }
        } catch (error) {
            this.logger.error(`Error when exec getSyncPaymentFeeProcessFailModel()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }

    async rollBackSyncPaymentFeeFailService(processId: string): Promise<any> {
        try {
            const syncPaymentFeeFail = await this.syncPaymentFeeProcessFailedModel.findOne({ processId }).lean().exec();
            if (!syncPaymentFeeFail) {
                throw new HttpException(`Không tìm thấy dữ liệu với processId là ${processId} `, 404);
            }
            const { error, syncStatus, ...data } = syncPaymentFeeFail;
            await this.syncPaymentFeeProcessModel.create({ ...data, syncStatus: SyncProcessStatus.PENDING });
            await this.syncPaymentFeeProcessFailedModel.deleteOne({ processId }).exec();
        } catch (error) {
            this.logger.error(`Error when exec rollBackSyncPatientFailService()\nError: ${error.message}`);
            const errorMessage = error?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            throw new HttpException(errorMessage, statusCode);
        }
    }
}
