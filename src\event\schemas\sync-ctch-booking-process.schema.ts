import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SYNC_CTCH_BOOKING_PROCESS } from './constants';
import { SyncProcessStatus } from '../dto/sync-status.dto';

const Schema = mongoose.Schema;

export const SyncCTCHBookingProcessSchema = new Schema({
    processId: { type: String },
    id: { type: Number, required: true },
    sourceId: { type: String, default: 'ctchhcm' },
    date_create: { type: Date }, // lấy đúng giờ bên mysql
    syncStatus: { type: String, default: SyncProcessStatus.INITIAL }, // pending -> active -> success| errored
}, {
    collection: SYNC_CTCH_BOOKING_PROCESS,
    timestamps: true,
}).plugin(jsonMongo);
