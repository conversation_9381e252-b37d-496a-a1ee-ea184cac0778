import { GlobalSettingModule } from './../src/global-setting/global-setting.module';
import { HttpModule, INestApplication } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { GlobalSettingService } from './../src/global-setting/global-setting.service';

// import { ModuleMocker } from 'jest-mock';
import { ConfigTestModule } from '../src/common/mock/global-setting/config-test.module';

describe('Global-setting Service', () => {
    // const moduleMocker = new ModuleMocker(global);
    const repoName = 'medpro-admin-api-v2';
    let app: INestApplication;
    let globalSettingService: GlobalSettingService;

    beforeAll(async () => {
        const moduleRef = await Test.createTestingModule({
            imports: [ConfigTestModule, HttpModule, GlobalSettingModule],
            providers: [],
        }).compile();
        app = moduleRef.createNestApplication();
        await app.init();
        globalSettingService = moduleRef.get(GlobalSettingService);
    });

    afterAll(async () => {
        await app.close();
    });

    it('PAYMENT_FEE_HISTORY_HTML', done => {
        globalSettingService.findByKeyAndRepoName('PAYMENT_FEE_HISTORY_HTML').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('TREE_ID_PAYMENT_V2', done => {
        globalSettingService.findByKeyAndRepoName('TREE_ID_PAYMENT_V2').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });
    it('PARTNER_RE_EXAMS_RELATIVE', done => {
        globalSettingService.findByKeyAndRepoName('PARTNER_RE_EXAMS_RELATIVE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('TOTAL_PAYMENT_MESSAGE_EXTRA', done => {
        globalSettingService.findByKeyAndRepoName('TOTAL_PAYMENT_MESSAGE_EXTRA').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('TOTAL_PAYMENT_MESSAGE', done => {
        globalSettingService.findByKeyAndRepoName('TOTAL_PAYMENT_MESSAGE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('COUNTDOWN', done => {
        globalSettingService.findByKeyAndRepoName('COUNTDOWN').then(value => {
            expect(typeof value).toBe('number');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('NOT_PAYMENT_YET_INFO', done => {
        globalSettingService.findByKeyAndRepoName('NOT_PAYMENT_YET_INFO').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('HOTLINE_INFO', done => {
        globalSettingService.findByKeyAndRepoName('HOTLINE_INFO').then(value => {
            expect(typeof value).toBe('number');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('BETA_IGNORE_MESSAGE', done => {
        globalSettingService.findByKeyAndRepoName('BETA_IGNORE_MESSAGE', repoName).then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('PARTNER_ACCEPT_NOT_BOOKING_DATE', done => {
        globalSettingService.findByKeyAndRepoName('PARTNER_ACCEPT_NOT_BOOKING_DATE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('FILTER_CHECK_ERROR_MESSAGE', done => {
        globalSettingService.findByKeyAndRepoName('FILTER_CHECK_ERROR_MESSAGE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('PARTNER_DISCOUNT_PRICE', done => {
        globalSettingService.findByKeyAndRepoName('PARTNER_DISCOUNT_PRICE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('PARTNER_NOT_EXPIRED', done => {
        globalSettingService.findByKeyAndRepoName('PARTNER_NOT_EXPIRED').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('CANCEL_BOOKING_GUIDE_NOT_SUBTOTAL', done => {
        globalSettingService.findByKeyAndRepoName('CANCEL_BOOKING_GUIDE_NOT_SUBTOTAL').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('ATM_PAYMENT', done => {
        globalSettingService.findByKeyAndRepoName('ATM_PAYMENT').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('VISA_PAYMENT', done => {
        globalSettingService.findByKeyAndRepoName('VISA_PAYMENT').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('WALLET_PAYMENT', done => {
        globalSettingService.findByKeyAndRepoName('WALLET_PAYMENT').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('DATE_WORK_ATM', done => {
        globalSettingService.findByKeyAndRepoName('WALLET_PAYMENT').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('DATE_WORK_VISA', done => {
        globalSettingService.findByKeyAndRepoName('DATE_WORK_VISA').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('DATE_WORK_VI_DIEN_TU', done => {
        globalSettingService.findByKeyAndRepoName('DATE_WORK_VI_DIEN_TU').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('CANCEL_BOOKING_GUIDE_HAVE_SUBTOTAL', done => {
        globalSettingService.findByKeyAndRepoName('CANCEL_BOOKING_GUIDE_HAVE_SUBTOTAL').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('HOT_LINE_PAYMENT', done => {
        globalSettingService.findByKeyAndRepoName('HOT_LINE_PAYMENT').then(value => {
            expect(typeof value).toBe('number');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('TEXT_MESSAGE_DISCOUNT_BOOKING', done => {
        globalSettingService.findByKeyAndRepoName('TEXT_MESSAGE_DISCOUNT_BOOKING').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('MAIL_SUPPORT_PAYMENT_GROUP', done => {
        globalSettingService.findByKeyAndRepoName('MAIL_SUPPORT_PAYMENT_GROUP').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('UMC_AGE_LIMIT_WITH_EXAM', done => {
        globalSettingService.findByKeyAndRepoName('UMC_AGE_LIMIT_WITH_EXAM').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('UMC_AGE_LT_NHI_MESSAGE', done => {
        globalSettingService.findByKeyAndRepoName('UMC_AGE_LT_NHI_MESSAGE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('CLINIC_REGISTER_MAIL_RECEIVER', done => {
        globalSettingService.findByKeyAndRepoName('CLINIC_REGISTER_MAIL_RECEIVER').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('CLINIC_REGISTER_MAIL_HTML', done => {
        globalSettingService.findByKeyAndRepoName('CLINIC_REGISTER_MAIL_HTML').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('CLINIC_CLIENT_ID', done => {
        globalSettingService.findByKeyAndRepoName('CLINIC_CLIENT_ID').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('CLINIC_SECRET_KEY', done => {
        globalSettingService.findByKeyAndRepoName('CLINIC_SECRET_KEY').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('COUNTRY_SUPPORT_XNC', done => {
        globalSettingService.findByKeyAndRepoName('COUNTRY_SUPPORT_XNC').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('MESSAGE_EVENT_REPO_APPLY', done => {
        globalSettingService.findByKeyAndRepoName('MESSAGE_EVENT_REPO_APPLY').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('APPROVE_RESTART_SYNC', done => {
        globalSettingService.findByKeyAndRepoName('APPROVE_RESTART_SYNC').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('IGNORE_DOCTOR_FEATURE', done => {
        globalSettingService.findByKeyAndRepoName('IGNORE_DOCTOR_FEATURE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('FILTER_FEATURE_PARTNER_CONFIG', done => {
        globalSettingService.findByKeyAndRepoName('FILTER_FEATURE_PARTNER_CONFIG').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    // it('MEDPRO_ID_EVENT', done => {
    //     globalSettingService.findByKeyAndRepoName('MEDPRO_ID_EVENT').then(value => {
    //         expect(typeof value).toBe('string');
    //         expect(value).toBeTruthy();
    //         done();
    //     });
    // });

    it('SORT_PARTNER_LIST', done => {
        globalSettingService.findByKeyAndRepoName('SORT_PARTNER_LIST').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    // it('CHO_RAY_DAT_KHAM', done => {
    //     globalSettingService.findByKeyAndRepoName('CHO_RAY_DAT_KHAM').then(value => {
    //         expect(typeof value).toBe('string');
    //         expect(value).toBeTruthy();
    //         done();
    //     });
    // });

    it('DELIVERY_STATUS_IN_PROGRESS', done => {
        globalSettingService.findByKeyAndRepoName('DELIVERY_STATUS_IN_PROGRESS').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('DELIVERY_STATUS_HOLD', done => {
        globalSettingService.findByKeyAndRepoName('DELIVERY_STATUS_HOLD').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('SORT_PARTNER_LIST', done => {
        globalSettingService.findByKeyAndRepoName('SORT_PARTNER_LIST').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('HTML_SYNC_V1_FAIL', done => {
        globalSettingService.findByKeyAndRepoName('HTML_SYNC_V1_FAIL').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('HTLM_MAIL_PAYMENT_UMC', done => {
        globalSettingService.findByKeyAndRepoName('HTLM_MAIL_PAYMENT_UMC').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('TITLE_NOT_PAYMENT_YET_REMINDER', done => {
        globalSettingService.findByKeyAndRepoName('TITLE_NOT_PAYMENT_YET_REMINDER').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('NOT_PAYMENT_YET_REMINDER', done => {
        globalSettingService.findByKeyAndRepoName('NOT_PAYMENT_YET_REMINDER').then(value => {
            expect(typeof value).toBe('number');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('HTML_UPDATE_BOOKING_FAIL', done => {
        globalSettingService.findByKeyAndRepoName('HTML_UPDATE_BOOKING_FAIL').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('BY_MEDPRO', done => {
        globalSettingService.findByKeyAndRepoName('BY_MEDPRO').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('BY_MEDPRO_WHITE', done => {
        globalSettingService.findByKeyAndRepoName('BY_MEDPRO_WHITE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('PARTNER_VALIDATE_PATIENT_AGE', done => {
        globalSettingService.findByKeyAndRepoName('PARTNER_VALIDATE_PATIENT_AGE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('WARNING_TRUOC_NGAY', done => {
        globalSettingService.findByKeyAndRepoName('WARNING_TRUOC_NGAY').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('UMC_AGE_LIMIT_WITH_EXAM', done => {
        globalSettingService.findByKeyAndRepoName('UMC_AGE_LIMIT_WITH_EXAM').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('WARNING_MESSAGE', done => {
        globalSettingService.findByKeyAndRepoName('WARNING_MESSAGE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('REEXAMS_GREETING', done => {
        globalSettingService.findByKeyAndRepoName('REEXAMS_GREETING').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('REEXAMS_BENEFIC', done => {
        globalSettingService.findByKeyAndRepoName('REEXAMS_BENEFIC').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('WARNING_REGISTER_MESSAGE', done => {
        globalSettingService.findByKeyAndRepoName('WARNING_REGISTER_MESSAGE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('AGE_LIMIT_CONFIG', done => {
        globalSettingService.findByKeyAndRepoName('AGE_LIMIT_CONFIG').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('UMC_V2_CREATE_PATIENT_OLD', done => {
        globalSettingService.findByKeyAndRepoName('UMC_V2_CREATE_PATIENT_OLD').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('RE_EXAM_HOSPITAL_V2', done => {
        globalSettingService.findByKeyAndRepoName('RE_EXAM_HOSPITAL_V2').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('BOOKING_DATE_INVALID_MESSAGE', done => {
        globalSettingService.findByKeyAndRepoName('BOOKING_DATE_INVALID_MESSAGE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('INVALID_DATE_MESSAGE', done => {
        globalSettingService.findByKeyAndRepoName('INVALID_DATE_MESSAGE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('VALID_DATE_RANGE', done => {
        globalSettingService.findByKeyAndRepoName('VALID_DATE_RANGE').then(value => {
            expect(typeof value).toBe('number');
            expect(value).toBeTruthy();
            done();
        });
    });

    // it('SMS_BOOKING_INFO_CSKH', done => {
    //     globalSettingService.findByKeyAndRepoName('SMS_BOOKING_INFO_CSKH').then(value => {
    //         expect(typeof value).toBe('string');
    //         expect(value).toBeTruthy();
    //         done();
    //     });
    // });

    // it('CSKH_DOMAIN', done => {
    //     globalSettingService.findByKeyAndRepoName('CSKH_DOMAIN').then(value => {
    //         expect(typeof value).toBe('string');
    //         expect(value).toBeTruthy();
    //         done();
    //     });
    // });

    it('PAYMENT_METHOD_CONFIG_UMC', done => {
        globalSettingService.findByKeyAndRepoName('PAYMENT_METHOD_CONFIG_UMC').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('PAYMENT_METHOD_CONFIG_SKIN', done => {
        globalSettingService.findByKeyAndRepoName('PAYMENT_METHOD_CONFIG_SKIN').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('PAYMENT_METHOD_CONFIG_ND1', done => {
        globalSettingService.findByKeyAndRepoName('PAYMENT_METHOD_CONFIG_ND1').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('SYNC_UMC_PATIENT', done => {
        globalSettingService.findByKeyAndRepoName('SYNC_UMC_PATIENT').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('SYNC_UMC_BOOKING', done => {
        globalSettingService.findByKeyAndRepoName('SYNC_UMC_BOOKING').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('SYNC_UMC_USER', done => {
        globalSettingService.findByKeyAndRepoName('SYNC_UMC_USER').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('MESSAGE_EVENT_JOB', done => {
        globalSettingService.findByKeyAndRepoName('MESSAGE_EVENT_JOB').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('SYNC_UMC_PAYMENT_FEE', done => {
        globalSettingService.findByKeyAndRepoName('SYNC_UMC_PAYMENT_FEE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('SAVE_USER_PROFILE_APPLY_SIGNIN_PROVIDER', done => {
        globalSettingService.findByKeyAndRepoName('SAVE_USER_PROFILE_APPLY_SIGNIN_PROVIDER').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });

    it('CHECK_PHONE_BEFORE_SYNC_SERVICE', done => {
        globalSettingService.findByKeyAndRepoName('CHECK_PHONE_BEFORE_SYNC_SERVICE').then(value => {
            expect(typeof value).toBe('string');
            expect(value).toBeTruthy();
            done();
        });
    });
});
