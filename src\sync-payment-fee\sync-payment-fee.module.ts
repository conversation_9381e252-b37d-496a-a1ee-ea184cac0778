import { PhoneSessionSchema } from './../user/schemas/phone-sessions.schema';
import { PHONE_SESSION_COLLECTION_NAME, USER_REQUEST_COLLECTION_NAME } from './../user/schemas/constants';
import { HttpModule, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { PassportModule } from '@nestjs/passport';
import { BookingOrderSchema } from 'src/booking-gateway/schemas/booking-order';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import {
  BOOKING_COLLECTION_NAME,
  BOOKING_ORDER_COLLECTION_NAME,
  BOOKING_SEARCH_COLLECTION_NAME,
  HOSPITAL_FEE_COLLECTION_NAME,
  NEW_BILL_LOG_COLLECTION_NAME,
  PAYMENT_COLLECTION_NAME,
  SECTION_COLLECTION_NAME,
} from 'src/booking-gateway/schemas/constants';
import { HospitalFeeSchema } from 'src/booking-gateway/schemas/hospital-fee.schema';
import { NewBillLogSchema } from 'src/booking-gateway/schemas/new-bill-log.schema';
import { PaymentSchema } from 'src/booking-gateway/schemas/payment.schema';
import { BookingSearchSchema } from 'src/booking-gateway/schemas/search-booking.schema';
import { SectionSchema } from 'src/booking-gateway/schemas/section.schema';
import { CitySchema } from 'src/city-mongo/schemas/city.schema';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { CountrySchema } from 'src/country-mongo/schemas/country.schema';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { DistrictSchema } from 'src/district-mongo/schemas/district.schema';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { DoctorSchema } from 'src/doctor-mongo/schemas/doctor.schema';
import { AddressTrackingSchema } from 'src/event/schemas/address-tracking.schema';
import {
  ADDRESS_TRACKING_COLLECTION_NAME,
  SEND_MAIL_OR_SMS,
  SYNC_CTCH_BOOKING,
  SYNC_CTCH_BOOKING_PROCESS, SYNC_CTCH_BOOKING_PROCESS_FAILED, SYNC_CTCH_BOOKING_SUCCESS,
  SYNC_CTCH_PATIENT,
  SYNC_CTCH_PATIENT_PROCESS,
  SYNC_CTCH_PATIENT_PROCESS_FAILED,
  SYNC_CTCH_PATIENT_SUCCESS,
  SYNC_DALIEU_BOOKING,
  SYNC_DALIEU_PATIENT,
  SYNC_DHYD_BOOKING,
  SYNC_DHYD_PATIENT,
  SYNC_NHI_DONG_1_BOOKING,
  SYNC_NHI_DONG_1_BOOKING_PROCESS,
  SYNC_NHI_DONG_1_BOOKING_PROCESS_FAILED,
  SYNC_NHI_DONG_1_BOOKING_SUCCESS,
  SYNC_NHI_DONG_1_PATIENT,
  SYNC_PAYMENT_FEE_PROCESS,
  SYNC_PAYMENT_FEE_PROCESS_FAILED,
  SYNC_PAYMENT_FEE_SUCCESS,
  SYNC_USER_PROCESS,
  SYNC_USER_PROCESS_FAILED,
  SYNC_USER_SUCCESS,
} from 'src/event/schemas/constants';
import { SendMailOrSmsSchema } from 'src/event/schemas/send-mail-or-sms.schema';
import { SyncDaLieuBookingSchema } from 'src/event/schemas/sync-dalieu-booking.schema';
import { SyncDaLieuPatientSchema } from 'src/event/schemas/sync-dalieu-patient.schema';
import { SyncDHYDBookingSchema } from 'src/event/schemas/sync-dhyd-booking.schema';
import { SyncDHYDPatientSchema } from 'src/event/schemas/sync-dhyd-patient.schema';
import { SyncNhiDong1BookingProcessFailedSchema } from 'src/event/schemas/sync-nhidong1-booking-process-failed.schema';
import { SyncNhiDong1BookingProcessSchema } from 'src/event/schemas/sync-nhidong1-booking-process.schema';
import { SyncNhiDong1BookingSuccessSchema } from 'src/event/schemas/sync-nhidong1-booking-success.schema';
import { SyncNhiDong1BookingSchema } from 'src/event/schemas/sync-nhidong1-booking.schema';
import { SyncNhiDong1PatientSchema } from 'src/event/schemas/sync-nhidong1-patient.schema';
import { SyncPaymentFeeProcessFailedSchema } from 'src/event/schemas/sync-payment-fee-process-failed.schema';
import { SyncPaymentFeeProcessSchema } from 'src/event/schemas/sync-payment-fee-process.schema';
import { SyncPaymentFeeSuccessSchema } from 'src/event/schemas/sync-payment-fee-success.schema';
import { SyncUserProcessFailedSchema } from 'src/event/schemas/sync-user-process-failed.schema';
import { SyncUserProcessSchema } from 'src/event/schemas/sync-user-process.schema';
import { SyncUserSuccessSchema } from 'src/event/schemas/sync-user-success.schema';
import { FilesService } from 'src/files/files.service';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { NATION_COLLECTION_NAME } from 'src/nation-mongo/schemas/constants';
import { NationSchema } from 'src/nation-mongo/schemas/nation.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { PatientMongoService } from 'src/patient-mongo/patient-mongo.service';
import {
  PATIENT_CODE_COLLECTION_NAME,
  PATIENT_COLLECTION_NAME,
  PATIENT_PROFILE_COLLECTION_NAME,
  PATIENT_RELATION_COLLECTION_NAME,
  PATIENT_SEARCH_LOG_COLLECTION_NAME,
  PATIENT_VERSION_COLLECTION_NAME,
  PATIENT_XC_COLLECTION_NAME,
  RELATIVE_TYPE_COLLECTION_NAME,
} from 'src/patient-mongo/schemas/constants';
import { PatientCodeSchema } from 'src/patient-mongo/schemas/patient-codes.schema';
import { PatientProfileSchema } from 'src/patient-mongo/schemas/patient-profile.schema';
import { PatientRelationSchema } from 'src/patient-mongo/schemas/patient-relation.schema';
import { PatientSearchLogSchema } from 'src/patient-mongo/schemas/patient-search-log.schema';
import { PatientVersionSchema } from 'src/patient-mongo/schemas/patient-version.schema';
import { PatientXcSchema } from 'src/patient-mongo/schemas/patient-xc.schema';
import { PatientSchema } from 'src/patient-mongo/schemas/patient.schema';
import { RelativeSchema } from 'src/patient-mongo/schemas/relative-mongo.schema';
import { PatientService } from 'src/patient/patient.service';
import { PaymentFeeGatewayService } from 'src/payment-fee-gateway/payment-fee-gateway.service';
import { PhoneLoginService } from 'src/phone-login/phone-login.service';
import { PROFESSION_COLLECTION_NAME } from 'src/profession-mongo/schemas/constants';
import { ProfessionSchema } from 'src/profession-mongo/schemas/profession.schema';
import { ReferralCodeService } from 'src/referral-code/referral-code.service';
import { REFERRAL_CODE_COLLECTION_NAME } from 'src/referral-code/schemas/constants';
import { ReferralCodeSchema } from 'src/referral-code/schemas/referral-code.schema';
import { ROOM_COLLECTION_NAME } from 'src/room-mongo/schemas/constants';
import { RoomSchema } from 'src/room-mongo/schemas/room.schema';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { ServiceSchema } from 'src/service-mongo/schemas/service.schema';
import { SessionService } from 'src/session/session.service';
import { SmsService } from 'src/sms/sms.service';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { SubjectSchema } from 'src/subject-mongo/schemas/subject.schema';
import {
  SYNC_USER_DALIEU_PROCESS_COLLECTION_NAME,
  SYNC_USER_DALIEU_PROCESS_FAILED_COLLECTION_NAME,
  SYNC_USER_DALIEU_SUCCESS_COLLECTION_NAME,
  SYNC_USER_DALIEU_UPGRADE_COLLECTION_NAME,
} from 'src/sync-da-lieu-medpro/schemas/constants';
import { SyncUserDaLieuProcessFailedSchema } from 'src/sync-da-lieu-medpro/schemas/sync-user-dalieu-process-failed.schema';
import { SyncUserDaLieuProcessSchema } from 'src/sync-da-lieu-medpro/schemas/sync-user-dalieu-process.schema';
import { SyncUserDaLieuSuccessSchema } from 'src/sync-da-lieu-medpro/schemas/sync-user-dalieu-success.schema';
import { SyncUserDaLieuUpgradeSchema } from 'src/sync-da-lieu-medpro/schemas/sync-user-dalieu-upgrade.schema';
import {
  SYNC_USER_TRUNGVUONG_PROCESS_COLLECTION_NAME,
  SYNC_USER_TRUNGVUONG_PROCESS_FAILED_COLLECTION_NAME,
  SYNC_USER_TRUNGVUONG_SUCCESS_COLLECTION_NAME,
  SYNC_USER_TRUNGVUONG_UPGRADE_COLLECTION_NAME,
} from 'src/sync-trung-vuong-medpro/schemas/constants';
import {
  SyncUserTrungVuongProcessFailedSchema,
} from 'src/sync-trung-vuong-medpro/schemas/sync-booking-process-failed.schema';
import { SyncUserTrungVuongProcessSchema } from 'src/sync-trung-vuong-medpro/schemas/sync-user-trungvuong-process.schema';
import { SyncUserTrungVuongSuccessSchema } from 'src/sync-trung-vuong-medpro/schemas/sync-user-trungvuong-success.schema';
import { SyncUserTrungVuongUpgradeSchema } from 'src/sync-trung-vuong-medpro/schemas/sync-user-trungvuong-upgrade.schema';
import { SyncUserService } from 'src/sync-user/sync-user.service';
import { USER_ACCOUNT_COLLECTION_NAME } from 'src/user-account/schemas/constants';
import { UserAccountSchema } from 'src/user-account/schemas/user-account.schema';
import {
  CONSTRAINTS_PROVIDER_COLLECTION_NAME,
  CONSTRAINTS_USER_COLLECTION_NAME,
  HOC_VI_COLLECTION_NAME,
  ORG_PROFILE_COLLECTION_NAME,
  REFERRAL_CODE_REGISTER_COLLECTION_NAME,
  SIGNIN_PROVIDER_COLLECTION_NAME,
  USER_APP_COLLECTION_NAME,
  USER_COLLECTION_NAME,
  USER_PROFILE_COLLECTION_NAME,
  VI_TRI_CONG_VIEC_COLLECTION_NAME,
} from 'src/user/schemas/constants';
import { HocViSchema } from 'src/user/schemas/hoc-vi.schema';
import { OrgProfileSchema } from 'src/user/schemas/org-profile.schema';
import { ProviderConstraintSchema } from 'src/user/schemas/provider-constraints.schema';
import { ReferralCodeRegisterSchema } from 'src/user/schemas/referral-code-register';
import { SignInProviderSchema } from 'src/user/schemas/signin-provider.schema';
import { UserAppSchema } from 'src/user/schemas/user-app.schema';
import { UserConstraintSchema } from 'src/user/schemas/user-constraints.schema';
import { UserProfileSchema } from 'src/user/schemas/user-profile.schema';
import { UserSchema } from 'src/user/schemas/user.schema';
import { ViTriCongViecSchema } from 'src/user/schemas/vi-tri-cong-viec.schema';
import { UserService } from 'src/user/user.service';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { WardSchema } from 'src/ward-mongo/schemas/ward.schema';

import { SyncPaymentFeeController } from './sync-payment-fee.controller';
import { SyncPaymentFeeService } from './sync-payment-fee.service';
import { SyncCTCHPatientSchema } from '../event/schemas/sync-ctch-patient.schema';
import { SyncCTCHPatientProcessSchema } from '../event/schemas/sync-ctch-patient-process.schema';
import { SyncCTCHPatientProcessFailedSchema } from '../event/schemas/sync-ctch-patient-process-failed.schema';
import { SyncCTCHPatientSuccessSchema } from '../event/schemas/sync-ctch-patient-success.schema';
import { SyncCTCHBookingSchema } from '../event/schemas/sync-ctch-booking.schema';
import { SyncCTCHBookingProcessSchema } from '../event/schemas/sync-ctch-booking-process.schema';
import { SyncCTCHBookingProcessFailedSchema } from '../event/schemas/sync-ctch-booking-process-failed.schema';
import { SyncCTCHBookingSuccessSchema } from '../event/schemas/sync-ctch-booking-success.schema';
import { TranslateModule } from 'src/translate/translate.module';
import { UserRequestsSchema } from 'src/user/schemas/user-requests.schema';
import { UserModule } from '../user/user.module';

@Module({
  imports: [
    HttpModule,
    PassportModule.register({ defaultStrategy: 'user-jwt' }),
    JwtModule.registerAsync({
      useExisting: JwtUserConfigService,
    }),
    MongooseModule.forFeature([
      { name: USER_COLLECTION_NAME, schema: UserSchema },
      { name: USER_PROFILE_COLLECTION_NAME, schema: UserProfileSchema },
      { name: ORG_PROFILE_COLLECTION_NAME, schema: OrgProfileSchema },
      { name: CONSTRAINTS_USER_COLLECTION_NAME, schema: UserConstraintSchema },
      { name: CONSTRAINTS_PROVIDER_COLLECTION_NAME, schema: ProviderConstraintSchema },
      { name: SIGNIN_PROVIDER_COLLECTION_NAME, schema: SignInProviderSchema },
      { name: USER_ACCOUNT_COLLECTION_NAME, schema: UserAccountSchema },
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
      { name: BOOKING_SEARCH_COLLECTION_NAME, schema: BookingSearchSchema },
      { name: PAYMENT_COLLECTION_NAME, schema: PaymentSchema },
      { name: SERVICE_COLLECTION_NAME, schema: ServiceSchema },
      { name: SECTION_COLLECTION_NAME, schema: SectionSchema },
      { name: SUBJECT_COLLECTION_NAME, schema: SubjectSchema },
      { name: ROOM_COLLECTION_NAME, schema: RoomSchema },
      { name: DOCTOR_COLLECTION_NAME, schema: DoctorSchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
      { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
      { name: PATIENT_CODE_COLLECTION_NAME, schema: PatientCodeSchema },
      { name: PATIENT_VERSION_COLLECTION_NAME, schema: PatientVersionSchema },
      { name: PROFESSION_COLLECTION_NAME, schema: ProfessionSchema },
      { name: NATION_COLLECTION_NAME, schema: NationSchema },
      { name: COUNTRY_COLLECTION_NAME, schema: CountrySchema },
      { name: CITY_COLLECTION_NAME, schema: CitySchema },
      { name: DISTRICT_COLLECTION_NAME, schema: DistrictSchema },
      { name: WARD_COLLECTION_NAME, schema: WardSchema },
      { name: RELATIVE_TYPE_COLLECTION_NAME, schema: RelativeSchema },
      { name: SYNC_USER_PROCESS, schema: SyncUserProcessSchema },
      { name: SYNC_USER_PROCESS_FAILED, schema: SyncUserProcessFailedSchema },
      { name: SYNC_USER_SUCCESS, schema: SyncUserSuccessSchema },
      { name: PATIENT_SEARCH_LOG_COLLECTION_NAME, schema: PatientSearchLogSchema },
      { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
      { name: HOC_VI_COLLECTION_NAME, schema: HocViSchema },
      { name: VI_TRI_CONG_VIEC_COLLECTION_NAME, schema: ViTriCongViecSchema },
      { name: REFERRAL_CODE_REGISTER_COLLECTION_NAME, schema: ReferralCodeRegisterSchema },
      { name: SYNC_USER_TRUNGVUONG_PROCESS_COLLECTION_NAME, schema: SyncUserTrungVuongProcessSchema },
      { name: SYNC_USER_TRUNGVUONG_PROCESS_FAILED_COLLECTION_NAME, schema: SyncUserTrungVuongProcessFailedSchema },
      { name: SYNC_USER_TRUNGVUONG_SUCCESS_COLLECTION_NAME, schema: SyncUserTrungVuongSuccessSchema },
      { name: SYNC_USER_TRUNGVUONG_UPGRADE_COLLECTION_NAME, schema: SyncUserTrungVuongUpgradeSchema },
      { name: SYNC_USER_DALIEU_PROCESS_COLLECTION_NAME, schema: SyncUserDaLieuProcessSchema },
      { name: SYNC_USER_DALIEU_PROCESS_FAILED_COLLECTION_NAME, schema: SyncUserDaLieuProcessFailedSchema },
      { name: SYNC_USER_DALIEU_SUCCESS_COLLECTION_NAME, schema: SyncUserDaLieuSuccessSchema },
      { name: SYNC_USER_DALIEU_UPGRADE_COLLECTION_NAME, schema: SyncUserDaLieuUpgradeSchema },
      { name: REFERRAL_CODE_COLLECTION_NAME, schema: ReferralCodeSchema },
      { name: SYNC_DALIEU_PATIENT, schema: SyncDaLieuPatientSchema },
      { name: SYNC_DALIEU_BOOKING, schema: SyncDaLieuBookingSchema },
      { name: SEND_MAIL_OR_SMS, schema: SendMailOrSmsSchema },
      { name: ADDRESS_TRACKING_COLLECTION_NAME, schema: AddressTrackingSchema },
      { name: SYNC_NHI_DONG_1_PATIENT, schema: SyncNhiDong1PatientSchema },
      { name: SYNC_DHYD_PATIENT, schema: SyncDHYDPatientSchema },
      { name: SYNC_DHYD_BOOKING, schema: SyncDHYDBookingSchema },
      { name: PATIENT_PROFILE_COLLECTION_NAME, schema: PatientProfileSchema },
      { name: BOOKING_ORDER_COLLECTION_NAME, schema: BookingOrderSchema },
      { name: NEW_BILL_LOG_COLLECTION_NAME, schema: NewBillLogSchema },
      // { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
      { name: USER_APP_COLLECTION_NAME, schema: UserAppSchema },
      { name: USER_REQUEST_COLLECTION_NAME, schema: UserRequestsSchema },
      { name: SYNC_NHI_DONG_1_BOOKING, schema: SyncNhiDong1BookingSchema },
      { name: SYNC_NHI_DONG_1_BOOKING_PROCESS, schema: SyncNhiDong1BookingProcessSchema },
      { name: SYNC_NHI_DONG_1_BOOKING_PROCESS_FAILED, schema: SyncNhiDong1BookingProcessFailedSchema },
      { name: SYNC_NHI_DONG_1_BOOKING_SUCCESS, schema: SyncNhiDong1BookingSuccessSchema },
      { name: HOSPITAL_FEE_COLLECTION_NAME, schema: HospitalFeeSchema },
      { name: SYNC_PAYMENT_FEE_PROCESS, schema: SyncPaymentFeeProcessSchema },
      { name: SYNC_PAYMENT_FEE_SUCCESS, schema: SyncPaymentFeeSuccessSchema },
      { name: SYNC_PAYMENT_FEE_PROCESS_FAILED, schema: SyncPaymentFeeProcessFailedSchema },
      { name: PATIENT_XC_COLLECTION_NAME, schema: PatientXcSchema },
      { name: PATIENT_RELATION_COLLECTION_NAME, schema: PatientRelationSchema },
      { name: SYNC_CTCH_PATIENT, schema: SyncCTCHPatientSchema },
      { name: SYNC_CTCH_PATIENT_PROCESS, schema: SyncCTCHPatientProcessSchema },
      { name: SYNC_CTCH_PATIENT_PROCESS_FAILED, schema: SyncCTCHPatientProcessFailedSchema },
      { name: SYNC_CTCH_PATIENT_SUCCESS, schema: SyncCTCHPatientSuccessSchema },
      { name: SYNC_CTCH_BOOKING, schema: SyncCTCHBookingSchema },
      { name: SYNC_CTCH_BOOKING_PROCESS, schema: SyncCTCHBookingProcessSchema },
      { name: SYNC_CTCH_BOOKING_PROCESS_FAILED, schema: SyncCTCHBookingProcessFailedSchema },
      { name: SYNC_CTCH_BOOKING_SUCCESS, schema: SyncCTCHBookingSuccessSchema },
      { name: PHONE_SESSION_COLLECTION_NAME, schema: PhoneSessionSchema },
    ]),
    TranslateModule,
    UserModule,
  ],
  providers: [SyncPaymentFeeService, SessionService, SyncUserService, PhoneLoginService, FilesService,
    SmsService, PatientService, PatientMongoService, ReferralCodeService, PaymentFeeGatewayService],
  controllers: [SyncPaymentFeeController],
  exports: [SyncPaymentFeeService],
})
export class SyncPaymentFeeModule { }
