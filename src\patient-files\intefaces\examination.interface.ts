import { IsDateString } from 'class-validator';
import { Document } from 'mongoose';

export interface IExamination extends Document {
    id: string,
    name: { type: string, required: false },
    listImage: { type: [string], required: true },
    dateExamination: { type: string, required: true },
    description: { type: string, required: false },
    code: string,
    patientCode: string,
    userId: string;
    isDeleted?: boolean;
    hospitalName: string;
    source: string;
}
