import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PATIENT_CODE_COLLECTION_NAME, MEDPRO } from './constants';

const Schema = mongoose.Schema;

const PatientCodeSchema = new Schema({
    id: String,
    createTime: Date,
    patientId: String,
    patientCode: String,
    insuranceCode: String,
    insuranceExpiredDate: Date,
    maDKBDHT: { type: String, default: '' },
    partnerId: String,
    appId: String,
    qrCodeContent: String,
}, {
    collection: PATIENT_CODE_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);

export { PatientCodeSchema };
