import * as queryString from 'query-string';    
import { Controller, Body, Post, Get, Headers, Query } from '@nestjs/common';
import { ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { UserZaloService } from './user-zalo.service';
import { CreateUserZaloDto } from './dto/create-user-zalo.dto';
import { GetTokenByAuthorizedCodeDto } from './dto/get-token-by-authorized-code.dto';
import { GetUserResourceByAccessTokenDto } from './dto/get-user-resource-by-access-token.dto';
import { LoginOrRegisterForUserZaloPayByCodeAndCodeVerifierDto } from './dto/login-or-register-for-user-zalo-pay-by-code-and-code-verifier.dto';

@ApiTags("UserZalo")
@Controller('user-zalo')
export class UserZaloController {
    constructor(private readonly userService: UserZaloService) {}

    @Post('create-user')
    @ApiOperation({ summary: 'Zalopay tạo tài khoản.', description: 'Zalopay tạo tài khoản.' })
    async createUserMomo(@Body() formData: CreateUserZaloDto): Promise<any> {
        return this.userService.createUserZalo(formData);
    }

    @Get('create-user-with-token')
    @ApiOperation({
        summary: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.',
        description: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.',
    })
    async createUserWithToken(@Headers('zaloid') token: string, @Headers('appid') appId: string): Promise<any> {
        return await this.userService.createUserWithToken(token, appId);
    }

    @Get("oauth-zalo-v4-params")
    @ApiOperation({
        summary: "Nhận code verifier và đường link để chuyển hướng qua ZaloPay để lấy code",
        description: "Nhận code verifier và đường link để chuyển hướng qua ZaloPay để lấy code - OAuth V1 Code flow for ZaloPay API(https://developers.zalo.me/docs/social-api/tham-khao/user-access-token-v4)"
    })
    async getParamsOAuthV1Code() {
        const urlAndCodeVerifier = await this.userService.getParamsOAuthV1Code();
        const queryFromUrlAndCodeVerifier = queryString.parseUrl(urlAndCodeVerifier.urlOAuthZaloPay).query;

        return {
            ...urlAndCodeVerifier,
            ...queryFromUrlAndCodeVerifier,
        };
    }

    @Get("oauth-zalo-v4-token")
    @ApiOperation({
        summary: "Lấy access token từ code",
        description: "Lấy access token từ code - OAuth V1 Code flow for ZaloPay API(https://developers.zalo.me/docs/social-api/tham-khao/user-access-token-v4)"
    })
    async getAccessTokenOAuthV1Code(
        @Query() getTokenByAuthorizedCodeDto: GetTokenByAuthorizedCodeDto
    ) {
        return this.userService.getUserTokenFromZaloPay(getTokenByAuthorizedCodeDto);
    }

    @Get("oauth-zalo-v4-user-resource")
    @ApiOperation({
        summary: "Lấy thông tin user từ access token",
        description: "Lấy thông tin user từ access token - OAuth V1 Code flow for ZaloPay API(https://developers.zalo.me/docs/social-api/tham-khao/user-access-token-v4)"
    })
    async getUserResourceOAuthV1Code(
        @Query() getUserResourceByAccessTokenDto: GetUserResourceByAccessTokenDto
    ) {
        return this.userService.getUserInfoByAccessTokenFromZaloPay(getUserResourceByAccessTokenDto);
    }

    @Post("login-or-register")
    @ApiOperation({
        summary: "Đăng nhập / Đăng kí cho user từ ZaloPay",
        description: "Đăng nhập / Đăng kí cho user từ ZaloPay"
    })
    handleLoginOrRegisterForUserZaloPayByCodeAndCodeVerifier(
        @Body() loginOrRegisterForUserZaloPayByCodeAndCodeVerifierDto: LoginOrRegisterForUserZaloPayByCodeAndCodeVerifierDto
    ) {
        return this.userService.handleLoginOrRegisterForUserZaloPayByCodeAndCodeVerifier(loginOrRegisterForUserZaloPayByCodeAndCodeVerifierDto)
    }
}
