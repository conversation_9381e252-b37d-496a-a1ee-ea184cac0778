import { HttpException, HttpService, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as FormData from 'form-data';

@Injectable()
export class ConfigsS3Service {
    constructor(private configService: ConfigService, private http: HttpService) {}

    get getS3Url(): string {
        return this.configService.get<string>('S3_URL');
    }

    get getS3Organization(): string {
        return this.configService.get<string>('S3_ORGANIZATION');
    }

    get getS3Token(): string {
        return this.configService.get<string>('S3_TOKEN');
    }

    get getS3HeaderConfig(): any {
        return {
            token: this.getS3Token,
            organization: this.getS3Organization,
            options: 'public',
        };
    }

    async upload(file: any): Promise<any> {
        let resS3: any;
        const api = `${this.getS3Url}/storage/upload/partner`;

        if (!file) {
            throw new HttpException('File không được để trống!', HttpStatus.BAD_REQUEST);
        }

        const formData = new FormData();
        formData.append('file', file?.buffer, file?.originalname);
        try {
            resS3 = await this.http.post(api, formData, {
                headers: {
                    ...formData.getHeaders(),
                    ...this.getS3HeaderConfig,
                },
            }).toPromise();
        } catch (error) {
            console.log(error)
            throw new HttpException(
                `S3 xảy ra lỗi : ${error?.response.message || error?.message}! Xin thử lại sau!`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }

        if (!resS3.data?.fileUrl) {
            throw new HttpException('Upload file không hành công!', HttpStatus.BAD_REQUEST);
        }

        return {
            url: `${resS3.data.fileUrl}`,
        };
    }
    
    async uploadPatientImages(file: any): Promise<any> {
        let resS3: any;
        const api = `${this.getS3Url}/storage/upload/partner`;
        
        if (!file) {
            throw new HttpException('File không được để trống!', HttpStatus.BAD_REQUEST);
        }

        const formData = new FormData();
        formData.append('file', file?.buffer, file?.originalname);
        try {
            resS3 = await this.http.post(api, formData, {
                headers: {
                    ...formData.getHeaders(),
                    ...this.getS3HeaderConfig,
                },
            }).toPromise();
        } catch (error) {
            console.log(error)
            throw new HttpException(
                `S3 xảy ra lỗi : ${error?.response.message || error?.message}! Xin thử lại sau!`,
                HttpStatus.INTERNAL_SERVER_ERROR,
            );
        }
        if (!resS3.data?.fileUrl) {
            throw new HttpException('Upload file không thành công!', HttpStatus.BAD_REQUEST);
        }

        return resS3.data.fileUrl
    }
}
