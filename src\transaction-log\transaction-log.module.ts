import { NewBillLogSchema } from 'src/booking-gateway/schemas/new-bill-log.schema';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { TRANSACTION_LOG_NAME, NEW_BILL_LOG_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { TransactionLogSchema } from 'src/booking-gateway/schemas/transaction-log.schema';
import { TransactionLogController } from './transaction-log.controller';
import { TransactionLogService } from './transaction-log.service';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: TRANSACTION_LOG_NAME, schema: TransactionLogSchema },
            { name: NEW_BILL_LOG_COLLECTION_NAME, schema: NewBillLogSchema },
            { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
        ]),
    ],
    controllers: [TransactionLogController],
    providers: [TransactionLogService],
})
export class TransactionLogsModule { }
