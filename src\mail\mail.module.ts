import { SignInProviderSchema } from './../user/schemas/signin-provider.schema';
import { SIGNIN_PROVIDER_COLLECTION_NAME } from './../user/schemas/constants';
import { MailTemplateSchema } from './schema/mail-template.schema';
import { HttpModule, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { CancelReservationsSchema } from 'src/booking-gateway/schemas/cancel-reservations.schema';
import {
  BOOKING_COLLECTION_NAME, PAYMENT_COLLECTION_NAME,
  HOSPITAL_FEE_COLLECTION_NAME,
  RETRY_TRASACTION_COLLECTION_NAME,
  CANCEL_RESERVATIONS_COLLECTION_NAME,
  MAIL_RECEIVER_COLLECTION_NAME,
  EXPIRED_BOOKING_COLLECTION_NAME,
} from 'src/booking-gateway/schemas/constants';
import { ExpiredBookingSchema } from 'src/booking-gateway/schemas/expired-booking.schema';
import { HospitalFeeSchema } from 'src/booking-gateway/schemas/hospital-fee.schema';
import { MailReceiverSchema } from 'src/booking-gateway/schemas/mail-receiver.schema';
import { PaymentSchema } from 'src/booking-gateway/schemas/payment.schema';
import { RetryTransactionSchema } from 'src/booking-gateway/schemas/retry-transactions.schema';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { CountrySchema } from 'src/country-mongo/schemas/country.schema';
import { EVENT_COLLECTION_NAME, NOTIFICATION_COLLECTION } from 'src/event/schemas/constants';
import { EventSchema } from 'src/event/schemas/event.schema';
import { NotificationSchema } from 'src/event/schemas/notification.schema';
import { FEATURE_COLLECTION_NAME } from 'src/feature-mongo/schemas/constants';
import { FeatureSchema } from 'src/feature-mongo/schemas/feature.schema';
import { GlobalSettingModule } from 'src/global-setting/global-setting.module';
import { BookingNumberSchema } from 'src/his-gateway/schemas/booking-numbers.schema';
import { BookingSlotSchema } from 'src/his-gateway/schemas/booking-slot.schema';
import { BookingHISSchema } from 'src/his-gateway/schemas/booking.schema';
import { CONSTRAINTS_SEQUENCE_NUMBER_COLLECTION_NAME, BOOKING_SLOT_COLLECTION_NAME, BOOKING_HIS_COLLECTION_NAME, BOOKING_NUMBER_COLLECTION_NAME } from 'src/his-gateway/schemas/constants';
import { ConstraintsSequenceNumberSchema } from 'src/his-gateway/schemas/constraints-sequence-number.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { PATIENT_SORT_COLLECTION_NAME, PATIENT_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { PatientVersionSchema } from 'src/patient-mongo/schemas/patient-version.schema';
import { PatientSchema } from 'src/patient-mongo/schemas/patient.schema';
import { PatientSortSchema } from 'src/patient-mongo/schemas/patients-sort.schema';
import { PUSH_DEVICE_COLLECTION_NAME, PUSH_DEVICE_ERROR_COLLECTION_NAME } from 'src/push-device/schemas/constants';
import { PushDeviceErrorSchema } from 'src/push-device/schemas/push-device-error.schema';
import { PushDeviceSchema } from 'src/push-device/schemas/push-device.schema';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import { MailService } from './mail.service';
import { MAIL_PROCESS_COLLECTION, MAIL_TEMPLATE_COLLECTION } from './schema/constant';
import { MailController } from './mail.controller';
import { MailProcessSchema } from './schema/mail-process.schema';

@Module({
  imports: [HttpModule,
    MongooseModule.forFeature([
      { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
      { name: PUSH_DEVICE_COLLECTION_NAME, schema: PushDeviceSchema },
      { name: EVENT_COLLECTION_NAME, schema: EventSchema },
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
      { name: PAYMENT_COLLECTION_NAME, schema: PaymentSchema },
      { name: USER_COLLECTION_NAME, schema: UserSchema },
      { name: HOSPITAL_FEE_COLLECTION_NAME, schema: HospitalFeeSchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
      { name: COUNTRY_COLLECTION_NAME, schema: CountrySchema },
      { name: NOTIFICATION_COLLECTION, schema: NotificationSchema },
      { name: RETRY_TRASACTION_COLLECTION_NAME, schema: RetryTransactionSchema },
      { name: PATIENT_SORT_COLLECTION_NAME, schema: PatientSortSchema },
      { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
      { name: PUSH_DEVICE_ERROR_COLLECTION_NAME, schema: PushDeviceErrorSchema },
      { name: CANCEL_RESERVATIONS_COLLECTION_NAME, schema: CancelReservationsSchema },
      { name: MAIL_RECEIVER_COLLECTION_NAME, schema: MailReceiverSchema },
      { name: PATIENT_VERSION_COLLECTION_NAME, schema: PatientVersionSchema },
      { name: FEATURE_COLLECTION_NAME, schema: FeatureSchema },
      { name: EXPIRED_BOOKING_COLLECTION_NAME, schema: ExpiredBookingSchema, },
      { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
      { name: CONSTRAINTS_SEQUENCE_NUMBER_COLLECTION_NAME, schema: ConstraintsSequenceNumberSchema, },
      { name: BOOKING_SLOT_COLLECTION_NAME, schema: BookingSlotSchema },
      { name: BOOKING_HIS_COLLECTION_NAME, schema: BookingHISSchema },
      { name: BOOKING_NUMBER_COLLECTION_NAME, schema: BookingNumberSchema },
      { name: MAIL_TEMPLATE_COLLECTION, schema: MailTemplateSchema },
      { name: SIGNIN_PROVIDER_COLLECTION_NAME, schema: SignInProviderSchema },
      { name: MAIL_PROCESS_COLLECTION, schema: MailProcessSchema },
    ]),
    GlobalSettingModule,
  ],
  providers: [MailService],
  controllers: [MailController],
})
export class MailModule { }
