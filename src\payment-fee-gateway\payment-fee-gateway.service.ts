import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { RestfulAPIOldHospitalConfigService } from 'src/config/config.rest-api.old.hospital';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { PkhHttpService } from 'src/config/config.http.service';
import { UrlConfigService } from 'src/config/config.url.service';
import * as crypto from 'crypto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { UtilService } from 'src/config/util.service';

@Injectable()
export class PaymentFeeGatewayService {
    private logger = new Logger(PaymentFeeGatewayService.name);
    private paymentHubUrl: string;
    private feeSecretKey: string;

    constructor(
        private readonly restApiMapping: RestfulAPIOldHospitalConfigService,
        private readonly httpService: PkhHttpService,
        private readonly urlConfigService: UrlConfigService,
        private eventEmitter: EventEmitter2,
        private readonly utilService: UtilService,
    ) {
        this.paymentHubUrl = this.urlConfigService.getPaymentHubFeeUrl();
        this.feeSecretKey = this.urlConfigService.getFeeSecretKey();
    }

    async search(code: string): Promise<any> {
        try {
            const data = (await this.searchFeeThirdParty(code).toPromise()).data;
            if (typeof data.error_code !== typeof undefined) {
                throw new HttpException(data, HttpStatus.NOT_FOUND);
            }
            return data;
        } catch (error) {
            const { response } = error;
            if (typeof response !== typeof undefined) {
                const { error_code } = response;
                if (typeof error_code !== typeof undefined) {
                    throw new HttpException('Không tìm thấy phiếu tạm ứng.', HttpStatus.BAD_REQUEST);
                }
            }
            throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async search_v2(partnerId: string, code: string): Promise<any> {
        try {
            let fee: any;
            switch (partnerId) {
                case 'choray':
                    fee = (await this.searchFeeThirdParty_v2(code).toPromise()).data;
                    break;
                case 'umc':
                    fee = await this.getBill(partnerId, code);
                    break;
                default:
                    break;
            }
            if (fee.data.feeDetail.length > 0) {
                if (partnerId === 'umc') {
                    return fee.data.feeDetail.map((element: any) => {
                        let modifyProp = {};
                        if (element.fee_id) {
                            modifyProp = { fee_code: element.fee_id };
                        }
                        if (element.status) {
                            modifyProp = { ...modifyProp, status: Number(element.status) };
                        }
                        return { ...element, ...modifyProp };
                    });
                }
                return fee.data.feeDetail;
            } else {
                throw new HttpException('Không tìm thấy phiếu tạm ứng.', HttpStatus.NOT_FOUND);
            }
        } catch (error) {
            this.logger.error(`Error when exec search_v2() with partnerId: ${partnerId} and feeId: ${code}\nError: ${error.message}`);
            const name = partnerId === 'choray' ? 'searchFeeThirdParty_v2' : 'callFeeHubGetBill';
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name,
                summary: 'Search payment fee detail with fee code',
                nameParent: 'search_v2',
                params: {
                    partnerId,
                    feeId: code,
                },
                errorBody: this.utilService.errorHandler(error),
                response: {},
                message: error?.message || `Lỗi từ ${name}`,
            });
            throw new HttpException('Không tìm thấy phiếu tạm ứng.', HttpStatus.NOT_FOUND);
        }
    }

    // tslint:disable-next-line:variable-name
    callFeeHubGetBill(partner_id: string, check_sum: string, fee_id: string): Observable<AxiosResponse<any>> {
        const bodyData = { check_sum, partner_id, fee_id };
        const baseUrl = `${this.paymentHubUrl}/partner/v1/feehub/getBill`;
        return this.httpService.postHttpRequest(baseUrl, { ...bodyData });
    }

    // for umc fee search
    async getBill(partnerId: string, feeId: string): Promise<any> {
        try {
            const message = `${feeId}|${partnerId}`.trim();
            // generate checksum key
            const checkSum = crypto
                .createHmac('sha1', this.feeSecretKey)
                .update(message)
                .digest('hex');
            return (await this.callFeeHubGetBill(partnerId, checkSum, feeId).toPromise()).data;
        } catch (error) {
            this.logger.error(`Error when exec getBill() with feeId: ${feeId}\nError: ${error.message}`);
            throw new HttpException(`Có lỗi xảy ra khi lấy thông tin hóa đơn, vui lòng kiểm tra lại`, HttpStatus.BAD_REQUEST);
        }
    }

    async searchByQrCode_v2(code: string): Promise<any> {
        try {
            const { data } = (await this.searchFeeQrCodeThirdParty_v2(code).toPromise()).data;
            if (data) {
                if (data.feeDetail.length > 0) {
                    return data.feeDetail;
                } else {
                    throw new HttpException('Không tìm thấy phiếu tạm ứng.', HttpStatus.NOT_FOUND);
                }
            } else {
                throw new HttpException('Không tìm thấy phiếu tạm ứng.', HttpStatus.NOT_FOUND);
            }
        } catch (error) {
            const { status, message } = error;
            throw new HttpException(message, status);
        }
    }

    async searchByQrCode(code: string): Promise<any> {
        try {
            const data = (await this.searchFeeQrCodeThirdParty(code).toPromise()).data;
            if (typeof data.error_code !== typeof undefined) {
                throw new HttpException(data, HttpStatus.NOT_FOUND);
            }
            return data;
        } catch (error) {
            const { response } = error;
            if (typeof response !== typeof undefined) {
                const { error_code } = response;
                if (typeof error_code !== typeof undefined) {
                    throw new HttpException('Không tìm thấy phiếu tạm ứng.', HttpStatus.BAD_REQUEST);
                }
            }
            throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    searchFeeThirdParty_v2(code: string): Observable<AxiosResponse<any>> {
        // const baseUrl = this.restApiMapping.ChoRayRestfulAPI();
        // const url = `${baseUrl}/fee/search_v2`;
        const url = this.urlConfigService.getPaymentHubGetFeeUrl();
        const params = {
            fee_id: code,
        };
        // console.log(url, params);
        return this.httpService.postHttpRequest(url, params);
    }

    searchFeeThirdParty(code: string): Observable<AxiosResponse<any>> {
        const baseUrl = this.restApiMapping.ChoRayRestfulAPI();
        const url = `${baseUrl}/fee/search_v2`;
        const params = {
            fee_id: code,
        };
        return this.httpService.postHttpRequest(url, params);
    }

    searchFeeQrCodeThirdParty_v2(qrcode: string): Observable<AxiosResponse<any>> {
        // const baseUrl = this.restApiMapping.ChoRayRestfulAPI();
        // const url = `${baseUrl}/fee/search_by_qrcode_v2`;
        const url = this.urlConfigService.getPaymentHubSearchQRCodeFeeUrl();
        const params = {
            hashQrcode: qrcode,
        };
        // console.log(url, params);
        return this.httpService.postHttpRequest(url, params);
    }

    searchFeeQrCodeThirdParty(qrcode: string): Observable<AxiosResponse<any>> {
        const baseUrl = this.restApiMapping.ChoRayRestfulAPI();
        const url = `${baseUrl}/fee/search_by_qrcode_v2`;
        const params = {
            qrcode,
        };
        return this.httpService.postHttpRequest(url, params);
    }
}
