define setup
	$(eval repo=$(1))
	$(eval container=$(2))
	$(eval path=$(3))
	$(eval pathrepo=$(3)/$(1))
	${eval environment=$(4)}
	yarn;\
	yarn build:prod;\
	if [ ! -d "$(path)/build/$(container)" ]; then\
		mkdir $(path)/build/$(container);\
		mkdir $(path)/build/$(container)/dist;\
		mkdir $(path)/build/$(container)/client;\
	fi;\
	yes | cp -rf $(pathrepo)/dist/* $(path)/build/$(container)/dist;\
	yes | cp -rf $(pathrepo)/client/* $(path)/build/$(container)/client;\
	yes | cp -f $(pathrepo)/package.json $(path)/build/$(container)/package.json;\
	yes | cp -f $(pathrepo)/yarn.lock $(path)/build/$(container)/yarn.lock;\
	yes | cp -f $(pathrepo)/docker-compose.yml $(path)/build/$(container)/docker-compose.yaml;\
	yes | cp -f $(pathrepo)/Dockerfile $(path)/build/$(container)/Dockerfile;\
	yes | cp -f $(pathrepo)/config/$(environment) $(path)/build/$(container)/.env;\
	cd $(path)/build/$(container);\
	docker-compose --env-file .env up  -d --build $(container);
endef

.PHONY: medpro-api-v2-testing
medpro-api-v2-testing:
	$(call setup,medpro-admin-api,medpro-api-v2-testing,/opt/nodejs,production-v2.env)
.PHONY: medpro-api-v3-testing
medpro-api-v3-testing:
	$(call setup,medpro-admin-api,medpro-api-v3-testing,/opt/nodejs,production-v3.env)