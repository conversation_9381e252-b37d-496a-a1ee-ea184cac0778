import { Module, HttpModule } from '@nestjs/common';
import { UserAccountController } from './user-account.controller';
import { UserAccountService } from './user-account.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
    USER_COLLECTION_NAME,
    SIGNIN_PROVIDER_COLLECTION_NAME,
    CONSTRAINTS_PROVIDER_COLLECTION_NAME,
    CONSTRAINTS_USER_COLLECTION_NAME,
    USER_PROFILE_COLLECTION_NAME,
    HOC_VI_COLLECTION_NAME,
    VI_TRI_CONG_VIEC_COLLECTION_NAME,
    ORG_PROFILE_COLLECTION_NAME,
    REFERRAL_CODE_REGISTER_COLLECTION_NAME,
    USER_APP_COLLECTION_NAME,
    USER_REQUEST_COLLECTION_NAME,
} from 'src/user/schemas/constants';
import { UserSchema } from 'src/user/schemas/user.schema';
import {
    USER_ACCOUNT_COLLECTION_NAME,
    ADMIN_USER_COLLECTION_NAME,
    PAYMENT_HOSPITAL_FEE_COLLECTION_NAME,
    CONSTRAINTS_CARD_COLLECTION_NAME,
    CONSTRAINTS_BILL_COLLECTION_NAME,
} from './schemas/constants';
import { UserAccountSchema } from './schemas/user-account.schema';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import {
    PATIENT_CODE_COLLECTION_NAME,
    PATIENT_COLLECTION_NAME,
    PATIENT_VERSION_COLLECTION_NAME,
    PATIENT_SEARCH_LOG_COLLECTION_NAME,
    RELATIVE_TYPE_COLLECTION_NAME,
    PATIENT_PROFILE_COLLECTION_NAME,
} from 'src/patient-mongo/schemas/constants';
import { PatientCodeSchema } from 'src/patient-mongo/schemas/patient-codes.schema';
import { PatientSchema } from 'src/patient-mongo/schemas/patient.schema';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { JwtUserYTeConfigService } from 'src/config/config.user-yte.jwt.service';
import { PatientMongoService } from 'src/patient-mongo/patient-mongo.service';
import { PROFESSION_COLLECTION_NAME } from 'src/profession-mongo/schemas/constants';
import { NATION_COLLECTION_NAME } from 'src/nation-mongo/schemas/constants';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { PatientVersionSchema } from 'src/patient-mongo/schemas/patient-version.schema';
import { PatientSearchLogSchema } from 'src/patient-mongo/schemas/patient-search-log.schema';
import { SignInProviderSchema } from 'src/user/schemas/signin-provider.schema';
import { ProfessionSchema } from 'src/profession-mongo/schemas/profession.schema';
import { NationSchema } from 'src/nation-mongo/schemas/nation.schema';
import { CountrySchema } from 'src/country-mongo/schemas/country.schema';
import { CitySchema } from 'src/city-mongo/schemas/city.schema';
import { DistrictSchema } from 'src/district-mongo/schemas/district.schema';
import { WardSchema } from 'src/ward-mongo/schemas/ward.schema';
import { RelativeSchema } from 'src/patient-mongo/schemas/relative-mongo.schema';
import { PatientService } from 'src/patient/patient.service';
import { SessionService } from 'src/session/session.service';
import { UserService } from 'src/user/user.service';
import { LocalUserStrategy } from 'src/user/local.user.strategy';
import { UserJwtStrategy } from 'src/user/jwt.strategy';
import { PhoneLoginService } from 'src/phone-login/phone-login.service';
import { SmsService } from 'src/sms/sms.service';
import { UserCBYTeJwtStrategy } from './jwt.strategy';
import { ProviderConstraintSchema } from 'src/user/schemas/provider-constraints.schema';
import { UserConstraintSchema } from 'src/user/schemas/user-constraints.schema';
import { AdminUserSchema } from './schemas/admin-user.schema';
import { PaymentHospitalFeeSchema } from './schemas/payment-hospital-fee.schema';
import {
    PAYMENT_COLLECTION_NAME,
    TRANSACTION_EVENT_NAME,
    BOOKING_SEARCH_COLLECTION_NAME,
    BOOKING_COLLECTION_NAME,
    BOOKING_ORDER_COLLECTION_NAME,
    NEW_BILL_LOG_COLLECTION_NAME,
} from 'src/booking-gateway/schemas/constants';
import { PaymentSchema } from 'src/booking-gateway/schemas/payment.schema';
import { CardConstraintSchema } from './schemas/card-constraints.schema';
import { BillConstraintSchema } from './schemas/bill-constraints.schema';
import { FEEDER_COLLECTION_NAME } from 'src/event-proccessor/schemas/constants';
import { FeederSchema } from 'src/event-proccessor/schemas/feeder.schema';
import { TransactionEventSchema } from 'src/booking-gateway/schemas/transaction-event.schema';
import { HospitalSchema } from 'src/hospital-mongo/schemas/hospital.schema';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { UserProfileSchema } from 'src/user/schemas/user-profile.schema';
import { BookingSearchSchema } from 'src/booking-gateway/schemas/search-booking.schema';
import { FilesService } from 'src/files/files.service';
import { HocViSchema } from 'src/user/schemas/hoc-vi.schema';
import { ViTriCongViecSchema } from 'src/user/schemas/vi-tri-cong-viec.schema';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { REFERRAL_CODE_COLLECTION_NAME } from 'src/referral-code/schemas/constants';
import { ReferralCodeSchema } from 'src/referral-code/schemas/referral-code.schema';
import { ReferralCodeService } from 'src/referral-code/referral-code.service';
import {
    SYNC_DALIEU_PATIENT,
    SYNC_DALIEU_BOOKING,
    SYNC_NHI_DONG_1_PATIENT,
    SYNC_NHI_DONG_1_BOOKING,
    SYNC_DHYD_PATIENT,
    SYNC_DHYD_BOOKING,
} from 'src/event/schemas/constants';
import { SyncDaLieuPatientSchema } from 'src/event/schemas/sync-dalieu-patient.schema';
import { SyncDaLieuBookingSchema } from 'src/event/schemas/sync-dalieu-booking.schema';
import { OrgProfileSchema } from 'src/user/schemas/org-profile.schema';
import { SyncNhiDong1PatientSchema } from 'src/event/schemas/sync-nhidong1-patient.schema';
import { SyncNhiDong1BookingSchema } from 'src/event/schemas/sync-nhidong1-booking.schema';
import { ReferralCodeRegisterSchema } from 'src/user/schemas/referral-code-register';
import { PatientProfileSchema } from 'src/patient-mongo/schemas/patient-profile.schema';
import { SyncDHYDPatientSchema } from 'src/event/schemas/sync-dhyd-patient.schema';
import { SyncDHYDBookingSchema } from 'src/event/schemas/sync-dhyd-booking.schema';
import { BookingOrderSchema } from 'src/booking-gateway/schemas/booking-order';
import { NewBillLogSchema } from 'src/booking-gateway/schemas/new-bill-log.schema';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { GlobalSettingSchema } from 'src/global-setting/schemas/global-setting.schema';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { UserAppSchema } from 'src/user/schemas/user-app.schema';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { ServiceSchema } from 'src/service-mongo/schemas/service.schema';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { SubjectSchema } from 'src/subject-mongo/schemas/subject.schema';
import { UserRequestsSchema } from 'src/user/schemas/user-requests.schema';
import { PatientMongoModule } from '../patient-mongo/patient-mongo.module';
import { UserModule } from 'src/user/user.module';

@Module({
  imports: [
    HttpModule,
    PassportModule.register({ defaultStrategy: 'user-cbyt-jwt' }),
    JwtModule.registerAsync({
      useExisting: JwtUserYTeConfigService,
    }),
    MongooseModule.forFeature([
      { name: USER_COLLECTION_NAME, schema: UserSchema },
      { name: USER_ACCOUNT_COLLECTION_NAME, schema: UserAccountSchema },
      { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
      { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
      { name: PATIENT_CODE_COLLECTION_NAME, schema: PatientCodeSchema },
      { name: PATIENT_VERSION_COLLECTION_NAME, schema: PatientVersionSchema },
      { name: PATIENT_SEARCH_LOG_COLLECTION_NAME, schema: PatientSearchLogSchema },
      { name: SIGNIN_PROVIDER_COLLECTION_NAME, schema: SignInProviderSchema },
      { name: PROFESSION_COLLECTION_NAME, schema: ProfessionSchema },
      { name: NATION_COLLECTION_NAME, schema: NationSchema },
      { name: COUNTRY_COLLECTION_NAME, schema: CountrySchema },
      { name: CITY_COLLECTION_NAME, schema: CitySchema },
      { name: DISTRICT_COLLECTION_NAME, schema: DistrictSchema },
      { name: WARD_COLLECTION_NAME, schema: WardSchema },
      { name: RELATIVE_TYPE_COLLECTION_NAME, schema: RelativeSchema },
      { name: CONSTRAINTS_PROVIDER_COLLECTION_NAME, schema: ProviderConstraintSchema },
      { name: CONSTRAINTS_USER_COLLECTION_NAME, schema: UserConstraintSchema },
      { name: ADMIN_USER_COLLECTION_NAME, schema: AdminUserSchema },
      { name: PAYMENT_HOSPITAL_FEE_COLLECTION_NAME, schema: PaymentHospitalFeeSchema },
      { name: PAYMENT_COLLECTION_NAME, schema: PaymentSchema },
      { name: CONSTRAINTS_CARD_COLLECTION_NAME, schema: CardConstraintSchema },
      { name: CONSTRAINTS_BILL_COLLECTION_NAME, schema: BillConstraintSchema },
      { name: FEEDER_COLLECTION_NAME, schema: FeederSchema },
      { name: TRANSACTION_EVENT_NAME, schema: TransactionEventSchema },
      { name: HOSPITAL_COLLECTION_NAME, schema: HospitalSchema },
      { name: USER_PROFILE_COLLECTION_NAME, schema: UserProfileSchema },
      { name: ORG_PROFILE_COLLECTION_NAME, schema: OrgProfileSchema },
      { name: BOOKING_SEARCH_COLLECTION_NAME, schema: BookingSearchSchema },
      { name: HOC_VI_COLLECTION_NAME, schema: HocViSchema },
      { name: VI_TRI_CONG_VIEC_COLLECTION_NAME, schema: ViTriCongViecSchema },
      { name: REFERRAL_CODE_REGISTER_COLLECTION_NAME, schema: ReferralCodeRegisterSchema },
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
      { name: REFERRAL_CODE_COLLECTION_NAME, schema: ReferralCodeSchema },
      { name: SYNC_DALIEU_PATIENT, schema: SyncDaLieuPatientSchema },
      { name: SYNC_DALIEU_BOOKING, schema: SyncDaLieuBookingSchema },
      { name: SYNC_NHI_DONG_1_PATIENT, schema: SyncNhiDong1PatientSchema },
      { name: SYNC_NHI_DONG_1_BOOKING, schema: SyncNhiDong1BookingSchema },
      { name: SYNC_DHYD_PATIENT, schema: SyncDHYDPatientSchema },
      { name: SYNC_DHYD_BOOKING, schema: SyncDHYDBookingSchema },
      { name: PATIENT_PROFILE_COLLECTION_NAME, schema: PatientProfileSchema },
      { name: BOOKING_ORDER_COLLECTION_NAME, schema: BookingOrderSchema },
      { name: NEW_BILL_LOG_COLLECTION_NAME, schema: NewBillLogSchema },
      { name: GLOBAL_SETTING_COLLECTION_NAME, schema: GlobalSettingSchema },
      { name: USER_APP_COLLECTION_NAME, schema: UserAppSchema },
      { name: USER_REQUEST_COLLECTION_NAME, schema: UserRequestsSchema },
      { name: SERVICE_COLLECTION_NAME, schema: ServiceSchema },
      { name: SUBJECT_COLLECTION_NAME, schema: SubjectSchema },
    ]),
    PatientMongoModule,
    UserModule,
  ],
  controllers: [UserAccountController],
  providers: [UserAccountService, PatientService, SessionService, FilesService,
    LocalUserStrategy, UserCBYTeJwtStrategy, UserJwtStrategy, PhoneLoginService, SmsService, ReferralCodeService, GlobalSettingService],
})
export class UserAccountModule { }
