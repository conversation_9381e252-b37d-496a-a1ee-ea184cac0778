import { Document } from 'mongoose';

export interface IPatient extends Document {
    id?: string;
    readonly surname: string;
    readonly name: string;
    readonly mobile: string;
    mobileLocaleIso: String,
    readonly birthdate: string;
    readonly birthyear: number;
    readonly sex: number;
    readonly cmnd: string;
    readonly email: string;
    readonly profession_id: string;
    profession?: string;
    readonly country_code: string;
    country_id?: string;
    country?: string;
    readonly dantoc_id: string;
    nation?: string;
    readonly city_id: string;
    city?: string;
    readonly district_id: string;
    district?: string;
    readonly ward_id: string;
    ward?: string;
    readonly address: string;
    relation?: {
        readonly relative_name: string;
        readonly relative_mobile: string;
        relativeMobileLocaleIso: string;
        readonly relative_type_id: string;
        readonly relative_email: string;
    };
    patientIdV1?: number;
    patientIdV1DaLieu?: number;
    patientIdV1UMC?: number;
    patientIdV1CTCH?: number;
    patientIdV1ThuDuc?: number;
    relative?: any;
    medpro_id?: string;
    partnerId: string;
    patientCode?: string;
    sourceId: string;
    userId: string;
    code?: string;
    cskhUserId?: string;
    isExam?: boolean;
}
