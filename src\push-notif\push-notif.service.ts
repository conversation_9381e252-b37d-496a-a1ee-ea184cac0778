import { Injectable, Inject, HttpException, HttpStatus } from '@nestjs/common';
import * as OneSignal from 'onesignal-node';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { NotificationSettingService } from 'src/notification-setting/notification-setting.service';
import { PushDeviceService } from 'src/push-device/push-device.service';
import { PushNotifDTO } from './dto/push-notif.dto';
import { find, map, first, chunk, uniq } from 'lodash';
import { OneSignalTeleMedicineConfigService } from 'src/config/config.onesignal.telemedicine.service';
import { PushNotifUserDTO } from './dto/push-notif-user.dto';
import { QueryPushNotifDTO } from './dto/query-push-notif.dto';
import { InjectModel } from '@nestjs/mongoose';
import { RECEIVER_LIST_COLLECTION_NAME, RECEIVER_VERSION_COLLECTION_NAME, MESSAGE_ITEM_COLLECTION_NAME, MESSAGE_GROUP_PHONE_COLLECTION_NAME } from 'src/message-config/schemas/constants';
import { Model } from 'mongoose';
import { IReceiverList } from 'src/message-config/intefaces/receiver-list.inteface';
import * as moment from 'moment';
import * as uuid from 'uuid';
import { IReceiverVersion } from 'src/message-config/intefaces/receiver-version.inteface';
import { CheckPhoneDTO } from 'src/message-config/dto/check-phone.dto';
import { IMessageItem } from 'src/message-config/intefaces/message-item.inteface';
import { EVENT_COLLECTION_NAME, MESSAGE_SEND_RECORD_COLLECTION_NAME } from 'src/event/schemas/constants';
import { IEvent } from 'src/event/intefaces/event.inteface';
import { CloneUserDTO } from 'src/message-config/dto/clone-user.dto';
import { IMessageGroupPhone } from 'src/message-config/intefaces/message-group-phone.inteface';
import { QueryPushGroupPhoneNotifDTO } from './dto/query-push-group-phone-notif.dto';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { IMessageSendRecord } from 'src/event/intefaces/message-send-record.inteface';
import { UrlConfigService } from 'src/config/config.url.service';
import { IsPushDeviceDTO } from './dto/is-push-device.dto';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { UtilService } from 'src/config/util.service';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { PUSH_DEVICE_COLLECTION_NAME } from 'src/push-device/schemas/constants';
import { IPushDevice } from 'src/push-device/intefaces/push-device.inteface';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LOG_SERVICE_EVENT } from '../audit-log/constant';
import { IPushNotifInfom } from '../event/intefaces/push-notif-inform.inteface';
import { MESSAGE_EVENT, MESSAGE_EVENT_NOTIF_INFORM } from '../message-event/constant';

@Injectable()
export class PushNotifService {

    private hospitalSubjectTableName = 'hospital_subject';
    private bookingTableName = 'booking';
    private scheduleTableName = 'schedule';
    private userTableName = 'user';
    private pushDeviceTableName = 'push_device';
    private notificationTableName = 'notification';
    private pushDeviceNhiDong1TableName = 'nd1_push_device';
    private notificationNhiDong1TableName = 'nd1_notification';

    private headerNotif = [
        {
            id: 1,
            name: 'BVTĐ: Thông tin Phiếu khám bệnh',
        },
        {
            id: 2,
            name: 'BVTĐ: Thay đổi Phiếu khám bệnh',
        },
        {
            id: 3,
            name: 'Ưu Đãi',
        },
        {
            id: 4,
            name: 'Hệ thống',
        },
    ];

    private listAppId: Set<{}>;
    private trackingInfo: any = {};
    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        private readonly notificationSettingService: NotificationSettingService,
        private readonly pushDeviceService: PushDeviceService,
        private readonly oneSignalService: OneSignalTeleMedicineConfigService,
        @InjectModel(RECEIVER_LIST_COLLECTION_NAME) private receiverListModel: Model<IReceiverList>,
        @InjectModel(RECEIVER_VERSION_COLLECTION_NAME) private receiverVersionModel: Model<IReceiverVersion>,
        @InjectModel(MESSAGE_ITEM_COLLECTION_NAME) private messageItemModel: Model<IMessageItem>,
        @InjectModel(EVENT_COLLECTION_NAME) private eventModel: Model<IEvent>,
        @InjectModel(MESSAGE_GROUP_PHONE_COLLECTION_NAME) private messageGroupPhoneModel: Model<IMessageGroupPhone>,
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(MESSAGE_SEND_RECORD_COLLECTION_NAME) private messageSendRecordModel: Model<IMessageSendRecord>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        private readonly urlConfigService: UrlConfigService,
        private readonly utilService: UtilService,
        private readonly configRepoService: ConfigRepoService,
        @InjectModel(PUSH_DEVICE_COLLECTION_NAME) private pushDeviceModel: Model<IPushDevice>,
        private readonly eventEmitter: EventEmitter2,

    ) {
        this.listAppId = this.utilService.listAppId();
        this.trackingInfo = { repoName: this.configRepoService.getRepoName() };
    }

    async userDevicesList(partnerId: string): Promise<any> {
        /* Lấy thông tin list phone */
        const list = await this.messageGroupPhoneModel.find({ partnerId }).exec();
        const pluckUserId = map(list, 'phone').map(value => `${value}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843'));
        const data = await this.pkhPatientKnex
            .select(
                `${this.userTableName}.phone`,
                `${this.pushDeviceTableName}.user_id`,
                `${this.pushDeviceTableName}.onesignal_id`,
                `${this.pushDeviceTableName}.onesignal_token`,
            )
            .from(this.userTableName)
            .innerJoin(this.pushDeviceTableName, `${this.pushDeviceTableName}.user_id`, `${this.userTableName}.id`)
            .whereIn(`${this.userTableName}.phone`, pluckUserId);
        return pluckUserId.map(item => {
            const findPhone = find(data, { phone: item });
            if (typeof findPhone !== typeof undefined) {
                return {
                    ...findPhone,
                };
            } else {
                return {
                    phone: item,
                    user_id: 0,
                    onesignal_id: null,
                    onesignal_token: null,
                };
            }
        });
    }

    async cloneUser(data: CloneUserDTO): Promise<any> {
        const yourphone = `${data.phone}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
        // return yourphone;
        /* lấy lại thông tin users */
        const findUser = await this.pkhPatientKnex(this.userTableName)
            .where(`username`, yourphone)
            .first();
        let info: any = {};
        if (findUser) {
            info = findUser;
        } else {
            /* tạo user */
            info = await this.pkhPatientKnex(this.userTableName).insert({
                username: yourphone,
                email: yourphone,
                fullname: yourphone,
            });
        }
        /* Kiểm tra xem push device có user này hay chưa */
        const findPushDevice = await this.pkhPatientKnex(this.pushDeviceTableName)
            .where({ onesignal_id: data.clientId, user_id: info.id })
            .first();
        if (findPushDevice) {
            return findPushDevice;
        } else {
            // insert
            const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');
            return this.pkhPatientKnex(this.pushDeviceTableName).insert({
                user_id: info.id,
                platform: data.platform,
                onesignal_id: data.clientId,
                onesignal_token: data.clientToken,
                date_create: currentTime,
                date_update: currentTime,
            });
        }
    }

    async checkPhone(data: CheckPhoneDTO): Promise<any> {
        const yourphone = `${data.phone}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
        const messageInfo = await this.messageItemModel.findOne({ messageId: data.messageId }).exec();
        if (!messageInfo) {
            throw new HttpException('Không tìm thấy thông tin message.', HttpStatus.NOT_FOUND);
        }
        // console.log('yourMesage', messageInfo);
        /* lấy lại thông tin users */
        if (this.listAppId.has(data.partnerId) && data.partnerId !== 'medpro' && data.partnerId !== 'nhidong1') {
            let findUser;
            try {
                findUser = await this.pkhPatientKnex(this.userTableName)
                    .where(`username`, yourphone)
                    .first();
            } catch (error) {
                console.log('timmmm user', error);
            }
            let info: any = {};
            if (findUser) {
                info = findUser;
            } else {
                /* tạo user */
                try {
                    const [idReturn] = await this.pkhPatientKnex(this.userTableName).insert({
                        username: yourphone,
                        email: yourphone,
                        fullname: yourphone,
                    });
                    /* lấy lại thông tin */
                    info = await this.pkhPatientKnex(this.userTableName).where('id', idReturn).first();
                    if (!info) {
                        throw new HttpException('Không tìm thấy thông tin user thông báo !', HttpStatus.NOT_FOUND);
                    }
                } catch (error) {
                    console.log('loi tao tim user', error);
                }
            }
            // console.log('info', info);
            try {
                /* tìm thông tin trong push device */
                const pushDevices = await this.pkhPatientKnex(this.pushDeviceTableName).where('user_id', info.id);
                // console.log('pushDevices', pushDevices);
                if (pushDevices.length > 0) {
                    const checkUrlNull: any = {};
                    if (!!messageInfo.url) {
                        checkUrlNull.url = messageInfo.url;
                    }
                    const [idNotif] = await this.pkhPatientKnex('notification').insert({
                        title: messageInfo.title,
                        content: messageInfo.content,
                        type: (!!messageInfo.url ? 3 : 4),
                        user_id: info.id,
                        ...checkUrlNull,
                    });
                    /* tìm lại thông tin notif vừa insert */
                    const getResultData = await this.pkhPatientKnex('notification')
                        .where({ id: idNotif }).first();
                    const messageInfoObj = messageInfo.toObject();
                    const reMapping = pushDevices.map(item => {
                        return {
                            id: uuid.v4().replace(/-/g, ''),
                            topicId: 'messages.push-notif',
                            createTime: moment().toISOString(),
                            userId: `${info.id}`,
                            partnerId: data.partnerId,
                            appId: data.partnerId,
                            title: messageInfo.title,
                            content: messageInfo.content,
                            isNotif: true,
                            isPushNotif: true,
                            type: 3,
                            eventData: {
                                ...messageInfoObj,
                                ...item,
                                clientId: item.onesignal_id,
                                clientToken: item.onesignal_token,
                                // type: 1,
                                ...getResultData,
                            },
                        };
                    });
                    // console.log(reMapping);
                    /* Tiến hành tạo event */
                    const insertMulti = await this.eventModel.insertMany(reMapping);
                    // console.log(insertMulti);
                    return {
                        isOK: true,
                    };
                } else {
                    throw new HttpException('Không tìm thấy thông tin thiết bị nhận thông báo !', HttpStatus.NOT_FOUND);
                }
            } catch (error) {
                console.log('errror final', error);
            }

        } else {
            const getPartnerConfig = await this.partnerConfigModel.findOne({
                partnerId: data.partnerId,
            }, { partnerId: true, notifAppId: true, notifApiKey: true }).exec();

            if (getPartnerConfig && getPartnerConfig.notifApiKey !== '' && getPartnerConfig.notifAppId !== '') {
                /* dành cho mấy bệnh viện mới */
                const checkUserMongo: IUser = await this.pushDeviceService.getUserByUsernameMedproId(yourphone);
                // return checkUserMongo._id;
                if (checkUserMongo) { /* có thông tin user thì kiếm tục xử lý */
                    const listDevices = await this.pushDeviceService.getPushDevicesByUserId(checkUserMongo._id, data.partnerId);
                    if (listDevices) {
                        const messageInfoObj = messageInfo.toObject();

                        const devices = listDevices.map(item => {
                            return {
                                id: uuid.v4().replace(/-/g, ''),
                                clientId: item.clientId,
                                clientToken: item.clientToken,
                            };
                        });

                        const eventObj = {
                            id: uuid.v4().replace(/-/g, ''),
                            topicId: 'messages.push-inform-notif',
                            createTime: moment().toISOString(),
                            userId: checkUserMongo._id,
                            partnerId: data.partnerId,
                            appId: data.partnerId,
                            title: messageInfo.title,
                            content: messageInfo.content,
                            isNotif: true,
                            isPushNotif: true,
                            type: 100,
                            eventData: {
                                ...messageInfoObj,
                            },
                            devices,
                        };

                        /* Tiến hành tạo event */
                        await this.eventModel.create(eventObj);

                        return {
                            isOK: true,
                        };
                    } else {
                        throw new HttpException('Không tìm thấy thông tin thiết bị nhận thông báo !', HttpStatus.NOT_FOUND);
                    }
                } else {
                    throw new HttpException('User chưa đăng ký medproId', HttpStatus.NOT_FOUND);
                }
            }
        }
    }

    async checkPhoneNhidong1(data: CheckPhoneDTO): Promise<any> {
        const yourphone = `${data.phone}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
        const messageInfo = await this.messageItemModel.findOne({ messageId: data.messageId }).exec();
        if (!messageInfo) {
            throw new HttpException('Không tìm thấy thông tin message.', HttpStatus.NOT_FOUND);
        }
        /* lấy lại thông tin users */
        const findUser = await this.pkhPatientKnex(this.userTableName)
            .where(`username`, yourphone)
            .first();
        let info: any = {};
        if (findUser) {
            info = findUser;
        } else {
            /* tạo user */
            info = await this.pkhPatientKnex(this.userTableName).insert({
                username: yourphone,
                email: yourphone,
                fullname: yourphone,
            });
        }
        /* tìm thông tin trong push device */
        const pushDevices = await this.pkhPatientKnex(this.pushDeviceNhiDong1TableName).where('user_id', info.id);
        if (pushDevices.length > 0) {
            const checkUrlNull: any = {};
            if (!!messageInfo.url) {
                checkUrlNull.url = messageInfo.url;
            }
            const [idNotif] = await this.pkhPatientKnex(this.notificationNhiDong1TableName).insert({
                title: messageInfo.title,
                content: messageInfo.content,
                type: (!!messageInfo.url ? 3 : 4),
                user_id: info.id,
                ...checkUrlNull,
            });
            /* tìm lại thông tin notif vừa insert */
            const getResultData = await this.pkhPatientKnex(this.notificationNhiDong1TableName)
                .where({ id: idNotif }).first();
            const messageInfoObj = messageInfo.toObject();
            const reMapping = pushDevices.map(item => {
                return {
                    id: uuid.v4().replace(/-/g, ''),
                    topicId: 'messages.push-notif',
                    createTime: moment().toISOString(),
                    userId: `${info.id}`,
                    partnerId: data.partnerId,
                    appId: data.partnerId,
                    title: messageInfo.title,
                    content: messageInfo.content,
                    isNotif: true,
                    isPushNotif: true,
                    type: 3,
                    eventData: {
                        ...messageInfoObj,
                        ...item,
                        clientId: item.onesignal_id,
                        clientToken: item.onesignal_token,
                        ...getResultData,
                    },
                };
            });
            /* Tiến hành tạo event */
            await this.eventModel.insertMany(reMapping);
            return {
                isOK: true,
            };
        } else {
            throw new HttpException('Không tìm thấy thông tin thiết bị nhận thông báo !', HttpStatus.NOT_FOUND);
        }
    }

    async buildReceiverPhoneCheckList(data: QueryPushGroupPhoneNotifDTO): Promise<any> {
        /* Lấy thông tin phone theo groupId, partnerId */
        const messageGroupPhone = await this.messageGroupPhoneModel.find({
            partnerId: data.partnerId,
            groupId: data.groupId,
        });
        /* lấy thông tin message info */
        const messageInfo = await this.messageItemModel.findOne({ messageId: data.messageId }).exec();
        /* tìm thông tin users theo phone */
        const pluckPhones = map(messageGroupPhone, 'phone');
        const mappingPhone = pluckPhones.map(item => `${item}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843'));

        if (this.listAppId.has(data.partnerId) && data.partnerId !== 'medpro' && data.partnerId !== 'nhidong1') {
            const users = await this.pkhPatientKnex(this.userTableName)
                .select('id')
                .whereIn(`phone`, mappingPhone);
            const mappingUsers = map(users, 'id');
            /* tìm thông tin push device */
            const uniqUser = uniq(mappingUsers);
            const mappNotif: any = {};
            for await (const u of uniqUser) {
                /* tiến hành insert vào trong bảng notifications */
                const checkUrlNull: any = {};
                if (!!messageInfo.url) {
                    checkUrlNull.url = messageInfo.url;
                }
                const [idNotif] = await this.pkhPatientKnex('notification').insert({
                    title: messageInfo.title,
                    content: messageInfo.content,
                    type: (!!messageInfo.url ? 3 : 4),
                    user_id: u,
                    ...checkUrlNull,
                });
                /* tìm lại thông tin notif vừa insert */
                const getResultData = await this.pkhPatientKnex('notification')
                    .where({ id: idNotif }).first();
                mappNotif[u] = getResultData;
            }
            // return mappingUsers;
            /* tìm thông tin trong push device */
            const pushDevices = await this.pkhPatientKnex(this.pushDeviceTableName).whereIn('user_id', mappingUsers);
            if (pushDevices.length > 0) {
                /* Tiến hành gửi message */
                /* lấy thông tin message config */
                const reMapping = pushDevices.map(item => {
                    const notifUMC = mappNotif[item.user_id] || {};
                    return {
                        id: uuid.v4().replace(/-/g, ''),
                        topicId: 'messages.push-notif',
                        createTime: moment().toISOString(),
                        userId: `${item.user_id}`,
                        partnerId: data.partnerId,
                        appId: data.partnerId,
                        title: messageInfo.title,
                        content: messageInfo.content,
                        isNotif: true,
                        isPushNotif: true,
                        type: 3,
                        eventData: {
                            ...messageInfo.toObject(),
                            ...item,
                            clientId: item.onesignal_id,
                            clientToken: item.onesignal_token,
                            ...notifUMC,
                        },
                    };
                });
                /* Tiến hành tạo event */
                await this.eventModel.insertMany(reMapping);
                return {
                    isOK: true,
                };
            } else {
                return {
                    isOK: false,
                    message: 'Không tìm thấy thông tin thiết bị nhận thông báo !',
                };
            }
        } else {
            /* dành cho các bv mới */
            const usersPhone: IUser[] = await this.pushDeviceService.getUsersByUserPhoneList(mappingPhone);
            const transformObjs = usersPhone.map(item => ({ id: item._id }));
            const mappingUsers = map(transformObjs, 'id');
            /* tìm thông tin push device */
            const uniqUser = uniq(mappingUsers);

            const chunkData = chunk(uniqUser, 5000);

            for (let chunkDatum of chunkData) {
                const messageInfoObj = messageInfo.toObject();
                const userEvents = chunkDatum.map(userId => {
                    const event = {
                        id: uuid.v4().replace(/-/g, ''),
                        topicId: 'messages.push-inform-notif',
                        createTime: moment().toISOString(),
                        partnerId: data.partnerId,
                        appId: data.partnerId,
                        title: messageInfo.title,
                        content: messageInfo.content,
                        isNotif: true,
                        isPushNotif: false,
                        type: 100,
                        eventData: {
                            ...messageInfoObj,
                            type: 100,
                        },
                        userId,
                        repoName: 'PushInformNotif',
                    }

                    this.eventEmitter.emit(MESSAGE_EVENT_NOTIF_INFORM, {
                        ...event,
                        topic: 'messages.push-inform-notif',
                        isPushNotif: true,
                    })
                    return event;
                })

                await this.eventModel.insertMany(userEvents);

            }

            return {
                uniqUserId: uniqUser.length,
            };
        }
    }

    async buildReceiverPhoneCheckListNhiDong1(data: QueryPushGroupPhoneNotifDTO): Promise<any> {
        /* Lấy thông tin phone theo groupId, partnerId */
        const messageGroupPhone = await this.messageGroupPhoneModel.find({
            partnerId: data.partnerId,
            groupId: data.groupId,
        });
        /* lấy thông tin message info */
        const messageInfo = await this.messageItemModel.findOne({ messageId: data.messageId }).exec();
        /* tìm thông tin users theo phone */
        const pluckPhones = map(messageGroupPhone, 'phone');
        const mappingPhone = pluckPhones.map(item => `${item}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843'));

        const users = await this.pkhPatientKnex(this.userTableName)
            .select('id')
            .whereIn(`phone`, mappingPhone);
        const mappingUsers = map(users, 'id');
        /* tìm thông tin push device */
        const uniqUser = uniq(mappingUsers);
        const mappNotif: any = {};
        for await (const u of uniqUser) {
            /* tiến hành insert vào trong bảng notifications */
            const checkUrlNull: any = {};
            if (!!messageInfo.url) {
                checkUrlNull.url = messageInfo.url;
            }
            const [idNotif] = await this.pkhPatientKnex(this.notificationNhiDong1TableName).insert({
                title: messageInfo.title,
                content: messageInfo.content,
                type: (!!messageInfo.url ? 3 : 4),
                user_id: u,
                ...checkUrlNull,
            });
            /* tìm lại thông tin notif vừa insert */
            const getResultData = await this.pkhPatientKnex(this.notificationNhiDong1TableName)
                .where({ id: idNotif }).first();
            mappNotif[u] = getResultData;
        }
        /* tìm thông tin trong push device */
        const pushDevices = await this.pkhPatientKnex(this.pushDeviceNhiDong1TableName).whereIn('user_id', mappingUsers);
        if (pushDevices.length > 0) {
            /* Tiến hành gửi message */
            /* lấy thông tin message config */
            const reMapping = pushDevices.map(item => {
                const notifUMC = mappNotif[item.user_id] || {};
                return {
                    id: uuid.v4().replace(/-/g, ''),
                    topicId: 'messages.push-notif',
                    createTime: moment().toISOString(),
                    userId: `${item.user_id}`,
                    partnerId: data.partnerId,
                    appId: data.partnerId,
                    title: messageInfo.title,
                    content: messageInfo.content,
                    isNotif: true,
                    isPushNotif: true,
                    type: 3,
                    eventData: {
                        ...messageInfo.toObject(),
                        ...item,
                        clientId: item.onesignal_id,
                        clientToken: item.onesignal_token,
                        ...notifUMC,
                    },
                };
            });
            /* Tiến hành tạo event */
            await this.eventModel.insertMany(reMapping);
            return {
                isOK: true,
            };
        } else {
            return {
                isOK: false,
                message: 'Không tìm thấy thông tin thiết bị nhận thông báo !',
            };
        }
    }

    async testInsertNotif(): Promise<any> {
        const messageInfo = {
            title: 'Nhantest',
            content: 'nhantest ontent',
            url: 'hfdfdf',
        };
        const [idNotif] = await this.pkhPatientKnex('notification').insert({
            title: messageInfo.title,
            content: messageInfo.content,
            type: (!!messageInfo.url ? 3 : 4),
            url: messageInfo.url,
        });
        const getResultData = await this.pkhPatientKnex('notification')
            .where({ id: idNotif }).first();
        return getResultData;
    }

    async buildReceiverList(queryPushNotifDTO: QueryPushNotifDTO): Promise<any> {
        const traceId = uuid.v4().replace(/-/g, '');
        const info = {
            functionName: 'buildReceiverList',
            ...this.trackingInfo,
        };
        /* lấy thông tin gần nhất theo group(partnerId, groupId) orderBy valueOfTimed DESC limit 1 */
        const versionList = await this.receiverVersionModel
            .find({
                partnerId: queryPushNotifDTO.partnerId,
                groupId: queryPushNotifDTO.groupId,
                requestId: queryPushNotifDTO.requestId,
            })
            .sort({ valueOfTime: 'desc' })
            .limit(1)
            .exec();
        // return checkDate;
        let fromDate = '1975-04-30 12:12:12';
        if (versionList.length > 0) {
            const value = first(versionList).valueOfTime;
            fromDate = moment(value).format('YYYY-MM-DD HH:mm:ss');
        }
        const current = moment();
        const valueOfTime = current.valueOf(); // moment('2020-03-25 16:30:25', 'YYYY-MM-DD HH:mm:ss').valueOf(); // moment().valueOf();
        // let data = [];

        if (this.listAppId.has(queryPushNotifDTO.partnerId) && queryPushNotifDTO.partnerId !== 'medpro' && queryPushNotifDTO.partnerId !== 'nhidong1') {
            if (Number(queryPushNotifDTO.groupId) > 0) {
                const data = await this.pkhPatientKnex
                    .select(
                        `${this.bookingTableName}.user_id as userId`,
                    )
                    .from(this.bookingTableName)
                    .innerJoin(this.scheduleTableName, `${this.scheduleTableName}.id`, `${this.bookingTableName}.schedule_id`)
                    .innerJoin(this.hospitalSubjectTableName, `${this.hospitalSubjectTableName}.id`, `${this.scheduleTableName}.hospital_subject_id`)
                    .where(`${this.hospitalSubjectTableName}.subject_id`, queryPushNotifDTO.groupId)
                    .where(`${this.hospitalSubjectTableName}.status`, 1)
                    .whereRaw(`${this.bookingTableName}.date_create >= ? AND ${this.bookingTableName}.date_create <= ?`,
                        [fromDate, current.format('YYYY-MM-DD HH:mm:ss')])
                    .groupBy(`${this.bookingTableName}.user_id`);
                if (data.length > 0) {
                    /* Tiến hành insert vào trong receiver version */
                    const uuidRedf = uuid.v4().replace(/-/g, '');
                    const receiverVersion = new this.receiverVersionModel({
                        id: uuidRedf,
                        groupId: queryPushNotifDTO.groupId,
                        phone: queryPushNotifDTO.phone,
                        messageId: queryPushNotifDTO.messageId,
                        requestId: queryPushNotifDTO.requestId,
                        valueOfTime,
                        partnerId: queryPushNotifDTO.partnerId,
                        active: true,
                    });
                    await receiverVersion.save();
                    /* tiến hành chunk data */
                    const chunkData = chunk(data, 10000);
                    for await (const chunkDetail of chunkData) {
                        /* tiến hành lưu thông tin vào trong bảng chờ */
                        const dataInsert = chunkDetail.map((item: { userId: number }) => ({
                            groupId: queryPushNotifDTO.groupId,
                            userId: item.userId,
                            valueOfTime,
                            partnerId: queryPushNotifDTO.partnerId,
                            versionId: uuidRedf,
                            isSync: true,
                        }));
                        await this.receiverListModel.insertMany(dataInsert);
                    }

                    return {
                        isOK: true,
                    };
                } else {
                    /* action cái dòng hiện tại lên */
                    if (versionList.length > 0) {
                        const firstRecord = first(versionList);
                        await this.receiverVersionModel.findByIdAndUpdate({ _id: firstRecord._id }, { active: true });
                        return {
                            isOK: true,
                        };
                    }
                }
                return {
                    isOK: false,
                };
            } else {
                const partners = await this.partnerConfigModel.find({}, { partnerId: true, notifAppId: true, notifApiKey: true }).exec();
                const messageInfo = await this.messageItemModel.findOne({ messageId: queryPushNotifDTO.messageId }).exec();
                const findAppId = find(partners, { partnerId: queryPushNotifDTO.partnerId });
                // console.log('findAppId', findAppId);
                if (typeof findAppId !== typeof undefined && findAppId.notifApiKey !== '' && findAppId.notifAppId !== '') {
                    const client = new OneSignal.Client(findAppId.notifAppId, findAppId.notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });
                    const isAllowAllUsersSegment: Set<any> = new Set(['0', 'all']);
                    let segments = isAllowAllUsersSegment.has(queryPushNotifDTO.groupId) ? 'All-Users' : 'TEST-USER';
                    // console.log('segments', segments);
                    const env = this.urlConfigService.getEnv();
                    let idNotif = 0;
                    if (env !== 'PRODUCTION') {
                        segments = `TEST-USER`;
                        /* tiến hành insert vào trong bảng notifications */
                        const [idNotif2] = await this.pkhPatientKnex('notification').insert({
                            title: messageInfo.title,
                            content: messageInfo.content,
                            type: 3,
                            url: messageInfo.url,
                            user_id: 80367, // may cua mr Nhan
                        });
                        idNotif = idNotif2;
                    } else {
                        /* tiến hành insert vào trong bảng notifications */
                        const checkUrlNull: any = {};
                        if (!!messageInfo.url) {
                            checkUrlNull.url = messageInfo.url;
                        }
                        try {
                            const [idNotif2] = await this.pkhPatientKnex('notification').insert({
                                title: messageInfo.title,
                                content: messageInfo.content,
                                type: (!!messageInfo.url ? 3 : 4),
                                ...checkUrlNull,
                            });
                            idNotif = idNotif2;
                        } catch (error) {
                            console.log('erorr insert notif', error);
                        }

                    }
                    /* tìm lại thông tin notif vừa insert */
                    const getResultData = await this.pkhPatientKnex('notification')
                        .where({ id: idNotif }).first();
                    const { url, ...restNotif } = getResultData;
                    const objRes: any = {};
                    if (!!messageInfo.url) {
                        objRes.url = messageInfo.url;
                    }
                    const defaultNotif = {
                        contents: {
                            en: messageInfo.content,
                            vi: messageInfo.content,
                        },
                        headings: {
                            en: messageInfo.title,
                            vi: messageInfo.title,
                        },
                        data: { ...restNotif, ...objRes, ...info },
                        included_segments: [segments],
                    };
                    try {
                        const response = await client.createNotification({ ...defaultNotif });
                        // console.log('response', response);
                        const { body } = response;
                        if (body) {
                            const { id, recipients } = body;
                            /* cập nhật thông tin vào trong message send recordS */
                            await this.messageSendRecordModel.findOneAndUpdate(
                                {
                                    id: queryPushNotifDTO.requestId,
                                },
                                {
                                    clientViewId: id,
                                    send_quantity: recipients,
                                    send_status: 'COMPLETED',
                                    completed_quantity: recipients,
                                },
                            ).exec();
                            return {
                                message: 'Gửi thông báo thành công!',
                                isOK: true,
                            };
                        } else {
                            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                                name: 'buildReceiverList',
                                summary: 'Push serve for Portal',
                                nameParent: 'buildReceiverList',
                                params: { ...queryPushNotifDTO, traceId },
                                errorBody: { ...body },
                                message: 'Lỗi từ OneSignal',
                            });
                            return {
                                isOK: false,
                                message: 'Gửi KHÔNG thành công. Vui lòng thử lại sau!',
                            };
                        }
                    } catch (error) {
                        console.log('errororrr', error);
                        this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                            name: 'buildReceiverList',
                            summary: 'Push serve for Portal',
                            nameParent: 'buildReceiverList',
                            params: { ...queryPushNotifDTO, traceId, segments },
                            errorBody: this.utilService.errorHandler(error),
                            message: error?.message,
                        });
                        return {
                            isOK: false,
                            message: 'Gửi KHÔNG thành công. Vui lòng thử lại sau.',
                        };
                    }
                }
            }
        } else {
            /* dành cho các bv mới */
            if (queryPushNotifDTO.groupId !== 'all') {
                // tìm booking theo subjectId */
                const dataBookings = await this.bookingModel
                    .find({
                        subjectId: queryPushNotifDTO.groupId,
                        partnerId: queryPushNotifDTO,
                    }, { userId: true })
                    .exec();
                if (dataBookings.length > 0) {
                    /* Tiến hành insert vào trong receiver version */
                    const uuidRedf = uuid.v4().replace(/-/g, '');
                    const receiverVersion = new this.receiverVersionModel({
                        id: uuidRedf,
                        groupId: queryPushNotifDTO.groupId,
                        phone: queryPushNotifDTO.phone,
                        messageId: queryPushNotifDTO.messageId,
                        requestId: queryPushNotifDTO.requestId,
                        valueOfTime,
                        partnerId: queryPushNotifDTO.partnerId,
                        active: true,
                    });
                    await receiverVersion.save();
                    /* tiến hành chunk data */
                    const chunkData = chunk(dataBookings, 10000);
                    for await (const chunkDetail of chunkData) {
                        /* tiến hành lưu thông tin vào trong bảng chờ */
                        const dataInsert = chunkDetail.map(item => ({
                            groupId: queryPushNotifDTO.groupId,
                            userMongo: item.userId,
                            valueOfTime,
                            partnerId: queryPushNotifDTO.partnerId,
                            versionId: uuidRedf,
                            isSync: true,
                        }));
                        await this.receiverListModel.insertMany(dataInsert);
                    }

                    return {
                        isOK: true,
                    };
                } else {
                    /* action cái dòng hiện tại lên */
                    if (versionList.length > 0) {
                        const firstRecord = first(versionList);
                        await this.receiverVersionModel.findByIdAndUpdate({ _id: firstRecord._id }, { active: true });
                        return {
                            isOK: true,
                        };
                    }
                }
                return {
                    isOK: false,
                };

            } else { /* gửi All-Users */
                const getPartner = await this.partnerConfigModel.findOne({
                    partnerId: queryPushNotifDTO.partnerId,
                }, { partnerId: true, notifAppId: true, notifApiKey: true }).exec();
                const messageInfo = await this.messageItemModel.findOne({ messageId: queryPushNotifDTO.messageId }).exec();
                if (getPartner && getPartner.notifApiKey !== '' && getPartner.notifAppId !== '') {
                    const client = new OneSignal.Client(getPartner.notifAppId, getPartner.notifApiKey,
                        { apiRoot: 'https://onesignal.com/api/v1' });
                    let segments = queryPushNotifDTO.groupId === 'all' ? 'All-Users' : 'TEST-USER';
                    const env = this.urlConfigService.getEnv();
                    if (env !== 'PRODUCTION') {
                        segments = `TEST-USER`;
                    }
                    const messageObj = messageInfo.toObject();
                    const defaultNotif = {
                        contents: {
                            en: messageInfo.content,
                            vi: messageInfo.content,
                        },
                        headings: {
                            en: messageInfo.title,
                            vi: messageInfo.title,
                        },
                        data: {
                            ...messageObj,
                            topicId: 'messages.push-inform-notif',
                            type: 100,
                            ...info,
                        },
                        included_segments: [segments],
                    };
                    try {
                        const response = await client.createNotification({ ...defaultNotif });
                        const { body } = response;
                        if (body) {
                            const { id, recipients } = body;
                            /* cập nhật thông tin vào trong message send recordS */
                            await this.messageSendRecordModel.findOneAndUpdate(
                                {
                                    id: queryPushNotifDTO.requestId,
                                },
                                {
                                    clientViewId: id,
                                    send_quantity: recipients,
                                    send_status: 'COMPLETED',
                                    completed_quantity: recipients,
                                },
                            ).exec();
                            return {
                                message: 'Gửi thông báo thành công!',
                                isOK: true,
                            };
                        } else {
                            return {
                                isOK: false,
                                message: 'Gửi KHÔNG thành công. Vui lòng thử lại sau!',
                            };
                        }
                    } catch (error) {
                        return {
                            isOK: false,
                            message: 'Gửi KHÔNG thành công. Vui lòng thử lại sau.',
                        };
                    }
                }
            }
        }
    }

    async buildReceiverListNhiDong1(queryPushNotifDTO: QueryPushNotifDTO): Promise<any> {
        const traceId = uuid.v4().replace(/-/g, '');
        const info = {
            functionName: 'buildReceiverListNhiDong1',
            ...this.trackingInfo,
        };
        /* lấy thông tin gần nhất theo group(partnerId, groupId) orderBy valueOfTimed DESC limit 1 */
        const versionList = await this.receiverVersionModel
            .find({
                partnerId: queryPushNotifDTO.partnerId,
                groupId: queryPushNotifDTO.groupId,
                requestId: queryPushNotifDTO.requestId,
            })
            .sort({ valueOfTime: 'desc' })
            .limit(1)
            .exec();
        let fromDate = '1975-04-30 12:12:12';
        if (versionList.length > 0) {
            const value = first(versionList).valueOfTime;
            fromDate = moment(value).format('YYYY-MM-DD HH:mm:ss');
        }
        const current = moment();
        const valueOfTime = current.valueOf(); // moment('2020-03-25 16:30:25', 'YYYY-MM-DD HH:mm:ss').valueOf(); // moment().valueOf();
        if (Number(queryPushNotifDTO.groupId) > 0) {
            const data = await this.pkhPatientKnex
                .select(
                    `${this.bookingTableName}.user_id as userId`,
                )
                .from(this.bookingTableName)
                .innerJoin(this.scheduleTableName, `${this.scheduleTableName}.id`, `${this.bookingTableName}.schedule_id`)
                .innerJoin(this.hospitalSubjectTableName, `${this.hospitalSubjectTableName}.id`, `${this.scheduleTableName}.hospital_subject_id`)
                .where(`${this.hospitalSubjectTableName}.subject_id`, queryPushNotifDTO.groupId)
                .where(`${this.hospitalSubjectTableName}.status`, 1)
                .whereRaw(`${this.bookingTableName}.date_create >= ? AND ${this.bookingTableName}.date_create <= ?`,
                    [fromDate, current.format('YYYY-MM-DD HH:mm:ss')])
                .groupBy(`${this.bookingTableName}.user_id`);
            if (data.length > 0) {
                /* Tiến hành insert vào trong receiver version */
                const uuidRedf = uuid.v4().replace(/-/g, '');
                const receiverVersion = new this.receiverVersionModel({
                    id: uuidRedf,
                    groupId: queryPushNotifDTO.groupId,
                    phone: queryPushNotifDTO.phone,
                    messageId: queryPushNotifDTO.messageId,
                    requestId: queryPushNotifDTO.requestId,
                    valueOfTime,
                    partnerId: queryPushNotifDTO.partnerId,
                    active: true,
                });
                await receiverVersion.save();
                /* tiến hành chunk data */
                const chunkData = chunk(data, 10000);
                for await (const chunkDetail of chunkData) {
                    /* tiến hành lưu thông tin vào trong bảng chờ */
                    const dataInsert = chunkDetail.map((item: { userId: number }) => ({
                        groupId: queryPushNotifDTO.groupId,
                        userId: item.userId,
                        valueOfTime,
                        partnerId: queryPushNotifDTO.partnerId,
                        versionId: uuidRedf,
                        isSync: true,
                    }));
                    await this.receiverListModel.insertMany(dataInsert);

                }
                return {
                    isOK: true,
                };
            } else {
                /* action cái dòng hiện tại lên */
                if (versionList.length > 0) {
                    const firstRecord = first(versionList);
                    await this.receiverVersionModel.findByIdAndUpdate({ _id: firstRecord._id }, { active: true });
                    return {
                        isOK: true,
                    };
                }
            }
            return {
                isOK: false,
            };
        } else {
            const partners = await this.partnerConfigModel.find({}, { partnerId: true, notifAppId: true, notifApiKey: true }).exec();
            const messageInfo = await this.messageItemModel.findOne({ messageId: queryPushNotifDTO.messageId }).exec();
            const findAppId = find(partners, { partnerId: queryPushNotifDTO.partnerId });
            if (typeof findAppId !== typeof undefined && findAppId.notifApiKey !== '' && findAppId.notifAppId !== '') {
                const client = new OneSignal.Client(findAppId.notifAppId, findAppId.notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });
                const isAllowAllUsersSegment: Set<any> = new Set(['0', 'all']);
                let segments = isAllowAllUsersSegment.has(queryPushNotifDTO.groupId) ? 'All-Users' : 'TEST-USER';
                //  queryPushNotifDTO.segments ? queryPushNotifDTO.segments : ;
                const env = this.urlConfigService.getEnv();
                let idNotif = 0;
                if (env !== 'PRODUCTION') {
                    segments = `TEST-USER`;
                    /* tiến hành insert vào trong bảng notifications */
                    const [idNotif2] = await this.pkhPatientKnex(this.notificationNhiDong1TableName).insert({
                        title: messageInfo.title,
                        content: messageInfo.content,
                        type: 3,
                        url: messageInfo.url,
                        user_id: 80367, // may cua mr Nhan
                    });
                    idNotif = idNotif2;
                } else {
                    /* tiến hành insert vào trong bảng notifications */
                    const checkUrlNull: any = {};
                    if (!!messageInfo.url) {
                        checkUrlNull.url = messageInfo.url;
                    }
                    const [idNotif2] = await this.pkhPatientKnex(this.notificationNhiDong1TableName).insert({
                        title: messageInfo.title,
                        content: messageInfo.content,
                        type: (!!messageInfo.url ? 3 : 4),
                        ...checkUrlNull,
                    });
                    idNotif = idNotif2;
                }
                /* tìm lại thông tin notif vừa insert */
                const getResultData = await this.pkhPatientKnex(this.notificationNhiDong1TableName)
                    .where({ id: idNotif }).first();
                const { url, ...restNotif } = getResultData;
                const objRes: any = {};
                if (!!messageInfo.url) {
                    objRes.url = messageInfo.url;
                }
                const defaultNotif = {
                    contents: {
                        en: messageInfo.content,
                        vi: messageInfo.content,
                    },
                    headings: {
                        en: messageInfo.title,
                        vi: messageInfo.title,
                    },
                    data: { ...restNotif, ...objRes, ...info },
                    included_segments: [segments],
                };
                try {
                    const response = await client.createNotification({ ...defaultNotif });
                    const { body } = response;
                    if (body) {
                        const { id, recipients } = body;
                        /* cập nhật thông tin vào trong message send recordS */
                        await this.messageSendRecordModel.findOneAndUpdate(
                            {
                                id: queryPushNotifDTO.requestId,
                            },
                            {
                                clientViewId: id,
                                send_quantity: recipients,
                                send_status: 'COMPLETED',
                                completed_quantity: recipients,
                            },
                        ).exec();
                        return {
                            message: 'Gửi thông báo thành công!',
                            isOK: true,
                        };
                    } else {
                        this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                            name: 'buildReceiverListNhiDong1',
                            summary: 'Push NhiDong1 serve for Portal',
                            nameParent: 'buildReceiverListNhiDong1',
                            params: { ...queryPushNotifDTO, traceId, segments },
                            errorBody: { ...body },
                            message: 'Lỗi từ OneSignal',
                        });
                        return {
                            isOK: false,
                            message: 'Gửi KHÔNG thành công. Vui lòng thử lại sau!',
                        };
                    }
                } catch (error) {
                    this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                        name: 'buildReceiverListNhiDong1',
                        summary: 'Push NhiDong1 serve for Portal',
                        nameParent: 'buildReceiverListNhiDong1',
                        params: { ...queryPushNotifDTO, traceId, segments },
                        errorBody: this.utilService.errorHandler(error),
                        message: error?.message,
                    });
                    return {
                        isOK: false,
                        message: 'Gửi KHÔNG thành công. Vui lòng thử lại sau.',
                    };
                }
            }
        }

    }

    async createNotification(pushNotifDTO: PushNotifDTO): Promise<any> {
        const info = {
            functionName: 'createNotification',
            ...this.trackingInfo,
        };
        /* get thong tin Notif */
        const notifSetting = await this.notificationSettingService.getUMCNotificationSettingById(pushNotifDTO.id);
        if (!notifSetting) {
            throw new HttpException('Không tìm thấy ID Notification thông tin!', HttpStatus.BAD_REQUEST);
        }
        if (!notifSetting.user_id) {
            throw new HttpException('Không tìm thấy User_ID thông tin!', HttpStatus.BAD_REQUEST);
        }
        const pushDevices = await this.pushDeviceService.getUMCPushDeviceByUserId(notifSetting.user_id);
        if (!pushDevices) {
            throw new HttpException('Không tìm thấy User_ID thông tin!', HttpStatus.BAD_REQUEST);
        }
        const pluckOneSignalIds = map(pushDevices, 'onesignal_id');
        const headerNotif = find(this.headerNotif, { id: Number(notifSetting.type) });
        const title = typeof headerNotif !== typeof undefined ? headerNotif.name : 'BV Thông Báo';
        // default notification config
        const defaultNotif = {
            contents: {
                en: notifSetting.title,
                vi: notifSetting.title,
            },
            headings: {
                en: title,
                vi: title,
            },
            data: { notifSetting, ...info },
            include_player_ids: pluckOneSignalIds,
        };
        const { apiKey, apiRoot, appId } = this.oneSignalService.getOneSignalConfig();
        const client = new OneSignal.Client(appId, apiKey, { apiRoot });
        try {
            const response = await client.createNotification({ ...defaultNotif });
            return response;
        } catch (error) {
            if (error instanceof OneSignal.HTTPError) {
                return error;
            }
        }
    }

    async createNotificationToUser(pushNotifUserDTO: PushNotifUserDTO): Promise<any> {
        const info = {
            functionName: 'createNotificationToUser',
            ...this.trackingInfo,
        };
        const jsonString = JSON.parse(pushNotifUserDTO.jsonSample);
        const userId = pushNotifUserDTO.userId;
        /* tìm lại các devices của user theo user_id */
        const pushDevices = await this.pushDeviceService.getUMCLatestPushDeviceByUserId(userId);
        if (!pushDevices) {
            throw new HttpException('Không tìm thấy User_ID thông tin!', HttpStatus.BAD_REQUEST);
        }
        const pluckOneSignalIds = map(pushDevices, 'onesignal_id');
        // default notification config
        const defaultNotif = {
            ...jsonString,
            data: { ...info },
            include_player_ids: pluckOneSignalIds,
        };
        const { apiKey, apiRoot, appId } = this.oneSignalService.getOneSignalConfig();
        const client = new OneSignal.Client(appId, apiKey, { apiRoot });
        try {
            const response = await client.createNotification({ ...defaultNotif });
            return response;
        } catch (error) {
            if (error instanceof OneSignal.HTTPError) {
                return error;
            }
        }
    }

    async pushMessageUMC(pushNotifUserDTO: PushNotifUserDTO): Promise<any> {
        const info = {
            functionName: 'pushMessageUMC',
            ...this.trackingInfo,
        };
        const jsonString = JSON.parse(pushNotifUserDTO.jsonSample);
        const userId = pushNotifUserDTO.userId;
        /* tìm lại các devices của user theo user_id */
        const pushDevices = await this.pushDeviceService.getUMCLatestPushDeviceByUserId(userId);
        if (!pushDevices) {
            throw new HttpException('Không tìm thấy User_ID thông tin!', HttpStatus.BAD_REQUEST);
        }
        const pluckOneSignalIds = map(pushDevices, 'onesignal_id');
        // default notification config
        const defaultNotif = {
            ...jsonString,
            data: { ...info },
            include_player_ids: pluckOneSignalIds,
        };
        const { apiKey, apiRoot, appId } = this.oneSignalService.getOneSignalConfig();
        const client = new OneSignal.Client(appId, apiKey, { apiRoot });
        try {
            const response = await client.createNotification({ ...defaultNotif });
            return response;
        } catch (error) {
            if (error instanceof OneSignal.HTTPError) {
                return error;
            }
        }
    }

    public async pushNotifyToPatientWithTemplate ({
        title,
        bookingId,
        message,
        icon,
        url
    }: {
        title: string,
        bookingId: string,
        message?: string,
        icon?: string,
        url?: string
    }) {
        const info = {
            functionName: 'pushCustomeMessage',
            ...this.trackingInfo,
        };
        const booking = await this.bookingModel.findOne({ id: bookingId }).exec();

        const { userId, appId, bookingCodeV1, bookingCode } = booking;
        // Override bookingCode
        const overrideBookingCode = booking?.bookingCodeV1 ? bookingCodeV1 : bookingCode;
        if (!message) {
            message = `Bạn có cuộc gọi nhỡ cho mã phiếu khám: ${overrideBookingCode}. Vui lòng kiểm tra lại!`;
        }
        /* tìm lại các devices của user theo user_id */
        const listDevices = await this.pushDeviceModel.find({ userId }).exec();
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: appId }).exec();
        if (typeof partnerConfig !== typeof undefined && partnerConfig.notifApiKey !== '' && partnerConfig.notifAppId !== '') {
            const client = new OneSignal.Client(partnerConfig.notifAppId, partnerConfig.notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });
            const deviceInfos = listDevices.filter(item => item.userId === userId && item.appId === appId);
            if (deviceInfos.length > 0) {
                /* lấy thông số config push notif */
                const mapClients = map(deviceInfos, 'clientId');
                const resultData: any = { 
                    url: url,
                    content: message
                };
                resultData.transactionId = booking.transactionId;
                resultData.type = 100;
                const defaultNotif = {
                    contents: {
                        en: message,
                        vi: message,
                    },
                    headings: {
                        en: title,
                        vi: title,
                    },
                    ...( icon ? { small_icon: icon } : {} ),
                    data: { ...resultData, ...info },
                    include_player_ids: mapClients,
                };
                try {
                    await client.createNotification({ ...defaultNotif });
                } catch (error) {
                    if (error instanceof OneSignal.HTTPError) {
                        return error;
                    }
                }
            }
        }
    }

    async pushNotifyToPatient({
        bookingId,
        message,
        icon,
    }:{
        bookingId: string, 
        message?: string
        icon?: string
    }) {   
        const info = {
            functionName: 'pushCustomeMessage',
            ...this.trackingInfo,
        };
        const booking = await this.bookingModel.findOne({ id: bookingId }).exec();

        const { userId, appId, bookingCodeV1, bookingCode } = booking;
        // Override bookingCode
        const overrideBookingCode = booking?.bookingCodeV1 ? bookingCodeV1 : bookingCode;
        if (!message) {
            message = `Bạn có cuộc gọi nhỡ cho mã phiếu khám: ${overrideBookingCode}. Vui lòng kiểm tra lại!`;
        }
        /* tìm lại các devices của user theo user_id */
        const listDevices = await this.pushDeviceModel.find({ userId }).exec();
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: appId }).exec();
        if (typeof partnerConfig !== typeof undefined && partnerConfig.notifApiKey !== '' && partnerConfig.notifAppId !== '') {
            const client = new OneSignal.Client(partnerConfig.notifAppId, partnerConfig.notifApiKey, { apiRoot: 'https://onesignal.com/api/v1' });
            const deviceInfos = listDevices.filter(item => item.userId === userId && item.appId === appId);
            if (deviceInfos.length > 0) {
                /* lấy thông số config push notif */
                const mapClients = map(deviceInfos, 'clientId');
                const resultData: any = { 
                    url: 'https://medpro.com.vn' 
                };
                resultData.transactionId = booking.transactionId;
                resultData.type = 1;
                const defaultNotif = {
                    contents: {
                        en: message,
                        vi: message,
                    },
                    headings: {
                        en: 'Thông báo!',
                        vi: 'Thông báo!',
                    },
                    ...( icon ? { small_icon: icon } : {} ),
                    data: { ...resultData, ...info },
                    include_player_ids: mapClients,
                };
                try {
                    await client.createNotification({ ...defaultNotif });
                } catch (error) {
                    if (error instanceof OneSignal.HTTPError) {
                        return error;
                    }
                }
            }
        }
    }

        async isPushDevice(formData: IsPushDeviceDTO): Promise<any> {
        return [
            {
                id: uuid.v4,
                partnerId: 'umc',
                devices: [
                    {
                        deviceId: 'aaaaaa',
                        platform: 'android',
                    },
                    {
                        deviceId: 'bbbbbb',
                        platform: 'android',
                    },
                    {
                        deviceId: 'ccc',
                        platform: 'ios',
                    },
                ],
            },
            {
                id: uuid.v4,
                partnerId: 'nhidong1',
                devices: [
                    {
                        deviceId: '3434343',
                        platform: 'android',
                    },
                    {
                        deviceId: 'bbfdfsfsfbbbb',
                        platform: 'android',
                    },
                    {
                        deviceId: 'ccfsfsdfsfc',
                        platform: 'ios',
                    },
                ],
            },
        ];
    }

        async testNotification(): Promise<any> {
        const info = {
            functionName: 'testNotification',
            ...this.trackingInfo,
        };
        const defaultNotif = {
            contents: {
                en: 'contents test',
                vi: 'contents test',
            },
            headings: {
                en: 'headings test',
                vi: 'headings test',
            },
            data: { url: 'https://test.pkh.vn', ...info },
            include_player_ids: ['1d362e84-6e46-4277-b4fc-9369e60047dd'],
        };
        const { apiKey, apiRoot, appId } = this.oneSignalService.getOneSignalConfig();
        const client = new OneSignal.Client(appId, apiKey, { apiRoot });
        try {
            const response = await client.createNotification({ ...defaultNotif });
            return response;
        } catch (error) {
            if (error instanceof OneSignal.HTTPError) {
                return error;
            }
        }
    }

    async getListPushDeviceByPhoneAndAppIdService(phone: string, appId?: string): Promise<any> {
        const yourphone = this.utilService.transformPhone(phone);
        try {
            const user = await this.userModel.findOne({
                username: yourphone,
            }).exec();
            if (!user) {
                throw new HttpException(`Không tìm thấy user với số điện thoại này ${phone}`, 404);
            }
            return await this.pushDeviceService.getPushDevicesByUserAndAppId(user._id, appId);
        } catch (error) {
            throw error;
        }
    }

    /* inject data test */
    // dataInsert = [...dataInsert, {
    //     groupId: queryPushNotifDTO.groupId,
    //     userId: 139728,
    //     valueOfTime,
    //     partnerId: queryPushNotifDTO.partnerId,
    //     versionId: uuidRedf,
    //     isSync: true,
    // }];

}
