import { Controller, Post, Body, Get, UseGuards, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBody } from '@nestjs/swagger';
import { ResourceService } from './resource.service';
import { CheckVersionDTO } from './dto/checkVersionDto';
import { GetConfigDTO } from './dto/getConfigDto';
import { GetArticleBySubIdDTO } from './dto/getArticleBySubIdDto';
import { GetCityDTO } from './dto/getCityDto';
import { GetDistrictDTO } from './dto/getDistrictDto';
import { GetWardDTO } from './dto/getWardDto';
import { GetAllWebBannerDTO } from './dto/getAllWebBannersDto';
import { GetPricesDTO } from './dto/getPricesDto';
import { GetTypePricesDTO } from './dto/getTypePricesDto';
import { GetGroupPricesDTO } from './dto/getGroupPricesDto';
import { AuthGuard } from '@nestjs/passport';
import { GetTranslateDTO } from './dto/getTranslateDto';

@Controller('resource')
@ApiTags('Resource - Quản lý Resource')
export class ResourceController {

    constructor(
        private readonly resourceService: ResourceService,
    ) { }

    @Get('checkversion')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Kiểm tra version mới.', description: 'Kiểm tra version mới.' })
    // @ApiBody({
    //     description: 'Kiểm tra version mới..',
    //     type: CheckVersionDTO,
    // })

    async getLatestVerionByPlatform(@Query() query: CheckVersionDTO): Promise<any> {
        return await this.resourceService.getLatestVerionByPlatform({
            version_code: query.version_code,
            version_number: query.version_number,
            platform: query.platform,
        });
    }

    @Get('getconfig')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách config theo platform.', description: 'Lấy danh sách config theo platform.' })
    // @ApiBody({
    //     description: 'Lấy danh sách config theo platform.',
    //     type: GetConfigDTO,
    // })

    async getListConfig(@Query() getConfigDTO: GetConfigDTO): Promise<any> {
        return await this.resourceService.getListConfig(getConfigDTO);
    }

    @Get('getarticle')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy thông tin bài viết.', description: 'Lấy thông tin bài viết.' })
    // @ApiBody({
    //     description: 'Lấy thông tin bài viết.',
    //     type: GetArticleBySubIdDTO,
    // })

    async getArticleBySubId(@Query() getArticleBySubIdDTO: GetArticleBySubIdDTO): Promise<any> {
        return await this.resourceService.getArticleBySubId(getArticleBySubIdDTO);
    }

    @Get('getallcountry')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách quốc gia.', description: 'Lấy danh sách quốc gia.' })
    async getAllCountry(): Promise<any> {
        return await this.resourceService.getAllCountry();
    }

    @Get('umc/getallcountry')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách quốc gia UMC.', description: 'Lấy danh sách quốc gia UMC.' })
    async getUMCAllCountry(): Promise<any> {
        return await this.resourceService.getUMCAllCountry();
    }

    @Get('trungvuong/getallcountry')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách quốc gia UMC.', description: 'Lấy danh sách quốc gia UMC.' })
    async getTrungVuongAllCountry(): Promise<any> {
        return await this.resourceService.getUMCAllCountry();
    }

    @Get('getcity')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách tỉnh thành', description: 'Lấy danh sách tỉnh thành' })
    // @ApiBody({
    //     description: 'Lấy danh sách tỉnh thành',
    //     type: GetCityDTO,
    // })

    async getCitiesByCountryCode(@Query() getCityDTO: GetCityDTO): Promise<any> {
        return await this.resourceService.getCitiesByCountryCode(getCityDTO);
    }

    @Get('get-translate')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách dịch thuật', description: 'Lấy danh sách tỉnh thành' })
    // @ApiBody({
    //     description: 'Lấy danh sách tỉnh thành',
    //     type: GetCityDTO,
    // })

    async getTranslate (@Query() translateDTO: GetTranslateDTO): Promise<any> {
        return await this.resourceService.getTranslate(translateDTO);
    }

    @Get('umc/getcity')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách tỉnh thành UMC', description: 'Lấy danh sách tỉnh thành UMC' })
    // @ApiBody({
    //     description: 'Lấy danh sách tỉnh thành',
    //     type: GetCityDTO,
    // })
    async getUMCCitiesByCountryCode(@Query() getCityDTO: GetCityDTO): Promise<any> {
        return await this.resourceService.getUMCCitiesByCountryCode(getCityDTO);
    }

    @Get('trungvuong/getcity')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách tỉnh thành UMC', description: 'Lấy danh sách tỉnh thành UMC' })
    // @ApiBody({
    //     description: 'Lấy danh sách tỉnh thành',
    //     type: GetCityDTO,
    // })
    async getTrungVuongCitiesByCountryCode(@Query() getCityDTO: GetCityDTO): Promise<any> {
        return await this.resourceService.getUMCCitiesByCountryCode(getCityDTO);
    }

    @Get('getdistrict')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách quận huyện', description: 'Lấy danh sách quận huyện' })
    // @ApiBody({
    //     description: 'Lấy danh sách quận huyện',
    //     type: GetDistrictDTO,
    // })

    async getDistrictsByCityId(@Query() getDistrictDTO: GetDistrictDTO): Promise<any> {
        return await this.resourceService.getDistrictsByCityId(getDistrictDTO);
    }

    @Get('umc/getdistrict')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách quận huyện', description: 'Lấy danh sách quận huyện' })
    // @ApiBody({
    //     description: 'Lấy danh sách quận huyện',
    //     type: GetDistrictDTO,
    // })
    async getUMCDistrictsByCityId(@Query() getDistrictDTO: GetDistrictDTO): Promise<any> {
        return await this.resourceService.getUMCDistrictsByCityId(getDistrictDTO);
    }

    @Get('trungvuong/getdistrict')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách quận huyện', description: 'Lấy danh sách quận huyện' })
    // @ApiBody({
    //     description: 'Lấy danh sách quận huyện',
    //     type: GetDistrictDTO,
    // })
    async getTrungVuongDistrictsByCityId(@Query() getDistrictDTO: GetDistrictDTO): Promise<any> {
        return await this.resourceService.getUMCDistrictsByCityId(getDistrictDTO);
    }
    @Get('getward')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách phường xã', description: 'Lấy danh sách phường xã' })
    // @ApiBody({
    //     description: 'Lấy danh sách phường xã',
    //     type: GetWardDTO,
    // })
    async getWardsByDistrictId(@Query() getWardDTO: GetWardDTO): Promise<any> {
        return await this.resourceService.getWardsByDistrictId(getWardDTO);
    }

    @Get('umc/getward')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách phường xã UMC', description: 'Lấy danh sách phường xã UMC' })
    // @ApiBody({
    //     description: 'Lấy danh sách phường xã',
    //     type: GetWardDTO,
    // })
    async getUMCWardsByDistrictId(@Query() getWardDTO: GetWardDTO): Promise<any> {
        return await this.resourceService.getUMCWardsByDistrictId(getWardDTO);
    }

    @Get('trungvuong/getward')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách phường xã UMC', description: 'Lấy danh sách phường xã UMC' })
    // @ApiBody({
    //     description: 'Lấy danh sách phường xã',
    //     type: GetWardDTO,
    // })
    async getTrungVuongWardsByDistrictId(@Query() getWardDTO: GetWardDTO): Promise<any> {
        return await this.resourceService.getUMCWardsByDistrictId(getWardDTO);
    }

    @Get('getdantoc')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách dân tộc.', description: 'Lấy danh sách dân tộc.' })
    async getAllNation(): Promise<any> {
        return await this.resourceService.getAllNation();
    }

    @Get('umc/getdantoc')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách dân tộc.', description: 'Lấy danh sách dân tộc.' })
    async getUMCAllNation(): Promise<any> {
        return await this.resourceService.getUMCAllNation();
    }

    @Get('trungvuong/getdantoc')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách dân tộc.', description: 'Lấy danh sách dân tộc.' })
    async getTrungVuongAllNation(): Promise<any> {
        return await this.resourceService.getUMCAllNation();
    }

    @Get('getallbanner')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách banners.(api này có thể ko dùng )', description: 'Lấy danh sách banners.' })
    async getAllBanners(): Promise<any> {
        return await this.resourceService.getAllBanners();
    }

    @Get('allbanner')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách banners cho web theo type', description: 'Lấy danh sách banners cho web theo type' })
    // @ApiBody({
    //     description: 'Lấy danh sách banners cho web theo type',
    //     type: GetAllWebBannerDTO,
    // })
    async getAllWebBannersByType(@Query() getAllWebBannerDTO: GetAllWebBannerDTO): Promise<any> {
        return this.resourceService.getAllWebBannersByType(getAllWebBannerDTO);
    }

    @Get('getallprofession')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách nghề nghiệp.', description: 'Lấy danh sách nghề nghiệp.' })
    async getOccupations(): Promise<any> {
        return await this.resourceService.getOccupations();
    }

    @Get('umc/getallprofession')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách nghề nghiệp.', description: 'Lấy danh sách nghề nghiệp.' })
    async getUMCOccupations(): Promise<any> {
        return await this.resourceService.getUMCOccupations();
    }

    @Get('trungvuong/getallprofession')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách nghề nghiệp.', description: 'Lấy danh sách nghề nghiệp.' })
    async getTrungVuongOccupations(): Promise<any> {
        return await this.resourceService.getUMCOccupations();
    }

    @Get('getholiday')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách ngày nghỉ.', description: 'Lấy danh sách ngày nghỉ.' })
    // tslint:disable-next-line: variable-name
    async getHoliday(@Query('hospital_id') hospital_id: number): Promise<any> {
        return await this.resourceService.getHoliday(hospital_id);
    }

    @Get('getalltype')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách mối quan hệ gia đình.', description: 'Lấy danh sách mối quan hệ gia đình.' })
    async getRelativeType(): Promise<any> {
        return await this.resourceService.getRelativeType();
    }

    @Get('umc/getalltype')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách mối quan hệ gia đình.', description: 'Lấy danh sách mối quan hệ gia đình.' })
    async getUMCRelativeType(): Promise<any> {
        return await this.resourceService.getUMCRelativeType();
    }

    @Get('trungvuong/getalltype')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách mối quan hệ gia đình.', description: 'Lấy danh sách mối quan hệ gia đình.' })
    async getTrungVuongRelativeType(): Promise<any> {
        return await this.resourceService.getUMCRelativeType();
    }

    @Get('getbranch')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy danh sách Branch.', description: 'Lấy danh sách Branch.' })
    async getBranch(): Promise<any> {
        return await this.resourceService.getBranch();
    }

    @Get('get_price')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Tìm kiếm tên dich vụ hỗ trợ.', description: 'Tìm kiếm tên dich vụ hỗ trợ.' })
    // @ApiBody({
    //     description: 'Tìm kiếm tên dich vụ hỗ trợ.',
    //     type: GetPricesDTO,
    // })
    async getPrices(@Query() getPricesDTO: GetPricesDTO): Promise<any> {
        return await this.resourceService.getPrices(getPricesDTO);
    }

    @Get('get_price_type')
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Tìm kiếm tên dich vụ hỗ trợ theo typeId.', description: 'Tìm kiếm tên dich vụ hỗ trợ theo typeId.' })
    // @ApiBody({
    //     description: 'Tìm kiếm tên dich vụ hỗ trợ theo typeId.',
    //     type: GetTypePricesDTO,
    // })
    async getTypePrices(@Query() getTypePricesDTO: GetTypePricesDTO): Promise<any> {
        return await this.resourceService.getTypePrices(getTypePricesDTO);
    }

    @Get('get_price_group')
    @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Tìm kiếm tên dich vụ hỗ trợ theo groupId.', description: 'Tìm kiếm tên dich vụ hỗ trợ theo typeId.' })
    @ApiBody({
        description: 'Tìm kiếm tên dich vụ hỗ trợ theo typeId.',
        type: GetGroupPricesDTO,
    })
    async getGroupPrices(@Body() getGroupPricesDTO: GetGroupPricesDTO): Promise<any> {
        return await this.resourceService.getGroupPrices(getGroupPricesDTO);
    }

    // @Post('getPrescription')
    // @ApiOperation({ summary: 'Lấy danh sách toa thuốc của bệnh nhân.', description: 'Lấy danh sách toa thuốc của bệnh nhân.' })
    // @ApiBody({
    //     description: 'Lấy danh sách toa thuốc của bệnh nhân.',
    //     type: GetGroupPricesDTO,
    // })
    // async getPrescription(@Body() getGroupPricesDTO: GetGroupPricesDTO): Promise<any> {
    //     return await this.resourceService.getPrescription(getGroupPricesDTO);
    // }

}
