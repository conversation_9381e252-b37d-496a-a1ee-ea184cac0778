import { GlobalSettingService } from './../../global-setting/global-setting.service';
import { ExceptionFilter, Catch, ArgumentsHost, HttpException } from '@nestjs/common';
import { Request, Response } from 'express';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(private readonly global: GlobalSettingService) { }

  async catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    const res = exception.getResponse();

    const env = await this.global.findByKeyAndRepoName('ENV_SWITCH_FILTER_HTTP_EXCEPTION');
    const data: any = res;
    const { key, locale } = data;

    if (env === 'ON' && key) {
      const globalLocale = await this.global.findByKeyAndRepoName(key, null, locale);
      if (!globalLocale) {
        void this.responseDetfaultError(response, status, res);
      }
      response
        .status(status)
        .json({
          statusCode: status,
          message: globalLocale,
        });
    } else {
      void this.responseDetfaultError(response, status, res);
    }
  }

  async responseDetfaultError(response: Response, status: number, res: string | object): Promise<void> {
    if (typeof res === 'object' ) {
      const data: any = res;
      const globalLocale = await this.global.findByKeyAndRepoName(data?.key);
      response
        .status(status)
        .json(globalLocale ? {
          statusCode: status,
          message: globalLocale,
        } : data );
    } else {
      response
        .status(status)
        .json({
          statusCode: status,
          message: res,
        });
    }
  }
}
