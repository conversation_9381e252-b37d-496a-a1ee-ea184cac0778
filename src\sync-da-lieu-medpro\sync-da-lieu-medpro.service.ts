import { Injectable, Inject, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { SYNC_USER_DALIEU_UPGRADE_COLLECTION_NAME } from './schemas/constants';
import { Model } from 'mongoose';
import { chunk } from 'lodash';
import { ISyncUserDaLieuUpgrade } from './interfaces/sync-user-dalieu-upgrade.inteface';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';

@Injectable()
export class SyncDaLieuMedproService {

    private userTableName = 'user';
    private skinUserPatientTableName = 'skin_user_patient';
    private logger = new Logger(SyncDaLieuMedproService.name);
    
    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @InjectModel(SYNC_USER_DALIEU_UPGRADE_COLLECTION_NAME) private userUpgradeModel: Model<ISyncUserDaLieuUpgrade>,
    ) { }

    async syncUser(): Promise<any> {
        try {
            /* lấy toàn bộ thông tin những user da lieu cua v1 */
            const skinUserPatients = await this.pkhPatientKnex(this.skinUserPatientTableName)
                .select('user_id')
                .groupBy('user_id');
            const chunkData = chunk(skinUserPatients, 10);
            let idIndex = 1;
            for (const chunkDetail of chunkData) {
                let dataInsert = [];
                for (const detail of chunkDetail) {
                    const detailAny: any = detail;
                    dataInsert = [...dataInsert, {
                        idIndx: idIndex,
                        userId: detailAny.user_id,
                        isOk: false,
                    }];
                    idIndex++;
                }
                await this.userUpgradeModel.insertMany(dataInsert);
            }
            return 'Đẩy dữ liệu vào thành công!';
        } catch (error) {
            this.logger.error('Lỗi khi đồng bộ dữ liệu user da lieu', error);
        }
    }
}
