import { Injectable, Inject, HttpException, HttpStatus, Logger } from '@nestjs/common';
import * as moment from 'moment';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { THU_DUC_HOSPITAL_CONNECTION } from 'src/config/thuDucHospitalConnection';
import { CHO_RAY_HOSPITAL_CONNECTION } from 'src/config/choRayHospitalConnection';
import * as uuid from 'uuid';
import { InjectModel } from '@nestjs/mongoose';
import { PUSH_DEVICE_COLLECTION_NAME } from './schemas/constants';
import { Model } from 'mongoose';
import { IPushDevice } from './intefaces/push-device.inteface';
import { PushDeviceMongoDTO } from './dto/push-device-mongo.dto';
import { IsPushDeviceDTO } from 'src/push-notif/dto/is-push-device.dto';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { map, first } from 'lodash';

@Injectable()
export class PushDeviceService {
    private logger = new Logger(PushDeviceService.name);
    private tableName = 'push_device';
    private userTableName = 'user';

    constructor(
        @Inject(THU_DUC_HOSPITAL_CONNECTION) private readonly thuDucHospitalKnex,
        @Inject(CHO_RAY_HOSPITAL_CONNECTION) private readonly choRayHospitalKnex,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @InjectModel(PUSH_DEVICE_COLLECTION_NAME) private pushDeviceModel: Model<IPushDevice>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
    ) { }

    async isPushDevice(formData: IsPushDeviceDTO): Promise<any> {
        const yourphone = `${formData.phone}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
        /* tiến hành kiểm tra thông tin trong bảng user */
        let user: any;
        try {
            user = await this.pkhPatientKnex(this.userTableName).where({
                username: yourphone,
            }).first();
        } catch (error) {
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }

        if (!user) {
            throw new HttpException('Không tìm thấy thông tin user', HttpStatus.NOT_FOUND);
        }

        /* bệnh viện cũ */
        const oldHospital = ['umc', 'nhidong1', 'ctchhcm', 'dalieuhcm'];
        let result: any = [];
        try {
            for await (const partnerId of oldHospital) {
                let pushDevices: any;
                switch (partnerId) {
                    case 'umc':
                        pushDevices = await this.pkhPatientKnex('push_device').where({
                            user_id: user.id,
                        });
                        break;
                    case 'dalieuhcm':
                        pushDevices = await this.pkhPatientKnex('skin_push_device').where({
                            user_id: user.id,
                        });
                        break;
                    case 'nhidong1':
                        pushDevices = await this.pkhPatientKnex('nd1_push_device').where({
                            user_id: user.id,
                        });
                        break;
                    case 'ctchhcm':
                        pushDevices = await this.pkhPatientKnex('ctch_push_device').where({
                            user_id: user.id,
                        });
                        break;
                }
                result = [...result, {
                    id: uuid.v4().replace(/-/g, ''),
                    partnerId,
                    devices: pushDevices.map(item => {
                        return {
                            clientId: item.onesignal_id,
                            clientToken: item.onesignal_token,
                        };
                    }),
                }];
            }
        } catch (error) {
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }

        try {
            /* kiểm tra thông tin user trong v2 */
            const userTrungVuong = await this.userModel.findOne({
                username: yourphone,
            }).exec();

            if (userTrungVuong) {
                /* lấy thông tin push devices */
                const pushDeviceTV = await this.pushDeviceModel.find({
                    user: userTrungVuong._id,
                    appId: 'trungvuong',
                    type: 'dkkb',
                }).exec();

                result = [...result, {
                    id: uuid.v4().replace(/-/g, ''),
                    partnerId: 'trungvuong',
                    devices: pushDeviceTV.map(item => {
                        return {
                            clientId: item.clientId,
                            clientToken: item.clientToken,
                        };
                    }),
                }];
            } else {
                result = [...result, {
                    id: uuid.v4().replace(/-/g, ''),
                    partnerId: 'trungvuong',
                    devices: [],
                }];
            }

            /* Tìm lại thông tin medproId */
            const userMedproId: IUser = await this.getUserByUsernameMedproId(yourphone);
            /* Bệnh viện mới */
            const hospitals = await this.hospitalModel.find({
                message: {
                    $in: [null, ''],
                },
                partnerId: { $nin: ['medpro', 'umc', 'ctchhcm', 'dalieuhcm', 'thuduc', 'minhanh', 'nhidong1', 'trungvuong', 'pkh'] },
            }, { partnerId: true, name: true }).exec();

            const listPartners = map(hospitals, 'partnerId');
            if (userMedproId) {
                /* lấy toàn bộ dữ liệu lên */
                const pushDevicesRemain = await this.pushDeviceModel.find({
                    appId: { $in: listPartners },
                    type: 'dkkb',
                    user: userMedproId._id,
                }).exec();
                /* Tiến hành lọc lại dữ liệu */
                const mappDataValues = pushDevicesRemain.map(item => item.toObject());
                for (const partnerIdSelected of listPartners) {
                    const devices = mappDataValues
                        .filter(item => item.appId === partnerIdSelected)
                        .map(item => {
                            return {
                                clientId: item.clientId,
                                clientToken: item.clientToken,
                            };
                        });
                    result = [...result, {
                        id: uuid.v4().replace(/-/g, ''),
                        partnerId: partnerIdSelected,
                        devices,
                    }];
                }
            } else {
                for (const empty of listPartners) {
                    result = [...result, {
                        id: uuid.v4().replace(/-/g, ''),
                        partnerId: empty,
                        devices: [],
                    }];
                }
            }
        } catch (error) {
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }

        return result;
    }

    async getUserByUsernameMedproId(userName: any): Promise<any> {
        const users = await this.userModel
            .find({ username: userName, medproId: `mp${userName}` })
            .limit(1)
            .exec();
        return first(users);
    }

    async getUsersByUserPhoneList(listUserPhone: string[]): Promise<any> {
        return this.userModel
            .find({
                username: {
                    $in: listUserPhone,
                },
            })
            .exec();
    }

    async getUMCPushDeviceByUserId(userId: number): Promise<any> {
        const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION);
        return getConnectionQuery.select('onesignal_id').where('user_id', userId);
    }

    async getPushDevicesByUserId(userId: string, partnerId: string): Promise<any> {
        return this.pushDeviceModel.find({ userId, appId: partnerId }).exec();
    }

    async getPushDevicesByUserList(userList: string[], partnerId: string): Promise<any> {
        return this.pushDeviceModel.find({
            userId: {
                $in: userList,
            },
            appId: partnerId,
        }).exec();
    }

    async getUMCLatestPushDeviceByUserId(userId: number): Promise<any> {
        const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION);
        return getConnectionQuery
            .select('onesignal_id')
            .where('user_id', userId)
            .orderBy(`date_update`, 'desc')
            .limit(1);
    }

    async insertPushDeviceId(partnerId: string, appId: string, pushDeviceDTO: PushDeviceMongoDTO): Promise<any> {
        /* kiểm tra đã tồn tại oneSignalId hay chưa */
        const isExists = await this.checkExistsClientId(pushDeviceDTO.clientId, appId);
        if (isExists) {
            return {
                status: true,
                isNew: false,
                message: 'Thiết bị đã đăng ký nhận push notification.',
            };
        }
        /* Tiến hành insert vào push device */
        const id = uuid.v4().replace(/-/g, '').replace(/-/g, '');
        const getType = pushDeviceDTO?.type || 'dkkb';
        const pushDeviceInfo = new this.pushDeviceModel({
            id,
            clientToken: pushDeviceDTO.clientToken,
            clientId: pushDeviceDTO.clientId,
            platform: pushDeviceDTO.platform,
            type: getType,
            appId,
            partnerId,
        });
        await pushDeviceInfo.save();
        return {
            status: true,
            isNew: true,
            message: 'Thiết bị đăng ký nhận push notification thành công.',
        };
    }

    async updatePushUserDeviceManual(deviceId: string, userId: string): Promise<any> {
        await this.updatePushDeviceById(deviceId, userId);
        return {
            status: true,
            message: 'Cập nhật thiết bị thuộc người dùng thành công!',
        };
    }

    async updatePushUserDevice(pushDeviceDTO: PushDeviceMongoDTO, userId: string): Promise<any> {

        /* tìm lại thông tin clientId */
        const pushDevice = await this.getPushDeviceByClientId(pushDeviceDTO.clientId);
        if (!pushDevice) {
            throw new HttpException('Không tìm thấy thông tin về thiết bị này.', HttpStatus.BAD_REQUEST);
        }
        /* cập nhật thông tin */
        try {
            const pushDeviceObj = pushDevice.toObject();
            await this.updatePushDeviceById(pushDeviceObj._id, userId);
            return {
                status: true,
                message: 'Cập nhật thiết bị thuộc người dùng thành công!',
            };
        } catch (error) {
            return {
                status: false,
                message: 'Lỗi xảy ra!',
            };
        }
    }

    async updatePushDeviceById(id: string, userId: string): Promise<any> {
        return this.pushDeviceModel.findByIdAndUpdate({ _id: id }, { userId }).exec();
        // const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION);
        // const currentTime = moment().format('YYYY-MM-DD HH:mm:ss');
        // return getConnectionQuery
        //     .where({ id })
        //     .update({
        //         user_id: userId,
        //         date_update: currentTime,
        //     });

    }

    async getUMCPushDeviceById(id: number): Promise<any> {
        const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION);
        return getConnectionQuery.where('id', id).first();
    }

    async getPushDeviceByClientId(clientId: string): Promise<any> {
        return this.pushDeviceModel.findOne({ clientId }).exec();
        // const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION);
        // return getConnectionQuery
        //     .select('id')
        //     .where('onesignal_id', oneSignalId)
        //     .first();
    }

    async checkExistsClientId(clientId: string, appId: string): Promise<any> {
        const countValue = await this.pushDeviceModel.count({ appId, clientId }).exec();
        // const getConnectionQuery = this.getConnection(PKH_PATIENT_CONNECTION);
        // const { countValue } = await getConnectionQuery
        //     .count('id', { as: 'countValue' })
        //     .where('onesignal_id', oneSignalId)
        //     .first();
        if (countValue > 0) {
            return true;
        }
        return false;
    }

    async getUMCUsersDevice(): Promise<any> {
        const tablePushDevice = 'push_device';
        const tableUser = 'user';
        return this.pkhPatientKnex('user')
            .select(`${tableUser}.id`, `${tableUser}.username`, `${tablePushDevice}.onesignal_id`)
            .innerJoin(tablePushDevice, `${tableUser}.id`, `${tablePushDevice}.user_id`)
            .groupBy(`${tableUser}.id`, `${tableUser}.username`, `${tablePushDevice}.onesignal_id`);
    }

    getConnection(hospitalConnection = PKH_PATIENT_CONNECTION) {
        let connectionQuery = null;
        switch (hospitalConnection) {
            case THU_DUC_HOSPITAL_CONNECTION:
                connectionQuery = this.thuDucHospitalKnex(this.tableName);
                break;
            case CHO_RAY_HOSPITAL_CONNECTION:
                connectionQuery = this.choRayHospitalKnex(this.tableName);
                break;
            default:
                connectionQuery = this.pkhPatientKnex(this.tableName);
        }
        return connectionQuery;
    }

    async isExistDevice(userId: string, clientId: string): Promise<boolean> {
        try {
            const userDevice = await this.pushDeviceModel.findOne({ userId, clientId });
            if (userDevice) {
                return true;
            }
            return false;
        } catch (error) {
            this.logger.error(`Error when exec isExistDevice() with userId: ${userId}\nError: ${error.message}`);
            return false;
        }
    }

    async userLogout(userId: string, clientId: string): Promise<any> {
        const isExistDevice = await this.isExistDevice(userId, clientId);
        if (!isExistDevice) {
            throw new HttpException(`clientId: ${clientId}, userId: ${userId} does not exist`, HttpStatus.NOT_FOUND);
        }
        try {
            return this.pushDeviceModel.findOneAndUpdate({ userId, clientId }, { userId: null }).exec();
        } catch (error) {
            this.logger.error(`Error when exec userLogout() with userId: ${userId}\nError: ${error.message}`);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
        }
    }

    async getPushDevicesByUserAndAppId(userId: string, appId?: string): Promise<any> {
        try {
            let filter : any = { userId }
            filter = appId ? {...filter,  appId } : filter
            return this.pushDeviceModel.find({
                ...filter
            }).exec();
        } catch (error) {
            throw error;
        }
    }

    async deletePushDevicesById(id: string): Promise<any> {
        try {
            return await this.pushDeviceModel.findOneAndDelete({ id }).exec();
        } catch (error) {
            throw error;
        }
    }
}
