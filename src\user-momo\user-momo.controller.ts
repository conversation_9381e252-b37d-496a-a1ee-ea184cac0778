import { Controller, Get, Query, Headers, Body, Post } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { CreateUserMomoDto } from './dto/create-user-momo.dto';
import { UserMomoService } from './user-momo.service';

@Controller('user-momo')
export class UserMomoController {

    constructor(
        private readonly userService: UserMomoService,
    ) { }

    @Get('generate-uuid-momo')
    @ApiOperation({ summary: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.', description: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.' })
    async generateUUID(): Promise<any> {
        return await this.userService.generateUUID();
    }

    @Get('get-momo-token')
    @ApiOperation({ summary: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.', description: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.' })
    async getMomoToken(@Query('token') token: string): Promise<any> {
        return await this.userService.getMomoToken(token);
    }

    @Get('create-user-with-token')
    @ApiOperation({ summary: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.', description: 'Lấy thông tin token bên MOMO gửi qua để tạo tài khoản.' })
    async createUserWithToken(@Headers('momoid') token: string): Promise<any> {
        return await this.userService.createUserWithToken(token);
    }

    @Post('create-user')
    @ApiOperation({ summary: 'MOMO tạo tài khoản.', description: 'MOMO tạo tài khoản.' })
    async createUserMomo(@Body() formData: CreateUserMomoDto): Promise<any> {
        return this.userService.createUserMomo(formData);
    }
}
