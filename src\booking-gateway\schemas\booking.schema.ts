import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { BOOKING_COLLECTION_NAME, SECTION_COLLECTION_NAME, BOOKING_ORDER_COLLECTION_NAME, BOOKING_COMPLAINS_COLLECTION_NAME, CANCELLATION_REASON_COLLECTION_NAME, VISA_USER_BOOKINGS_COLLECTION_NAME } from './constants';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { PATIENT_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME, PATIENT_PROFILE_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { BOOKING_SLOT_COLLECTION_NAME } from 'src/his-gateway/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { ROOM_COLLECTION_NAME } from 'src/room-mongo/schemas/constants';
import { DOCTOR_COLLECTION_NAME } from 'src/doctor-mongo/schemas/constants';
import { CheckFilterSchema } from 'src/filter-process/schemas/check-filter.schema';
import { ServiceSchema } from 'src/service-mongo/schemas/service.schema';
import { AddOnServiceSchema } from './addon-service.schema';
import { PatientXcSchema } from 'src/patient-mongo/schemas/patient-xc.schema';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';

const Schema = mongoose.Schema;

export const BookingSchema = new Schema({
    id: { type: String },
    bookingId: { type: String, unique: true, required: true },
    bookingCode: { type: String },
    bookingCodeV1: { type: String },
    insuranceCode: { type: String },
    insuranceType: { type: String },
    insuranceChoice: { type: String },
    insuranceTransferCode: { type: String }, // mã chuyển tuyến
    bookingSlotId: { type: String, required: true },
    bookingSlot: { type: Schema.Types.ObjectId, ref: BOOKING_SLOT_COLLECTION_NAME },
    sequenceNumber: { type: Number },
    date: { type: Date },
    subjectId: { type: String },
    subject: { type: Schema.Types.ObjectId, ref: SUBJECT_COLLECTION_NAME },
    roomId: { type: String },
    room: { type: Schema.Types.ObjectId, ref: ROOM_COLLECTION_NAME },
    sectionId: { type: String },
    section: { type: Schema.Types.ObjectId, ref: SECTION_COLLECTION_NAME },
    doctorId: { type: String },
    doctor: { type: Schema.Types.ObjectId, ref: DOCTOR_COLLECTION_NAME },
    serviceId: { type: String },
    service: { type: Schema.Types.ObjectId, ref: SERVICE_COLLECTION_NAME },
    status: { type: Number },
    paymentStatus: { type: Number },
    paymentMessage: { type: String },
    transactionId: { type: String },
    errorCode: { type: Number },
    errorDescription: { type: String },
    paymentId: { type: String },
    patientId: { type: String },
    patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
    /* patient version */
    patientVersionId: { type: String },
    patientVersion: { type: Schema.Types.ObjectId, ref: PATIENT_VERSION_COLLECTION_NAME },
    /* patient version */
    userId: { type: String },
    user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    prevUserId: { type: String },
    changeTo: { type: String },
    partnerId: { type: String },
    appId: { type: String },
    partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
    platform: { type: String },
    invoiceId: { type: String },
    invoiceCode: { type: String },
    visible: { type: Boolean, default: true }, /* hiển thị phiếu khám hay ko */
    checkInRoom: { type: Object, default: {} },
    syncStatus: { type: String },
    syncDate: { type: Date },
    syncAt: { type: Date },
    bookingNote: { type: String, default: '' },
    noPayment: { type: Boolean, default: false },
    sharePayment: { type: Boolean, default: false },
    serviceType: { type: String, default: '' },
    idReExam: { type: String, default: '' },
    syncBookingType: { type: Number, default: 2 }, // dành cho các booking ở v1
    syncBookingIdV1: { type: Number, default: 0 },
    syncUserIdV1: { type: Number, default: 0 },
    syncPatientIdV1: { type: Number, default: 0 },
    patientNameV1: { type: String },
    patientPhoneV1: { type: String },
    patientMSBNV1: { type: String },
    bookingChangeTime: { type: Date },
    prevBookingCode: { type: String },
    nextBookingCode: { type: String },
    patientProfile: { type: Schema.Types.ObjectId, ref: PATIENT_PROFILE_COLLECTION_NAME },
    referralCode: { type: String },
    vatInvoice: { type: String },
    vatInvoiceData: {
        companyName: { type: String },
        taxIdNumber: { type: String },
        address: { type: String },
    },
    extraInfo: {
        transactionIdV1: { type: String, default: '' },
        methodIdV1: { type: Number, default: 0 },
        booking: { type: Object, default: {} },
    },
    /* cskh */
    cskhUserId: { type: String },
    cskh: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    orderId: { type: String },
    bookingOrder: { type: Schema.Types.ObjectId, ref: BOOKING_ORDER_COLLECTION_NAME },
    bookingOrderId: { type: String },
    bookingsRelation: [{
        idBooking: { type: String },
        info: { type: Object, default: {} },
    }],
    canceledDate: { type: Date },
    bookingExpiredData: { type: Object, default: {} },
    treeId: { type: String },
    bookingInternalId: { type: String },
    insuranceFileUrl: { type: String },
    filterCheckData: { type: [CheckFilterSchema] },
    addonServices: { type: [AddOnServiceSchema] },
    serviceInfo: { type: AddOnServiceSchema },
    smsCode: { type: String, required: true, unique: true },
    promotion: { type: Boolean, default: false },
    relatePromotion:  { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
    promotionChilds:  [{ type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME }],
    xcInfo: { type: PatientXcSchema },
    secretBooking: { type: String },
    bookingComplain:{ type: Schema.Types.ObjectId, ref: BOOKING_COMPLAINS_COLLECTION_NAME },
    endTime: { type: Date },
    numberConfirmed: { type: Boolean, default: false },
    isChanged: { type: Boolean, default: false },
    implementAgent: { type: String },
    implementLocation: { type: String },
    telemedInfoBooking: {
        files: { type: [String] },
        dataInfo : [{
            title: { type: String },
            key: { type: String },
            value: { type: String },
        }],
    },
    optionBHYT: { type: Number },
    cancellationReason: { type: Schema.Types.ObjectId, ref: CANCELLATION_REASON_COLLECTION_NAME },
    locale: { type: String },
    repoName: { type: String },
    enableTelemed: { type: Boolean, default: false },
    medproCare: Schema.Types.Mixed,
    care247: Schema.Types.Mixed,
    isCashBack: { type: Boolean },
    getSequenceNumberAfterSuccessPayment : Boolean,
    visaUserBooking: { type: Schema.Types.ObjectId, ref: VISA_USER_BOOKINGS_COLLECTION_NAME },
    version: { type: String },
    devicemodel: { type: String },
}, {
    collection: BOOKING_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
