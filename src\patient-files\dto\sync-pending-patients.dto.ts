import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsArray } from 'class-validator';

export class SyncPendingPatientsDTO {
    @ApiProperty({
        description: '<PERSON><PERSON> sách CMND cụ thể cần đồng bộ (t<PERSON><PERSON> chọn). <PERSON><PERSON><PERSON> không truyền sẽ xử lý tất cả pending patients',
        example: ['123456789', '987654321'],
        required: false,
        type: [String]
    })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    cmnds?: string[];

    @ApiProperty({
        description: 'Chế độ xử lý: "all" (tất cả), "specific" (chỉ CMND được chỉ định)',
        example: 'all',
        required: false,
        enum: ['all', 'specific'],
        default: 'all'
    })
    @IsOptional()
    @IsString()
    mode?: 'all' | 'specific' = 'all';
}
