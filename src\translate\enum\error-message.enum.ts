export enum ErrorMessageKey {
    SYSTEM_ERROR = 'systemError',
    PATIENT_ALREADY_ADD_TO_USER = 'patientAlreadyAddToUser',
    INVALID_LOGIN_INFO = 'invalidLoginInfo',
    PATIENT_ALREADY_EXIST = 'patientAlreadyExist',
    BEFORE_TIME_CANCEL_BOOKING_ACCEPTED_PORTAL = 'beforeTimeCancelAcceptedPortal',
    BEFORE_TIME_CANCEL_BOOKING_ACCEPTED = 'beforeTimeCancelAccepted',

    INVALID_INSURANCE_CODE = 'invalidInsuranceCode',
    NOT_FOUND_INSURANCE_INFO = 'notFoundInsuranceInfo',
    INVALID_INSURANCE_FIRST_REGISTER_PLACE = 'invalidInsuranceFirstRegisterPlace',
    VERIFY_INSURANCE_NOT_FOUND_PATIENT = 'verifyInsuranceNotFoundPatient',
    INSURANCE_EXPIRED = 'insuranceExpired',

    EXAM_RESULTS_NOT_FOUND_PATIENT = 'examResultsNotFoundPatient',
    EXAM_RESULTS_NOT_FOUND = 'examResultsNotFound',
    EXAM_RESULTS_NOT_FOUND_TEST_RESULT = 'examResultsNotFoundTestResult',
}

export enum ErrorMessage {
    systemError = 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau!',
    patientAlreadyAddToUser = 'Thông tin bệnh nhân này đã được thêm vào user rồi.',
    invalidLoginInfo = 'Thông tin đăng nhập không chính xác.',
    patientAlreadyExist = 'Hồ sơ này đã tồn tại',
    beforeTimeCancelAcceptedPortal = 'Bạn chỉ có thể HỦY phiếu khám bệnh trước {{time}} ngày {{date}}.',
    beforeTimeCancelAccepted = 'Bạn chỉ có thể HỦY phiếu khám bệnh trước {{time}} của ngày liền trước ngày khám.',

    invalidInsuranceCode = 'BHYT không được chứa ký tự đặc biệt! Vui lòng nhập lại!',
    notFoundInsuranceInfo = 'Hệ thống không tìm thấy thông tin.',
    invalidInsuranceFirstRegisterPlace = 'Nơi đăng ký khám bảo hiểm ban đầu của bạn không hợp lệ, Vui lòng kiểm tra lại thông tin.',
    verifyInsuranceNotFoundPatient = 'Không tìm thấy thông tin hồ sơ bệnh nhân.',
    insuranceExpired = 'Thẻ bảo hiểm hết hạn, vui lòng nhập thẻ khác hoặc chọn lại hình thức khám.',

    examResultsNotFoundPatient = 'Không tìm thấy hồ sơ khám bệnh, vui lòng kiểm tra lại mã bệnh nhân!',
    examResultsNotFound = 'Không tìm thấy kết quả khám bệnh.',
    examResultsNotFoundTestResult = 'Không tìm thấy thông tin kết quả xét nghiệm',
}
