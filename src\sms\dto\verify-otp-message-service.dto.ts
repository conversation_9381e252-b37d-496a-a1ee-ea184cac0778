import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, Length } from 'class-validator';
import { VerifyOTPDto } from './verify-otp.dto';

export class VerifyOtpMessageServiceDto extends VerifyOTPDto {
    @ApiProperty({ description: 'Vui lòng nhập phone!', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập phone!' })
    readonly phone: string;

    @ApiProperty({ description: 'Vui lòng nhập code!', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập code!' })
    @Length(6, 6, { message: 'code bao gồm 6 ký tự số!' })
    readonly code: string;
}
