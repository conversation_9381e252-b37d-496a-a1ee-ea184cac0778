import { CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { <PERSON>ache, CachingConfig } from 'cache-manager';
import { REPO_NAME_BETA } from '../common/constants';
import { ConfigRepoService } from '../config/config.repo.service';

@Injectable()
export class CacheManagerService {
    repoName: string;

    constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache, private configRepoService: ConfigRepoService) {
        this.repoName = this.configRepoService.getRepoName();
    }

    async get(key: string): Promise<any> {
        if (!this.configRepoService.enableCaching()) {
            return null;
        }

        return await this.cacheManager.get(key);
    }

    async set(key: string, value: any, options?: CachingConfig): Promise<void> {
        if (!this.configRepoService.enableCaching()) {
            return;
        }

        await this.cacheManager.set(key, value, options);
    }

    async del(key: string): Promise<void> {
        await this.cacheManager.del(key);
    }

    async delByPattern(pattern: string | RegExp): Promise<any> {
        let regex = new RegExp(pattern);

        const keys = await this.cacheManager.store.keys();

        try {
            return keys.filter(key => key.match(regex)).map(key => this.cacheManager.del(key));
        } catch (err) {
            console.log(`error del key by pattern ${pattern}: `, err);

            throw err;
        }
    }
}
