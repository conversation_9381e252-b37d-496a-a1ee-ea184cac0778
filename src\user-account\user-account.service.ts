import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
    USER_ACCOUNT_COLLECTION_NAME, ADMIN_USER_COLLECTION_NAME,
    PAYMENT_HOSPITAL_FEE_COLLECTION_NAME, CONSTRAINTS_CARD_COLLECTION_NAME,
    CONSTRAINTS_BILL_COLLECTION_NAME,
} from './schemas/constants';
import { IUserAccount } from './interfaces/user-account.interface';
import * as crypto from 'crypto';
import * as jwt from 'jsonwebtoken';
import { CronJob } from 'cron';
import { UrlConfigService } from 'src/config/config.url.service';
// import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { PkhHttpService } from 'src/config/config.http.service';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { get, find } from 'lodash';
import * as uuid from 'uuid';
import * as moment from 'moment';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { PATIENT_CODE_COLLECTION_NAME, PATIENT_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { IPatientCodes } from 'src/patient-mongo/intefaces/patient-codes.inteface';
import { IPatient } from 'src/patient-mongo/intefaces/patient.inteface';
import { PayHospitalFeeDTO } from './dto/pay-hospital-fee.dto';
import { WithdrawalDTO } from './dto/withdrawal.dto';
import { DepositDTO } from './dto/deposit.dto';
import { AdminUserLoginDTO } from './dto/admin-user-login.dto';
import { JwtService, JwtModuleOptions } from '@nestjs/jwt';
import { PatientMongoService } from 'src/patient-mongo/patient-mongo.service';
import { JwtUserYTeConfigService } from 'src/config/config.user-yte.jwt.service';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { SearchPatientExtraInfoMongoDTO } from 'src/patient/dto/search-patient-extra-info-mongo.dto';
import { LinkMSBNDTO } from './dto/link-msbn.dto';
import { UserService } from 'src/user/user.service';
import { LinkCardIdDTO } from './dto/link-card-id.dto';
import { CreatePINDTO } from './dto/create-pin.dto';
import { SearchPatientInfoMongoDTO } from 'src/patient/dto/search-patient-info-mongo.dto';
import { CheckLinkMSBNDTO } from './dto/check-link-msbn.dto';
import { CheckLinkCardDTO } from './dto/check-link-card.dto';
import { IAdminUser } from './interfaces/admin-user.inteface';
import { GetCardInfoDTO } from './dto/get-card-info.dto';
import { GetHospitalFeeDTO } from './dto/get-hospital-fee.dto';
import { PaymentFeeByCardDTO } from './dto/payment-fee-card.dto';
import { UtilService } from 'src/config/util.service';
import { PrefixTransactionCodeTTConfigService } from 'src/config/config.prefix.transcode.service';
import { IPaymentHospitalFee } from './interfaces/payment-hospital-fee.inteface';
import { PAYMENT_COLLECTION_NAME, TRANSACTION_EVENT_NAME } from 'src/booking-gateway/schemas/constants';
import { IPayment } from 'src/booking-gateway/intefaces/payment.inteface';
import { KiotGetCardInfoDTO } from './dto/kiot-get-card-info';
import { ICardConstraints } from './interfaces/card-constraints.interface';
import { CheckDepositBalanceGatewayDTO } from 'src/booking-gateway/dto/check-deposit-balance-gateway.dto';
import { IBillConstraints } from './interfaces/bill-constraints.interface';
import { KiotCreatePINDTO } from './dto/kiot-create-pin.dto';
import { SchedulerRegistry } from '@nestjs/schedule';
import { SmsService } from 'src/sms/sms.service';
import { KiotChangePINDTO } from './dto/kiot-change-pin';
import { FEEDER_COLLECTION_NAME } from 'src/event-proccessor/schemas/constants';
import { IFeeder } from 'src/event-proccessor/intefaces/feeder.inteface';
import { ITransactionEvent } from 'src/booking-gateway/intefaces/transaction-event.inteface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { GetBillInfoDTO } from './dto/get-bill-info.dto';

@Injectable()
export class UserAccountService {

    private cardManagementUrl: string = '';
    private loginUrl: string;
    private listAppId: Set<{}>;

    constructor(
        @InjectModel(USER_ACCOUNT_COLLECTION_NAME) private userAccountModel: Model<IUserAccount>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(ADMIN_USER_COLLECTION_NAME) private adminUserModel: Model<IAdminUser>,
        @InjectModel(PATIENT_COLLECTION_NAME) private patientModel: Model<IPatient>,
        @InjectModel(PATIENT_CODE_COLLECTION_NAME) private patientCodeModel: Model<IPatientCodes>,
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(PAYMENT_HOSPITAL_FEE_COLLECTION_NAME) private paymentHospitalFeeModel: Model<IPaymentHospitalFee>,
        @InjectModel(PAYMENT_COLLECTION_NAME) private paymentModel: Model<IPayment>,
        @InjectModel(CONSTRAINTS_CARD_COLLECTION_NAME) private constraintCardModel: Model<ICardConstraints>,
        @InjectModel(CONSTRAINTS_BILL_COLLECTION_NAME) private constraintBillModel: Model<IBillConstraints>,
        @InjectModel(TRANSACTION_EVENT_NAME) private transactionEventModel: Model<ITransactionEvent>,
        @InjectModel(FEEDER_COLLECTION_NAME) private feederModel: Model<IFeeder>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        private scheduler: SchedulerRegistry,
        private readonly utilService: UtilService,
        private readonly urlConfigService: UrlConfigService,
        private readonly jwtService: JwtService,
        private readonly smsService: SmsService,
        private readonly httpService: PkhHttpService,
        private readonly patientMongoService: PatientMongoService,
        private readonly userService: UserService,
        private readonly transactionConfig: PrefixTransactionCodeTTConfigService,
        private readonly jwtUserYTeConfigService: JwtUserYTeConfigService,
        private readonly jwtUserConfigService: JwtUserConfigService,
    ) {
        this.cardManagementUrl = this.urlConfigService.CardManagementRestfulAPI();
        this.loginUrl = this.urlConfigService.loginPortalUrl();
        this.listAppId = this.utilService.listAppId();
    }

    async searchPatientExtra(appId: string, partnerId: string, formData: SearchPatientExtraInfoMongoDTO): Promise<any> {
        try {
            const params = {
                Ho: formData.surName,
                Ten: formData.firstName,
                NamSinh: formData.birthYear,
                GioiTinh: formData.gender,
                IDTinh: formData.cityId,
            };
            const data = (await this.patientMongoService.getPatientExtraByHIS(partnerId, params).toPromise()).data;
            if (data.length === 0) {
                throw new HttpException({
                    status: HttpStatus.NOT_FOUND,
                }, HttpStatus.NOT_FOUND);
            }
            return data;
        } catch (error) {
            console.log(JSON.stringify(error, null, 2));
            const { response } = error;
            const { status } = response;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hệ thống chưa xử lý được thao thác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
            }
        }
    }

    async searchPatient(appId: string, partnerId: string, formData: SearchPatientInfoMongoDTO): Promise<any> {
        try {
            const data = (await this.patientMongoService.getPatientByHIS(partnerId, formData.msbn).toPromise()).data;
            const getSoHS = get(data, 'SoHS', null);
            if (!!getSoHS === false) {
                throw new HttpException({
                    status: HttpStatus.NOT_FOUND,
                }, HttpStatus.NOT_FOUND);
            }
            return [data];
        } catch (error) {
            console.log(JSON.stringify(error, null, 2));
            const { response } = error;
            const { status } = response;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hệ thống chưa xử lý được thao thác này. Vui lòng thử lại sau.', HttpStatus.BAD_REQUEST);
            }
        }
    }

    async checkLinkCardId(appId: string, partnerId: string, formData: CheckLinkCardDTO): Promise<any> {
        /* kiểm tra xem userPhone này đã có tài khoản hay chưa */
        const checkUserAccount = await this.userAccountModel.findOne({
            cardId: formData.cardId,
            accountId: { $gt: 0 },
        }).exec();

        if (checkUserAccount) {
            return {
                isNew: false,
                userAccount: {
                    ...checkUserAccount.toObject(),
                },
            };
            // throw new HttpException('Mã số hồ sơ này đã đăng ký thẻ', HttpStatus.BAD_REQUEST);
        } else {
            return {
                isNew: true,
            };
        }
        // const { msbn, user, accountId, userPhone, cardId } = checkUserAccount.toObject();

        // return {
        //     msbn,
        //     accountId,
        //     userPhone,
        //     user,
        //     cardId,
        //     message: 'Mã số hồ sơ này đã đăng ký medproId và đã tạo thẻ khám bệnh.',
        // };
    }

    async checkLinkMSBN(appId: string, partnerId: string, formData: CheckLinkMSBNDTO): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra xem phone này đã có mã số bn hay chưa */
        // const yourphone = `${formData.phone}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
        // const checkUserMongo: IUser = await this.userService.checkExistsUserByUsernameMedproId(yourphone);
        // const bojUser: any = {};

        // if (checkUserMongo) {
        /* kiểm tra xem userPhone này đã có tài khoản hay chưa */
        const checkUserAccount = await this.userAccountModel.findOne({
            // userPhone: yourphone,
            msbn: formData.msbn,
            accountId: { $gt: 0 },
        });

        if (checkUserAccount) {
            return {
                isNew: false,
                userAccount: {
                    ...checkUserAccount.toObject(),
                },
            };
            // throw new HttpException('Mã số hồ sơ này đã đăng ký thẻ', HttpStatus.BAD_REQUEST);
        } else {
            return {
                isNew: true,
            };
        }
        // const { msbn, user, accountId, userPhone } = checkUserAccount.toObject();

        // return {
        //     msbn,
        //     accountId,
        //     userPhone,
        //     user,
        //     message: 'Mã số hồ sơ này đã đăng ký medproId và đã tạo thẻ khám bệnh.',
        // };
        // } else {
        //     throw new HttpException('Mã số hồ sơ này chưa đăng ký medproId', HttpStatus.BAD_REQUEST);
        // }
    }

    async linkMSBN_bk(appId: string, partnerId: string, formData: LinkMSBNDTO): Promise<any> {

        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra xem phone này đã có mã số bn hay chưa */
        const yourphone = `${formData.phone}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
        // const checkUserMongo: IUser = await this.userService.checkExistsUserByUsernameMedproId(yourphone);
        // const bojUser: any = {};

        // if (checkUserMongo) {
        //     /* kiểm tra xem patientCode có thuộc userId hay không */
        //     const getPatientByCode = await this.patientCodeModel.findOne({ partnerId, patientCode: formData.msbn }).exec();
        //     if (!getPatientByCode) {
        //         throw new HttpException('Không tìm thấy thông tin hồ sơ theo mã số bệnh nhân.', HttpStatus.NOT_FOUND);
        //     }
        //     const getPatient = await this.patientModel.findOne({ id: getPatientByCode.patientId }).exec();
        //     if (!getPatient) {
        //         throw new HttpException('Không tìm thấy thông tin hồ sơ.', HttpStatus.NOT_FOUND);
        //     }
        //     /* Kiểm tra xem patient này có thuộc user hay ko */
        //     const checkValue = await this.checkUMCPatientBelongsToUser(checkUserMongo._id, getPatient._id);
        //     if (checkValue) {
        //         bojUser.user = checkUserMongo._id;
        //     }
        // }

        const info = {
            firstname: formData.name,
            lastname: formData.surname,
            middlename: '',
            patientCode: formData.msbn,
        };
        try {
            const accountInfo = (await this.createAccountProxy(info).toPromise()).data;
            const { status, data = {} } = accountInfo;
            if (status === 200) {
                // data gia tri sample {
                //     account: {
                //       accountId: 118,
                //       patientCode: 'TTDF01554TT333',
                //       savingAccountId: 45,
                //       submittedOnDate: '2020/10/30'
                //     }
                //   }
                const accountId = get(data, 'account.accountId', 0);
                const savingAccountId = get(data, 'account.savingAccountId', 0);
                const createdTime = moment().toDate();
                if (accountId > 0 && savingAccountId > 0) {

                    /* Kiểm tra xem đã tạo userAccount hay chưa */
                    const checkUserAccount = await this.userAccountModel.findOne({ partnerId, msbn: info.patientCode }).exec();
                    if (checkUserAccount) {
                        return this.userAccountModel
                            .findOneAndUpdate({ id: checkUserAccount.id, accountId: { $eq: 0 } }, {
                                // ...bojUser,
                                accountId,
                                savingAccountId,
                                updatedDate: createdTime,
                            }, { new: true }).exec();
                    } else {
                        /* tiến hành lưu thông tin */
                        const newUserAccount = new this.userAccountModel({
                            id: uuid.v4().replace(/-/g, ''),
                            partnerId,
                            partner: getPartner._id,
                            // ...bojUser, // thông tin chính xác user medproId đang tham chiếu
                            userPhone: yourphone,
                            msbn: info.patientCode,
                            accountId,
                            savingAccountId,
                            name: formData.name,
                            surname: formData.surname,
                            createdDate: createdTime,
                            updatedDate: createdTime,
                        });
                        return newUserAccount.save().catch(error => {
                            const code = error.code;
                            switch (code) {
                                case 11000:
                                    throw new HttpException({
                                        errorCode: error.code,
                                        statusCode: HttpStatus.CONFLICT,
                                        message: 'Thông tin số điện thoại hoặc Mã số bệnh nhân đã tồn tại.',
                                    }, HttpStatus.CONFLICT);
                                default:
                                    throw new HttpException({
                                        errorCode: error.code,
                                        statusCode: HttpStatus.BAD_REQUEST,
                                        message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.',
                                    }, HttpStatus.BAD_REQUEST);
                            }
                        });
                    }

                } else {
                    throw new HttpException({
                        status: HttpStatus.BAD_REQUEST, // throw ra trong cai error.response.status
                        message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.',
                    }, HttpStatus.BAD_REQUEST);
                }
            }
        } catch (error) {
            console.log(error);
            const { response } = error;
            const { status } = response;
            switch (status) {
                case HttpStatus.FORBIDDEN: {
                    // console.log(JSON.stringify(response, null, 2));
                    throw new HttpException('Mã số bệnh nhân đã tồn tại.', HttpStatus.CONFLICT);
                }
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', HttpStatus.BAD_REQUEST);
            }
        }
    }

    async autoSyncMedproId(userName: string, fullname: string = ''): Promise<any> {
        /* tiến hành tạo medproid nếu chưa có */
        const userSalt = this.userService.generateSalt();
        const userHashPwd = this.userService.generateHashPwd('12343234', userSalt);
        /* tạo thông tin user và sign_in_provider */
        const checkUserMongo: IUser = await this.userService.checkExistsUserByUsernameMedproId(userName);
        let result: IUser = checkUserMongo;
        result = await this.userService.insertNewMedproId(checkUserMongo, userName, userSalt, userHashPwd, fullname);
        await this.userService.insertNewPasswordProvider(result, userName, userSalt, userHashPwd, fullname);
        return result;
    }

    async linkMSBN(appId: string, partnerId: string, formData: LinkMSBNDTO): Promise<any> {

        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra xem phone này đã có mã số bn hay chưa */
        const yourphone = `${formData.phone}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
        const userObj: any = {};
        try {
            const fullname = `${formData.surname} ${formData.name}`;
            const medproUser: IUser = await this.autoSyncMedproId(yourphone, fullname);
            /* Tiến hành tìm lại hồ sơ ben HIS theo patientCode */
            const patientResult = await this.patientMongoService.searchPatientByMsbnCard(medproUser._id, appId, partnerId, { msbn: formData.msbn });
            console.log('patientResult', patientResult);
            const getPatient = get(patientResult, 'patient', null);
            if (getPatient) {
                await this.patientMongoService.syncAddPatientToUser(medproUser, getPatient);
            }
            userObj.user = medproUser._id;
        } catch (error) {
            console.log(error);
        }

        const createdTime = moment().toDate();
        /* tiến hành lưu thông tin */
        const newUserAccount = new this.userAccountModel({
            id: uuid.v4().replace(/-/g, ''),
            partnerId,
            partner: getPartner._id,
            userPhone: yourphone,
            ...userObj,
            msbn: formData.msbn,
            name: formData.name,
            surname: formData.surname,
            createdDate: createdTime,
            updatedDate: createdTime,
        });

        const accUser = await newUserAccount.save().catch(error => {
            const code = error.code;
            switch (code) {
                case 11000:
                    throw new HttpException({
                        errorCode: error.code,
                        statusCode: HttpStatus.CONFLICT,
                        message: 'Thông tin số điện thoại hoặc Mã số bệnh nhân đã tồn tại.',
                    }, HttpStatus.CONFLICT);
                default:
                    throw new HttpException({
                        errorCode: error.code,
                        statusCode: HttpStatus.BAD_REQUEST,
                        message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.',
                    }, HttpStatus.BAD_REQUEST);
            }
        });

        const info = {
            firstname: accUser.name,
            lastname: accUser.surname,
            middlename: '',
            patientCode: accUser.msbn,
        };

        try {
            const accountInfo = (await this.createAccountProxy(info).toPromise()).data;
            const { status, data = {} } = accountInfo;
            if (status === 200) {
                const accountId = get(data, 'account.accountId', 0);
                const savingAccountId = get(data, 'account.savingAccountId', 0);
                if (accountId > 0 && savingAccountId > 0) {
                    return this.userAccountModel
                        .findOneAndUpdate({ id: accUser.id, accountId: { $eq: 0 } }, {
                            accountId,
                            savingAccountId,
                            updatedDate: createdTime,
                        }, { new: true }).exec();

                } else {
                    throw new HttpException({
                        status: HttpStatus.BAD_REQUEST, // throw ra trong cai error.response.status
                        message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.',
                    }, HttpStatus.BAD_REQUEST);
                }
            }
        } catch (error) {
            const { response } = error;
            const { status } = response;
            switch (status) {
                case HttpStatus.FORBIDDEN: {
                    throw new HttpException('Mã số bệnh nhân đã tồn tại.', HttpStatus.CONFLICT);
                }
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', HttpStatus.BAD_REQUEST);
            }
        }
    }

    async kiotCreateNewPIN(formData: KiotCreatePINDTO): Promise<any> {
        const createdTime = moment().toDate();
        const hashPin = crypto.createHmac('sha256', this.urlConfigService.getKeyHashedPin())
            .update(formData.PIN)
            .digest('hex');

        const info = await this.userAccountModel
            .findOneAndUpdate({ cardId: formData.cardId, PIN: { $in: [null, ''] } }, {
                PIN: hashPin,
                updatedDate: createdTime,
            }, { new: true })
            .exec();

        if (info) {
            const payload = { username: '', sub: 0, userMongoId: info.user };
            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();
            const token = jwt.sign(payload, jwtOptions.secret, jwtOptions.signOptions);
            try {
                const balanceInfo = (await this.getBalanceProxy(info.accountId).toPromise()).data;
                const { status, data = {} } = balanceInfo;
                if (status === 200) {
                    return {
                        userAccount: info.toObject(),
                        ...data,
                        token,
                    };
                } else {
                    throw new HttpException({
                        status,
                        // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                    }, status);
                }
            } catch (error) {
                const { response } = error;
                const { status } = response;
                throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
            }
            // return {
            //     cardId: formData.cardId,
            //     message: 'Tạo mã PIN thành công.',
            // };
        } else {
            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', HttpStatus.FORBIDDEN);
        }
    }

    async createPIN(appId: string, partnerId: string, formData: CreatePINDTO): Promise<any> {
        /* tìm lại thông accountId */
        const hashPin = crypto.createHmac('sha256', this.urlConfigService.getKeyHashedPin())
            .update(formData.PIN)
            .digest('hex');

        const findAccount = await this.userAccountModel.findOne({ accountId: formData.accountId }).exec();
        if (!findAccount) {
            throw new HttpException('Không tìm thấy thông tin tài khoản.', HttpStatus.NOT_FOUND);
        }
        /* cập nhật thông tin PIN */
        const createdTime = moment().toDate();
        await this.userAccountModel.findByIdAndUpdate({ _id: findAccount._id }, { PIN: hashPin, updatedDate: createdTime }).exec();
        return {
            isOk: true,
            message: 'Tạo mã PIN thành công.',
        };
    }

    async createPINWarning(appId: string, partnerId: string, formData: CreatePINDTO): Promise<any> {
        throw new HttpException('Hiện tại, Cán bộ không được phép tạo mã PIN.', HttpStatus.FORBIDDEN);
    }

    async detailInfo(appId: string, partnerId: string, id: string): Promise<any> {
        /* tìm lại thông accountId */
        const findAccount = await this.userAccountModel.findOne({ id, partnerId }).exec();
        if (!findAccount) {
            throw new HttpException('Không tìm thấy thông tin tài khoản.', HttpStatus.NOT_FOUND);
        }
        /* cập nhật thông tin PIN */
        return findAccount;
    }

    async linkCardId(appId: string, partnerId: string, formData: LinkCardIdDTO): Promise<any> {

        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem có msbn này đã có trong user-account hay chua */
        const checkUserAccount = await this.userAccountModel
            .findOne({ partnerId, msbn: formData.msbn })
            .exec();
        if (!checkUserAccount) {
            throw new HttpException('Vui lòng liên kết số điện thoại với thông tin tài khoản thẻ trước.', HttpStatus.BAD_REQUEST);
        } else {

            const newCardConstraints = new this.constraintCardModel({
                cardId: formData.cardId,
            });

            await newCardConstraints.save().catch(error => {
                const code = error.code;
                switch (code) {
                    case 11000:
                        throw new HttpException({
                            errorCode: error.code,
                            statusCode: HttpStatus.CONFLICT,
                            message: 'Thẻ này đã sử dụng cho tài khoản khác.',
                        }, HttpStatus.CONFLICT);
                    default:
                        throw new HttpException({
                            errorCode: error.code,
                            statusCode: HttpStatus.BAD_REQUEST,
                            message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.',
                        }, HttpStatus.BAD_REQUEST);
                }
            });

            /* cập nhật CardId vào trong user-account */
            const createdTime = moment().toDate();
            const info = await this.userAccountModel
                .findOneAndUpdate({ id: checkUserAccount.id, cardId: { $in: [null, ''] } }, {
                    cardId: formData.cardId,
                    updatedDate: createdTime,
                }, { new: true })
                .exec();
            if (info) {
                /* tạo lịch. trong vòng 30 phút mà ko kích hoạt thẻ thì tự động gửi PIN về sms */
                const getEnv = this.urlConfigService.getEnv();
                const minutes = getEnv !== 'PRODUCTION' ? 2 : 30;
                await this.remindAutoSendPIN(info, minutes);
                return info;
            } else {
                throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', HttpStatus.FORBIDDEN);
            }
        }
    }

    async remindAutoSendPIN(userAccount: IUserAccount, defaultMinutes: number = 30): Promise<any> {
        const remindTime = moment().add(defaultMinutes, 'minutes').toDate();
        const name = `reminder_changedPIN_${userAccount.cardId}_${remindTime.valueOf()}`;
        const job = new CronJob(remindTime, async () => {
            /* Tìm lại thông tin userAccount */
            const getUserAccount = await this.userAccountModel.findOne({ id: userAccount.id, PIN: { $in: [null, ''] } }).exec();
            if (!getUserAccount) {
                /* tức là chưa tạo mã pin */
                const getEnv = this.urlConfigService.getEnv();
                const randomNumber = getEnv !== 'PRODUCTION' ? '1234' : this.utilService.randomNumber();
                /* cập nhật lại PIN */
                const hashPin = crypto.createHmac('sha256', this.urlConfigService.getKeyHashedPin())
                    .update(randomNumber)
                    .digest('hex');
                const createdTime = moment().toDate();
                const info = await this.userAccountModel
                    .findOneAndUpdate({ cardId: userAccount.cardId, PIN: { $in: [null, ''] } }, {
                        PIN: hashPin,
                        updatedDate: createdTime,
                        firstLoginChangedPIN: true,
                    }, { new: true })
                    .exec();

                if (info) {
                    /* tiến hành gửi sms */
                    const phone = userAccount.userPhone.replace(/^[+]84|^0/, '84').replace(/^9/, '849').replace(/^3/, '843');
                    await this.smsService.sendSmsMedpro(phone);
                } else {
                    console.log(`Không thể cập nhật mã PIN cho thẻ ${userAccount.cardId} trong hệ thống`);
                }
            } else {
                const reCheck = await this.userAccountModel.findById({ _id: userAccount._id }).exec();
                if (!reCheck) {
                    console.log('reCheck', reCheck)
                } else {
                    console.log(`Người dùng đã tạo mã PIN cho card ${userAccount.cardId}`);
                }
            }
            this.scheduler.deleteCronJob(name);
        }, () => {
            console.log(`on Complete`);
        });
        this.scheduler.addCronJob(name, job);
        job.start();

    }

    async syncAdminUser(partnerId: string, formData: { readonly userId: number; readonly email: string }): Promise<any> {
        /* check trước khi sync */
        const checkUserAccount = await this.adminUserModel
            .findOne({ email: formData.email })
            .exec();
        if (!checkUserAccount) {

            /* kiểm tra xem partner có config chưa */
            const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
            if (!getPartner) {
                throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
            }
            /* tiến hành insert */

            const newAdminUser = new this.adminUserModel({
                id: uuid.v4().replace(/-/g, ''),
                partnerId,
                partner: getPartner._id,
                ...formData,
                status: true,
            });
            await newAdminUser.save().catch(error => {
                console.log('error', error)
            });
        }
        return true;
    }

    async updateAdminUserStatus(partnerId: string, formData: { email: string, userId: number, status: boolean, apiKey: string }): Promise<any> {
        if (formData.apiKey === 'check-api-key') {
            return this.adminUserModel.findOneAndUpdate({
                email: formData.email,
                userId: formData.userId,
            }, {
                status: formData.status,
            }, { new: true }).exec();
        } else {
            throw new HttpException('Vui lòng kiểm tra lại thông tin', HttpStatus.BAD_REQUEST);
        }
    }

    async cardInfo(formData: GetCardInfoDTO): Promise<any> {
        /* lấy thông tin card */
        const userAccount = await this.userAccountModel.findOne({
            cardId: formData.cardId,
        }).exec();

        if (!userAccount) {
            throw new HttpException('Không tìm thấy thông tin thẻ', HttpStatus.NOT_FOUND);
        }
        /* lấy thông tin balance */

        try {
            const balanceInfo = (await this.getBalanceProxy(userAccount.accountId).toPromise()).data;
            const { status, data = {} } = balanceInfo;
            if (status === 200) {
                return {
                    userAccount: userAccount.toObject(),
                    ...data,
                };
            } else {
                throw new HttpException({
                    status,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, status);
            }
        } catch (error) {
            const { response } = error;
            const { status } = response;
            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
        }
    }

    async kiotCardInfoShort(partnerId: string, formData: GetCardInfoDTO): Promise<any> {
        /* lấy thông tin card */
        const userAccount = await this.userAccountModel.findOne({
            cardId: formData.cardId,
            partnerId,
        }).exec();

        if (!userAccount) {
            throw new HttpException('Không tìm thấy thông tin thẻ', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem card này đã có nhập mã pin chưa */
        const objUserAccount = userAccount.toObject();
        const getPin = get(userAccount, 'PIN', null);
        const objNextStep: any = { isRequiredLogin: false };
        if (getPin) {
            objNextStep.isRequiredLogin = true;
        }
        /* trả về kết quả */
        return {
            fullname: `${userAccount.surname} ${userAccount.name}`,
            cardId: formData.cardId,
            ...objNextStep,
        };
    }

    async kiotChangePIN(formData: KiotChangePINDTO): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyChangePINJwtOptions();
        let cardId = '';
        try {
            const jwtVerify: any = jwt.verify(formData.secretKey, jwtOptions.secret);
            cardId = jwtVerify.cardId;
        } catch (error) {
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }
        const hashPin = crypto.createHmac('sha256', this.urlConfigService.getKeyHashedPin())
            .update(formData.PIN)
            .digest('hex');
        /* tiến hành xử lý change PIN */
        const createdTime = moment().toDate();
        const findUserAccount = await this.userAccountModel.findOneAndUpdate({
            cardId,
            firstLoginChangedPIN: true,
        }, {
            PIN: hashPin,
            updatedDate: createdTime,
            firstLoginChangedPIN: false,
        }).exec();
        if (findUserAccount) {
            /* Tiến hành lấy lại thông tin mới nhất */
            const userCardInfo = await this.userAccountModel.findOne({ cardId }).exec();
            /* tiến hành tạo thông tin token đăng nhập */
            const payload = { username: '', sub: 0, userMongoId: userCardInfo.user };
            const jwtOptionsUser: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();
            const token = jwt.sign(payload, jwtOptionsUser.secret, jwtOptionsUser.signOptions);
            try {
                const balanceInfo = (await this.getBalanceProxy(userCardInfo.accountId).toPromise()).data;
                const { status, data = {} } = balanceInfo;
                if (status === 200) {
                    return {
                        // isChangePIN: false,
                        userAccount: userCardInfo.toObject(),
                        ...data,
                        token,
                    };
                } else {
                    throw new HttpException({
                        status,
                        // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                    }, status);
                }
            } catch (error) {
                const { response } = error;
                const { status } = response;
                throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
            }
        } else {
            throw new HttpException('Mã PIN đã được thay đổi. Vui lòng đăng nhập lại.', HttpStatus.FORBIDDEN);
        }
    }

    async kiotCardInfo(partnerId: string, formData: KiotGetCardInfoDTO): Promise<any> {
        /* lấy thông tin card */
        const hashPin = crypto.createHmac('sha256', this.urlConfigService.getKeyHashedPin())
            .update(formData.PIN)
            .digest('hex');
        const userAccount = await this.userAccountModel.findOne({
            cardId: formData.cardId,
            PIN: hashPin,
            partnerId,
        }).exec();

        if (!userAccount) {
            throw new HttpException('Thông tin đăng nhập không chính xác.', HttpStatus.NOT_FOUND);
        }

        /* Kiểm tra xem card này có bị đánh đánh dấu firstLoginChangedPIN hay ko */
        if (userAccount.firstLoginChangedPIN) {
            const payloadChangePIN = { cardId: '' };
            const jwtOptionsChangePIN: JwtModuleOptions = this.jwtUserConfigService.verifyChangePINJwtOptions();
            const tokenChangePIN = jwt.sign(payloadChangePIN, jwtOptionsChangePIN.secret, jwtOptionsChangePIN.signOptions);
            return {
                isChangePIN: true,
                secretKey: tokenChangePIN,
            };
        }
        /* tạo thông tin token */
        const payload = { username: '', sub: 0, userMongoId: userAccount.user };
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();
        const token = jwt.sign(payload, jwtOptions.secret, jwtOptions.signOptions);
        try {
            const balanceInfo = (await this.getBalanceProxy(userAccount.accountId).toPromise()).data;
            const { status, data = {} } = balanceInfo;
            if (status === 200) {
                return {
                    isChangePIN: false,
                    userAccount: userAccount.toObject(),
                    ...data,
                    token,
                };
            } else {
                throw new HttpException({
                    status,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, status);
            }
        } catch (error) {
            const { response } = error;
            const { status } = response;
            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
        }
    }

    async login(partnerId: string, formData: AdminUserLoginDTO): Promise<any> {
        try {
            const parms = {
                partnerId,
                ...formData,
            };
            const result = (await this.httpService.postHttpRequest(this.loginUrl, parms).toPromise()).data;
            /* tạo token */
            const userId = get(result, 'user.id');
            const email = get(result, 'user.email');
            const payload = {
                userId,
                email,
            };
            await this.syncAdminUser(partnerId, payload);
            const jwtOptions: JwtModuleOptions = this.jwtUserYTeConfigService.createJwtOptions();
            const token = this.jwtService.sign({ ...payload }, {
                expiresIn: jwtOptions.signOptions.expiresIn,
            });
            // const token = this.jwtService.sign(payload);
            return {
                ...result,
                token,
            };
        } catch (error) {
            console.log(error);
            throw new HttpException('Thông tin đăng nhập không chính xác.', HttpStatus.BAD_REQUEST);
        }
    }

    async checkUMCPatientBelongsToUser(userId: string, patientId: string): Promise<any> {
        /* Kiểm tra xem patient này đã thuộc về user hay chua */
        const userInfoWithPatients = await this.getCountPatientInUserPatientUMC(userId);
        // console.log(userInfoWithPatients.patients);
        // console.log(patientId);
        const findIndexValue = userInfoWithPatients.patients.indexOf(patientId);
        // console.log(findPatient);
        if (findIndexValue > -1) {
            return true;
        } else {
            return false;
        }
    }

    async getCountPatientInUserPatientUMC(userId: string): Promise<any> {
        return this.userModel.findById({ _id: userId }, { patients: true }).exec();
    }

    async getDetail(appId: string, partnerId: string, userId: string): Promise<any> {
        /* lấy thông tin user account */
        console.log(userId);
        const userAccount = await this.userAccountModel.findOne({ user: userId, partnerId }).exec();
        console.log(userAccount);
        if (!userAccount) {
            throw new HttpException('Không tìm thấy thông tin tài khoản thẻ.', HttpStatus.NOT_FOUND);
        }
        /* lấy thông tin accountId */
        try {
            const balanceInfo = (await this.getBalanceProxy(userAccount.accountId).toPromise()).data;
            const { status, data = {} } = balanceInfo;
            if (status === 200) {
                return {
                    userAccount: userAccount.toObject(),
                    ...data,
                };
            } else {
                throw new HttpException({
                    status,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, status);
            }
        } catch (error) {
            const { response } = error;
            const { status } = response;
            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
        }
    }

    async getBalance(appId: string, partnerId: string, userId: string, patientCode: string): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem userId này có tồn tại hay không */
        const getUser = await this.userModel.findById({ _id: userId }).exec();
        if (!getUser) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra xem patientCode có thuộc userId hay không */
        const getPatientByCode = await this.patientCodeModel.findOne({ partnerId, patientCode }).exec();
        if (!getPatientByCode) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ theo mã số bệnh nhân.', HttpStatus.NOT_FOUND);
        }
        const getPatient = await this.patientModel.findOne({ id: getPatientByCode.patientId }).exec();
        if (!getPatient) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem patient này có thuộc user hay ko */
        const checkValue = await this.checkUMCPatientBelongsToUser(userId, getPatient._id);
        if (!checkValue) {
            throw new HttpException('Hồ sơ này không thuộc người dùng.', HttpStatus.NOT_FOUND);
        }
        /* tiến hành lấy thông tin */
        const getUserAccount = await this.userAccountModel.findOne({
            partnerId,
            user: userId,
            msbn: patientCode,
        }).exec();

        if (!getUserAccount) {
            throw new HttpException('Không tìm thấy tài khoản người dùng.', HttpStatus.NOT_FOUND);
        }

        /* lấy thông tin accountId */
        try {
            const balanceInfo = (await this.getBalanceProxy(getUserAccount.accountId).toPromise()).data;
            const { status, data = {} } = balanceInfo;
            if (status === 200) {
                return data;
            } else {
                throw new HttpException({
                    status,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, status);
            }
        } catch (error) {
            const { response } = error;
            const { status } = response;
            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
        }
    }

    async getTransactions(appId: string, partnerId: string, userId: string, patientCode: string): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem userId này có tồn tại hay không */
        const getUser = await this.userModel.findById({ _id: userId }).exec();
        if (!getUser) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra xem patientCode có thuộc userId hay không */
        const getPatientByCode = await this.patientCodeModel.findOne({ partnerId, patientCode }).exec();
        if (!getPatientByCode) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ theo mã số bệnh nhân.', HttpStatus.NOT_FOUND);
        }
        const getPatient = await this.patientModel.findOne({ id: getPatientByCode.patientId }).exec();
        if (!getPatient) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem patient này có thuộc user hay ko */
        const checkValue = await this.checkUMCPatientBelongsToUser(userId, getPatient._id);
        if (!checkValue) {
            throw new HttpException('Hồ sơ này không thuộc người dùng.', HttpStatus.NOT_FOUND);
        }
        /* tiến hành lấy thông tin */
        const getUserAccount = await this.userAccountModel.findOne({
            partnerId,
            user: userId,
            msbn: patientCode,
        }).exec();

        if (!getUserAccount) {
            throw new HttpException('Không tìm thấy tài khoản người dùng.', HttpStatus.NOT_FOUND);
        }

        /* lấy thông tin accountId */
        try {
            const transactions = (await this.getTransactionsProxy(getUserAccount.accountId).toPromise()).data;
            const { status, data = {} } = transactions;
            if (status === 200) {
                return data;
            } else {
                throw new HttpException({
                    status,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, status);
            }
        } catch (error) {
            const { response } = error;
            const { status } = response;
            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
        }
    }

    async getTransactionDetail(appId: string, partnerId: string, userId: string, accountId: number, transactionId: number): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem userId này có tồn tại hay không */
        const getUser = await this.userModel.findById({ _id: userId }).exec();
        if (!getUser) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.NOT_FOUND);
        }
        /* lấy thông tin accountId */
        try {
            const transactionData = (await this.getTransactionDetailProxy(accountId, transactionId).toPromise()).data;
            const { status, data = {} } = transactionData;
            if (status === 200) {
                return data;
            } else {
                throw new HttpException({
                    status,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, status);
            }
        } catch (error) {
            const { response } = error;
            const { status } = response;
            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
        }
    }

    async payHospitalFee(appId: string, partnerId: string, userId: string, formData: PayHospitalFeeDTO): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem userId này có tồn tại hay không */
        const getUser = await this.userModel.findById({ _id: userId }).exec();
        if (!getUser) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.NOT_FOUND);
        }
        /* lấy thông tin accountId */
        try {
            const payHospitalFeeData = (await this.payHospitalFeeProxy(formData).toPromise()).data;
            const { status, data = {} } = payHospitalFeeData;
            if (status === 200) {
                return data;
            } else {
                throw new HttpException({
                    status,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, status);
            }
        } catch (error) {
            const { response } = error;
            const { status } = response;
            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
        }
    }

    async withdrawal(appId: string, partnerId: string, userId: string, formData: WithdrawalDTO): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem userId này có tồn tại hay không */
        const getUser = await this.userModel.findById({ _id: userId }).exec();
        if (!getUser) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.NOT_FOUND);
        }
        /* lấy thông tin accountId */
        try {
            const withdrawalData = (await this.withdrawalProxy(formData).toPromise()).data;
            const { status, data = {} } = withdrawalData;
            if (status === 200) {
                return data;
            } else {
                throw new HttpException({
                    status,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, status);
            }
        } catch (error) {
            const { response } = error;
            const { status } = response;
            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
        }
    }

    async adminCreateDeposit(appId: string, partnerId: string, formData: DepositDTO): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem userId này có tồn tại hay không */
        // const getUser = await this.userModel.findById({ _id: userId }).exec();
        // if (!getUser) {
        //     throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.NOT_FOUND);
        // }
        /* lấy thông tin accountId */
        try {
            const depositData = (await this.depositProxy(formData).toPromise()).data;
            const { status, data = {} } = depositData;
            if (status === 200) {
                /* lấy thông tin balance */
                const balanceInfo = (await this.getBalanceProxy(formData.accountId).toPromise()).data;
                const { status: StatusBalance, data: dataBalance = {} } = balanceInfo;
                if (StatusBalance === 200) {
                    return {
                        transaction: data,
                        ...dataBalance,
                    };
                } else {
                    throw new HttpException({
                        status,
                        // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                    }, status);
                }
            } else {
                throw new HttpException({
                    status,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, status);
            }
        } catch (error) {
            const { response } = error;
            const { status } = response;
            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
        }
    }

    async deposit(appId: string, partnerId: string, userId: string, formData: DepositDTO): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem userId này có tồn tại hay không */
        const getUser = await this.userModel.findById({ _id: userId }).exec();
        if (!getUser) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.NOT_FOUND);
        }
        /* lấy thông tin accountId */
        try {
            const depositData = (await this.depositProxy(formData).toPromise()).data;
            const { status, data = {} } = depositData;
            if (status === 200) {
                return data;
            } else {
                throw new HttpException({
                    status,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, status);
            }
        } catch (error) {
            const { response } = error;
            const { status } = response;
            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
        }
    }

    async getBillInfo(appId: string, partnerId: string, userId: string, formData: GetBillInfoDTO): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra lại thông tin user */
        const userMongo = await this.userModel.findById({ _id: userId }).exec();
        if (!userMongo) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.UNAUTHORIZED);
        }

        try {
            const dataPost = {
                partnerId,
                ...formData,
            };
            const getBillInfo = (await this.getPaymentRequestByBillIdOrPatientId(dataPost).toPromise()).data;
            console.log('getBillInfo', getBillInfo);
            const { status, data = {} } = getBillInfo;
            if (status === 200) {
                const getBill = get(data, 'bill');
                if (getBill) {
                    const getBillId = get(getBill, 'billId');
                    if (getBillId) {
                        const getPaymentStatus = get(getBill, 'paymentStatus');
                        return {
                            ...getBill,
                            statusText: getPaymentStatus === 'paid' ? 'Đã thanh toán' : 'Chưa thanh toán',
                        };
                    } else {
                        throw new HttpException({
                            status: HttpStatus.FORBIDDEN,
                            // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                        }, HttpStatus.FORBIDDEN);
                    }
                } else {
                    throw new HttpException({
                        status: HttpStatus.FORBIDDEN,
                        // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                    }, HttpStatus.FORBIDDEN);
                }
            } else {
                throw new HttpException({
                    status,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, status);
            }
        } catch (error) {
            console.log(error);
            const { response } = error;
            const { status } = response;
            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
        }
    }

    async transactionTracking(appId: string, partnerId: string, userId: string): Promise<any> {
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra lại thông tin user */
        const userMongo = await this.userModel.findById({ _id: userId }).exec();
        if (!userMongo) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.UNAUTHORIZED);
        }
        /* lấy thông tin giao dịch thanh toán */
        const payments = await this.paymentModel.find({
            userId: userMongo._id,
            historyType: 1, // những dòng thuộc về phần card
            status: 2, // trạng thái thành công
            partnerId,
        })
            .populate('paymentHospitalFee')
            .sort({ createdAt: 'desc' })
            .exec();
        return payments;
    }

    async checkDepositBalance(appId: string, partnerId: string, userId: string, formData: CheckDepositBalanceGatewayDTO): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra lại thông tin user */
        const userMongo = await this.userModel.findById({ _id: userId }).exec();
        if (!userMongo) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.UNAUTHORIZED);
        }
        /* lấy thông tin account */
        const userAccount = await this.userAccountModel.findOne({ user: userMongo._id, userPhone: userMongo.username }).exec();
        if (!userAccount) {
            throw new HttpException('Không tìm thấy thông tin tài khoản thẻ.', HttpStatus.NOT_FOUND);
        }
        /* lấy thông tin accountId */
        try {
            const dataPost = {
                partnerId,
                ...formData,
            };
            const getBillInfo = (await this.getPaymentRequestByBillIdOrPatientId(dataPost).toPromise()).data;
            console.log('getBillInfo', getBillInfo);
            const { status: statusBill, data: dataBill = {}, description = '' } = getBillInfo;
            console.log('Bill info', dataBill);
            const balanceInfo = (await this.getBalanceProxy(userAccount.accountId).toPromise()).data;
            const { status, data: dataBalance = {} } = balanceInfo;
            if (statusBill === 200 && status === 200) {
                /* Kiểm tra xem đủ tiền hay ko */
                const { bill: { totalAmount = 0 } } = dataBill;
                const userBalance = dataBalance?.balance?.accountBalance || 0;
                if (totalAmount === 0) {
                    throw new HttpException({
                        status: HttpStatus.BAD_REQUEST,
                        message: 'Vui lòng kiểm tra lại thông tin phiếu yêu cầu tạm ứng.',
                    }, HttpStatus.BAD_REQUEST);
                }
                if (userBalance < totalAmount) {
                    return {
                        depositRequired: true,
                        value: totalAmount - userBalance,
                        description,
                    };
                } else {
                    return {
                        depositRequired: false,
                    };
                }
            } else {
                throw new HttpException({
                    status,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, status);
            }
        } catch (error) {
            console.log(error);
            const { response } = error;
            const { status } = response;
            const showMessage = response?.message || 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau';
            throw new HttpException(showMessage, status);
        }

    }

    async paymentFeeByCard(appId: string, partnerId: string, userId: string, formData: PaymentFeeByCardDTO): Promise<any> {
        console.log('chay vao thanh toan the');
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra lại thông tin user */
        const userMongo = await this.userModel.findById({ _id: userId }).exec();
        if (!userMongo) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.UNAUTHORIZED);
        }
        // console.log('userMongo', userMongo);
        /* Lấy thông tin accountId */
        const hashPin = crypto.createHmac('sha256', this.urlConfigService.getKeyHashedPin())
            .update(formData.PIN)
            .digest('hex');
        console.log('hash pin', hashPin);
        const userAccount = await this.userAccountModel.findOne({ user: userMongo._id, userPhone: userMongo.username, PIN: hashPin }).exec();
        if (!userAccount) {
            throw new HttpException('Không tìm thấy thông tin tài khoản thẻ.', HttpStatus.NOT_FOUND);
        }
        console.log('userAccount', userAccount);
        /* tìm lại thông tin cái billId */
        let userBalance = 0;
        try {
            let dataPost: any = {
                partnerId,
                billId: formData.billId,
            };
            if (formData.patientId) {
                dataPost = { ...dataPost, patientId: formData.patientId };
            }
            const getBillInfo = (await this.getPaymentRequestByBillIdOrPatientId(dataPost).toPromise()).data;
            console.log('getBillInfo', getBillInfo);
            const { status, data = {} } = getBillInfo;
            console.log('Bill info', data);
            if (status === 200) {
                /* override */
                const { bill: { totalAmount, description = '', paymentStatus } } = data;
                if (paymentStatus === 'paid') {
                    throw new HttpException({
                        status: HttpStatus.FORBIDDEN,
                        message: 'Phiếu yêu cầu này đã thanh toán rồi.',
                    }, HttpStatus.FORBIDDEN);
                }
                /* Kiểm tra số dư trước khi thực hiện thanh toán */
                const balanceInfo = (await this.getBalanceProxy(userAccount.accountId).toPromise()).data;
                const { status: StatusBalance, data: dataBalance = {} } = balanceInfo;
                console.log('balanceInfo', dataBalance);
                if (StatusBalance === 200) {
                    userBalance = dataBalance?.balance?.accountBalance || 0;
                    if (totalAmount > userBalance) {
                        throw new HttpException({
                            status: HttpStatus.FORBIDDEN,
                            message: 'Số dư hiện tại của bạn không đủ để thanh toán.',
                        }, HttpStatus.FORBIDDEN);
                    }
                } else {
                    throw new HttpException({
                        status: HttpStatus.FORBIDDEN,
                        // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                    }, HttpStatus.FORBIDDEN);
                }
                /* Tiến hành insert bill Contrainsts */

                const newBillConstraints = new this.constraintBillModel({
                    billId: formData.billId,
                });

                await newBillConstraints.save().catch(error => {
                    const code = error.code;
                    switch (code) {
                        case 11000:
                            throw new HttpException({
                                errorCode: error.code,
                                statusCode: HttpStatus.CONFLICT,
                                message: 'Phiếu yêu cầu này đã thanh toán rồi.',
                            }, HttpStatus.CONFLICT);
                        default:
                            throw new HttpException({
                                errorCode: error.code,
                                statusCode: HttpStatus.BAD_REQUEST,
                                message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.',
                            }, HttpStatus.BAD_REQUEST);
                    }
                });

                let checkPayHospitalFeeSuccess = false;
                let checkConfirmPaymentSuccess = false;
                /* Tiến hành gọi thanh toán fee */
                const rand9Char = this.utilService.randomText(12).toUpperCase();
                // tạo transaction code
                const transactionCode = this.transactionConfig.getPrefixTransactionCodeTT(rand9Char, 'CM');
                try {
                    const params = {
                        accountId: userAccount.accountId,
                        paymentHubTransactionId: transactionCode,
                        paymentTypeId: 1,
                        transactionAmount: totalAmount,
                    };
                    console.log('params payHospital Fee', params);
                    const payHospitalFeeData = (await this.payHospitalFeeProxy(params).toPromise()).data;
                    const { status: statusPayFee, data: dataPayFee = {} } = payHospitalFeeData;
                    console.log('dataPayFee payHospital Fee', dataPayFee);
                    if (statusPayFee === 200) {
                        /* tiến hành tạo payment thanh toan tạm ứng */
                        checkPayHospitalFeeSuccess = true;
                        const currentTime = moment();
                        const createDate = currentTime.toDate();
                        const paymentTime = currentTime.format('YYYY-MM-DD HH:mm:ss');

                        const newPaymentHosptialFee = new this.paymentHospitalFeeModel({
                            id: uuid.v4().replace(/-/g, ''),
                            partnerId,
                            partner: getPartner._id,
                            user: userMongo._id,
                            email: formData?.email || '',
                            userAccount: userAccount._id,
                            billId: formData.billId,
                            transactionId: transactionCode,
                            // patientVersion: { type: Schema.Types.ObjectId, ref: PATIENT_VERSION_COLLECTION_NAME },
                            // patientVersionId: { type: String },
                            status: 1,
                            paymentStatus: 2,
                            // invoiceId: { type: String, default: '' },
                            // invoiceCode: { type: String, default: '' },
                            billInfo: JSON.stringify(data),
                            transactionLog: JSON.stringify(dataPayFee),
                            date: createDate,
                        });
                        const paymentHospitalFeeData = await newPaymentHosptialFee.save();
                        /* Tiến hành lưu thông tin payment */
                        const newPayment = new this.paymentModel({
                            id: uuid.v4().replace(/-/g, ''),
                            type: 4,
                            date: createDate,
                            amount: totalAmount,
                            subTotal: totalAmount,
                            totalFee: 0,
                            medproFee: 0,
                            transferFee: 0,
                            status: 2, // 1: tạo đơn hàng 2: thanh toán thành công 3: thất bại 4: Expired
                            paymentMethod: 'CARD',
                            paymentMethodDetail: 'CARD',
                            gatewayId: 'CARD',
                            partnerId,
                            transactionId: transactionCode,
                            paymentHospitalFeeId: paymentHospitalFeeData.id,
                            paymentHospitalFee: paymentHospitalFeeData._id,
                            paymentTime,
                            userId: userMongo._id,
                            historyType: 1,
                            transactionContent: description,
                        });
                        const newPaymentInfo = await newPayment.save();

                        /* gửi request confirmPayment */
                        const pramsConfirmPayment = {
                            billId: formData.billId,
                            gatewayId: 'CARD',
                            paymentAmount: totalAmount,
                            paymentMethod: 'CARD',
                            transactionId: transactionCode,
                        };
                        const confirmPayment = (await this.confirmPaymentProxy(pramsConfirmPayment).toPromise()).data;
                        const { status: statusConfirmPayment, data: dataConfirmPayment = {} } = confirmPayment;
                        console.log('dataConfirmPayment', dataConfirmPayment);
                        if (statusConfirmPayment === 200) {
                            checkConfirmPaymentSuccess = true;
                            const toObj = paymentHospitalFeeData.toObject();
                            const { transactionLog, billInfo } = toObj;

                            try {
                                /* tiến hành insert vào trong transacton event */
                                const transactionEvent = new this.transactionEventModel({
                                    transactionEvent: `deposit.payment.confirm.${transactionCode}`,
                                });
                                await transactionEvent.save();
                                /* Tiến hành tạo feeder */
                                let titleMesasge = `Bạn đã thanh toán phiếu thành công. Mã phiếu: ${formData.billId}`;
                                if (this.listAppId.has(appId)) {
                                    const getHospitalInfo = await this.hospitalModel.findOne({ partnerId }).exec();
                                    titleMesasge = `Bạn đã thanh toán phiếu thành công tại ${getHospitalInfo.name}. Mã phiếu: ${formData.billId}`;
                                }
                                /* lưu feeder lại */
                                const newFeeder = new this.feederModel({
                                    id: uuid.v4().replace(/-/g, ''),
                                    date: moment().toDate(),
                                    topicId: 'deposit.payment.confirm',
                                    userId: userMongo._id,
                                    partnerId,
                                    appId,
                                    title: titleMesasge,
                                    feederData: {
                                        ...newPaymentInfo.toObject(),
                                        // type: 1,
                                        paymentHospitalFeeData,
                                    },
                                });
                                await newFeeder.save();

                            } catch (error) {
                                console.log('Lỗi khi lưu feeder', error);
                            }

                            return {
                                ...toObj,
                                transactionLog: JSON.parse(transactionLog),
                                billInfo: JSON.parse(billInfo),
                                message: 'Thanh toán thành công',
                            };
                        } else {
                            /* Tiến hành log lại dòng thông tin ở trên */

                            throw new HttpException({
                                status: HttpStatus.FORBIDDEN,
                                // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                            }, HttpStatus.FORBIDDEN);
                        }
                    } else {
                        throw new HttpException({
                            status: HttpStatus.FORBIDDEN,
                            // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                        }, HttpStatus.FORBIDDEN);
                    }
                } catch (error) {
                    const { response } = error;
                    const { status: statusCodePayFee } = response;
                    if (!checkPayHospitalFeeSuccess) { /* thao tác gọi qua payHospitalFee failed */
                        /* chứng tỏ là payHospitalFee failed - remove billId constraints */
                        await this.constraintBillModel.findOneAndDelete({ billId: formData.billId }).exec();
                    } else {
                        if (!checkConfirmPaymentSuccess) {
                            console.log(`Mã giao dịch ${transactionCode} trong paymentHospitalFeeModel chưa được confirm.`);
                        }
                        /* gọi qua payHospitalFee thành công. Check tiếp xem confirmPayment thanh cong hay ko */
                        // TODO: tiep tuc kiem tra neh
                    }
                    /* tiến hành xóa các thông tin cần thiết */
                    throw new HttpException({
                        status: statusCodePayFee,
                        message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                    }, statusCodePayFee);
                }
            } else {
                throw new HttpException({
                    status: HttpStatus.FORBIDDEN,
                    // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                }, HttpStatus.FORBIDDEN);
            }
        } catch (error) {
            console.log(error);
            const { response } = error;
            const { status } = response;
            const showMessage = response?.message || 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau';
            throw new HttpException(showMessage, status);
        }
    }

    async getHospitalFee(appId: string, partnerId: string, userId: string): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }

        /* kiểm tra lại thông tin user */
        const userMongo = await this.userModel.findById({ _id: userId }, { username: true, medproId: true }).exec();
        if (!userMongo) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.UNAUTHORIZED);
        }
        /* tìm lại thông tin mà thẻ khám bệnh đang có. */
        const userAccount = await this.userAccountModel.findOne({
            userPhone: userMongo.username,
            user: userMongo._id,
            partnerId,
        }).exec();

        if (!userAccount) {
            return [];
        }
        /* lấy danh sách hồ sơ bệnh nhân có patientCode */
        const patientList = await this.patientMongoService.getAllPatientsByUserIdForHospitalFee(appId, partnerId, { userMongoId: userId, id: 0 });
        const patientCodeHasCard = userAccount.msbn;
        // console.log('patientList', patientList);
        // console.log('userAccount', userAccount);
        const findPatient = find(patientList, { patientCode: patientCodeHasCard });
        if (typeof findPatient !== typeof undefined) {
            /* tiến hành request lấy thông tin danh sách thanh toán tạm ứng */
            console.log('findPatient', findPatient);
            try {
                const paymentRequestForPatientCode = (await this.getPaymentRequestForPatientProxy(patientCodeHasCard, partnerId).toPromise()).data;
                const { status, data = {} } = paymentRequestForPatientCode;
                if (status === 200) {
                    /* override */
                    const { bill } = data;
                    return {
                        ...bill,
                        patientCode: bill.patientId,
                        patientId: findPatient.id,
                        patientNameMedpro: `${findPatient.surname} ${findPatient.name}`,
                    };
                } else {
                    throw new HttpException({
                        status,
                        // message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau',
                    }, status);
                }
            } catch (error) {
                const { response } = error;
                const { status } = response;
                throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', status);
            }

        } else {
            return [];
        }

    }

    async checkAvailable(appId: string, partnerId: string, userId: string): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra lại thông tin user */
        const userMongo = await this.userModel.findById({ _id: userId }, { username: true, medproId: true }).exec();
        if (!userMongo) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.UNAUTHORIZED);
        }
        /* Kiểm tra xem người dùng đã có tạo thẻ hay chưa */
        const getuserAccount = await this.userAccountModel.findOne({ userPhone: userMongo.username }).exec();
        if (!getuserAccount) {
            // throw new HttpException('Bạn chưa có thông tin thẻ.', HttpStatus.NOT_FOUND);
            return {
                available: false,
                message: 'Bạn chưa có thông tin thẻ.',
            };
        }
        /* kiểm tra tiếp xem có liên kết NFC chưa */
        const toObj = getuserAccount.toObject();
        const getCardId = get(toObj, 'cardId', '');
        if (getCardId) {
            return {
                // userAccount: toObj,
                available: true,
                message: 'Bạn đã liên kết thẻ thành công!',
            };
        } else {
            return {
                // userAccount: toObj,
                available: true,
                message: 'Vui lòng tạo liên kết thẻ NFC',
            };
        }
    }

    async createAccount(appId: string, partnerId: string, userId: string, patientCode: string): Promise<any> {
        /* kiểm tra xem partner có config chưa */
        const getPartner = await this.partnerConfigModel.findOne({ partnerId }).exec();
        if (!getPartner) {
            throw new HttpException('Vui lòng thiết lập config cho partner.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem userId này có tồn tại hay không */
        const getUser = await this.userModel.findById({ _id: userId }).exec();
        if (!getUser) {
            throw new HttpException('Không tìm thấy thông tin người dùng.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra xem patientCode có thuộc userId hay không */
        const getPatientByCode = await this.patientCodeModel.findOne({ partnerId, patientCode }).exec();
        if (!getPatientByCode) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ theo mã số bệnh nhân.', HttpStatus.NOT_FOUND);
        }
        const getPatient = await this.patientModel.findOne({ id: getPatientByCode.patientId }).exec();
        if (!getPatient) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem patient này có thuộc user hay ko */
        const checkValue = await this.checkUMCPatientBelongsToUser(userId, getPatient._id);
        if (!checkValue) {
            throw new HttpException('Hồ sơ này không thuộc người dùng.', HttpStatus.NOT_FOUND);
        }
        const info = {
            firstname: getPatient.name,
            lastname: getPatient.surname,
            middlename: '',
            patientCode,
        };
        try {
            const accountInfo = (await this.createAccountProxy(info).toPromise()).data;
            const { status, data = {} } = accountInfo;
            if (status === 200) {
                const accountId = get(data, 'account.accountId', 0);
                const savingAccountId = get(data, 'account.savingAccountId', 0);
                const createdTime = moment().toDate();
                if (accountId > 0 && savingAccountId > 0) {
                    /* tiến hành lưu thông tin */
                    const newUserAccount = new this.userAccountModel({
                        id: uuid.v4().replace(/-/g, ''),
                        partnerId,
                        partner: getPartner._id,
                        user: userId,
                        msbn: patientCode,
                        accountId,
                        savingAccountId,
                        name: getPatient.name,
                        surname: getPatient.surname,
                        createdDate: createdTime,
                        updatedDate: createdTime,
                    });
                    return newUserAccount.save().catch(error => {
                        const code = error.code;
                        switch (code) {
                            case 11000:
                                throw new HttpException({
                                    errorCode: error.code,
                                    statusCode: HttpStatus.CONFLICT,
                                    message: 'Mã số bệnh nhân đã tồn tại trong hệ thống.',
                                }, HttpStatus.CONFLICT);
                            default:
                                throw new HttpException({
                                    errorCode: error.code,
                                    statusCode: HttpStatus.BAD_REQUEST,
                                    message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.',
                                }, HttpStatus.BAD_REQUEST);
                        }
                    });
                } else {
                    throw new HttpException({
                        status: HttpStatus.BAD_REQUEST, // throw ra trong cai error.response.status
                        message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.',
                    }, HttpStatus.BAD_REQUEST);
                }
            }
        } catch (error) {
            const { response } = error;
            const { status } = response;
            switch (status) {
                case HttpStatus.FORBIDDEN: {
                    // console.log(JSON.stringify(error, null, 2));
                    // console.log(JSON.stringify(response, null, 2));
                    throw new HttpException('Mã số bệnh nhân đã tồn tại.', HttpStatus.CONFLICT);
                }
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau', HttpStatus.BAD_REQUEST);
            }
        }
    }

    createAccountProxy(data: any): Observable<AxiosResponse<any>> {
        const {
            firstname,
            lastname,
            middlename = '',
            patientCode,
        } = data;
        const url = `${this.cardManagementUrl}/card/v1/account/createAccount`;
        return this.httpService.postHttpRequest(url, { firstname, lastname, middlename, patientCode });
    }

    getBalanceProxy(accountId: number): Observable<AxiosResponse<any>> {
        const url = `${this.cardManagementUrl}/card/v1/account/getBalance/${accountId}`;
        return this.httpService.getHttpRequest(url);
    }

    getTransactionsProxy(accountId: number): Observable<AxiosResponse<any>> {
        const url = `${this.cardManagementUrl}/card/v1/account/getTransaction`;
        return this.httpService.postHttpRequest(url, {
            accountId,
        });
    }

    getTransactionDetailProxy(accountId: number, transactionId: number): Observable<AxiosResponse<any>> {
        const url = `${this.cardManagementUrl}/card/v1/account/getTransaction/${accountId}/${transactionId}`;
        return this.httpService.getHttpRequest(url);
    }

    payHospitalFeeProxy(formData: PayHospitalFeeDTO): Observable<AxiosResponse<any>> {
        const url = `${this.cardManagementUrl}/card/v1/account/payHospitalFee`;
        return this.httpService.postHttpRequest(url, {
            ...formData,
        });
    }

    withdrawalProxy(formData: WithdrawalDTO): Observable<AxiosResponse<any>> {
        const url = `${this.cardManagementUrl}/card/v1/account/withdrawal`;
        return this.httpService.postHttpRequest(url, {
            ...formData,
        });
    }

    depositProxy(formData: DepositDTO): Observable<AxiosResponse<any>> {
        const url = `${this.cardManagementUrl}/card/v1/account/deposit`;
        return this.httpService.postHttpRequest(url, {
            ...formData,
        });
    }

    getPaymentRequestForPatientProxy(patientCode: string, partnerId: string): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getPaymentHubFeeUrl();
        const url = `${baseUrl}/partner/v1/hospitalFee/getPaymentRequestForPatient/${patientCode}/${partnerId}`;
        return this.httpService.getHttpRequest(url);
    }

    getPaymentRequestByBillId(billId: string): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getPaymentHubFeeUrl();
        const url = `${baseUrl}/partner/v1/hospitalFee/getPaymentRequest/${billId}`;
        return this.httpService.getHttpRequest(url);
    }

    getPaymentRequestByBillIdOrPatientId(data: any): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getPaymentHubFeeUrl();
        const url = `${baseUrl}/partner/v1/hospitalFee/getPaymentRequest`;
        console.log('p: ', data);
        let rewrite = '';
        if (data.billId) {
            rewrite = `${baseUrl}/partner/v1/hospitalFee/getPaymentRequest/${data.billId}/${data.partnerId}`;
        } else {
            rewrite = `${baseUrl}/partner/v1/hospitalFee/getPaymentRequestForPatient/${data.patientId}/${data.partnerId}`;
        }
        return this.httpService.getHttpRequest(rewrite);
    }

    confirmPaymentProxy(data: any): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getPaymentHubFeeUrl();
        const url = `${baseUrl}/partner/v1/hospitalFee/confirmPayment`;
        return this.httpService.postHttpRequest(url, data);
    }

    async test(): Promise<any> {
        try {
            const createdTime = moment().toDate();
            const newUserAccount = new this.userAccountModel({
                id: uuid.v4().replace(/-/g, ''),
                partnerId: 'trungvuong',
                partner: '5efea6360209c63e04a3442d',
                user: '5f605f67b842900019cc32cc',
                msbn: '**************',
                accountId: 33,
                savingAccountId: 66,
                createdDate: createdTime,
                updatedDate: createdTime,
            });
            return newUserAccount.save().catch(error => {
                const code = error.code;
                switch (code) {
                    case 11000:
                        // throw new HttpException('Mã số bệnh nhân đã tồn tại trong hệ thống.', HttpStatus.CONFLICT);
                        throw new HttpException({
                            errorCode: error.code,
                            statusCode: HttpStatus.CONFLICT,
                            message: 'Mã số bệnh nhân đã tồn tại trong hệ thống.',
                        }, HttpStatus.CONFLICT);
                    default:
                        throw new HttpException({
                            errorCode: error.code,
                            statusCode: HttpStatus.BAD_REQUEST,
                            message: 'Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.',
                        }, HttpStatus.BAD_REQUEST);
                }
            });
        } catch (error) {
            // console.log('chay vao trong này');
            console.log(JSON.stringify(error, null, 2));
        }
    }
}
