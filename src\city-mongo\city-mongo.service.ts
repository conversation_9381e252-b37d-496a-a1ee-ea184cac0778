import { Injectable, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { ICountry } from 'src/country-mongo/interfaces/country.interface';
import { Model } from 'mongoose';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { CITY_COLLECTION_NAME } from './schemas/constants';
import { ICity } from './interfaces/city.interface';
import { TranslateService } from '../translate/translate.service';

@Injectable()
export class CityMongoService {
    constructor(
        @InjectModel(CITY_COLLECTION_NAME) private cityModel: Model<ICity>,
        @InjectModel(COUNTRY_COLLECTION_NAME) private countryModel: Model<ICountry>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitaltModel: Model<IHospital>,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        private readonly translateService: TranslateService,
    ) { }

    // tslint:disable-next-line: variable-name
    async find(partnerId: string, country_code: string, locale = 'vi'): Promise<any> {
        const cities = await this.cityModel
            // .find({ partnerId: 'medpro', parent: country_code }, { name: 1, code: 1, id: 1 })
            .find({ partnerId: 'medpro', parent: 'VIE', status: 1 }, { name: 1, code: 1, id: 1 })
            .sort({ name: 'asc' })
            .exec();
        return Promise.all(cities.map(async city => {
          const dataTranslate = await this.translateService.getByBundle('medpro', 'cities', city._id, {}, null, locale);
          return {
            ...city.toObject(),
            ...dataTranslate,
          };
        }));
    }

    async seed(partnerId: string): Promise<any> {
        const hospital = await this.hospitaltModel.findOne({ partnerId }).exec();
        const cities = await this.pkhPatientKnex('dm_city');
        for await (const city of cities) {
            const cityM = new this.cityModel(
                {
                    code: city.id,
                    name: city.name,
                    status: 1,
                    parent: city.country_code,
                    partnerId,
                    hospitalId: hospital._id,
                    createTime: 0,
                    sourceUpdateTime: 0,
                    updateTime: 0,
                });
            await cityM.save();
        }
        return true;
    }
}
