import { Modu<PERSON> } from '@nestjs/common';
import { RelativeController } from './relative.controller';
import { RelativeService } from './relative.service';
import { MongooseModule } from '@nestjs/mongoose';
import { RELATIVE_TYPE_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { RelativeSchema } from 'src/patient-mongo/schemas/relative-mongo.schema';

@Module({
  imports: [MongooseModule.forFeature([
    { name: RELATIVE_TYPE_COLLECTION_NAME, schema: RelativeSchema },
  ])],
  controllers: [RelativeController],
  providers: [RelativeService],
})
export class RelativeModule { }
