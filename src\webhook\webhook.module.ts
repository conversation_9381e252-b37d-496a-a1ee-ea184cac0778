import { HttpModule, MiddlewareConsumer, Module, NestModule, RequestMethod } from '@nestjs/common';
import { RawBodyMiddleware } from 'src/middleware/raw-body.middleware';
import { WebhookController } from './webhook.controller';

@Module({
  imports: [HttpModule],
  controllers: [WebhookController],
})
export class WebhookModule {}
// implements NestModule {
//   public configure(consumer: MiddlewareConsumer): void {
//       consumer
//           .apply(RawBodyMiddleware)
//           .forRoutes({
//               path: '/webhook/care-soft',
//               method: RequestMethod.POST,
//           });
//   }
// }
