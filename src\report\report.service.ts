import { ClientUtilService } from './../config/client-util.service';
import { UrlConfigService } from './../config/config.url.service';
import { UtilService } from './../config/util.service';
import { USER_APP_COLLECTION_NAME, USER_COLLECTION_NAME } from './../user/schemas/constants';
import { BOOKING_COLLECTION_NAME } from './../booking-gateway/schemas/constants';
import { IBooking } from './../booking-gateway/intefaces/booking.inteface';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { IUser } from './../user/interfaces/user.interface';
import { DataReportRealtimeDto } from './dto/data-report-realtime.dto';
import { OptionsDataReportDto } from './dto/options-data-report.dto';
import * as moment from 'moment';
import * as querystring from 'query-string';

@Injectable()
export class ReportService {
    private readonly apiGaUrl: string;

    constructor(
        @InjectModel(BOOKING_COLLECTION_NAME) private readonly bookingModel: Model<IBooking>,
        @InjectModel(USER_COLLECTION_NAME) private readonly userModel: Model<IUser>,
        @InjectModel(USER_APP_COLLECTION_NAME) private readonly userAppModel: Model<IUser>,
        private readonly util: UtilService,
        private readonly urlConfigService: UrlConfigService,
        private readonly client: ClientUtilService,
    ) {
        this.apiGaUrl = `${this.urlConfigService.getGoogleAnalyticsUrl}`;
    }

    async getReportDataRealtime(options?: OptionsDataReportDto): Promise<DataReportRealtimeDto> {
        const [userRegisters, bookingExams, userNews, userActives, userSessions] = await Promise.all([
            this.getUserRegisters(),
            this.getBookingExams(options),
            this.getUserNews(options),
            this.getUserActive(),
            this.getUserSesions(),
        ]);

        return {
            ...userRegisters,
            ...bookingExams,
            ...userNews,
            ...userActives,
            ...userSessions,
        };
    }

    async getUserRegisters(): Promise<any> {
        const userRegisters = await this.userModel.countDocuments().exec();

        return {
            userRegisters: this.util.formattNumber(userRegisters, 'K+'),
        };
    }

    async getUserNews(options?: OptionsDataReportDto): Promise<any> {
        let between: any = {};
        if (!options?.startDate || !options?.endDate) {
            const currentDate = moment().toDate();
            const dateMonthRange = this.util.getMonthDateRange(currentDate.getUTCFullYear(), currentDate.getUTCMonth());
            between = {
                $gte: dateMonthRange.start.toDate(),
                $lte: dateMonthRange.end.toDate(),
            };
        } else {
            between = {
                $gte: options?.startDate,
                $lte: options?.endDate,
            };
        }

        const userNews = await this.userAppModel.countDocuments({
            createdAt: between,
        }).exec();

        return {
            userNews: `${userNews}+`,
        };
    }

    async getBookingExams(options?: OptionsDataReportDto): Promise<any> {
        let between: any = {};
        if (!options?.startDate || !options?.endDate) {
            const currentDate = moment();
            const startDate = currentDate.subtract(1, 'days').set({hours: 0, minutes: 0, seconds: 0 }).toDate();
            const endDate = moment(startDate).set({ hours: 23, minutes: 59, seconds: 59 }).toDate();
            between = {
                $gte: startDate,
                $lte: endDate,
            };
        } else {
            between = {
                $gte: options?.startDate,
                $lte: options?.endDate,
            };
        }

        const bookingExams = await this.bookingModel.countDocuments({
            date: between,
        }).exec();

        return {
            bookingExams: `${bookingExams}+`,
        };
    }

    async getUserActive(): Promise<any> {
        const api = querystring.stringifyUrl({
            url: `${this.apiGaUrl}/report`,
            query: {
                type: 'CLINIC',
                organization: this.urlConfigService.getGoogleAnalyticsOrganization,
                token: this.urlConfigService.getGoogleAnalyticsToken,
            },
        });
        const data = await this.client.get(api);
        return {
            userActives:  this.util.formattNumber(data?.activeUsers, 'K+'),
        };
    }

    async getUserSesions(): Promise<any> {
        const api = querystring.stringifyUrl({
            url: `${this.apiGaUrl}/report`,
            query: {
                type: 'CLINIC',
                organization: this.urlConfigService.getGoogleAnalyticsOrganization,
                token: this.urlConfigService.getGoogleAnalyticsToken,
            },
        });
        const data = await this.client.get(api);
        return {
            userSessions:  this.util.formattNumber(data?.sessions, 'K+'),
        };
    }
}
