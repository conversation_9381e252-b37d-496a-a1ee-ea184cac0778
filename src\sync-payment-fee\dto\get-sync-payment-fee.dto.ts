import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class GetSyncPaymentFeeDto {
    @ApiProperty({ description: 'pageIndex', required: true, type: Number, default: 0 })
    @IsNotEmpty({ message: 'pageIndex is required' })
    @Transform(value => Number(value))
    pageIndex: number;

    @ApiProperty({ description: 'pageSize', required: false, type: Number, default: 10 })
    @Transform(value => Number(value))
    pageSize?: number;
}
