import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { TreeNodeService } from './tree-node.service';
import { TreeNodeDto } from './dto/tree-node.dto';
import { REPO_NAME_BETA } from '../common/constants';
import { ConfigRepoService } from '../config/config.repo.service';
import { GlobalSettingService } from '../global-setting/global-setting.service';

@Controller('tree')
export class TreeNodeController {
    repoName: string;

    constructor(
        private readonly service: TreeNodeService,
        private configRepoService: ConfigRepoService,
        private globalSettingService: GlobalSettingService,
    ) {
        this.repoName = this.configRepoService.getRepoName();
    }

    @Post('create-node')
    createNode(@Body() body: TreeNodeDto) {
        return this.service.createNode(body);
    }

    @Put('update-node')
    updateNode(@Body() body: TreeNodeDto) {
        const { _id, ...rest } = body;
        return this.service.updateNode(_id, rest);
    }

    @Delete('delete-node/:id')
    deleteNode(@Param('id') id: string) {
        return this.service.deleteNode(id);
    }

    @Get('root')
    getRoot(@Query('type') type: string) {
        return this.service.getRoot(type);
    }

    @Get()
    async getAll() {
        return this.service.getAll();
    }
}
