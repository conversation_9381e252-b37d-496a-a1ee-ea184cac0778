import { ArgumentsHost, Catch, ConflictException, ExceptionFilter, HttpStatus } from '@nestjs/common';
import { MongoError } from 'mongodb';
import { Request, Response } from 'express';
import * as _ from 'lodash';

@Catch()
export class MongoExceptionFilter implements ExceptionFilter {
    catch(exception: any , host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const request = ctx.getRequest<Request>();
        const exceptionObj = JSON.parse(JSON.stringify(exception));
        if ( exceptionObj.name === 'MongoError' ) {
            switch (exception.code) {
                case 11000:
                    const key = _.first(Object.keys(exceptionObj.keyValue));
                    response.status(HttpStatus.CONFLICT).json({
                        statusCode: HttpStatus.CONFLICT,
                        message: `${key} đã tồn tại giá trị này! Xin nhập giá trị khác !`,
                    });
                    break;
                default:
                    response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
                        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                        message: exception.message,
                    });
                    break;
            }
        } else {
            response.status(exception.getStatus()).json({
                statusCode: exception.getStatus() ,
                message: exception.message,
            });
        }
    }
}
