import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SYNC_CTCH_BOOKING_PROCESS_FAILED } from './constants';

const Schema = mongoose.Schema;

export const SyncCTCHBookingProcessFailedSchema = new Schema({
    processId: { type: String },
    id: { type: Number, required: true },
    sourceId: { type: String, default: 'ctchhcm' },
    date_create: { type: Date }, // lấy đúng giờ bên mysql
    syncStatus: { type: String, default: 'errored' }, // pending -> active -> success| errored
}, {
    collection: SYNC_CTCH_BOOKING_PROCESS_FAILED,
    timestamps: true,
}).plugin(jsonMongo);
