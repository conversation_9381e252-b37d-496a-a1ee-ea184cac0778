import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDateString, IsNotEmpty, IsOptional } from 'class-validator';
import { createHealthIndexDTO } from './health-index.dto';

export class createExaminationResultDTO extends createHealthIndexDTO {
    @ApiProperty()
    @IsOptional()
    name?: string;

    @ApiProperty()
    @IsDateString(
        { strict: true },
        {
            message: 'Thông tin ngày khám. ISOString',
        },
    )
    @IsNotEmpty()
    dateExamination: string;

    @IsOptional()
    @Transform(value => `${value}`.trim())
    readonly description: string;

    patientId?: string;

    @IsOptional()
    files: [string];

    @ApiProperty()
    @IsOptional()
    hospitalName: string;

    @IsOptional()
    source: string;
}
