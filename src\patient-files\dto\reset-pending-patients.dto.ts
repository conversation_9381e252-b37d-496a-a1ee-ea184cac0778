import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsArray } from 'class-validator';

export class ResetPendingPatientsDTO {
    @ApiProperty({
        description: 'Danh sách trạng thái cần reset về pending',
        example: ['no_match', 'error'],
        required: false,
        type: [String],
        default: ['no_match', 'error']
    })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    statuses?: string[] = ['no_match', 'error'];

    @ApiProperty({
        description: 'Danh sách CMND cụ thể cần reset (tùy chọn). <PERSON><PERSON><PERSON> không truyền sẽ reset tất cả',
        example: ['123456789', '987654321'],
        required: false,
        type: [String]
    })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    cmnds?: string[];
}
