import * as jsonMongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { LIST_CDN_IMAGE_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const UploadCdnImagePatientSchema = new Schema(
    {
        fileUrl: { type: String, required: true },
        fileName: { type: String, required: true },
        fileType: { type: String },
        examResultId: { type: String, required: false },
        userId: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    },
    {
        collection: LIST_CDN_IMAGE_COLLECTION_NAME,
        timestamps: true, // Tự động thêm createdAt & updatedAt
    },
).plugin(jsonMongo);
