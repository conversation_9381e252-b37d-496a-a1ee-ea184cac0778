import {ApiProperty} from '@nestjs/swagger';
import {Transform} from 'class-transformer';
import {MaxLength, IsNotEmpty} from 'class-validator';

export class HomeBannerActionDto {
    @ApiProperty({description: 'id', required: false, type: String})
    @Transform(value => `${value}`.trim())
    readonly id?: string;

    @ApiProperty({description: 'Vui lòng nhập action', required: true, type: String})
    @Transform(value => `${value}`.trim())
    // @MaxLength(100, {message: 'Không vượt quá 100 ký tự'})
    // @IsNotEmpty({message: 'Vui lòng bổ sung thông tin'})
    readonly action?: string;

    @ApiProperty({description: 'Vui lòng nhập imageUrl', required: true, type: String})
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({message: '<PERSON><PERSON> lòng bổ sung thông tin'})
    readonly imageUrl: string;

    @ApiProperty({description: 'Type mở webview hoặc browser', required: false, type: Number})
    @Transform(value => Number(value))
    type?: number;
}
