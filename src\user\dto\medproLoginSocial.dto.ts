import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsMobilePhone, IsEnum, ValidateIf } from 'class-validator';

export class MedproLoginSocialDTO {
    @ApiProperty({
        description: 'Số điện thoại',
        required: false,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'Vui lòng nhập số điện thoại',
    // })
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'S<PERSON> điện thoại không hợp lệ.',
    })
    @ValidateIf(o => o.phone)
    readonly phone: string;

    @ApiProperty({
        description: 'type',
        required: true,
        type: String,
        enum: [
            'password', 'google', 'facebook', 'zalo', 'firebase',
        ],
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: '<PERSON><PERSON> lòng bổ sung thông tin',
    })
    @IsEnum({
        password: 'password',
        google: 'google',
        facebook: 'facebook',
        zalo: 'zalo',
        firebase: 'firebase',
    }, {
        message: 'Vui lòng gửi lên đúng thông tin [password, google, facebook, zalo, firebase] ',
    })
    readonly type: string;

    @ApiProperty({
        description: 'password',
        required: false,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'Mật khẩu',
    // })
    readonly password?: string;

    @ApiProperty({
        description: 'Token ID lấy từ firebase',
        required: false,
        type: String,
    })
    readonly token?: string;

}
