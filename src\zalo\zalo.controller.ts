import { UserAccessTokenDto } from './dto/user-access-token.dto';
import { Body, Controller, Get, Headers, Inject, Post } from '@nestjs/common';
import { ZaloService } from './zalo.service';
import { MedproLoginSocialDTO } from 'src/user/dto/medproLoginSocial.dto';

@Controller('zalo')
export class ZaloController {
    @Inject(ZaloService)
    private readonly service: ZaloService;

    @Get('authorization-code')
    getAuthorizationCodeGrant(@Headers('platform') platform: string, @Headers('appid') appid: string) {
        return this.service.getAuthorizationCodeGrant(appid, platform);
    }

    @Post('access-token')
    getUserAccessToken(
        @Headers('appid') appid: string,
        @Body() formdata: UserAccessTokenDto,
    ): Promise<any> {
        return this.service.getUserAccessToken(appid, formdata);
    }
}
