import { ValidationArguments, ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';
import * as moment from 'moment';

@ValidatorConstraint()
export class CheckBirthDateSignInProvider implements ValidatorConstraintInterface {

    validate(text: string, validationArguments: ValidationArguments) {
        const constraint = validationArguments.constraints[0];
        const yearInput = moment(text).year();
        return yearInput >= constraint;
    }
}
