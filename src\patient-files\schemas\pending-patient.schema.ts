import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PENDING_PATINET_HEALTH_INDEX } from './constants';

const Schema = mongoose.Schema;

const PendingPatientSchema = new Schema(
  {
    cmnd: { type: String, required: true },
    healthData: {
      examination: String,
      examinationId: String,
      glucoseU: String,
      bp: String,
      height: Number,
      weight: Number,
      bmi: Number,
      waist: String,
      pulse: String,
      temperature: String,
      respiratoryRate: String,
      drugAllergyStatus: String,
      ingredientAllergyStatus: String,
      foodAllergyStatus: String,
      familyHistoryPulse: String,
      familyHistoryHypertension: String,
      familyHistoryMental: String,
      familyHistoryCancer: String,
      familyHistoryAsthma: String,
      familyHistoryEpilepsy: String,
      familyHistoryTuberculosis: String,
    },
    status: { type: String, default: 'pending' }, // pending, no_match, error (synced sẽ bị xóa)
    createdAt: { type: Date, default: Date.now },
    linkedAt: { type: Date }, // Thời gian xử lý
    linkedPatientIds: [{ type: String }], // Danh sách patient IDs đã được link (trước khi xóa)
    errorMessage: { type: String }, // Thông báo lỗi nếu status = 'error'
  },
  {
    collection: PENDING_PATINET_HEALTH_INDEX,
    timestamps: true,
  },
).plugin(jsonMongo);

export { PendingPatientSchema };