import { CacheModule, Module } from '@nestjs/common';
import { CacheManagerController } from './cache-manager.controller';
import { CacheManagerService } from './cache-manager.service';
import * as redisStore from 'cache-manager-redis-store';
import { ConfigModule } from 'src/config/config.module';
import { ConfigCacheManagerService } from 'src/config/config.cache-manager.service';
import { MEMORY, REDIS } from './cache-manager.constant';
import { Logger } from '@nestjs/common';

@Module({
    imports: [
        CacheModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configCacheManagerService: ConfigCacheManagerService) => {
                const typeCache = configCacheManagerService.getCacheManagerType();
                const logger = new Logger(`CacheMannager`);
                const ttl = configCacheManagerService.getCacheTtlDefault();
                switch (typeCache) {
                    case REDIS:
                        const host = configCacheManagerService.getRedisHost();
                        const port = configCacheManagerService.getRedisPort();
                        logger.warn(`Redis listen on ${host}:${port}`);
                        return {
                            store: redisStore,
                            host,
                            port,
                            ttl,
                        };
                    case MEMORY:
                        logger.warn(`Cache Memory`);
                        return {
                            ttl,
                        };
                }
            },
            inject: [ConfigCacheManagerService],
        }),
    ],
    controllers: [CacheManagerController],
    providers: [CacheManagerService],
    exports: [CacheManagerService],
})
export class CacheManagerModule {}
