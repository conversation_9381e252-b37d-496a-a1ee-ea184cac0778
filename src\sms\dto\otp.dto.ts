import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class OtpDto {
    @ApiProperty({ description: 'Vui lòng nhập partnerId!', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập partnerId!' })
    readonly partnerId: string;

    @ApiProperty({ description: 'Vui lòng nhập appId!', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập appId!' })
    readonly appId: string;
}
