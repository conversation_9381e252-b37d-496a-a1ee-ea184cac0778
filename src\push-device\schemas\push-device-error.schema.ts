import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { PUSH_DEVICE_COLLECTION_NAME, PUSH_DEVICE_ERROR_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const PushDeviceErrorSchema = new Schema({
    clientId: String,
    clientToken: String,
    userId: String,
    appId: String,
    partnerId: String,
    platform: String,
}, {
    collection: PUSH_DEVICE_ERROR_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
