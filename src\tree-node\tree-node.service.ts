import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { TREE_NODE_COLLECTION_NAME } from './schemas/constants';
import { ITreeNode } from './intefaces/tree-node.inteface';
import { TreeNodeDto } from './dto/tree-node.dto';
import { ConfigRepoService } from '../config/config.repo.service';
import { REPO_NAME_BETA } from '../common/constants';
import { orderBy } from 'lodash';

@Injectable()
export class TreeNodeService {
    repoName: string;

    constructor(@InjectModel(TREE_NODE_COLLECTION_NAME) private model: Model<ITreeNode>, private configRepoService: ConfigRepoService) {
        this.repoName = configRepoService.getRepoName();
    }

    async getAll() {
        let condition: any = {
            status: 1,
        };

        if (new Set(REPO_NAME_BETA).has(this.repoName)) {
            condition = {};
        }

        let res :any = await this.model.find(condition).exec();

        if (new Set(REPO_NAME_BETA).has(this.repoName)) {
            res = res.map(r => r.toObject());
        } else {
            res = res.map(r => {
                const { children = [], ...rest } = r.toObject();

                return {
                    children: children.filter(child => child.status !== 0),
                    ...rest,
                };
            });
        }

        res = res.map(item => {
            const { children = [], ...rest } = item;
            return {
                ...rest,
                children: orderBy(children, ['order'], ['asc']),
            }
        })

        return orderBy(res, ['order'], ['asc']);
    }

    getRoot(type: string) {
        return this.model.findOne({ type }).exec();
    }

    createNode(data: TreeNodeDto) {
        return this.model.create(data);
    }

    updateNode(id: string, data: Partial<TreeNodeDto>) {
        return this.model.updateOne({ _id: id }, data).exec();
    }

    deleteNode(id: string) {
        return this.model.deleteOne({ _id: id }).exec();
    }
}
