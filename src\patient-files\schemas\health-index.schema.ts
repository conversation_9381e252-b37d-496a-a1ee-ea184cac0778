import * as json<PERSON><PERSON><PERSON> from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { HEALTH_INDEX_COLLECTION_NAME, PATIENT_COLLECTION_NAME, PATIENT_EXAMINATION_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

function toUpperCaseSetter(value: string) {
  if (!value) return value;
  return value.toUpperCase();
}

export const HealthIndexSchema = new Schema(
  {
    examination: { type: Schema.Types.ObjectId, ref: PATIENT_EXAMINATION_COLLECTION_NAME, required: true },
    userId: { type: String, ref: USER_COLLECTION_NAME, required: false },
    patientId: { type: String, ref: PATIENT_COLLECTION_NAME, required: false },
    examinationId: { type: String, ref: PATIENT_EXAMINATION_COLLECTION_NAME, required: true },

    // Chỉ số sức khỏe
    glucoseU: { type: String, required: false },
    bp: { type: String, required: false },
    height: { type: Number, required: false },
    weight: { type: Number, required: false },
    bmi: { type: Number, required: false },
    waist: { type: String, required: false },
    bloodType: { type: String, required: false },
    pulse: { type: String, required: false },
    temperature: { type: String, required: false },
    respiratoryRate: { type: String, required: false },

    // Dị ứng
    drugAllergyStatus: { 
      type: String, 
      enum: ['YES', 'NO', 'UNKNOWN'], 
      required: false,
      set: toUpperCaseSetter,
    },
    drugAllergyNote: { type: String, required: false },
    ingredientAllergyStatus: { 
      type: String, 
      enum: ['YES', 'NO', 'UNKNOWN'], 
      required: false,
      set: toUpperCaseSetter,
    },
    ingredientAllergyNote: { type: String, required: false },
    foodAllergyStatus: { 
      type: String, 
      enum: ['YES', 'NO', 'UNKNOWN'], 
      required: false,
      set: toUpperCaseSetter,
    },
    foodAllergyNote: { type: String, required: false },

    // Tiền sử bệnh lý gia đình
    familyHistoryPulse: { type: String, required: false },
    familyHistoryHypertension: { 
      type: String, 
      enum: ['YES', 'NO', 'UNKNOWN'], 
      required: false,
      set: toUpperCaseSetter,
    },
    familyHistoryMental: { 
      type: String, 
      enum: ['YES', 'NO', 'UNKNOWN'], 
      required: false,
      set: toUpperCaseSetter,
    },
    familyHistoryCancer: { 
      type: String, 
      enum: ['YES', 'NO', 'UNKNOWN'], 
      required: false,
      set: toUpperCaseSetter,
    },
    familyHistoryAsthma: { 
      type: String, 
      enum: ['YES', 'NO', 'UNKNOWN'], 
      required: false,
      set: toUpperCaseSetter,
    },
    familyHistoryEpilepsy: { 
      type: String, 
      enum: ['YES', 'NO', 'UNKNOWN'], 
      required: false,
      set: toUpperCaseSetter,
    },
    familyHistoryTuberculosis: { 
      type: String, 
      enum: ['YES', 'NO', 'UNKNOWN'], 
      required: false,
      set: toUpperCaseSetter,
    },
  },
  {
    collection: HEALTH_INDEX_COLLECTION_NAME,
    timestamps: true,
  },
).plugin(jsonMongo);
