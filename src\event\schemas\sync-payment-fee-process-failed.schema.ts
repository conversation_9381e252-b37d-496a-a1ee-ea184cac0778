import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SYNC_PAYMENT_FEE_PROCESS_FAILED } from './constants';

const Schema = mongoose.Schema;

export const SyncPaymentFeeProcessFailedSchema = new Schema({
    processId: { type: String },
    id: { type: Number, required: true },
    idAutoIncrement: { type: Number, required: true },
    sourceId: { type: String, default: 'umc' },
    date_create: { type: Date, required: true }, // lấy đúng giờ bên mysql
    payment_fee_id: { type: Number, required: true },
    syncStatus: { type: String, default: 'errored' }, // pending -> active -> success| errored
    error:  { type: Schema.Types.Mixed, required: false },
}, {
    collection: SYNC_PAYMENT_FEE_PROCESS_FAILED,
    timestamps: true,
}).plugin(jsonMongo);
