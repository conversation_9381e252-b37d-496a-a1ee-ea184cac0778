import { Module, HttpModule } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BookingSchema } from 'src/booking-gateway/schemas/booking.schema';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { GlobalSettingModule } from 'src/global-setting/global-setting.module';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { PartnerConfigSchema } from 'src/partner-config/schemas/partner-config.schema';
import { SmsService } from './sms.service';

@Module({
  imports: [
    HttpModule,
    MongooseModule.forFeature([
      { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
      { name: PARTNER_CONFIG_COLLECTION_NAME, schema: PartnerConfigSchema },
    ]),
    GlobalSettingModule,
  ],
  providers: [SmsService],
})
export class SmsModule { }
