import { Injectable, CanActivate, ExecutionContext, Scope, Inject } from '@nestjs/common';
import { Observable } from 'rxjs';
import { UserService } from 'src/user/user.service';

@Injectable()
export class MedproIdGuard implements CanActivate {

    constructor(private readonly userService: UserService) {}

    canActivate(
        context: ExecutionContext,
    ): boolean | Promise<boolean> | Observable<boolean> {
        const request = context.switchToHttp().getRequest();
        // console.log('afdsfasff', request.user);
        return true;
    }
}
