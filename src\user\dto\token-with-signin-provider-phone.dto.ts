import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';

export class TokenWithSignInProviderPhoneDto {

    @ApiProperty({
        description: 'Token sau khi nhập otp',
        required: true,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng gửi lên token',
    })
    readonly token: string;

    @ApiProperty({
        description: 'type',
        required: true,
        type: String,
        enum: ['password', 'google', 'facebook', 'zalo', 'firebase'],
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: '<PERSON>ui lòng bổ sung thông tin',
    })
    @IsEnum(
        {
            password: 'password',
            google: 'google',
            facebook: 'facebook',
            zalo: 'zalo',
            firebase: 'firebase',
        },
        {
            message: '<PERSON><PERSON> lòng gửi lên đúng thông tin [password, google, facebook, zalo, firebase] ',
        },
    )
    readonly type: string;

}