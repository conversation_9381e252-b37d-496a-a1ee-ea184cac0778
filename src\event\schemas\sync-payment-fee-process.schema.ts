import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SYNC_PAYMENT_FEE_PROCESS } from './constants';
import { SyncProcessStatus } from '../dto/sync-status.dto';

const Schema = mongoose.Schema;

export const SyncPaymentFeeProcessSchema = new Schema({
    processId: { type: String },
    id: { type: Number, required: true },
    idAutoIncrement: { type: Number, required: true },
    sourceId: { type: String, default: 'umc' },
    date_create: { type: Date }, // l<PERSON>y đúng giờ bên mysql
    payment_fee_id: { type: Number, required: true },
    syncStatus: { type: String, default: SyncProcessStatus.INITIAL }, // pending -> active -> success| errored
}, {
    collection: SYNC_PAYMENT_FEE_PROCESS,
    timestamps: true,
}).plugin(jsonMongo);
