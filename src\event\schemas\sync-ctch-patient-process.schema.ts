import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SYNC_DHYD_PATIENT_PROCESS } from './constants';
import { SyncProcessStatus } from '../dto/sync-status.dto';

const Schema = mongoose.Schema;

export const SyncCTCHPatientProcessSchema = new Schema({
    processId: { type: String },
    id: { type: Number, required: true },
    userId: { type: Number },
    medproId: { type: String },
    sourceId: { type: String, default: 'ctchhcm' },
    date_create: { type: Date }, // l<PERSON>y đúng giờ bên mysql
    syncStatus: { type: String, default: SyncProcessStatus.INITIAL }, // pending -> active -> success| errored
}, {
    collection: SYNC_DHYD_PATIENT_PROCESS,
    timestamps: true,
}).plugin(jsonMongo);
