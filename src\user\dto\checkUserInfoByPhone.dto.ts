import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsMobilePhone, IsEnum, ValidateIf } from 'class-validator';

export class CheckUserInfoByPhoneDTO {
    @ApiProperty({
        description: 'Số điện thoại',
        required: false,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Số điện thoại không hợp lệ.',
    })
    @ValidateIf(o => o.phone)
    readonly phone: string;

    // @ApiProperty({
    //     description: 'deviceId',
    //     required: false,
    //     type: 'string',
    // })
    // @Transform(value => `${value}`.trim())
    // readonly deviceId?: string;

    captchaResponse: string;
    captchaType?: string;

}
