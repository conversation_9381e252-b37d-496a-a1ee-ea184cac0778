import { ApiProperty } from '@nestjs/swagger';
import { IsMobilePhone, IsNotEmpty } from 'class-validator';
import { OtpDto } from './otp.dto';

export class SendOTPMessageHubDTO extends OtpDto {
    @ApiProperty({ description: 'Số điện thoại', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập số điện thoại!' })
    @IsMobilePhone('vi-VN', { strictMode: false }, { message: 'Số điện thoại gửi lên không đúng định dạng!' })
    readonly phone: string;

    @ApiProperty({ description: 'signKey', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập signKey!' })
    readonly signKey?: string;

    @ApiProperty({ description: 'clientId', required: true, type: String })
    @IsNotEmpty({ message: '<PERSON>ui lòng nhập clientId!' })
    readonly clientId?: string;
}
