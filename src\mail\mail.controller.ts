import { Body, Controller, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { MailService } from './mail.service';

@Controller('mail')
@ApiTags('Mail Service')
export class MailController {
    constructor(private readonly service: MailService) {}

    @Post('user-booking-success')
    onUserBookingSuccess(@Query('bookingObjectId') id: string): Promise<void> {
        return this.service.onUserBookingSuccess(id);
    }

    @Post('user-booking-update-success')
    onUserUpdateBookingSuccess(@Query('bookingObjectId') id: string): Promise<void> {
        return this.service.onUserUpdateBookingSuccess(id);
    }

    @Post('user-booking-cancel-success')
    onUserCancelBookingSuccess(@Query('bookingObjectId') id: string): Promise<void> {
        return this.service.onUserCancelBookingSuccess(id);
    }

    @Post('user-register-success')
    onUserRegisterSuccess(@Body() formData: any): Promise<void> {
        return this.service.onUserRegisterSuccess(formData);
    }

    @Post('user-create-patient-success')
    onUserCreatePatientSuccess(@Body() formData: any): Promise<void> {
        return this.service.onUserCreatePatientSuccess(formData);
    }

    @Post('patient-booking-success')
    onPatientBookingSuccess(@Query('bookingObjectId') id: string): Promise<void> {
        return this.service.onPatientBookingSuccess(id);
    }

    @Post('patient-booking-cancel-success')
    onPatientCancelBookingSuccess(@Query('bookingObjectId') id: string): Promise<void> {
        return this.service.onPatientCancelBookingSuccess(id);
    }

    @Post('patient-create-success')
    onPatientCreateSuccess(@Body() formData: any): Promise<void> {
        return this.service.onPatientCreateSuccess(formData);
    }

    @Post('patient-update-success')
    onPatientUpdateBookingSuccess(@Query('bookingObjectId') id: string): Promise<void> {
        return this.service.onPatientUpdateBookingSuccess(id);
    }
}
