import { ApiProperty } from '@nestjs/swagger';
import { IsMobilePhone, IsInt, IsNotEmpty, ValidateIf } from 'class-validator';
import { Transform } from 'class-transformer';
export class VerifyPhonePatientMongoDTO {

    @ApiProperty({
        description: 'Số điện thoại',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng gửi lên thông tin',
    })
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Vui lòng nhập đúng định dạng mobile phone',
    })
    readonly phone: string;

    @ApiProperty({
        description: 'Id của bệnh nhân',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    // @IsNotEmpty({
    //     message: 'Vui lòng gửi lên thông tin',
    // })
    @ValidateIf((obj) => !obj.secretPatientId)
    readonly patientId: string;

    @ApiProperty({
        description: '<PERSON><PERSON> hồ sơ',
        required: true,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng gửi lên thông tin',
    })
    readonly msbn: string;

    secretPatientId?: string
}
