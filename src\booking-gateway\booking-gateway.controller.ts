import { AppCskhInterceptor } from './../middleware/app-cskh.interceptor';
import { PermissionGuard } from './../user-permission/guard/permission.guard';
import { Permission } from './../user-permission/decorator/permission.decorator';
import {
    Controller,
    Post,
    Param,
    Query,
    HttpCode,
    HttpStatus,
    Get,
    HttpException,
    Headers,
    Req,
    UseGuards,
    Body,
    Request,
    Delete,
    Patch,
    UseInterceptors,
    HttpService,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiHeader, ApiBody, ApiQuery } from '@nestjs/swagger';
import { BookingGatewayService } from './booking-gateway.service';
import { BookingSlotFormDTO } from './dto/booking-slot-form.dto';
import { OutBookingDTO } from './dto/out-booking.dto';
import { OutBadRequestBookingDTO } from './dto/out-bad-request-booking.dto';
import { ConfirmBookingDTO } from './dto/confirm-booking.dto';
import { AuthGuard } from '@nestjs/passport';
import { UpdateStatusDTO } from './dto/update-status.dto';
import * as moment from 'moment';
import { PaymentFeeFormDTO } from './dto/payment-fee-form.dto';
import { ListenBookingChangedDTO } from './dto/listen-booking-changed.dto';
import { ListenBookingSTTChangedDTO } from './dto/listen-booking-stt-changed.dto';
import { UpdateBookingTelemedStatusDTO } from './dto/update-booking-telemed-status.dto';
import { ListenReExamChangedDTO } from './dto/listen-reexam-changed.dto';
import { CreateDepositGatewayDTO } from './dto/create-deposit-gateway.dto';
import { SearchBookingCSDTO } from './dto/search-booking.dto';
import { ListenSyncPatientCodeFromHisDTO } from './dto/listen-sync-patient-code-from-his.dto';
import { ViewBookingCSDTO } from './dto/view-booking.dto';
import { ListenBookingDateChangedDTO } from './dto/listen-booking-date-changed.dto';
import { GetInfoByPhoneDTO } from './dto/get-info-by-phone.dto';
import { PortalCancelBookingDTO } from './dto/portal-cancel-booking.dto';
import { CrExamResultsDTO } from './dto/cr-exam-results.dto';
import { HealthHistoriesByPatientIdDTO } from './dto/health-histories-by-patient-id.dto';
import { ChangeBookingDTO } from './dto/change-booking.dto';
import { GetInfoByPhoneThirdPartnerDTO } from './dto/get-info-by-phone-third-partner.dto';
import { HealthHistoriesByExamIdDTO } from './dto/health-histories-by-exam-id.dto';
import { SendBookingMailSMSDTO } from './dto/send-booking-mail.dto';
import { SendBookingSMSDTO } from './dto/send-booking-sms.dto';
import { BookingShareToPayDTO } from './dto/booking-share-to-pay.dto';
import { ReserveByTranstionDTO } from './dto/reserve-by-transation.dto';
import { HealthHistoriesByExamGroupIdDTO } from './dto/health-histories-by-exam-group-id.dto copy';
import { BookingMultipleDTO } from './dto/multiple-bookings.dto';
import { ViewTransactionCSDTO } from './dto/view-transaction.dto';
import { CheckTransactionDTO } from './dto/check-transaction.dto';
import { IBooking } from './intefaces/booking.inteface';
import { ChangeBookingStatusDto } from './dto/change-booking-status.dto';
import { GetBookingResultDto } from './dto/get-booking-result.dto';
import { BookingTrackingPageOptionsDto } from './dto/booking-tracking-page-options.dto';
import { CskhOfflineRegisterDto } from './dto/cskh-offline-register.dto';
import { RetryTransactionPageOptionsDto } from './dto/retry-transactions-page-options.dto';
import { BookigPageOptionsDto } from './dto/booking-page-option.dto';
import { PaymentPageOptionDto } from './dto/payment-page-option.dto';
import { PermissionEnum } from 'src/user-permission/common/permission.enum';
import { BookingResultCacheInterceptor } from 'src/middleware/booking-result-cache.interceptor';
import { CancelReservationDto } from './dto/cancel-reservation.dto';
import { BookingOptionDto } from './dto/booking-options.dto';
import { UrlConfigService } from '../config/config.url.service';
import { MedproCareTrackingDTO } from './dto/medpro-care-tracking.dto';
import { ComplainBookingDTO } from './dto/complain-booking';
import { CancelBookingDto } from './dto/cancel-booking.dto';

@Controller('booking-gateway')
@ApiTags('Booking Gateway- Quản lý Booking trên MongoDB')
export class BookingGatewayController {

    constructor(
        private readonly service: BookingGatewayService,
        private httpService: HttpService,
        private urlConfigService: UrlConfigService,
    ) { }

    @Post('test-check-booking-rules')
    async testCheckBookingRules(): Promise<any> {
        return this.service.checkBookingRules('dkdongnai', 'dkdongnai', {
            patientId: 'patientId001',
            hasInsuranceCode: true,
            insuranceChoice: 'DUNG_TUYEN',
            insuranceCode: 'TT0701',
            startTime: '2021-02-02T00:00:00.000Z',
            bookingUUID: 'vp0000111111',
        });
    }

    @Post('all-invisible-bookings')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async allInvisibleBookingByNoGroupUser(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req): Promise<any> {
        const user = req.user;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return this.service.allInvisibleBookingByNoGroupUser(userMongoId, partnerid, appid);
    }

    @Get('all-invisible-bookings-by-user')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async allInvisibleBookingByUser(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req): Promise<any> {
        const user = req.user;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return this.service.allInvisibleBookingByUser(userMongoId, partnerid, appid);
    }

    @Get('invisible')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async invisibleBooking(
        @Query('id') id: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.invisibleBooking(id, userMongoId);
    }

    @Post('hot-fix-data-numbers')
    async hotfixDataNumbers(): Promise<any> {
        return this.service.hotfixDataNumbers();
    }

    @Get('check-insurance-reserve')
    async checkInsuranceReserve(): Promise<any> {
        return this.service.checkInsuranceReserve('leloi', {
            patientId: '2e300d251b4546e8b13d26c80758e0dc',
            insuranceCode: 'CH4775104005748',
            endTime: '2020-12-22T00:30:00.000Z',
        });
    }

    @Get('check-rule')
    async checkRule(): Promise<any> {
        return this.service.checkRule();
    }

    @Get('check-retry')
    async checkRetry(): Promise<any> {
        return this.service.checkRetry();
    }

    @Get('test-update-status-v1')
    async testUpdateStatusV1(
        @Query('transactionId') transactionId: string,
        @Query('partnerid') partnerId: string,
    ): Promise<any> {
        return this.service.testUpdateStatusV1(partnerId, transactionId);
    }

    @Get('test-pop')
    async testPopulate(): Promise<any> {
        return this.service.testPopulate();
    }

    @Get('test-transform-room-section')
    async testTransformRoomAndSection(): Promise<any> {
        return this.service.transformRoomSectionV1NhiDong1('nhidong1', {
            id: 302,
            bv_id: 344,
            name: 'B15 - Khám Ngoại 2',
            price: 80000,
            nd1_service_id: 6,
            nd1_section_id: 3,
            nd1_subject_id: 16,
            description: 'Khu khám',
            bhyt: null,
            is_old: 0,
        },
            { id: 3, name: 'Khu AB' },
        );
    }

    @Get('test-mapping-payment')
    async testPaymentMapping(): Promise<any> {
        return this.service.findPaymentMethod('nhidong1', 'visa', 'VISA');
    }

    @Get('test-insert-multi')
    async testInsertMulti(): Promise<any> {
        return this.service.testInsertMulti();
    }

    @Get('test-qua-ngay-kham')
    async testQuaNgayKham(): Promise<any> {
        return this.service.testQuaNgayKham();
    }

    @Get('test-transaction-event')
    async testTransactionEvent(): Promise<any> {
        return this.service.testTransactionEvent();
    }

    @Get('test-update-fee-v2')
    async testUpdateFeeV2(): Promise<any> {
        return this.service.testUpdateFeeV2();
    }

    @Get('test-transaction')
    async testTransaction(): Promise<any> {
        return this.service.transaction('VPDev2007175E4OX4WBHSGP');
    }

    @Get('test-booking-patient')
    async testBookingPatient(): Promise<any> {
        return this.service.testBookingPatient();
    }

    @Get('test-moment')
    async testMoment(): Promise<any> {
        const time = '2020-07-21T18:29:00.000Z';
        return {
            current: moment().format('DD-MM-YYYY HH:mm:ss'),
            value: time,
            isUTC: moment(time).isUTC() ? 'UTC' : 'NON-UTC',
            momentFormat: moment(time).format('DD-MM-YYYY HH:mm:ss'),
            momentFormatLocal: moment(time).local().format('DD-MM-YYYY HH:mm:ss'),
            momentFormatUTC: moment(time).utc().format('DD-MM-YYYY HH:mm:ss'),
            // ket_qua:
        };
    }

    @Post('visible-all-bookings')
    async visibleAllBookings(): Promise<any> {
        return this.service.visibleAllBookings();
    }

    @Get('update-status-payment-fee-manual')
    async updateStatusPaymentFeeManual(@Query('code') code: string): Promise<any> {
        return this.service.updateStatusPaymentFeeManual(code);
    }

    @Get('re-payment')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async rePayment(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
        @Query('id') id: string,
        @Query('redirectUrl') redirectUrl: string,
        @Query('methodId') methodId: string,
        @Query('paymentTypeDetail') paymentTypeDetail: string,
        @Headers('locale') locale: string,
        @Query('groupId') groupId?: number,
        @Query('customerIpAddress') customerIpAddress?: string,
        @Query('browserScreenHeight') browserScreenHeight?: string,
        @Query('browserScreenWidth') browserScreenWidth?: string,
    ): Promise<any> {
        const user = req.user;
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }

        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        // let getGroupId = 1;
        // if (typeof groupId !== typeof undefined && (groupId === 3 || groupId === 4)) {
        //     getGroupId = groupId;
        // }
        const userId = objUser.userMongoId;
        return this.service.rePayment(partnerid, appId, userId, id, redirectUrl, methodId, paymentTypeDetail, null, cskhInfo, locale,
            customerIpAddress, browserScreenHeight, browserScreenWidth
        );
    }

    @Get('re-payment-share-to-pay')
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    async rePaymentCSKH(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('locale') locale: string,
        // @Headers('cskhtoken') cskhToken: string,
        // @Req() req,
        @Query('id') id: string,
        @Query('redirectUrl') redirectUrl: string,
        @Query('methodId') methodId: string,
        @Query('paymentTypeDetail') paymentTypeDetail: string,
        @Headers('platform') platform?: string,
        // @Query('groupId') groupId?: number,
    ): Promise<any> {
        // const user = req.user;
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }

        // const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        // const objUser: any = {
        //     // ...user,
        // };

        // if (cskhInfo && cskhInfo.userIdPatient) {
        //     objUser.userMongoId = cskhInfo.userIdPatient;
        // }
        const getGroupId = 1;
        return this.service.rePaymentShareToPay(partnerid, appId, id, redirectUrl, methodId, paymentTypeDetail, getGroupId, platform, null, locale);
    }

    @Post('change-booking')
    async changeBooking(
        @Body() formData: ChangeBookingDTO,
    ): Promise<any> {
        return this.service.changeBooking(formData);
    }

    @Get('test-thoi-gian')
    async test(): Promise<any> {
        const currentDate = moment('2020-07-05').subtract(1, 'day'); // '2020-05-14
        const mark = currentDate.format('YYYY-MM-DD');
        const noBookingAfterTime = '16:00:00';
        const timeAfter = moment(mark, 'YYYY-MM-DD').format('YYYY-MM-DD');
        if (moment('2020-07-05 15:30:00', 'YYYY-MM-DD HH:mm:ss').isAfter(timeAfter + ' ' + noBookingAfterTime)) {
            throw new HttpException(
                'Bạn chỉ có thể HỦY phiếu khám bệnh trước thời điểm 16h00 của ngày liền trước ngày khám.',
                HttpStatus.BAD_REQUEST);
        }
        return true;
    }

    @Post('reserve')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @UseInterceptors(AppCskhInterceptor)
    @ApiOperation({
        summary: 'Reserve Booking ',
        description: 'Reserve Booking',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async reserveBooking(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Headers('locale') locale: string,
        @Headers('authorization') authorization: string,
        @Headers('platform') platform: string,
        @Headers('version') version: string,
        @Headers('devicemodel') devicemodel: string,
        @Req() req,
        @Body() bookingData: BookingSlotFormDTO,
    ): Promise<any> {
        const user = req.user;
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const userId = objUser.userMongoId;
        return this.service.reserveBooking(req, partnerid, appId, userId, bookingData, null, cskhInfo, authorization, locale, platform, version, devicemodel);
    }

    @Post('reserve-by-transation')
    @UseInterceptors(AppCskhInterceptor)
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Reserve Booking  Noauth',
        description: 'Reserve Booking Noauth',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    async reserveByTransation(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Body() bookingData: ReserveByTranstionDTO): Promise<any> {
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }
        return this.service.reserveFromTransaction(bookingData);
    }

    @Post('reserve-multiple')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @UseInterceptors(AppCskhInterceptor)
    @ApiOperation({
        summary: 'Reserve Booking ',
        description: 'Reserve Booking',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async reserveMultipleBooking(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('cskhtoken') cskhToken: string,
        @Headers('locale') locale: string,
        @Headers('authorization') authorization: string,
        @Headers('platform') platform: string,
        @Headers('version') version: string,
        @Req() req,
        @Body() bookingData: BookingMultipleDTO): Promise<any> {
        const user = req.user;
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const userId = objUser.userMongoId;
        return this.service.reserveMultipleBooking(req, partnerid, appId, userId, bookingData, null, cskhInfo, authorization, locale, platform, version);
    }

    @Post('payment-fee')
    @ApiOperation({
        description: 'Dùng cho thanh toán viện phí của bệnh viện chợ rẫy',
        summary: 'Dùng cho thanh toán viện phí của bệnh viện chợ rẫy',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async paymentFee(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string, @Req() req, @Body() paymentFeeForm: PaymentFeeFormDTO): Promise<any> {
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }
        const userId = req.user.userMongoId;
        return this.service.paymentFee(partnerid, appId, userId, paymentFeeForm);
    }

    @Post('create-deposit')
    @ApiOperation({
        description: 'Dùng cho thanh toán tiền qua cổng. sau đó nạp tiền vào trong thẻ',
        summary: 'Dùng cho thanh toán tiền qua cổng. sau đó nạp tiền vào trong thẻ',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async createDeposit(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string, @Req() req,
        @Body() formData: CreateDepositGatewayDTO): Promise<any> {
        if (!partnerid) {
            throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        }
        if (!appId) {
            appId = partnerid;
        }
        const userId = req.user.userMongoId;
        return this.service.createDeposit(partnerid, appId, userId, formData);
    }

    /* nhận thông tin thay đổi invoiceId, invoiceCode trong booking */
    @Post('listen-booking-changed')
    async listenBookingChanged(@Body() formData: ListenBookingChangedDTO): Promise<any> {
        return this.service.listenBookingChanged(formData);
    }

    /* nhận thông tin thay đổi Số thứ tự, giờ khám dự kiến trong booking */
    @Post('listen-booking-stt-changed')
    async listenBookingSTTChanged(@Body() formData: ListenBookingSTTChangedDTO): Promise<any> {
        return this.service.listenBookingSTTChanged(formData);
    }

    /* nhận thông tin thay đổi Số thứ tự, giờ khám dự kiến trong booking */
    // @Post('listen-booking-date-changed')
    // async listenBookingDateChanged(@Body() formData: ListenBookingDateChangedDTO): Promise<any> {
    //     return this.service.listenBookingDateChanged(formData);
    // }

    @Post('listen-patient-code-his')
    /* nhận thông tin khi đồng bộ msbn */
    async listenSyncPatientCodeFromHIS(
        @Body() formData: ListenSyncPatientCodeFromHisDTO,
    ): Promise<any> {
        return this.service.listenSyncPatientCodeFromHIS(formData);
    }

    @Post('listen-re-exam-changed')
    async listenReExamChanged(@Body() formData: ListenReExamChangedDTO): Promise<any> {
        return this.service.listenReExamChanged(formData);
    }

    /* Lịch sử thanh toán viện phí */
    @Post('payment-fee-tracking')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async paymentFeeTracking(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string, @Req() req,
    ): Promise<any> {
        const userId = req.user.userMongoId;
        return this.service.paymentFeeTracking(partnerid, appId, userId);
    }

    /* cập nhật trạng thái */
    @Post('update-status')
    async updateStatus(@Body() updateStatus: UpdateStatusDTO): Promise<any> {
        return this.service.updatePaymentStatus(updateStatus.transactionId, updateStatus.status, updateStatus.message);
    }

    /* cập nhật thông tin newBill từ FeeHub */
    @Post('billPaid')
    async processBillPaid(@Body() newBill: any): Promise<any> {
        return this.service.processBillPaid(newBill);
    }

    @Post('testBillPaid')
    async processTestBillPaid(@Body() newBill: any): Promise<any> {
        return this.service.processTestBillPaid(newBill);
    }

    /* cập nhật trạng thái booking telemed status */
    @Post('update-booking-telemed-status')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async updateBookingTelemedStatus(@Body() updateStatus: UpdateBookingTelemedStatusDTO, @Req() req): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateBookingTelemedStatus(userMongoId, updateStatus);
    }

    // @Get('get-payment-info')
    // async getPaymentGatewayInfo(
    //     @Headers('partnerid') partnerid: string,
    //     @Headers('appid') appId: string,
    //     @Query('transactionId') transactionId: string,
    //     @Headers('locale') locale: string,
    //     @Headers('platform') platform?: string,
    //     @Query('secretBooking') secretBooking?: string,
    //     ): Promise<any> {
    //     return this.service.getPaymentGatewayInfo(appId, partnerid, transactionId, secretBooking, platform, locale);
    // }

    @Get('get-payment-info')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getPaymentGatewayInfo(
        @Query() query: BookingOptionDto,
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('locale') locale: string,
        @Headers('platform') platform: string,
        @Req() req
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingInfoByOption(userMongoId, appId, partnerid, query, platform, locale);
    }

    // /* Lấy lại thông tin lúc thanh toán  */
    // @Get('get-payment-info-bk')
    // // @UseGuards(AuthGuard('user-jwt'))
    // async getPaymentGatewayInfo_BK(
    //     @Headers('partnerid') partnerid: string,
    //     @Headers('appid') appId: string,
    //     @Query('transactionId') transactionId: string,
    //     @Headers('cskhtoken') cskhToken: string,
    //     @Req() req,
    //     @Headers('platform') platform?: string,
    //     ): Promise<any> {
    //     if (!appId) {
    //         appId = partnerid;
    //     }

    //     if (!partnerid) {
    //         partnerid = appId;
    //     }

    //     const user = req.user;

    //     const cskhInfo = await this.service.verifyCskhToken(cskhToken);

    //     const objUser = {
    //         ...user,
    //     };

    //     const { userMongoId } = objUser;
    //     return this.service.getPaymentGatewayInfo(appId, partnerid, transactionId, userMongoId, cskhInfo, platform);
    // }

    @Post('check-transaction')
    async checkTransaction(
        @Body() formData: CheckTransactionDTO,
    ): Promise<any> {
        return this.service.transaction(formData.transactionId, 0);
    }

    @Get('manual-remind-booking')
    async manualRemindBooking(
        @Query('transactionId') transactionId: string,
        @Query('minutes') minutes: number,
    ): Promise<any> {
        return this.service.manualRemindBooking(transactionId, minutes);
    }

    @Post('remind-before-30-minutes')
    async remindBefore30Minutes(): Promise<any> {
        return this.service.remindBefore30Minutes();
    }

    @Post('remind-expired-booking')
    async remindExpiredBookings(
        @Query('partnerId') partnerId?: string,
        @Query('serviceId') serviceId?: string,
        @Query('fromDate') fromDate?: Date,
        @Query('toDate') toDate?: Date,
    ): Promise<any> {
        return this.service.remindExpiredBookings(partnerId, serviceId, fromDate, toDate);
    }

    @Post('booking-share-to-pay')
    async bookingShareToPay(
        @Body() formData: BookingShareToPayDTO,
    ): Promise<any> {
        return this.service.getBookingShareToPay(formData);
    }

    @Post('booking-share-to-pay-umc')
    async bookingShareToPayUmc(
        @Body() formData: BookingShareToPayDTO,
        @Headers('appid') appId?: string,
    ): Promise<any> {
        return this.service.getBookingShareToPayUMC(formData, appId);
    }

    /* Lấy lại thông tin  */
    // @Get('get-booking-by-transaction-code')
    // async getBookingWithTransaction(
    //     @Headers('partnerid') partnerid: string,
    //     @Headers('appid') appId: string,
    //     @Query('transactionId') transactionId: string,
    //     @Headers('locale') locale: string,
    //     @Headers('platform') platform?: string,
    //     @Query('secretBooking') secretBooking?: string,
    //     ): Promise<any> {
    //     return this.service.getBookingInfoAfterTransaction(appId, partnerid, transactionId, secretBooking, platform, locale);
    // }

    @Get('get-booking-by-transaction-code')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getBookingWithTransaction(
        @Query() option: BookingOptionDto,
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appId: string,
        @Headers('locale') locale: string,
        @Req() req,
        @Headers('platform') platform?: string,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingInfoByOption(userMongoId, appId, partnerid, option, platform, locale);
    }

    /* Lấy lại thông tin  */
    // @Get('get-booking-by-transaction-code')
    // // @UseGuards(AuthGuard('user-jwt'))
    // async getBookingWithTransaction(
    //     @Headers('partnerid') partnerid: string,
    //     @Headers('appid') appId: string,
    //     @Query('transactionId') transactionId: string,
    //     @Headers('cskhtoken') cskhToken: string,
    //     @Req() req,
    //     @Headers('platform') platform?: string,
    //     ): Promise<any> {

    //     if (!appId) {
    //         appId = partnerid;
    //     }

    //     if (!partnerid) {
    //         partnerid = appId;
    //     }

    //     const user = req.user;
    //     const cskhInfo = await this.service.verifyCskhToken(cskhToken);

    //     const objUser = {
    //         ...user,
    //     };

    //     const { userMongoId } = objUser;
    //     return this.service.getBookingInfoAfterTransaction(appId, partnerid, transactionId, userMongoId, cskhInfo, platform);
    // }

    // @Get('test-payment')
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    // async testPayment(@Req() req, @Headers('partnerid') partnerid: string): Promise<any> {
    //     const userId = req.user.userMongoId;
    //     return this.service.testPayment(userId, partnerid);
    // }

    @Get('cancel-reservation')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Cancel Reservation',
        description: 'Cancel Reservation',
    })
    @HttpCode(HttpStatus.OK)
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async cancelReservation(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Query('id') id: string,
        @Req() req,
        @Headers('platform') platform?: string,
        @Headers('locale') locale: string = 'vi',
        ): Promise<any> {

        if (!!partnerid === false) {
            throw new HttpException('Vui lòng gửi partnerId lên.', HttpStatus.BAD_REQUEST);
        }

        const user = req.user;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return this.service.cancelReservation2(partnerid, appid, userMongoId, id, req.ipInfo, cskhInfo, platform, locale);
    }

    @Post('cancel-reservation')
    @ApiBearerAuth()
    @Permission(PermissionEnum.CANCEL_BOOKING)
    @UseGuards(AuthGuard('user-jwt'), PermissionGuard)
    @ApiOperation({
        summary: 'Cancel Reservation',
        description: 'Cancel Reservation',
    })
    @HttpCode(HttpStatus.OK)
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async cancelReservationV2(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Query('id') id: string,
        @Req() req,
        @Body() formData: CancelReservationDto,
        @Headers('platform') platform?: string,
        @Headers('locale') locale: string = 'vi',
        ): Promise<any> {

        if (!!partnerid === false) {
            throw new HttpException('Vui lòng gửi partnerId lên.', HttpStatus.BAD_REQUEST);
        }

        const user = req.user;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return this.service.cancelReservation2(partnerid, appid, userMongoId, id, req.ipInfo, cskhInfo, platform, locale);
    }

    @Post('portal-cancel-booking')
    async portalCancelBooking(
        @Body() formData: PortalCancelBookingDTO,
        @Req() req,
    ): Promise<any> {
        return this.service.portalCancelBooking(formData, req.ipInfo);
    }

    @Post('exam-results')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Lấy thông tin bên chợ rẫy',
        description: 'Cancel Reservation',
    })
    async examResults(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('authorization') authorization: string,
        @Headers('locale') locale: string = 'vi',
        @Body() formData: CrExamResultsDTO,
        @Req() req): Promise<any> {

        const url = `${this.urlConfigService.getUrlAPI118()}/booking-gateway/exam-results`
        try {
            const { data } = await this.httpService
                .post(url, formData, {
                    headers: {
                        partnerid,
                        appid,
                        authorization,
                        locale: locale || 'vi',
                    },
                })
                .toPromise();
            return data;
        } catch (err) {
            if (err.response?.data) {
                throw new HttpException(err.response?.data, err.response?.status);
            } else {
                console.log('err proxy exam result: ', err);
                throw new HttpException('Có lỗi xảy ra, vui lòng thử lại sau', 400);
            }
        }
    }

    @Post('get-health-histories')
    @ApiBearerAuth()
    // @ApiOperation({ summary: 'Lấy thông tin user - user profile', description: 'Lấy thông tin user profile. Cung cấp ***Bearer token*** header.' })
    @UseGuards(AuthGuard('user-jwt'))
    async getHealthHistories(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: HealthHistoriesByPatientIdDTO,
        @Request() req): Promise<any> {
        return this.service.getHealthHistories(appId, partnerId, req.user.userMongoId, formData);
    }

    @Post('get-health-exam-histories')
    @ApiBearerAuth()
    // @ApiOperation({ summary: 'Lấy thông tin user - user profile', description: 'Lấy thông tin user profile. Cung cấp ***Bearer token*** header.' })
    @UseGuards(AuthGuard('user-jwt'))
    async getHealthExamHistories(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: HealthHistoriesByPatientIdDTO,
        @Request() req): Promise<any> {
        return this.service.getHealthExamHistories(appId, partnerId, req.user.userMongoId, formData);
    }

    @Get('historyExamByUser')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async historyExamByUser(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Request() req): Promise<any> {
        return this.service.historyExamByUser(appId, partnerId, req.user.userMongoId);
    }

    @Post('historyExamByPatient')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async historyExamByPatient(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: HealthHistoriesByPatientIdDTO,
        @Request() req): Promise<any> {
        return this.service.historyExamByPatient(appId, partnerId, req.user.userMongoId, formData);
    }

    @Post('historyExam')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async historyExam(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: HealthHistoriesByExamIdDTO,
        @Request() req): Promise<any> {
        return this.service.historyExam(appId, partnerId, req.user.userMongoId, formData);
    }

    @Post('historyExamGroup')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async historyExamGroup(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: HealthHistoriesByPatientIdDTO,
        @Request() req): Promise<any> {
        return this.service.historyExamGroup(appId, partnerId, req.user.userMongoId, formData);
    }

    @Post('historyExamGroupDetail')
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    async historyExamGroupDetail(
        @Headers('appid') appId: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: HealthHistoriesByExamGroupIdDTO,
    ): Promise<any> {
        return this.service.historyExamGroupDetail(appId, partnerId, formData);
    }

    // @Get('test-exam-result')
    // async testExamREsultNeku(): Promise<any> {
    //     return this.service.reGetUserId('choray', '111111', '3035171');
    // }

    @Get('get-detail-health-history/:id')
    async getDetailHealthHistory(
        // @Headers('appid') appId: string,
        // @Headers('partnerid') partnerId: string,
        @Param('id') id: string): Promise<any> {
        return this.service.getDetailHealthHistory(id);
    }

    // @Get('cancel')
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('user-jwt'))
    // @ApiOperation({
    //     summary: 'Cancel Reservation',
    //     description: 'Cancel Reservation',
    // })
    // @HttpCode(HttpStatus.OK)
    // @ApiResponse({
    //     status: HttpStatus.OK,
    //     type: OutBookingDTO,
    //     description: 'Kết quả trả về reserve booking.',
    // })
    // @HttpCode(HttpStatus.BAD_REQUEST)
    // @ApiResponse({
    //     status: HttpStatus.BAD_REQUEST,
    //     type: OutBadRequestBookingDTO,
    //     description: 'Kết quả trả về khi lỗi 400 - Bad request',
    // })
    // async cancel(@Headers('partnerid') partnerid: string, @Query('bookingId') bookingId: string, @Req() req): Promise<any> {
    //     const user = req.user;
    //     const { userMongoId } = user;
    //     return this.service.cancelReservation(partnerid, userMongoId, bookingId, req.ipInfo);
    // }

    @Get('confirm')
    @ApiOperation({
        summary: 'Confirm Booking',
        description: 'Confirm Booking',
    })
    async confirm(@Headers('partnerid') partnerid: string, @Query() confirmBookingForm: ConfirmBookingDTO): Promise<any> {
        return this.service.confirmBooking(partnerid, confirmBookingForm);
    }

    @Get('booking-status')
    @ApiOperation({
        summary: 'Lấy thông tin trạng thái của booking',
        description: 'Lấy thông tin trạng thái của booking',
    })
    async getBookingStatus(@Headers('partnerid') partnerid: string, @Query('bookingId') bookingId: string): Promise<any> {
        return this.service.getBookingStatus(partnerid, bookingId);
    }

    @Get('booking-info')
    @ApiOperation({
        summary: 'Lấy thông tin của booking',
        description: 'Lấy thông tin của booking',
    })
    async getBookingInfo(@Headers('partnerid') partnerid: string, @Query('bookingId') bookingId: string): Promise<any> {
        return this.service.getBookingDetailInfo(partnerid, bookingId);
    }

    @Get('his-booking-status')
    @ApiOperation({
        summary: 'Lấy thông tin trạng thái của booking từ HIS',
        description: 'Lấy thông tin trạng thái của booking từ HIS',
    })
    async getBookingHisStatus(@Query('bookingId') bookingId: string): Promise<any> {
        return this.service.getBookingHisStatus(bookingId);
    }

    @Get('his-booking-info')
    @ApiOperation({
        summary: 'Lấy thông tin của booking từ HIS',
        description: 'Lấy thông tin của booking từ HIS',
    })
    async getHisBookingDetailInfo(@Query('bookingId') bookingId: string): Promise<any> {
        return this.service.getHisBookingDetailInfo(bookingId);
    }

    @Post('all-bookings')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async allBookingByNoGroupUser(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Headers('locale') locale: string,
        @Req() req): Promise<any> {
        const user = req.user;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return this.service.allBookingByNoGroupUser(userMongoId, partnerid, appid, locale);
    }

    @Get('all-bookings-by-user')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async allBookingByUser(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Headers('cskhtoken') cskhToken: string,
        @Headers('locale') locale: string,
        @Req() req): Promise<any> {
        const user = req.user;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const { userMongoId } = objUser;
        return this.service.allBookingByUser(userMongoId, partnerid, appid, locale);
    }

    /* cskh */
    @Post('cskh/all-bookings')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async allBookingByCSKH(
        @Body() formData: SearchBookingCSDTO,
        @Headers('locale') locale: string,
        @Req() req): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.allBookingByCSKH(userMongoId, formData, locale);
    }

    @Post('cskh/booking-info')
    async getBookingInfoCSKH(
        @Body() formData: ViewBookingCSDTO,
        @Headers('locale') locale: string,
        @Headers('platform') platform?: string,
        @Headers('appid') appId?: string,
        ): Promise<any> {
        return this.service.getBookingInfoCSKH('aaaa', formData, platform, appId, locale);
    }

    @Post('cskh/transaction-info')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getTransactionInfoCSKH(
        @Body() formData: ViewTransactionCSDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getTransactionInfoCSKH(userMongoId, formData);
    }

    @Post('cskh/get-info-by-phone')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getInfoByPhone(
        @Body() formData: GetInfoByPhoneDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getInfoByPhone(formData, userMongoId);
    }

    @Post('cskh/get-info-by-phone-v2')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getInfoByPhoneV2(
        @Body() formData: GetInfoByPhoneDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getInfoByPhoneV2(formData, userMongoId);
    }

    @Post('third-partner/get-info-by-phone')
    async thirdPartnerGetInfoByPhone(
        @Body() formData: GetInfoByPhoneThirdPartnerDTO,
    ): Promise<any> {
        return this.service.thirdPartnerGetInfoByPhone(formData);
    }

    @Post('send-mail-sms')
    async sendMailOrSMS(
        @Body() formData: SendBookingMailSMSDTO,
    ): Promise<any> {
        return this.service.sendMailOrSMS(formData);
    }

    @Post('send-sms')
    async sendSMS(
        @Body() formData: SendBookingSMSDTO,
    ): Promise<any> {
        return this.service.sendSMS(formData);
    }

    @Get('/list-booking-error')
    @ApiOperation({
        summary: 'Lấy danh sách booking không có appid và partnerid',
    })
    async getBookingHaveNotPartnerIdAndAppId(): Promise<IBooking[]> {
       return this.service.getBookingHaveNotPartnerIdAndAppId();
    }

    @Delete('/delete-booking-null-appid-partnerid/:id')
    @HttpCode(204)
    @ApiOperation({
        summary: 'Xóa booking không có appid và partnerid',
    })
    @ApiParam({
        name : 'id',
        type: String,
        required: true,
    })
    async deleteBookingHaveNotPartnerIdAndAppId(@Param('id') id: string): Promise<void> {
       return this.service.deleteBookingHaveNotPartnerIdAndAppId(id);
    }

    @Post('get-bookings-and-results')
    @UseInterceptors(BookingResultCacheInterceptor)
    async getBookingsAndResults(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerId: string,
        @Body() formData: GetBookingResultDto,
    ): Promise<any> {
        return this.service.getBookingsAndResults(formData, appid, partnerId);
    }

    @Get('test-discount-booking-as-partner')
    async discountBookingAsPartnerTest(
        @Query('bookingCode') bookingCode: string,
    ): Promise<void> {
        return this.service.discountBookingAsPartnerTest(bookingCode);
    }

    // @Post('test-bill-paid')
    // async testBillPaid(@Body() formData: any) {
    //     return this.service.testBillPaid(formData)
    // }

    @Get('retry-sync-v1')
    async retrySyncV1(@Query('transactionId') transactionId: string, @Query('bookingCode') bookingCode: string): Promise<any> {
        return this.service.retrySyncV1(transactionId, bookingCode);
    }

    @Get('retry-sync-v1-multi')
    async retrySyncV1Multi(@Query('transactionId') transactionId: string): Promise<any> {
        return this.service.retrySyncV1Multi(transactionId);
    }

    @Patch('change-booking-status')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async changeBookingStatus(@Body() formData: ChangeBookingStatusDto, @Req() req): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.changeBookingStatus(formData, userMongoId);
    }

    @Get('payment-fee-history')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getPaymentFeeHistoryUmc(
        @Req() req,
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string,
    ) {
        return this.service.getPaymentFeeHistoryUmc(req.user.userMongoId, partnerid, appid);
    }

    @Get('get-payment')
    async getPayment(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string,
        @Query('transactionId') transactionId: string,
    ): Promise<any> {
        if (!partnerid) {
            partnerid = appid;
        }
        return this.service.getPayment(partnerid, transactionId);
    }

    @Get('decode-short-link')
    async decodeShortLink(@Query('code') code: string): Promise<any> {
        return this.service.decodeShortLink(code);
    }

    @Post('cskh/patients-by-phone')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getPatientsByPhoneCskh(
        @Body() formData: GetInfoByPhoneDTO,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getPatientsByPhoneCskh(formData, userMongoId);
    }

    @Get('cskh/bookings-by-patient')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async getBookingsByPatientCskh(
        @Headers('cskhtoken') cskhToken: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;

        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }
        const { userMongoId } = objUser;

        return this.service.getBookingsByPatientCskh(cskhInfo.patientId, userMongoId);
    }

    @Get('test-sms')
    async testSmsService(@Query('bookingCode') bookingCode: string): Promise<string> {
        return this.service.testSmsService(bookingCode);
    }

    @Post('update-booking')
    @Permission(PermissionEnum.UPDATE_BOOKING)
    @UseGuards(AuthGuard('user-jwt'), PermissionGuard)
    @ApiBearerAuth()
    @ApiBody({
        type: ListenBookingDateChangedDTO,
    })
    async updateBooking(
        @Headers('partnerid') partnerid: string,
        @Body() formData: ListenBookingDateChangedDTO,
        @Req() req): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.updateBooking(userMongoId, partnerid, formData);
    }

    @Patch('change-sharepayment-to-repayment')
    @ApiBody({
        type: ChangeBookingStatusDto,
    })
    async changeBookingShareToRePayment(@Body() formData: ChangeBookingStatusDto): Promise<any> {
        return this.service.changeBookingShareToRePayment(formData);
    }

    @Get('booking-update-history')
    @UseGuards(AuthGuard('user-jwt'))
    @ApiBearerAuth()
    async getBookingTrackings(@Query() params: BookingTrackingPageOptionsDto, @Req() req): Promise<any> {
        const user = req.user;
        const { userMongoId } = user;
        return this.service.getBookingTrackings(params, userMongoId);
    }

    @Post('register-cskh-offline')
    @HttpCode(HttpStatus.NO_CONTENT)
    @ApiBody({
        type: CskhOfflineRegisterDto,
    })
    async registerCSoffline(@Body() formData: CskhOfflineRegisterDto): Promise<any> {
        return this.service.registerCSoffline(formData);
    }

    @Get('test-check-constraint')
    async testCheckConstraintPatient(): Promise<void> {
        return this.service.testCheckConstraintPatient();
    }

    @Get('sync/section')
    @ApiQuery({
        name: 'bookingId',
        description: 'User mongo id',
        required: false,
    })
    syncSectionForUMC(@Query('bookingId') bookingId?: string): Promise<any> {
        return this.service.fixBookingUmcRenameSectionName(bookingId);
    }

    @Get('retry-transactions')
    async getRetryTransactions(@Query() pageOptions: RetryTransactionPageOptionsDto): Promise<any> {
        return this.service.getRetryTransactions(pageOptions);
    }

    @Get('page')
    async pageBookings(@Query() pageOptions: BookigPageOptionsDto): Promise<any> {
        return this.service.pageBookings(pageOptions);
    }

    @Get('payment/page')
    async pagePayments(@Query() pageOptions: PaymentPageOptionDto): Promise<any> {
        return this.service.pagePayments(pageOptions);
    }

    @Get('booking-detail')
    async getBookingDetailById(@Query('id') id: string): Promise<any> {
        return this.service.getBookingDetailById(id);
    }

    @Get('get-invalid-bookings')
    getInvalidBookingFromV1(@Query('partnerId') partnerId: string): Promise<any> {
        return this.service.getInvalidBookingV1(partnerId);
    }

    @Patch('remind/booking/specify')
    remindTelemdBooking(@Body() payload: IBooking, @Query('minutes') minute: number): Promise<any> {
        if (minute === 60) {
            return this.service.handleRemindTelelemedBooking(payload);
        }
        return this.service.handleRemindBooking(payload);
    }

    @Get('cancellation-reasons')
    getAllCancellationReasons(): Promise<any> {
        return this.service.getAllCancellationReasons();
    }

    @Get('payment-status')
    getPayments(
        @Headers('partnerid') partnerid: string,
        @Headers('locale') locale: string,
    ): Promise<any> {
        return this.service.getPayments(locale, partnerid);
    }

    // @Get('testEventDalieu')
    // testEventDalieu(@Query('bookingCode') bookingCode: string): Promise<any> {
    //     console.log("🚀 ~ file: booking-gateway.controller.ts ~ line 1384 ~ BookingGatewayController ~ testEventDalieu ~ bookingCode", bookingCode)
    //     return this.service.testEventDalieu(bookingCode);
    // }

    // @Get('testGetMessageInfoSms')
    // testGetMessageInfoSms(): Promise<any> {
    //     return this.service.testGetMessageInfoSms()
    // }

    // @Post('testBookingGuide')
    // testBookingGuide(@Body() formdata: any): Promise<any> {
    //     return this.service.testBookingGuide(formdata?.bookingIds)
    // }

    @Post('medpro-care/tracking')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async trackingMedproCare(@Req() req, @Body() formData: MedproCareTrackingDTO, @Headers('partnerid') partnerId: string): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return await this.service.trackingMedproCare(formData, userId, partnerId);
    }

    @Post('check-sync-booking')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async checkSyncBooking(@Req() req, @Body() formData: { bookingId: string }): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return await this.service.checkSyncBooking(formData, userId);
    }
    @Post('complain-booking')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async complainBooking(@Req() req, @Body() formData: ComplainBookingDTO): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return await this.service.complainBooking(formData, userId);
    }

    @Post('medpro-care/reserve')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Reserve Booking ',
        description: 'Reserve Booking',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async reserveMedproCare(
        // @Headers('partnerid') partnerid: string,
        @Req() req,
        @Body() medproCareWithBooking: any,
        @Headers('appid') appId: string,
        @Headers('platform') platform: string,
        @Headers('version') version: string,
        @Headers('authorization') authorization: string,
        @Headers('cskhtoken') cskhToken?: string): Promise<any> {
        const user = req.user;
        console.log('user: ', user);
        
        // if (!partnerid) {
        //     throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        // }
        // if (!appId) {
        //     appId = partnerid;
        // }
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const userId = objUser.userMongoId;
        return this.service.reserveMedproCare(appId, medproCareWithBooking, platform, version, authorization, cskhToken);
    }

    @Post('medpro-care/addon')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getMedproCareAddon(@Req() req, @Headers('authorization') authorization: string, @Body() body: { bookingId: string }): Promise<any> {
        const user = req.user;
        const userId = user.userMongoId;
        return await this.service.getMedproCareAddon(body, authorization);
    }

    @Post('medpro-care/addon/reserve')
    @ApiBearerAuth()
    @UseInterceptors(AppCskhInterceptor)
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Reserve Booking ',
        description: 'Reserve Booking',
    })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingDTO,
        description: 'Kết quả trả về reserve booking.',
    })
    @HttpCode(HttpStatus.BAD_REQUEST)
    @ApiResponse({
        status: HttpStatus.BAD_REQUEST,
        type: OutBadRequestBookingDTO,
        description: 'Kết quả trả về khi lỗi 400 - Bad request',
    })
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async reserveMedproCareAddon(
        @Req() req,
        @Body() medproCareAddon: any,
        @Headers('appid') appId: string,
        @Headers('platform') platform: string,
        @Headers('version') version: string,
        @Headers('authorization') authorization: string,
        @Headers('cskhtoken') cskhToken?: string): Promise<any> {
        const user = req.user;
        // if (!partnerid) {
        //     throw new HttpException('Thông tin partnerId chưa chính xác.', HttpStatus.BAD_REQUEST);
        // }
        // if (!appId) {
        //     appId = partnerid;
        // }
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        const objUser = {
            ...user,
        };

        if (cskhInfo && cskhInfo.userIdPatient) {
            objUser.userMongoId = cskhInfo.userIdPatient;
        }

        const userId = objUser.userMongoId;
        return this.service.reserveMedproCareAddon(appId, medproCareAddon, platform, version, authorization, cskhToken);
    }

    @Post('medpro-care/information')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getMedproCareInformation(@Req() req, @Headers('authorization') authorization: string, @Body() body: { transactionId: string }): Promise<any> {
        return await this.service.getMedproCareInformation(body, authorization);
    }

    @Post('share-booking')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async shareBooking(
        @Req() req, 
        @Headers('authorization') authorization: string,
        @Body() body: { bookingId: string },
        @Headers('appid') appId: string,
        @Headers('version') version: string,
        @Headers('platform') platform: string
    ): Promise<any> {
        return this.service.shareBooking(body, authorization);
    }

    @Post('cancel-booking-by-id/:bookingId')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({
        summary: 'Cancel booking by bookingId',
        description: 'Cancel a booking by its bookingId',
    })
    @ApiHeader({
        name: 'cskhtoken',
        required: false,
    })
    async cancelBookingById(
        @Headers('appid') appid: string,
        @Headers('partnerid') partnerid: string,
        @Headers('platform') platform: string,
        @Headers('version') version: string,
        @Headers('authorization') authorization: string,
        @Headers('cskhtoken') cskhToken: string,
        @Body() dto: CancelBookingDto,
        @Param('bookingId') bookingId: string,
        @Req() req,
    ): Promise<any> {
        const user = req.user;
        const userMongoId = user?.userMongoId;
        const ipInfo = req.ipInfo;
        const cskhInfo = await this.service.verifyCskhToken(cskhToken);

        if (!appid || !partnerid || !bookingId) {
            throw new HttpException('appid, partnerid, and bookingId are required', HttpStatus.BAD_REQUEST);
        }

        return this.service.cancelBookingById(
            appid,
            bookingId,
            dto,
            partnerid,
            userMongoId,
            ipInfo,
            cskhInfo,
            platform,
            version,
            authorization,
            cskhToken,
        );
    }

    @Get('cancellation-reasons/list')
    @HttpCode(HttpStatus.OK)
    async listCancelledBookings(): Promise<any[]> {
        return await this.service.getCancellationReasons();
    }

    @Get('account-type')
    @HttpCode(HttpStatus.OK)
    async listAccountType(): Promise<any> {
        return await this.service.getAccountType();
    }
}
