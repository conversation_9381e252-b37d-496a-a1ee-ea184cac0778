import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { SmsDto } from './sms.dto';

export class SmsMessageServiceDto extends SmsDto {
    @ApiProperty({ description: 'partner', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập partner!' })
    readonly partner?: string;

    @ApiProperty({ description: 'accessKey', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập accessKey!' })
    readonly accessKey?: string;

    @ApiProperty({ description: 'clientId', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập clientId!' })
    readonly clientId: string;
}
