import * as mongoose from 'mongoose';
import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import { PAYMENT_COLLECTION_NAME, HOSPITAL_FEE_COLLECTION_NAME, BOOKING_ORDER_COLLECTION_NAME, BOOKING_COLLECTION_NAME } from './constants';
import { PATIENT_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { PAYMENT_HOSPITAL_FEE_COLLECTION_NAME } from 'src/user-account/schemas/constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const PaymentSchema = new Schema({
    id: { type: String },
    type: { type: Number, default: 1 }, // 1=bookingFee , 2=hospitalFee, 3=vaccine, 4=payment-hospital-fee (card)
    bookingId: { type: String },
    booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
    date: { type: Date, required: true },
    patientId: { type: String },
    patient: { type: Schema.Types.ObjectId, ref: PATIENT_COLLECTION_NAME },
    // paymentId: { type: String, required: true },
    amount: { type: Number, required: true },
    subTotal: { type: Number, required: true },
    totalFee: { type: Number, default: 0 },
    medproFee: { type: Number, default: 0 },
    transferFee: { type: Number, default: 0 },
    status: { type: Number, required: true }, // 1: tạo đơn hàng 2: thanh toán thành công 3: thất bại 4: Expired
    paymentMethod: { type: String, required: true },
    paymentMethodDetail: { type: String },
    gatewayId: { type: String },
    transactionId: { type: String, required: true },
    gatewayTransactionId : { type: String },
    bookingCode: { type: String },
    feeCode: { type: String },
    orderId: { type: String },
    partnerId: { type: String },
    partner: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
    appId: { type: String, default: '' },  // lưu cái này dành cho nạp tiền
    paymentTime: { type: String },
    message: { type: String },
    userId: { type: Schema.Types.ObjectId }, // dành cho thanh toán viện phí
    bankInfo: {
        name: { type: String },
        accountHolder: { type: String },
        accountNumber: { type: String },
        bankBranch: { type: String },
    },
    hospitalFee: { type: Schema.Types.ObjectId, ref: HOSPITAL_FEE_COLLECTION_NAME },
    paymentHospitalFeeId: { type: String },
    paymentHospitalFee: { type: Schema.Types.ObjectId, ref: PAYMENT_HOSPITAL_FEE_COLLECTION_NAME },
    /* thêm để kiểm tra trường hợp nhiều giao dịch thành công */
    reference: { type: Number, default: 1 },
    extraInfo: {
        transactionIdV1: { type: String, default: '' },
        methodIdV1: { type: Number, default: 0 },
        booking: { type: Object, default: {} },
    },
    email: { type: String, default: '' }, // lưu cái này dành cho email nhận thông báo nạp tiền
    platform: { type: String, default: '' }, // lưu cái này dành cho nạp tiền
    noPayment: { type: Boolean, default: false },
    sharePayment: { type: Boolean, default: false },
    plusOrMinus: { type: Boolean, default: false }, // lưu cái này dành cho nạp tiền
    historyType: { type: Number, default: 0 }, // lưu cái này danh cho card
    transactionContent: { type: String }, // lưu cái này dành cho giao dịch thẻ
    bookingOrder: { type: Schema.Types.ObjectId, ref: BOOKING_ORDER_COLLECTION_NAME },
    bookingOrderId: { type: String },
    phoneCall1900:  { type: String },
    syncAt: { type: Date },
    chargeFeeInfo: { type: Schema.Types.Mixed },
    groupId: { type: Number },
    refundStatus: { type: Number },
    medproCareFee: { type: Number, default: 0 },
    discountInfo: {
        service: { type: Schema.Types.Mixed },
        discountPrice: { type: Number },
    },
}, {
    collection: PAYMENT_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
