import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { BOOKING_COLLECTION_NAME, VISA_USER_BOOKINGS_COLLECTION_NAME } from './constants';
import { USER_COLLECTION_NAME } from '../../user/schemas/constants';

const Schema = mongoose.Schema;

export const VisaUserBookingsSchema = new Schema({
    user: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    userId: { type: String },
    email: { type: String },
    phone: { type: String },
    fullname: { type: String },
    bookingCode: { type: String },
    bookingId: { type: String },
    booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME },
}, {
    collection: VISA_USER_BOOKINGS_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
