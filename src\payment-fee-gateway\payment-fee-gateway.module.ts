import { Module } from '@nestjs/common';
import { PaymentFeeGatewayService } from './payment-fee-gateway.service';
import { PaymentFeeGatewayController } from './payment-fee-gateway.controller';
import { SentryToken } from 'src/sentry/sentry.module';

@Module({
  imports: [SentryToken],
  providers: [PaymentFeeGatewayService],
  controllers: [PaymentFeeGatewayController],
})
export class PaymentFeeGatewayModule {}
