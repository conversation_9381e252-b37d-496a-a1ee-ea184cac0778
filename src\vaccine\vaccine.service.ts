import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { get, reduce, map, uniqBy, flatMap, first, sortBy } from 'lodash';
import * as uuid from 'uuid';
import * as moment from 'moment';
import * as slugify from 'slugify';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { IService } from 'src/service-mongo/interfaces/service.interface';
import { SERVICE_PACKAGE_COLLECTION_NAME, ORDER_COLLECTION_NAME, ORDER_ITEM_COLLECTION_NAME } from './schemas/constants';
import { IServicePackage } from './interfaces/service-package.interface';
import { CreateSinglePackageDTO } from './dto/create-single-package.dto';
import { CreateMultiplePackageDTO } from './dto/create-multiple-package.dto';
import { AddServiceIntoPackageDTO } from './dto/add-service-into-package.dto';
import { ServicePackageType } from './dto/service-package-type.dto';
import { CreateOrderVaccineDTO } from './dto/create-order.dto';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { PrefixTransactionCodeTTConfigService } from 'src/config/config.prefix.transcode.service';
import { UtilService } from 'src/config/util.service';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { IPackageItem } from './interfaces/package-item.interface';
import { UrlConfigService } from 'src/config/config.url.service';
import { PkhHttpService } from 'src/config/config.http.service';
import { Observable, async } from 'rxjs';
import { AxiosResponse } from 'axios';
import { PATIENT_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { IPatient } from 'src/patient-mongo/intefaces/patient.inteface';
import { IPatientVersion } from 'src/patient-mongo/intefaces/patient-version.inteface';
import { PatientMongoService } from 'src/patient-mongo/patient-mongo.service';
import { IOrder } from './interfaces/order.inteface';
import { IOrderItem } from './interfaces/order-item.inteface';
import { BookingGatewayService } from 'src/booking-gateway/booking-gateway.service';
import { EventService } from 'src/event/event.service';
import { OrderItemDTO } from './dto/order-item.dto';
import { ReserveVaccineDTO } from './dto/reserve-vaccine.dto';
import { OrderItemStatus } from './dto/order-item-status.dto';
import { HisGatewayService } from 'src/his-gateway/his-gateway.service';
import { RE_EXAM_COLLECTION_NAME } from 'src/re-exam/schemas/constants';
import { IReExam } from 'src/re-exam/intefaces/re-exam.inteface';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';

@Injectable()
export class VaccineService {
    constructor(
        @InjectModel(ORDER_COLLECTION_NAME) private orderModel: Model<IOrder>,
        @InjectModel(ORDER_ITEM_COLLECTION_NAME) private orderItemModel: Model<IOrderItem>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(PATIENT_COLLECTION_NAME) private patientModel: Model<IPatient>,
        @InjectModel(PATIENT_VERSION_COLLECTION_NAME) private patientVersionModel: Model<IPatientVersion>,
        @InjectModel(SERVICE_COLLECTION_NAME) private serviceModel: Model<IService>,
        @InjectModel(SERVICE_PACKAGE_COLLECTION_NAME) private servicePackageModel: Model<IServicePackage>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(RE_EXAM_COLLECTION_NAME) private reExamModel: Model<IReExam>,
        private readonly transactionConfig: PrefixTransactionCodeTTConfigService,
        private readonly utilService: UtilService,
        private readonly urlConfigService: UrlConfigService,
        private readonly httpService: PkhHttpService,
        private readonly patientMongoService: PatientMongoService,
        private readonly bookingGatewayService: BookingGatewayService,
        private readonly eventService: EventService,
        private hisGatewayService: HisGatewayService,
        private readonly eventEmitter: EventEmitter2,
    ) {}

    async getServicePackage(type: string): Promise<any> {
        const getUrl = this.urlConfigService.getInvoiceUrl();
        switch (type) {
            case ServicePackageType.SINGLE: {
                const data = await this.servicePackageModel
                    .find({ type })
                    .populate('services')
                    .exec();
                return data.map((item: IServicePackage) => {
                    const service: IService = first(item.services);
                    const imageUrl = `${getUrl}/image/service/${service.id}`;
                    const toObject = item.toObject();
                    return {
                        ...toObject,
                        imageUrl,
                    };
                });
            }
            case ServicePackageType.MULTIPLE: {
                const data = await this.servicePackageModel
                    .find({ type })
                    .populate('services')
                    .exec();
                return data.map((item: IServicePackage) => {
                    const imageUrl = `${getUrl}/image/service-package/${item.id}`;
                    const toObject = item.toObject();
                    return {
                        ...toObject,
                        imageUrl,
                    };
                });
            }
            default:
                return this.servicePackageModel
                    .find({})
                    .populate('services')
                    .exec();
        }
    }

    async getServiceInfo(id: string, partnerId: string, appId: string): Promise<any> {
        const service = await this.serviceModel.findOne({ id, partnerId }).exec();
        const getUrl = this.urlConfigService.getInvoiceUrl();
        const imageUrl = `${getUrl}/image/service/${service.id}`;
        const toObject = service.toObject();
        return {
            ...toObject,
            imageUrl,
        };
    }

    async createSinglePackage(appId: string, partnerId: string, formData: CreateSinglePackageDTO): Promise<any> {
        const idService = uuid.v4().replace(/-/g, '');
        const idPackage = uuid.v4().replace(/-/g, '');
        /* tạo thông tin bên service */
        const searchUnicode = slugify.default(formData.name, {
            replacement: ' ',
            lower: true,
            locale: 'vi',
        });
        const serviceCreate = new this.serviceModel({
            id: idService,
            name: formData.name,
            code: formData.code,
            partnerId,
            description: formData.description,
            price: formData.price,
            createTime: moment().toDate(),
            searchUnicode,
        });
        const resultService = await serviceCreate.save();

        const singlePackage = new this.servicePackageModel({
            id: idPackage,
            name: formData.name,
            code: formData.code,
            partnerId,
            description: formData.description,
            price: formData.price,
            type: ServicePackageType.SINGLE,
            services: [resultService],
        });
        return singlePackage.save();
    }

    async createMultiplePackage(appId: string, partnerId: string, formData: CreateMultiplePackageDTO): Promise<any> {
        const id = uuid.v4().replace(/-/g, '');
        const multipleService = new this.servicePackageModel({
            id,
            name: formData.name,
            partnerId,
            description: formData.description,
            type: ServicePackageType.MULTIPLE,
        });
        return multipleService.save();
    }

    async addServiceIntoPackage(appId: string, partnerId: string, formData: AddServiceIntoPackageDTO): Promise<any> {
        const servicePackage = await this.servicePackageModel
            .findOne({ partnerId, id: formData.idPackage }) // tìm id của thằng cha
            .exec();

        const childService = await this.serviceModel.findOne({ id: formData.idService }).exec();
        servicePackage.services.push(childService);
        /* tính lại giá */
        const servicePackagePrice = await (await this.servicePackageModel.findOne({ id: formData.idPackage })).populate('services').execPopulate();
        const total = reduce(
            servicePackagePrice.services,
            (sum: any, item: IService) => {
                return sum + item.price;
            },
            childService.price,
        );
        servicePackage.price = total;
        await servicePackage.save();
        /* trả về thông tin kèm gói kèm theo detail services */
        return await (await this.servicePackageModel.findOne({ id: formData.idPackage })).populate('services').execPopulate();
    }

    async info(appId: string, partnerId: string, id: string): Promise<any> {
        try {
            const getUrl = this.urlConfigService.getInvoiceUrl();
            const servicePackage = await (await this.servicePackageModel.findOne({ partnerId, id })).populate('services').execPopulate();
            switch (servicePackage.type) {
                case ServicePackageType.SINGLE: {
                    const service: IService = first(servicePackage.services);
                    const imageUrl = `${getUrl}/image/service/${service.id}`;
                    const toObject = servicePackage.toObject();
                    return {
                        ...toObject,
                        imageUrl,
                    };
                }
                case ServicePackageType.MULTIPLE: {
                    const imageUrl = `${getUrl}/image/service-package/${servicePackage.id}`;
                    const toObject = servicePackage.toObject();
                    return {
                        ...toObject,
                        imageUrl,
                    };
                }
                default:
                    return {};
            }
        } catch (error) {
            throw new HttpException('Không tìm thấy thông tin gói dịch vụ.', HttpStatus.NOT_FOUND);
        }
    }

    async reserveVaccine(appId: string, partnerId: string, userMongoId: string, formData: ReserveVaccineDTO): Promise<any> {
        /* kiểm tra xem đúng order hay ko */
        const getOrder = await this.orderModel.findOne({ id: formData.orderId }).exec();
        if (!getOrder) {
            throw new HttpException('Không tìm thấy thông tin đơn hàng.', HttpStatus.NOT_FOUND);
        }
        /* Kiểm tra xem thông tin order có thuộc user này ko */

        /* Kiểm tra orderItem */
        const getOrderItem = await this.orderItemModel.findOne({ orderId: getOrder.id, id: formData.orderItemId }).exec();
        if (getOrderItem.status === OrderItemStatus.DA_TIEM) {
            throw new HttpException('Mũi vắc-xin này đã tiêm.', HttpStatus.BAD_REQUEST);
        }
        /* Tiến hành lưu thông tin booking slot */
        const formatNgay = moment(formData.startTime)
            .add(7, 'hours')
            .format('YYMMDD');
        const bookingSlotId = `${formatNgay}_${formData.bookingSlotId}`;
        const bookingSlotFormDTO = {
            ...formData,
            bookingSlotId,
            partnerId,
            appId,
            serviceId: getOrderItem.serviceId,
            // orderItemCode: getOrderItem.orderItemCode,
        };
        // {
        //     "orderId": "6e4b27d07b0844b48d2e2749e73891fe",
        //     "orderItemId": "e76fce9b787842e68de57427bf5f91f0",
        //     "patientId": "fc6e8f7633894ca4a5413a1c30868ed8",
        //     "bookingSlotId": "02572b4d82984b8a989fe21445eabcee_1_07:00_nhidongtp",
        //     "startTimeString": "2020-10-15T00:00:00.000Z",
        //     "startTime": "2020-10-15T00:00:00.000Z",
        //     "endTime": "2020-10-15T00:30:00.000Z",
        //     "maxSlot": 10,
        //     "availableSlot": 10
        //   }
        /* Tiến hành insert booking slot */
        const getBookingSlot = await this.hisGatewayService.insertOrUpdateBookingSlotVaccine(appId, partnerId, bookingSlotFormDTO);

        const curSlot = getBookingSlot.maxSlot - getBookingSlot.availableSlot;
        const minutes = moment(getBookingSlot.endTime).diff(moment(getBookingSlot.startTime), 'minutes'); // bao nhiêu phút
        const perSlotMinute = Math.floor(minutes / getBookingSlot.maxSlot); // 1 slot trong mấy phút
        const bookingObjParams: any = {};
        // bookingObjParams.bookingId = `${getBookingSlot.bookingSlotId}_${curSlot}`;
        bookingObjParams.bookingSlotId = getBookingSlot.bookingSlotId;
        bookingObjParams.date = getBookingSlot.startTime;
        // bookingObjParams.serviceId = getBookingSlot.serviceId;

        if (getBookingSlot.availableSlot > 0) {
            const availableSlot = getBookingSlot.availableSlot - 1; // cập nhật lại bookingSlot
            const minutesAdd = curSlot * perSlotMinute;
            await this.hisGatewayService.updateOneBookingSlot(getBookingSlot.bookingSlotId, availableSlot);
            // bookingObjParams.status = BookingStatus.RESERVE;
            bookingObjParams.date = moment(getBookingSlot.startTime)
                .add(minutesAdd, 'minutes')
                .toDate();
            /* tiến hành cập nhật lại orderItem */
            const orderItemData = await this.orderItemModel
                .findOneAndUpdate(
                    { id: getOrderItem.id, status: { $ne: OrderItemStatus.DA_TIEM } },
                    {
                        date: bookingObjParams.date,
                        bookingSlotId: bookingObjParams.bookingSlotId,
                        bookingSlot: getBookingSlot._id,
                        status: 0,
                    },
                    { new: true },
                )
                .exec();
            /* tiến hành tạo event */
            await this.eventService.createEvent({
                topicId: 'order-items.reserve',
                createTime: moment().toISOString(),
                title: '',
                userId: userMongoId,
                partnerId,
                appId,
                eventData: {
                    ...orderItemData.toObject(),
                },
            });
            return orderItemData;
        } else {
            throw new HttpException('Giờ khám đã kín, vui lòng chọn giờ khác', HttpStatus.BAD_REQUEST);
        }
    }

    async createOrder(appId: string, partnerId: string, userMongoId: string, formData: CreateOrderVaccineDTO): Promise<any> {
        /* Kiểm tra xem userMongoId này còn tồn tại hay ko */
        const getUser = await this.userModel.findById({ _id: userMongoId }, { username: true }).exec();
        if (!getUser) {
            throw new HttpException('Vui lòng đăng nhập lại.', HttpStatus.UNAUTHORIZED);
        }
        /* kiểm tra thông tin partner id */
        const findPartner = await this.hospitalModel.findOne({ partnerId }).exec();
        if (!findPartner) {
            throw new HttpException('Không tìm thấy thông tin partner', HttpStatus.BAD_REQUEST);
        }
        // const partnerObj = findPartner.toObject();
        /* kiểm tra lại thông tin patient */
        const findPatients = await this.patientModel
            .find({ id: formData.patientId })
            .populate('city')
            .populate('district')
            .populate('ward')
            .limit(1)
            .exec();
        const findPatient: IPatient = first(findPatients);
        if (findPatient) {
            formData.patientId = findPatient.id;
            formData.patient = findPatient._id;
        } else {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.UNAUTHORIZED);
        }

        /* Kiểm tra lại thông tin gói */
        const packages = formData.packages;
        if (packages.length === 0) {
            throw new HttpException('Vui lòng chọn gói tiêm chủng.', HttpStatus.BAD_REQUEST);
        }
        const listPackagesMap = uniqBy(packages, 'servicePackageId'); // unique lại cái list packages
        const uniqueMap = map(listPackagesMap, 'servicePackageId');
        const countCheck = await this.servicePackageModel
            .find({
                id: {
                    $in: uniqueMap,
                },
            })
            .count();
        if (countCheck !== uniqueMap.length) {
            throw new HttpException('Vui lòng kiểm tra lại thông tin gói tiêm chủng.', HttpStatus.BAD_REQUEST);
        }

        /* Tính lại giá tiền của từng gói */
        const listFullPackagesInfo = await this.servicePackageModel
            .find({
                id: {
                    $in: uniqueMap,
                },
            })
            .populate('services')
            .exec();

        /* Tiến hành lấy thông tin phí */
        const mapServices = flatMap(listFullPackagesInfo, 'services');
        const totalPrice = reduce(
            mapServices,
            (sum: number, item: IService) => {
                return sum + item.price;
            },
            0,
        );
        const subTotal = totalPrice;
        let amount = 0;
        let totalFee = 0;
        let medproFee = 0;
        let transferFee = 0;
        let gatewayId = 'zalo';
        let chageFreeValue: any = {};
        let paymentType = '';
        let code = '';
        const bankAccountInfo: any = {};
        try {
            chageFreeValue = (await this.chargeFee(partnerId, formData.methodId, formData.paymentTypeDetail, subTotal, 1).toPromise()).data;
            // console.log(chageFreeValue);
            amount = Number(chageFreeValue.grandTotal);
            totalFee = Number(chageFreeValue.totalFee);
            medproFee = Number(chageFreeValue.medproFee);
            transferFee = totalFee - medproFee;
            gatewayId = chageFreeValue.gatewayId;
            paymentType = chageFreeValue.type;
            code = chageFreeValue.code;
            /* lấy lại thông tin bankAccount */
            if (typeof chageFreeValue.bankAccount !== typeof undefined) {
                // console.log(chageFreeValue);
                bankAccountInfo.name = typeof chageFreeValue.name !== typeof undefined ? chageFreeValue.name : '';
                if (typeof chageFreeValue.bankAccount !== typeof undefined) {
                    const { accountHolder = '', accountNumber = '', bankBranch = '' } = chageFreeValue.bankAccount;
                    bankAccountInfo.accountHolder = accountHolder;
                    bankAccountInfo.accountNumber = accountNumber;
                    bankAccountInfo.bankBranch = bankBranch;
                }
            }
        } catch (error) {
            throw new HttpException('Không tìm thấy thông tin phí.', HttpStatus.BAD_REQUEST);
        }
        /* tiến hành lấy thông tin patient version */
        const findPatientVersion = await this.patientVersionModel
            .find({ patientId: formData.patientId })
            .sort({ createdAt: 'desc' })
            .limit(1)
            .exec();
        const firstPatientVersion = first(findPatientVersion);
        if (firstPatientVersion) {
            formData.patientVersionId = firstPatientVersion.id;
            formData.patientVersion = firstPatientVersion._id;
        } else {
            /* tiến hành sao chép */
            const patientVersionInsert: IPatientVersion = await this.patientMongoService.copyPatient(findPatient);
            formData.patientVersionId = patientVersionInsert.id;
            formData.patientVersion = patientVersionInsert._id;
        }

        /* Tiến hành tạo order và orderitem */
        const rand9Char = this.utilService.randomText(12).toUpperCase();
        const transactionId = this.transactionConfig.getPrefixTransactionCodeTT(rand9Char);

        const rand6Char = this.utilService.randomText(6).toUpperCase();
        const orderCode = this.transactionConfig.getPrefixTransactionCodeTT(rand6Char, 'T');

        const idOrder = uuid.v4().replace(/-/g, '');
        const myOrderModel = new this.orderModel({
            id: idOrder,
            orderCode,
            transactionId,
            paymentStatus: 1,
            patientId: findPatient.id,
            patient: findPatient._id,
            patientVersionId: firstPatientVersion.id,
            patientVersion: firstPatientVersion._id,
            userId: userMongoId,
            partnerId,
            partner: findPartner._id,
            appId,
            platform: formData.platform,
            content: 'Thanh toán Tiêm chủng Vắc-xin',
        });
        const myOrder = await myOrderModel.save();

        const cbWebView = formData?.cbWebView || null;
        const result = await this.bookingGatewayService.submitPayment(
            partnerId,
            userMongoId,
            findPatient.toObject(),
            chageFreeValue,
            formData.redirectUrl,
            transactionId,
            formData.platform,
            `PKH - Thanh toán Tiêm chủng Vắc-xin ${transactionId}`,
            cbWebView,
        );
        const {
            data: { OrderInfo },
        } = result;
        // console.log('thông tin order', OrderInfo);
        const gatewayStatus = Number(OrderInfo.status);
        /* Cập nhật lại order */
        await this.orderModel.findByIdAndUpdate({ _id: myOrder._id }, { paymentStatus: gatewayStatus });
        /* Tiến hành tạo Order Item */
        let orderItems: OrderItemDTO[] = [];
        for (const item2 of listFullPackagesInfo) {
            const item: IServicePackage = item2;
            const services = item.services;
            for (const service2 of services) {
                const service: IService = service2;
                const idItem = uuid.v4().replace(/-/g, '');
                const rand6Char2 = this.utilService.randomText(6).toUpperCase();
                const orderItemCode = this.transactionConfig.getPrefixTransactionCodeTT(rand6Char2, 'VX');
                const orderItem: OrderItemDTO = {
                    id: idItem,
                    orderId: idOrder,
                    orderItemCode,
                    partnerId,
                    appId,
                    quantity: 1,
                    serviceId: service.id,
                    service: service._id,
                    servicePackageId: item.id,
                    servicePackage: item._id,
                    status: OrderItemStatus.CHUA_DAT_LICH,
                };
                orderItems = [...orderItems, orderItem];
            }
        }
        /* Insert many orderItem */
        await this.orderItemModel.insertMany(orderItems);
        // tạo thông tin payment
        const paymentSave = await this.bookingGatewayService.createPayment(
            idOrder,
            null,
            moment().toDate(),
            formData.patientId,
            formData.patient,
            amount,
            subTotal,
            totalFee,
            medproFee,
            transferFee,
            gatewayId,
            code,
            paymentType,
            transactionId,
            orderCode,
            OrderInfo.orderId,
            partnerId,
            gatewayStatus,
            bankAccountInfo,
            false,
            3,
            '',
            '',
            chageFreeValue,
        );
        // cập nhật lại booking status
        await this.eventService.createEvent({
            topicId: 'payments.receive',
            createTime: moment().toISOString(),
            title: '',
            userId: userMongoId,
            partnerId,
            appId,
            eventData: {
                ...paymentSave.toObject(),
            },
        });
        /* Trả thông tin về client */
        return {
            idOrder,
            ...OrderInfo,
            isGateway: formData.methodId === 'ck' ? 0 : 1,
        };
    }

    chargeFee(partnerId: string, methodId: string, detail: string, price: number, groupId: number): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/payment/v1/charge/${partnerId}/${groupId}/${methodId}/${detail}/${price}`;
        // console.log(url);
        return this.httpService.getHttpRequest(url);
    }

    async getOrder(id: string): Promise<any> {
        const getOrder = await (await this.orderModel.findOne({ id })) // : '4b651c3b87044706b326072023200a54'
            .populate('partner')
            .populate('patientVersion')
            .execPopulate();
        /* tìm lại thông tin orderItem */
        const getOrderItems = await this.orderItemModel
            .find({
                orderId: getOrder.id,
            })
            .populate('service')
            .populate('servicePackage')
            .exec();
        const mapObj = getOrderItems.map(item => item.toObject());
        const orderObj = getOrder.toObject();
        return {
            ...orderObj,
            packages: mapObj,
        };
    }

    async checkCount(): Promise<any> {
        // const checkListPackages = await this.servicePackageModel.find({
        //     id: {
        //         $in: ['c7764692bd4f427187de3b49b3815d10', '77cb8e703b4148a5acb8227fc2e6086e'],
        //     },
        // }).count();
        // return checkListPackages;

        const listFullPackagesInfo = await this.servicePackageModel
            .find({
                id: {
                    $in: ['c7764692bd4f427187de3b49b3815d10', '77cb8e703b4148a5acb8227fc2e6086e'],
                },
            })
            .populate('services')
            .exec();
        const mapServices = flatMap(listFullPackagesInfo, 'services');
        const totalPrice = reduce(
            mapServices,
            (sum: number, item: IService) => {
                return sum + item.price;
            },
            0,
        );
        // let listServices = [];
        // for(const info: IServicePackage of listFullPackagesInfo){
        //     listServices = [
        //         ...listServices, ...info.services
        //     ]

        // }
        return totalPrice;
    }

    async allOrdersByUser(userMongoId: string, partnerId: string, appId: string): Promise<any> {
        const orderFilter = { appId };
        const getPatientsList = await this.orderModel.find({ userId: userMongoId }, { patient: true }).exec();
        const mapPatients = map(getPatientsList, 'patient');
        const patients = await this.patientModel
            .find({ _id: { $in: mapPatients } }, { name: true, surname: true, id: true, birthdate: true, birthyear: true })
            .exec();
        if (mapPatients) {
            /* tìm danh sách patient */
            const ordersData = await this.orderModel
                .find({ patient: { $in: mapPatients }, ...orderFilter, visible: true })
                .populate('service')
                .sort({ createdAt: 'desc' })
                .exec();
            /* duyệt từng thằng bệnh nhân */
            let resultData = [];
            const getUrl = this.urlConfigService.getInvoiceUrl();
            for await (const patientInfo of patients) {
                const patientObj = patientInfo.toObject();
                const filterOrderPatients = ordersData.filter(order => order.patientId === patientObj.id);
                let listItems = [];
                for await (const orderRef of filterOrderPatients) {
                    const order: IOrder = orderRef;
                    /* tìm lại thông tin orderItem theo orderId */
                    const orderItems = await this.orderItemModel
                        .find({ orderId: order.id })
                        .populate('service')
                        .populate('servicePackage')
                        .exec();
                    const maptoObj = orderItems.map(item => {
                        const boj = item.toObject();
                        const { service } = boj;
                        const imageUrl = `${getUrl}/image/service/${service.id}`;
                        const boaa = {
                            ...boj,
                            service: {
                                ...boj.service,
                                imageUrl,
                            },
                        };
                        const serviceStatus = this.utilService.orderItemStatus(item.status);
                        return {
                            ...boaa,
                            serviceStatus,
                        };
                    });
                    listItems = [...listItems, ...maptoObj];
                }
                /* tiến hành sort lại */
                const sortItemList = sortBy(listItems, { status: 'desc' });
                resultData = [
                    ...resultData,
                    {
                        patient: {
                            ...patientObj,
                            birthdate: moment(patientObj.birthdate).isValid() ? moment(patientObj.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
                        },
                        orderItems: sortItemList,
                    },
                ];
            }
            return resultData;
        }
        return [];
    }

    async getInfoVaccineService(partnerId: string, patientId?: string): Promise<any> {
        let clinicEndpoint = `clinic-service/api/vaccines/${partnerId}`;
        if (patientId) {
            clinicEndpoint = clinicEndpoint.concat(`?patientId=${patientId}`);
        }
        try {
            const baseUrl = `${this.urlConfigService.getPortalProxyUrl()}/${clinicEndpoint}`;
            return (await this.httpService.getHttpRequest(baseUrl).toPromise()).data;
        } catch (error) {
            const errorMessage = error.response || 'Hệ thống chưa xử lý được thao tác này. Vui lòng kiểm tra lại!';
            const errorStatus = error.status || HttpStatus.BAD_REQUEST;
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'callInfoVaccineInjectionService',
                summary: 'Lấy thông tin Tiêm chủng Vaccine',
                nameParent: 'getInfoVaccineService',
                params: partnerId,
                errorBody: this.utilService.errorHandler(error),
                message: errorMessage,
            });
            throw new HttpException(errorMessage, errorStatus);
        }
    }
}
