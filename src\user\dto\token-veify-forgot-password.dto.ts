import { ApiProperty } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsNotEmpty } from "class-validator";

export class TokenVerifyForgotPasswordDto { 
    @ApiProperty({
        description: 'Token sau khi nhập otp',
        required: true,
        type: 'string',
    })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({
        message: 'Vui lòng gửi lên token',
    })
    readonly token: string;
}