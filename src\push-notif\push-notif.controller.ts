import { Controller, Post, HttpCode, HttpStatus, Body, Get, Req, Headers, UseGuards, HttpException, Delete, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, <PERSON>piHeader, ApiParam } from '@nestjs/swagger';
import { PushNotifService } from './push-notif.service';
import { PushNotifDTO } from './dto/push-notif.dto';
import { PushDeviceDTO } from 'src/push-device/dto/push-device.dto';
import { PushDeviceService } from 'src/push-device/push-device.service';
import { PushDeviceMongoDTO } from 'src/push-device/dto/push-device-mongo.dto';
import { AuthGuard } from '@nestjs/passport';
import { IsPushDeviceDTO } from './dto/is-push-device.dto';
import { QueryPushDevicesByPhoneDto } from './dto/query-push-devices.dto';
import { BoGuard } from 'src/common/guards/bo.guard';

@Controller('push-notif')
@ApiTags('Push Notification - Thực hiện gửi Notification tới Mobile App')
export class PushNotifController {

    constructor(
        private readonly pushNotifService: PushNotifService,
        private readonly pushDeviceService: PushDeviceService,
    ) { }

    @Get('test-insert-notif')
    async testInsertNotif(): Promise<any> {
        return this.pushNotifService.testInsertNotif();
    }

    @Post('register')
    @ApiOperation({ summary: 'Đăng ký nhận Push Notification.', description: 'Đăng ký nhận Push Notification.' })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        // type: OutDoctorsDTO,
        description: 'Đăng ký nhận Push Notification.',
    })
    async registerNotification(
        @Headers('partnerid') partnerid: string, @Headers('appid') appid: string, @Body() pushDeviceDTO: PushDeviceMongoDTO): Promise<any> {
        return this.pushDeviceService.insertPushDeviceId(partnerid, appid, pushDeviceDTO);
    }

    @Post('update-user-device')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({ summary: 'Cập nhật Thiết bị thuộc về người dùng.', description: 'Cập nhật Thiết bị thuộc về người dùng.' })
    // @HttpCode(HttpStatus.OK)
    // @ApiResponse({
    //     status: HttpStatus.OK,
    //     // type: OutDoctorsDTO,
    //     description: 'Cập nhật Thiết bị thuộc về người dùng.',
    // })
    async updateUserDeviceNotification(@Req() req, @Body() pushDeviceDTO: PushDeviceMongoDTO): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException({
                message: 'Token không hợp lệ.',
                error: 401,
            }, HttpStatus.UNAUTHORIZED);
        }
        return this.pushDeviceService.updatePushUserDevice(pushDeviceDTO, user.userMongoId);
    }

    @Post('create-notification')
    @ApiOperation({ summary: 'Create Notification.', description: 'Create Notification.' })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        // type: OutDoctorsDTO,
        description: 'Create Notification.',
    })
    async createNotification(@Body() pushNotifDTO: PushNotifDTO): Promise<any> {
        return this.pushNotifService.createNotification(pushNotifDTO);
    }

    @Get('test-push-notif')
    async testNotification(): Promise<any> {
        return this.pushNotifService.testNotification();
    }

    @Post('push-devices-by-phone')
    async isPushDevice(@Body() formData: IsPushDeviceDTO): Promise<any> {
        return this.pushDeviceService.isPushDevice(formData);
    }

    @Post('list-push-devices')
    @ApiHeader({
        name: 'appid',
        required: false,
    })
    async getPushDevicesByPhoneAndAppId(
        @Body() formData: QueryPushDevicesByPhoneDto,
        @Headers('appid') appId?: string,
    ): Promise<any> {
        return this.pushNotifService.getListPushDeviceByPhoneAndAppIdService(formData.phone , appId);
    }

    @Delete('delete-device/:id')
    @UseGuards(BoGuard)
    @HttpCode(204)
    @ApiParam({
        name: 'id',
        type: String,
        required: true,
    })
    async deltePushDeviceById(
        @Param('id') id: string,
    ): Promise<any> {
        return this.pushDeviceService.deletePushDevicesById(id);
    }

}
