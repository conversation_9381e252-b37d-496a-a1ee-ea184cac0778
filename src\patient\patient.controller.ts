import { Controller, Post, UseGuards, Get, Req, Delete, Query, Headers } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { PatientService } from './patient.service';
import { AuthGuard } from '@nestjs/passport';
import { SearchPatientDTO } from './dto/search-patient.dto';
import { VerifyPhonePatientDTO } from './dto/verify-phone-patient.dto';
import { AddPatientToUserDTO } from './dto/add-patient-to-user.dto';
import { SearchPatientExtraInfoDTO } from './dto/search-patient-extra-info.dto';
import { PatientFormDataDTO } from './dto/patient-form-data.dto';
import { UpdatePatientFormDataDTO } from './dto/update-patient-form-data.dto';
import { VerifyPhoneWithoutMSBNPatientDTO } from './dto/verify-phone-patient-without-msbn.dto';
import { UpdatePatientFormDataWithoutMsbnDTO } from './dto/update-patient-form-data-without-msbn.dto';

@Controller('patient')
@ApiTags('Patient - Quản lý thông tin Bệnh nhân')
export class PatientController {

    constructor(
        private readonly patientService: PatientService,
    ) { }

    @Post('list')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    async getAllPatients(): Promise<any> {
        return await this.patientService.getAllPatients();
    }

    @Get('umc/getbyuserid')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getAllUMCPatientsByUserId(@Req() req): Promise<any> {
        const userId = req.user.id;
        const data = await this.patientService.getAllUMCPatientsByUserId(userId);
        return data.map(patient => {
            const secretMobile = this.patientService.secretMobile(patient.mobile);
            return {
                ...patient,
                mobile: secretMobile,
            };
        });
    }

    @Get('trungvuong/getbyuserid')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getAllTrungVuongPatientsByUserId(@Req() req): Promise<any> {
        const userId = req.user.id;
        const data = await this.patientService.getAllUMCPatientsByUserId(userId);
        return data.map(patient => {
            const secretMobile = this.patientService.secretMobile(patient.mobile);
            return {
                ...patient,
                mobile: secretMobile,
            };
        });
    }

    @Get('getbyuserid')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getAllPatientsByUserId(@Req() req, @Headers('partnerid') partnerid: string): Promise<any> {
        const userId = req.user.id;
        const data = await this.patientService.getAllUMCPatientsByUserId(userId);
        return data.map(patient => {
            const secretMobile = this.patientService.secretMobile(patient.mobile);
            return {
                ...patient,
                mobile: secretMobile,
            };
        });
    }

    @Get('cho-ray/getbyuserid')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getAllChoRayPatientsByUserId(@Req() req): Promise<any> {
        const userId = req.user.id;
        const data = await this.patientService.getAllChoRayPatientsByUserId(userId);
        return data.map(patient => {
            const secretMobile = this.patientService.secretMobile(patient.mobile);
            return {
                ...patient,
                mobile: secretMobile,
            };
        });
    }

    @Get('thu-duc/getbyuserid')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getAllThuDucPatientsByUserId(@Req() req): Promise<any> {
        const userId = req.user.id;
        const data = await this.patientService.getAllThuDucPatientsByUserId(userId);
        return data.map(patient => {
            const secretMobile = this.patientService.secretMobile(patient.mobile);
            return {
                ...patient,
                mobile: secretMobile,
            };
        });
    }

    @Delete('umc/delete')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async deleteUMCPatient(@Req() req, @Query('patientId') patientId: number): Promise<any> {
        const userId = req.user.id;
        return this.patientService.deleteUMCPatient(userId, patientId);
    }

    @Delete('trungvuong/delete')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async deleteTrungVuongPatient(@Req() req, @Query('patientId') patientId: number): Promise<any> {
        const userId = req.user.id;
        return this.patientService.deleteUMCPatient(userId, patientId);
    }

    @Delete('cho-ray/delete')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async deleteChoRayPatient(@Req() req, @Query('patientId') patientId: number): Promise<any> {
        const userId = req.user.id;
        return this.patientService.deleteChoRayPatient(userId, patientId);
    }

    @Delete('thu-duc/delete')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async deleteThuDucPatient(@Req() req, @Query('patientId') patientId: number): Promise<any> {
        const userId = req.user.id;
        return this.patientService.deleteThuDucPatient(userId, patientId);
    }

    @Post('umc/getbymsbn')
    @ApiOperation({
        summary: 'Tìm hồ sơ bệnh nhân theo mã số bệnh nhân.',
        description: 'Tìm hồ sơ bệnh nhân theo mã số bệnh nhân.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async searchUMCPatientByMsbn(@Query() searchPatientDTO: SearchPatientDTO): Promise<any> {
        return this.patientService.searchUMCPatientByMsbn(searchPatientDTO);
    }

    @Post('trungvuong/getbymsbn')
    @ApiOperation({
        summary: 'Tìm hồ sơ bệnh nhân theo mã số bệnh nhân.',
        description: 'Tìm hồ sơ bệnh nhân theo mã số bệnh nhân.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async searchTrungVuongPatientByMsbn(@Query() searchPatientDTO: SearchPatientDTO): Promise<any> {
        return this.patientService.searchUMCPatientByMsbn(searchPatientDTO);
    }

    @Post('umc/find-patient-by-extra-info')
    @ApiOperation({
        summary: 'Tìm hồ sơ bệnh nhân theo họ, tên, giới tính, năm sinh, tỉnh thành.',
        description: 'Tìm hồ sơ bệnh nhân theo họ, tên, giới tính, năm sinh, tỉnh thành.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async findUMCPatientByExtraInfo(@Query() searchPatientExtraInfoDTO: SearchPatientExtraInfoDTO): Promise<any> {
        return this.patientService.findUMCPatientByExtraInfo(searchPatientExtraInfoDTO);
    }

    @Post('trungvuong/find-patient-by-extra-info')
    @ApiOperation({
        summary: 'Tìm hồ sơ bệnh nhân theo họ, tên, giới tính, năm sinh, tỉnh thành.',
        description: 'Tìm hồ sơ bệnh nhân theo họ, tên, giới tính, năm sinh, tỉnh thành.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async findTrungVuongPatientByExtraInfo(@Query() searchPatientExtraInfoDTO: SearchPatientExtraInfoDTO): Promise<any> {
        return this.patientService.findUMCPatientByExtraInfo(searchPatientExtraInfoDTO);
    }

    @Post('umc/verify-phone')
    @ApiOperation({
        summary: 'Xác thực xem số điện thoại có trùng khớp với hồ sơ bệnh nhân.',
        description: 'Xác thực xem số điện thoại có trùng khớp với hồ sơ bệnh nhân.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async verifyUMCPatientByPhone(@Query() verifyPhonePatientDTO: VerifyPhonePatientDTO): Promise<any> {
        return this.patientService.verifyUMCPatientByPhone(verifyPhonePatientDTO);
    }

    @Post('umc/verify-phone-without-msbn')
    @ApiOperation({
        summary: 'Xác thực xem số điện thoại có trùng khớp với hồ sơ bệnh nhân.',
        description: 'Xác thực xem số điện thoại có trùng khớp với hồ sơ bệnh nhân.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async verifyUMCPatientByPhoneWithoutMsbn(@Req() req, @Query() verifyPhoneWithoutMSBNPatientDTO: VerifyPhoneWithoutMSBNPatientDTO): Promise<any> {
        const userId = req.user.id;
        return this.patientService.verifyUMCPatientByPhoneWithoutMsbn(verifyPhoneWithoutMSBNPatientDTO, userId);
    }

    @Post('trungvuong/verify-phone')
    @ApiOperation({
        summary: 'Xác thực xem số điện thoại có trùng khớp với hồ sơ bệnh nhân.',
        description: 'Xác thực xem số điện thoại có trùng khớp với hồ sơ bệnh nhân.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async verifyTrungVuongPatientByPhone(@Query() verifyPhonePatientDTO: VerifyPhonePatientDTO): Promise<any> {
        return this.patientService.verifyUMCPatientByPhone(verifyPhonePatientDTO);
    }

    @Post('trungvuong/verify-phone-without-msbn')
    @ApiOperation({
        summary: 'Xác thực xem số điện thoại có trùng khớp với hồ sơ bệnh nhân.',
        description: 'Xác thực xem số điện thoại có trùng khớp với hồ sơ bệnh nhân.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async verifyTrungVuongPatientByPhoneWithoutMsbn(
        @Req() req,
        @Query() verifyPhoneWithoutMSBNPatientDTO: VerifyPhoneWithoutMSBNPatientDTO): Promise<any> {
        const userId = req.user.id;
        return this.patientService.verifyUMCPatientByPhoneWithoutMsbn(verifyPhoneWithoutMSBNPatientDTO, userId);
    }

    @Post('umc/add-patient-to-user')
    @ApiOperation({
        summary: 'Gửi secret key lên để decode xem patientId thuộc UserId.',
        description: 'Gửi secret key lên để decode xem patientId thuộc UserId.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async addPatientToUserUMCPatient(@Req() req, @Query() addPatientToUserDTO: AddPatientToUserDTO): Promise<any> {
        const user = req.user;
        return this.patientService.addPatientToUserUMCPatient(addPatientToUserDTO, user.id);
    }

    @Post('trungvuong/add-patient-to-user')
    @ApiOperation({
        summary: 'Gửi secret key lên để decode xem patientId thuộc UserId.',
        description: 'Gửi secret key lên để decode xem patientId thuộc UserId.',
    })
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async addPatientToUserTrungVuongPatient(@Req() req, @Query() addPatientToUserDTO: AddPatientToUserDTO): Promise<any> {
        const user = req.user;
        return this.patientService.addPatientToUserUMCPatient(addPatientToUserDTO, user.id);
    }

    @Post('umc/insert')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async insertUMCPatient(@Req() req, @Query() patientFormData: PatientFormDataDTO): Promise<any> {
        const user = req.user;
        return this.patientService.insertUMCPatient(patientFormData, user.id);
    }

    @Post('trungvuong/insert')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async insertTrungVuongPatient(@Req() req, @Query() patientFormData: PatientFormDataDTO): Promise<any> {
        const user = req.user;
        return this.patientService.insertUMCPatient(patientFormData, user.id);
    }

    @Get('umc/detail-for-update')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getUMCPatientDetailForUpdate(@Req() req, @Query('id') id: number): Promise<any> {
        const user = req.user;
        return this.patientService.getUMCPatientDetailForUpdate(id, user.id);
    }

    @Get('trungvuong/detail-for-update')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async getTrungVuongPatientDetailForUpdate(@Req() req, @Query('id') id: number): Promise<any> {
        const user = req.user;
        return this.patientService.getUMCPatientDetailForUpdate(id, user.id);
    }

    @Post('umc/update-msbn')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async updateUMCPatient(@Req() req, @Query() updatePatientFormDataDTO: UpdatePatientFormDataDTO): Promise<any> {
        const user = req.user;
        return this.patientService.updateUMCPatient(updatePatientFormDataDTO, user.id);
    }

    @Post('umc/update-without-msbn')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async updateUMCPatientWithoutMSBN(@Req() req, @Query() updatePatientFormDataDTO: UpdatePatientFormDataWithoutMsbnDTO): Promise<any> {
        const user = req.user;
        return this.patientService.updateUMCPatientWithoutMSBN(updatePatientFormDataDTO, user.id);
    }

    @Post('trungvuong/update')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    async updateTrungVuongPatient(@Req() req, @Query() updatePatientFormDataDTO: UpdatePatientFormDataDTO): Promise<any> {
        const user = req.user;
        return this.patientService.updateUMCPatient(updatePatientFormDataDTO, user.id);
    }

}
