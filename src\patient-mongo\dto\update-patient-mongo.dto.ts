import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsJWT, ValidateIf, IsMobilePhone, IsEmail, MaxLength, IsNumberString, Validate } from 'class-validator';
import { CheckCMND } from 'src/patient/dto/check-cmnd';
export class UpdatePatientMongoDTO {

    @ApiProperty({
        description: 'id bệnh nhân',
        required: true,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @Transform(value => `${value}`.trim())
    readonly id: string;

    @ApiProperty({
        description: 'Mobile',
        required: true,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @IsMobilePhone('vi-VN', { strictMode: false }, {
        message: 'Vui lòng nhập đúng định dạng mobile phone',
    })
    @Transform(value => `${value}`.trim())
    readonly mobile: string;

    @ApiProperty({
        description: 'Email',
        required: false,
        type: String,
    })
    @IsEmail({}, { message: 'Email không đúng định dạng' })
    @ValidateIf(o => o.email)
    @Transform(value => `${value}`.trim())
    readonly email: string;

    @ApiProperty({
        description: 'CMND',
        required: false,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @Validate(CheckCMND, [9, 12], {
        message: 'Vui lòng nhập đúng số CMND',
    })
    @IsNumberString({}, {
        message: 'Vui lòng nhập đúng CMND',
    })
    @ValidateIf(o => o.cmnd)
    @Transform(value => `${value}`.trim())
    readonly cmnd: string;

    @ApiProperty({
        description: 'Nghề nghiệp',
        required: false,
        type: String,
    })
    // tslint:disable-next-line: variable-name
    readonly profession_id: string;

    @ApiProperty({
        description: 'Quốc gia',
        required: true,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    // tslint:disable-next-line: variable-name
    readonly country_code: string;

    @ApiProperty({
        description: 'Dân tộc',
        required: true,
        type: String,
    })
    // tslint:disable-next-line: variable-name
    readonly dantoc_id: string;

    @ApiProperty({
        description: 'Tỉnh/Thành phố',
        required: true,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    // tslint:disable-next-line: variable-name
    readonly city_id: string;

    @ApiProperty({
        description: 'Quận/Huyện',
        required: false,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.country_code === 'VIE')
    // tslint:disable-next-line: variable-name
    readonly district_id: string;

    @ApiProperty({
        description: 'Phường/Xã',
        required: false,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @ValidateIf(o => o.country_code === 'VIE')
    // tslint:disable-next-line: variable-name
    readonly ward_id: string;

    @ApiProperty({
        description: 'Địa chỉ ( số nhà + tên đường)',
        required: true,
        type: String,
    })
    @IsNotEmpty({
        message: 'Vui lòng bổ sung thông tin',
    })
    @MaxLength(100, {
        message: 'Vui lòng không vượt quá 100 ký tự.',
    })
    @Transform(value => `${value}`.trim())
    @ValidateIf(o => o.country_code === 'VIE')
    readonly address: string;
}
