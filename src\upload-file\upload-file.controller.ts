import { Api<PERSON>peration, ApiParam, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { Controller, Post, UploadedFiles, UseInterceptors, Headers, Get, Param, Res, Req, HttpService, Query, UploadedFile } from '@nestjs/common';
import { FileFieldsInterceptor, FileInterceptor } from '@nestjs/platform-express';
import { UploadFileService } from './upload-file.service';
import { BASE_DIR } from './constant';

@Controller('upload-file')
export class UploadFileController {

    constructor(
        private service: UploadFileService,
        private httpService: HttpService,
    ) { }

    @Post()
    @UseInterceptors(
        FileFieldsInterceptor([
            { name: 'file', maxCount: 1 },
        ]),
    )
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    async uploadFile(
        @Headers('partnerid') partnerid: string,
        @UploadedFiles() files,
    ): Promise<any> {
        return this.service.uploadFile(partnerid, files);
    }

    @Get('get-file-by-booking')
    @ApiOperation({ summary: `Download file` })
    @ApiParam({
        type: String,
        name: 'idBooking',
        description: 'booking id',
    })
    async dowloadFileStream(@Query('idBooking') id: string, @Res() res): Promise<any> {
        return this.service.getFileByBooking(id, res);
    }

    // @Get(':imagePath')
    // @ApiOperation({ summary: `Lấy image theo imagePath` })
    // @ApiParam({
    //     name: `imagePath`,
    //     type: String,
    //     required: true,
    //     description: `path image to see upload file`,
    // })
    // async getIconFile(@Param('imagePath') image: string, @Res() res, @Req() req) {
    //     return await res.sendFile(image);
    // }
    @Post('upload')
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @UseInterceptors(FileInterceptor('file'))
    async uploadFileS3(@UploadedFile() file): Promise<any> {
        return this.service.uploadFileS3(file);
    }
}
