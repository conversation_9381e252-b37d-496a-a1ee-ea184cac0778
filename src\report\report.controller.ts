import { HttpCacheInterceptor } from './../middleware/http.interceptor';
import { <PERSON>acheTTL, Controller, Get, Query, UseInterceptors } from '@nestjs/common';
import { DataReportRealtimeDto } from './dto/data-report-realtime.dto';
import { OptionsDataReportDto } from './dto/options-data-report.dto';
import { ReportService } from './report.service';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

@Controller('report')
@ApiTags('Thống kê dữ liệu')
export class ReportController {
    constructor(private readonly service: ReportService) {}

    @Get('realtime')
    @CacheTTL(90)
    @UseInterceptors(HttpCacheInterceptor)
    @ApiOperation({ summary: 'Lấy dữ liệu thống kê người dùng, l<PERSON><PERSON><PERSON> kh<PERSON><PERSON>, người dùng mới hiển thị realtime'})
    getReportDataRealtime(@Query() options: OptionsDataReportDto): Promise<DataReportRealtimeDto> {
        return this.service.getReportDataRealtime(options);
    }
}
