import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, MaxLength } from 'class-validator';

export class SmsDto {
    @ApiProperty({ description: 'Vui lòng nhập messageId!', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập messageId!' })
    readonly messageId: string;

    @ApiProperty({ description: 'Nội dung tin nhắn', required: true, type: String })
    @MaxLength(450, { message: 'Tin nhắn không được vượt quá 450 ký tự!' })
    @IsNotEmpty({ message: 'Vui lòng nhập nội dung tin nhắn!' })
    readonly message: string;

    @ApiProperty({ description: 'Vui lòng nhập partnerId!', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập partnerId!' })
    readonly partnerId: string;

    @ApiProperty({ description: 'Vui lòng nhập appId!', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập appId!' })
    readonly appId: string;
}
