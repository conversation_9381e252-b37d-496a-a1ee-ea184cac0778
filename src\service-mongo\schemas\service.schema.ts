import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SERVICE_COLLECTION_NAME } from './constants';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';

const Schema = mongoose.Schema;

export const ServiceSchema = new Schema({
    id: String,
    code: String,
    name: String,
    shortName: String,
    partnerId: String,
    description: String,
    createTime: Date,
    searchUnicode: String,
    price: Number,
    originalPrice: Number,
    serviceType: String,
    isRequiredCheckInsurance: { type: Boolean, default: true },
    type: String,
    hospitalId: { type: Schema.Types.ObjectId, ref: HOSPITAL_COLLECTION_NAME },
    addonServiceIds: { type: [String]},
    daysOff: String,
}, {
    collection: SERVICE_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);
