import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiQuery } from '@nestjs/swagger';
import { Nhidong1Service } from './nhidong1.service';

@Controller('nhidong1')
export class Nhidong1Controller {
    constructor(private service: Nhidong1Service) {}

    @Get('bookings-by-date')
    @ApiOperation({ summary: 'Lấy danh sách booking' })
    @ApiQuery({ name: 'date', allowEmptyValue: false, example: '31012022' })
    getBookingV1Nhidong1(@Query('date') date: string): Promise<any> {
        return this.service.getBookingByDate(date);
    }
}
