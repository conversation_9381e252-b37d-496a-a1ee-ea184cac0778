import { HttpException, Injectable,
    // , <PERSON><PERSON>, OnModuleDestroy, OnModuleInit
} from '@nestjs/common';
import { ClientUtilService } from './client-util.service';
import { ConfigService } from '@nestjs/config';
// import { connect, Empty, headers, NatsConnection, JSONCodec } from "nats";
import * as axios from 'axios';
// import { get, isEmpty } from 'lodash';

@Injectable()
export class ClientNatsService {
// implements OnModuleInit, OnModuleDestroy

    // private readonly log = new Logger(ClientNatsService.name)
    // private nc: NatsConnection;
    // private readonly jc = JSONCodec();

    constructor(private readonly config: ConfigService, private readonly client: ClientUtilService) { }

    // async onModuleInit() {
    //     if (this.getNatsEnv === 'ON') {
    //         try {
    //             this.nc = await connect(this.getNatsServers);
    //         } catch (error) {
    //             this.log.warn(`Nats connection refuse ${error?.message}`)
    //         }
    //     }
    // }

    // async onModuleDestroy() {
    //     await this.nc.flush();
    //     await this.nc.close();
    // }

    async request(topic: string, data: any, inputHeader?: any, api: string = 'http://localhost:1234', method: axios.Method = 'POST'): Promise<any> {
        // let response: any;
        const response = await this.client.request(method, api, data, inputHeader);
        // if (!this.nc || this.getNatsEnv === 'OFF') {
        //     response = await this.client.request(method, api, data, inputHeader);
        // }
        // const h = headers();
        // if (inputHeader) {
        //     for (const key of Object.keys(inputHeader)) {
        //         const valueHeader = get(inputHeader, key, '');
        //         if (isEmpty(valueHeader)) {
        //             continue;
        //         } else {
        //             h.append(key, valueHeader);
        //         }
        //     }
        // }
        // try {
        //     const m = await this.nc.request(topic, data ? this.jc.encode(data) : Empty, {
        //         headers: h,
        //         timeout: 1500,
        //     })
        //     response =  this.jc.decode(m.data);
        // } catch (error) {
        //     response = await this.client.request(method, api, data, inputHeader);
        // }

        if (response?.err || response?.statusCode < 200 || response?.statusCode >= 400) {
            throw new HttpException(response?.err?.message || 'Không lấy được dữ liệu!', response?.statusCode);
        }

        return response?.data;
    }

    // get getNatsHost(): string {
    //     return this.config.get<string>('NATS_HOST') || 'localhost';
    // }

    // get getNatsPort(): number {
    //     return this.config.get<number>('NATS_PORT') || 4444;
    // }

    // get getNatsEnv(): string {
    //     return this.config.get<string>('NATS_ENV') || 'OFF';
    // }

    // get getNatsCluster(): string {
    //     return this.config.get<string>('NATS_CLUSTER') || 'OFF';
    // }

    // get getNatsClusterHost(): string {
    //     return this.config.get<string>('NATS_CLUSTER_HOST');
    // }

    // get getNatsServers(): any {
    //     if (this.getNatsCluster === 'ON') {
    //         const hostCluster = this.getNatsClusterHost;
    //         this.log.verbose(`Connect nats with cluster mode: ${hostCluster}`);

    //         return {
    //             servers: hostCluster.split(','),
    //         };
    //     }

    //     const host = this.getNatsHost;
    //     const port = this.getNatsPort;

    //     return {
    //         servers: [`nats://${host}:${port}`],
    //     };
    // }
}