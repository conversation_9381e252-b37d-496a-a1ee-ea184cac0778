import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { RELATIVE_TYPE_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;
const RelativeSchema = new Schema({
    id: String,
    code: String,
    name: String,
    status: Number,
    partnerId: String,
}, {
    collection: RELATIVE_TYPE_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);

export { RelativeSchema };
