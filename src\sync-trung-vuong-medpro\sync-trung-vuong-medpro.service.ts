import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { USER_COLLECTION_NAME, SIGNIN_PROVIDER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { Model } from 'mongoose';
import { IUser } from 'src/user/interfaces/user.interface';
import { ISignInProvider } from 'src/user/interfaces/sign-in-provider.interface';
import { SYNC_USER_TRUNGVUONG_UPGRADE_COLLECTION_NAME } from './schemas/constants';
import { ISyncUserTrungVuongUpgrade } from './interfaces/sync-user-trungvuong-upgrade.inteface';
import { first } from 'lodash';

@Injectable()
export class SyncTrungVuongMedproService {

    private logger = new Logger(SyncTrungVuongMedproService.name);

    constructor(
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(SIGNIN_PROVIDER_COLLECTION_NAME) private signInProviderModel: Model<ISignInProvider>,
        @InjectModel(SYNC_USER_TRUNGVUONG_UPGRADE_COLLECTION_NAME) private userUpgradeModel: Model<ISyncUserTrungVuongUpgrade>,
    ) { }

    async syncUser(): Promise<any> {
        try {
            /* lấy toàn bộ thông tin những user thuộc medpro 1.0 */
            const users = await this.userModel.find({
                medproId: {
                    $in: [null, ''],
                },
            }, {
                id: true,
                username: true,
            }).exec();
            const mapUsers = users
                .map((item: IUser) => {
                    return {
                        userId: item.id,
                        username: item.username,
                    };
                });
            return this.userUpgradeModel.insertMany(mapUsers);
            // const firstE = first(mapUsers);
            // return this.userUpgradeModel.insertMany([firstE]);
        } catch (error) {
            this.logger.error('Lỗi khi đồng bộ dữ liệu user trung vuong', error);
        }
    }
}
