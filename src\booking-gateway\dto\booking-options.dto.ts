import { ApiProperty } from '@nestjs/swagger';

export class BookingOptionDto {
    @ApiProperty({
        description: 'transactionId',
        required: false,
    })
    transactionId: string;

    @ApiProperty({
        description: 'smsCode',
        required: false,
    })
    smsCode?: string;

    @ApiProperty({
        description: 'secretBooking',
        required: false,
    })
    secretBooking?: string;
}