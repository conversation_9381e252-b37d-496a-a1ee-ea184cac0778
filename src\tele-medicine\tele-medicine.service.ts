import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AxiosResponse } from 'axios';
import { Model } from 'mongoose';
import { Observable } from 'rxjs';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { PkhHttpService } from 'src/config/config.http.service';
import { UrlConfigService } from 'src/config/config.url.service';
import { PushNotifService } from 'src/push-notif/push-notif.service';
import { UpdateMakeCallStatusDTO } from './dto/update-make-call-status.dto';

@Injectable()
export class TeleMedicineService {
    private logger = new Logger(TeleMedicineService.name);
    private telemedUrl: string;
    constructor(
        private readonly urlConfigService: UrlConfigService,
        private readonly httpService: PkhHttpService,
        private readonly pushNotifService: PushNotifService,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
    ) {
        this.telemedUrl = this.urlConfigService.getTelemedUrl();
    }
    async userBusy(bookingId: string): Promise<any> {
        const bookingInfo = await this.bookingModel.findOne({ id: bookingId }).exec();
        if (!bookingInfo) {
            throw new HttpException('Không tìm thấy thông tin phiếu khám.', HttpStatus.NOT_FOUND);
        }
        try {
            const response = (await this.callTeleMedpro(bookingId).toPromise()).data;
            return {
                data: response,
            };
        } catch (error) {
            this.logger.error(`Error when exec userBusy()\nError message: ${error.message}`);
        }
    }

    callTeleMedpro(bookingId: string): Observable<AxiosResponse<any>> {
        return this.httpService.getHttpRequest(`${this.telemedUrl}/telemed/userBusy/${bookingId}`);
    }

    async updateMakeCallStatus(formData: UpdateMakeCallStatusDTO): Promise<any> {
        try {
            const res = await (await this.callUpdateMakeCallStatus(formData).toPromise()).data;
            if (formData.status === 'busy' && res.status === 'success') {
                await this.pushNotifService.pushNotifyToPatient({
                    bookingId: formData.bookingId,
                    icon: "missed_call_small_icon"
                });
            }
            return res;
        } catch (error) {
            const errorMessage = error?.response?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            this.logger.error(`Error when exec updateMakeCallStatus() with bookingId: ${formData.bookingId}\nError: ${error.message}`);
            throw new HttpException(errorMessage, statusCode);
        }
    }

    callUpdateMakeCallStatus(formData: UpdateMakeCallStatusDTO) {
        const baseUrl = this.urlConfigService.getTelemedUrl();
        const url = `${baseUrl}/telemed/updateMakeCallStatus`;
        return this.httpService.postHttpRequest(url, { ...formData });
    }

    async getMakeCallStatus(bookingId: string, role?: string): Promise<any> {
        try {
            return (await this.callGetMakeCallStatus(bookingId, role).toPromise()).data;
        } catch (error) {
            const errorMessage = error?.response?.message || `Hệ thống chưa xử lý được thao tác này, vui lòng kiểm tra lại!`;
            const statusCode = error?.status || HttpStatus.BAD_REQUEST;
            this.logger.error(`Error when exec getMakeCallStatus() with bookingId: ${bookingId}\nError: ${error.message}`);
            throw new HttpException(errorMessage, statusCode);
        }
    }

    callGetMakeCallStatus(bookingId: string, role: string = '') {
        const baseUrl = this.urlConfigService.getTelemedUrl();
        const url = `${baseUrl}/telemed/getMakeCallStatus`;
        return this.httpService.postHttpRequest(url, { bookingId, role });
    }

    // callMessageHubPush() {}
}
