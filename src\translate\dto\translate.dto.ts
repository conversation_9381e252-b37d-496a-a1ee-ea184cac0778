import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsMongoId, IsNotEmpty } from 'class-validator';

export class TranslateDto {
    @ApiProperty()
    @IsNotEmpty()
    @Transform(value => `${value}`.trim())
    collection: string;

    @ApiProperty()
    @IsNotEmpty()
    @Transform(value => `${value}`.trim())
    objectId: string;

    @ApiProperty()
    @IsNotEmpty()
    prop: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    key?: string;
}
