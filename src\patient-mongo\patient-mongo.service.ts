import { ClientUtilService } from './../config/client-util.service';
import { ISubject } from './../subject-mongo/interfaces/subject.interface';
import { IService } from './../service-mongo/interfaces/service.interface';
import { SUBJECT_COLLECTION_NAME } from './../subject-mongo/schemas/constants';
import { SERVICE_COLLECTION_NAME } from './../service-mongo/schemas/constants';
import { LOG_SERVICE_EVENT } from './../audit-log/constant';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { Injectable, HttpException, HttpStatus, HttpService, Inject, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment';
import * as uuid from 'uuid';
import { cloneDeep, find, first, get, groupBy, identity, isEmpty, isNil, isNumber, last, map, orderBy, pick, pickBy, set, split } from 'lodash';
import { isEmail, isMobilePhone } from 'class-validator';
import * as jwt from 'jsonwebtoken';
// import * as queryString from 'query-string';
import {
    PATIENT_COLLECTION_NAME, PATIENT_CODE_COLLECTION_NAME, PATIENT_SEARCH_LOG_COLLECTION_NAME,
    PATIENT_VERSION_COLLECTION_NAME,
    RELATIVE_TYPE_COLLECTION_NAME,
    PATIENT_PROFILE_COLLECTION_NAME,
    PATIENT_RELATION_COLLECTION_NAME,
    PATIENT_TRACKING_COLLECTION_NAME,
    PATIENT_INSURANCE_CCCD,
} from './schemas/constants';
import { Model } from 'mongoose';
import { IPatient } from './intefaces/patient.inteface';
import { PatientFormMongoDTO } from './dto/patient-form-mongo.dto';
import { PatientService } from 'src/patient/patient.service';
import { UserInfoVerifyTokenDTO } from './dto/userInfo';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { PROFESSION_COLLECTION_NAME } from 'src/profession-mongo/schemas/constants';
import { IProfession } from 'src/profession-mongo/interfaces/profession.interface';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { ICountry } from 'src/country-mongo/interfaces/country.interface';
import { INation } from 'src/nation-mongo/interfaces/nation.interface';
import { NATION_COLLECTION_NAME } from 'src/nation-mongo/schemas/constants';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { ICity } from 'src/city-mongo/interfaces/city.interface';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { IDistrict } from 'src/district-mongo/interfaces/district.interface';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { IWard } from 'src/ward-mongo/interfaces/ward.interface';
import { UtilService } from 'src/config/util.service';
import { UMCPatientResponseDTO } from 'src/patient/dto/umc-patient-response.dto';
import { UMCPartnerConfigService } from 'src/config/config.umc.partner.service';
import { SearchPatientDTO } from 'src/patient/dto/search-patient.dto';
import { from, Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { SearchPatientExtraInfoDTO } from 'src/patient/dto/search-patient-extra-info.dto';
import { UMCPatientExtraInfoResponseDTO } from 'src/patient/dto/umc-patient-extra-info-response.dto';
import { JwtModuleOptions } from '@nestjs/jwt';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { UrlConfigService } from 'src/config/config.url.service';
import { VerifyPhonePatientDTO } from 'src/patient/dto/verify-phone-patient.dto';
import { VerifyPhonePatientMongoDTO } from 'src/patient/dto/verify-phone-patient-mongo.dto';
import { SearchPatientExtraInfoMongoDTO } from 'src/patient/dto/search-patient-extra-info-mongo.dto';
import { AddPatientToUserDTO } from 'src/patient/dto/add-patient-to-user.dto';
import { UpdatePatientMongoDTO } from './dto/update-patient-mongo.dto';
import { PatientFormMongoUpdateDTO } from './dto/patient-form-update-mongo.dto';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { IPatientCodes } from './intefaces/patient-codes.inteface';
import { IPatientSearchLog } from './intefaces/patient-search-log.inteface';
import { GetInsuranceInfoDTO } from './dto/get-insurance-info.dto';
import { GetInsuranceDateDTO } from './dto/get-insurance-date.dto';
import { GetInsuranceParseAddressDTO } from './dto/get-insurance-parse-address.dto';
import * as slugify from 'slugify';
import { IPatientVersion } from './intefaces/patient-version.inteface';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { SessionService } from 'src/session/session.service';
import { RestfulAPIOldHospitalConfigService } from 'src/config/config.rest-api.old.hospital';
import { IRelative } from './intefaces/relative.inteface';
import { UserService } from 'src/user/user.service';
import { SearchPhoneCMNDPatientExtraInfoMongoDTO } from 'src/patient/dto/search-phone-cmnd-patient-extra-info-mongo.dto';
import { ChorayPatientResponseDTO } from 'src/booking-gateway/dto/cr-patient-response.dto';
import { CSKHTokenVerifyDTO } from './dto/cskh-token-verify.dto';
import { VerifyInsuranceCodePatientMongoDTO } from 'src/patient/dto/verify-insurance-code-patient-mongo.dto';
import { PatientProfileDTO } from './dto/patient-profile.dto';
import { IPatientProfile } from './intefaces/patient-profile.interface';
import { GetPatientProfileDTO } from './dto/get-patient-profile.dto';
import { ConstraintSearchLogDTO } from './dto/constraint-search-log.dto';
import { YearOldValidationDto } from './dto/year-old-validation.dto';
import { GLOBAL_SETTING_COLLECTION_NAME } from 'src/global-setting/schemas/constants';
import { IGlobalSetting } from 'src/global-setting/interfaces/global-setting.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { FindPatientDto } from './dto/find-patient-profile.dto';
import {BOOKING_COLLECTION_NAME, CREATE_FIND_BHYT_LOGS_EVENT, CREATE_FIND_PATIENT_MSBN_LOGS_EVENT, PATIENT_TRACKING_EVENT, UPDATE_ERROR_RESERVE_LOGS_EVENT, UPDATE_RESERVE_LOGS_EVENT} from 'src/booking-gateway/schemas/constants';
import { EVENT_LINK_PENDING_HEALTH_DATA } from 'src/message-event/constant';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { DataBhytDto } from 'src/partner-config/dto/bhyt-data.dto';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { IPatientRelation } from './intefaces/patient-relations.interface';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { ValidateBookingRuleDto } from './dto/validate-booking-rule.dto';
import { IPatientTracking, PatientTrackingData } from './intefaces/patient-tracking.inteface';
import { LarkService } from '../config/lark.service';
import { PatientTrackingActionTextEnum } from '../common/enums/patient-tracking-action-text.enum';
import { REPO_NAME_BETA } from '../common/constants';
import { HeadersDto } from '../common/base/headers.dto';
import { ErrorMessage } from '../common/enums/message-error.enum';
import { FindPatientHisDto } from './dto/find-patient-his.dto';
import { timeout } from 'rxjs/operators';
import { UserZaloInfoDto } from 'src/user-zalo/dto/user-zalo-info.dto';
import { RequestSentryService } from 'src/sentry/request-sentry-service';
import { EVENT_FIND_PATIENT_CODE_BY_MSBN } from '../message-event/constant';

@Injectable()
export class PatientMongoService {
    private logger = new Logger(PatientMongoService.name);
    private countPatient = 10;
    private countSearchPatient = 4;
    private listAppId: Set<{}>;
    private readonly AGE_LIMIT_CONFIG: string = 'AGE_LIMIT_CONFIG';
    private readonly WARNING_MESSAGE: string = 'WARNING_MESSAGE';
    private readonly repoName: string;
    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @InjectModel(PATIENT_COLLECTION_NAME) private patientModel: Model<IPatient>,
        @InjectModel(PATIENT_VERSION_COLLECTION_NAME) private patientVersionModel: Model<IPatientVersion>,
        @InjectModel(PATIENT_CODE_COLLECTION_NAME) private patientCodeModel: Model<IPatientCodes>,
        @InjectModel(PATIENT_SEARCH_LOG_COLLECTION_NAME) private patientSearchLogModel: Model<IPatientSearchLog>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(COUNTRY_COLLECTION_NAME) private countryModel: Model<ICountry>,
        @InjectModel(NATION_COLLECTION_NAME) private nationModel: Model<INation>,
        @InjectModel(PROFESSION_COLLECTION_NAME) private professionModel: Model<IProfession>,
        @InjectModel(CITY_COLLECTION_NAME) private cityModel: Model<ICity>,
        @InjectModel(DISTRICT_COLLECTION_NAME) private districtModel: Model<IDistrict>,
        @InjectModel(RELATIVE_TYPE_COLLECTION_NAME) private relativeModel: Model<IRelative>,
        @InjectModel(WARD_COLLECTION_NAME) private wardModel: Model<IWard>,
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(PATIENT_PROFILE_COLLECTION_NAME) private patientProfileModel: Model<IPatientProfile>,
        @InjectModel(GLOBAL_SETTING_COLLECTION_NAME) private readonly globalSettingModel: Model<IGlobalSetting>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private readonly hospitalModel: Model<IHospital>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(PATIENT_RELATION_COLLECTION_NAME) private patientRelationModel: Model<IPatientRelation>,
        @InjectModel(PATIENT_TRACKING_COLLECTION_NAME) private patientTrackingModel: Model<IPatientTracking>,
        @InjectModel(SERVICE_COLLECTION_NAME) private readonly serviceModel: Model<IService>,
        @InjectModel(SUBJECT_COLLECTION_NAME) private readonly subjectModel: Model<ISubject>,
        @InjectModel(PATIENT_INSURANCE_CCCD) private readonly patientInsuranceCccdModel: Model<any>,
        private patientService: PatientService,
        private utilService: UtilService,
        private readonly uMCPartnerConfigService: UMCPartnerConfigService,
        private readonly httpService: HttpService,
        private readonly jwtUserConfigService: JwtUserConfigService,
        private readonly urlConfigService: UrlConfigService,
        private readonly sessionService: SessionService,
        private readonly oldUrl: RestfulAPIOldHospitalConfigService,
        private userService: UserService,
        private emitService: EventEmitter2,
        private readonly globalSettingService: GlobalSettingService,
        private readonly client: ClientUtilService,
        private readonly repoConfigService: ConfigRepoService,
        private readonly larkService: LarkService,
        private readonly sentryService: RequestSentryService,
    ) {
        this.listAppId = this.utilService.listAppId();
        this.repoName = this.repoConfigService.getRepoName();
    }

    async createPatientProfile(appId: string, partnerId: string, userId: string, formData: PatientProfileDTO): Promise<any> {
        const newPatientProfile = new this.patientProfileModel({
            id: uuid.v4().replace(/-/g, ''),
            ...formData,
            userId,
            appId,
            partnerId,
        });
        try {
            return newPatientProfile.save();
        } catch (error) {
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async getPatientProfile(appId: string, partnerId: string, userId: string, formData: GetPatientProfileDTO): Promise<any> {
        const list = await this.patientProfileModel.find({
            userId,
            patientId: formData.patientId,
        }).exec();
        return list;
    }

    async insertChoRayPatient(appId: string, partnerId: string, data: ChorayPatientResponseDTO, userId: string): Promise<any> {
        /* Tiến hành constraints search */
        await this.constraintSearchLog(userId, partnerId);

        const patient: UMCPatientResponseDTO = await this.patientTransformer(data);
        data.mabhyt = last(split(data.msbn, '.'));
        /* insert thêm vào trong patient_codes */
        const objInsurance: any = {};
        if (typeof data.mabhyt !== typeof undefined && `${data.mabhyt}`.trim() !== '') {
            objInsurance.insuranceCode = `${data.mabhyt}`.trim();
        }
        try {
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            const { isVerifiedByPhone: isVerify = false } = partnerConfig;
            let isVerifiedByPhone = isVerify;
            /* Kiểm tra xem có config user-profiles có bật attribute_mode_dev_patner_enable */
            const checkModeDevPartnerEnable = await this.userService.getUserProfileVerifyPhone(partnerId, userId);
            if (checkModeDevPartnerEnable) {
                isVerifiedByPhone = false;
            }
            /* Kiểm tra thêm xem tài khoản đăng nhập có trùng với sdt trong hồ sơ */
            const getUsername: IUser = await this.userService.getUserByMongoUserId(userId);
            /* lấy thông tin mobile để check. nếu không có thì tạo tài khoản luôn */
            let mobile = get(patient, 'DienThoai', '');
            /* Kiểm tra xem mobile có hợp lệ hay ko */
            if (`${mobile}`.length === 9) {
                mobile = `0${mobile}`;
            }
            const checkPhone = isMobilePhone(mobile, 'vi-VN');
            if (checkPhone) {
                const yourphone = `${mobile}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
                if (yourphone === getUsername.username) {
                    isVerifiedByPhone = false;
                }
            }
            /* kiểm tra xem thông tin ngày có chưa. nếu có rồi thì update */
            const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, patient.SoHS);
            if (checkExists) {
                const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                    DiDong: patient.DiDong,
                    DienThoai: patient.DienThoai,
                    SoCMND: patient.SoCMND,
                    NgaySinh: patient.NgaySinh,
                    NamSinh: patient.NamSinh,
                    GioiTinh: patient.GioiTinh,
                    Ten: patient.Ten,
                    Ho: patient.Ho,
                    ...objInsurance,
                    isExam: true,
                    SoHS: patient.SoHS,
                    partnerId,
                });
                const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appId, updateInfo._id);
                const patientCheck = await this.getPatientByIdPatient(checkExists.patientId);
                const checkUserHavePatientCode = await this.checkExistsUMCPatientBelongsToUser(userId, patientCheck);
                const secretKeyObj: any = {};
                if (!checkUserHavePatientCode) {
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                }
                const { insuranceCode = '', ...restPatientInfo } = returnInfo;
                const resultData = [{
                    ...restPatientInfo,
                    mobile: this.secretMobile(returnInfo.mobile),
                    isVerifiedByPhone,
                    ...secretKeyObj,
                    patientCode: `${patient.SoHS}`.trim(),
                }];
                return resultData;
            } else {
                patient.partnerId = partnerId;
                patient.isExam = true;
                const patientInfo = await this.insertUMCSyncPatient(patient);
                const patientObj = patientInfo.toObject();
                const patientCodeInfo = new this.patientCodeModel({
                    id: `${partnerId}_${patientObj.id}`,
                    createTime: moment().toDate(),
                    patientId: patientObj.id,
                    patientCode: `${patient.SoHS}`.trim(),
                    ...objInsurance,
                    partnerId,
                    appId,
                });
                await patientCodeInfo.save();
                // console.log(aaaa);
                /* kết thúc phần insert vào trong patient codes */
                const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appId, patientObj._id);
                const secretKeyObj: any = {};
                if (!isVerifiedByPhone) {
                    const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                    secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                }
                const { insuranceCode, ...restPatientInfo } = returnInfo;
                return [{
                    ...restPatientInfo,
                    mobile: this.secretMobile(returnInfo.mobile),
                    isVerifiedByPhone,
                    ...secretKeyObj,
                    patientCode: `${patient.SoHS}`.trim(),
                }];
            }
        } catch (error) {
            console.log(error);
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async patientTransformer(data: ChorayPatientResponseDTO): Promise<UMCPatientResponseDTO> {
        const patient: UMCPatientResponseDTO = {
            DiDong: data.dienthoai,
            DienThoai: data.dienthoai,
            DiaChi: data.diachi,
            GioiTinh: Number(data.gioitinh) === 1,
            Ho: data.ho,
            Ten: data.ten,
            IDDanToc: null,
            IDNgheNghiep: null,
            IDPhuongXa: null,
            IDQuanHuyen: null,
            IDTinh: null,
            MaQuocGia: null,
            NamSinh: Number(data.namsinh),
            NgaySinh: data.ngaysinh,
            NgungSD: false,
            SoCMND: '',
            SoHS: data.msbn,
        };
        return patient;
    }

    async autoGenerateMedproId(): Promise<any> {
        const patients = await this.patientModel.find({
            code: '',
        });
        if (patients.length > 0) {
            for await (const patient of patients) {
                const medproId = this.generateMedproID(6);
                await this.patientModel.findOneAndUpdate({
                    id: patient.id,
                }, {
                    code: medproId,
                }).exec();
            }
        }
        return patients;
    }

    async getInsuranceInfo(partnerId: string, appId: string, insuranceForm: GetInsuranceInfoDTO): Promise<any> {
        try {
            const params = {
                ...insuranceForm,
                partnerId,
            };
            const baseUrl = this.urlConfigService.getBookingTreeUrl();
            const url = `${baseUrl}/his-connector/api/insurance/getInfo`;
            const data = (await this.getInsuranceInfoHIS(url, params).toPromise()).data;
            /* tiếp tục lấy thông tin thời hạn */
            let dataDate: any = {};
            const urlDate = `${baseUrl}/his-connector/api/insurance/getDate`;
            dataDate = (await this.getInsuranceInfoHIS(url, params).toPromise()).data;
            return { ...data, ...dataDate };
        } catch (error) {
            const { response } = error;
            if (response) {
                const { data, status } = response;
                if (status === HttpStatus.NOT_FOUND) {
                    const { errorDetail } = data;
                    if (errorDetail) {
                        throw new HttpException(errorDetail, HttpStatus.NOT_FOUND);
                    }
                }
            }
            throw new HttpException('Hệ thống không tìm thấy thông tin.', HttpStatus.NOT_FOUND);
        }
    }

    // async testInsurance(): Promise<any> {
    //     try {
    //         const data = (await this.getPatientExtraByInsuranceCodeHIS('trungvuong', 'CH4775104005748').toPromise()).data;
    //         return data;
    //     } catch (error) {
    //         console.log(error);
    //     }
    // }

    async getInsuranceDate(partnerId: string, appId: string, insuranceForm: GetInsuranceDateDTO, req: any, userId: string, cskhInfo?: any): Promise<any> {
        let data: any = {};
        const params = {
            ...insuranceForm,
            partnerId,
            ...(insuranceForm.bookingDate === 'Invalid date' && { bookingDate: moment().utc().add(7, 'hours').format('YYYY-MM-DD') })
        };
        const uuidLog = uuid.v4().replace(/-/g, '');
        try {
            const patientInfo = await this.patientModel.findOne({ id: insuranceForm.patientId }, { id: true }).exec();
            const patientObj = patientInfo.toObject();
            //event logs
            this.emitService.emit(CREATE_FIND_BHYT_LOGS_EVENT, {
                uuid: uuidLog,
                partnerId,
                appId,
                userId: userId,
                cskhInfo: cskhInfo,
                timestamp: new Date(),
                url: req.url,
                method: req.method,
                headers: req.headers,
                params: req.params,
                body: req.body,
                query: req.query,
                patientId: patientObj._id,
                nameRepo: this.repoName,
                action: 'getInsuranceDate',
                insuranceId: insuranceForm.insuranceId,
            });
        } catch (error) {
            
        }
        
        try {
            
            const baseUrl = this.urlConfigService.getBookingTreeUrl();
            const url = `${baseUrl}/his-connector/api/insurance/getDate`;
            const startHisConnector = moment().toDate()
            this.logger.log(`START tra cứu: ${startHisConnector}`)
            data = (await this.getInsuranceInfoHIS(url, params).toPromise()).data;

            this.emitService.emit(UPDATE_RESERVE_LOGS_EVENT, {
                uuid: uuidLog,
                data
            });

            const endHisConnector = moment().toDate();
            this.logger.log(`END  tra cứu: ${moment(endHisConnector).diff(startHisConnector, 'milliseconds')} ms`)
        } catch (error) {
            const { response } = error;
            if (response) {
                const { data: dataError, status } = response;
                if (status === HttpStatus.NOT_FOUND) {
                    const { errorDetail } = dataError;
                    if (dataError?.checkCode && dataError?.crossFacilityMessage) {
                        this.emitService.emit(LOG_SERVICE_EVENT, {
                            name: 'getInsuranceInfoHIS',
                            summary: 'Lấy thông tin bảo hiểm bên HIS',
                            nameParent: 'getInsuranceDate',
                            params,
                            errorBody: dataError,
                            response: {soucre: data},
                            message: dataError?.crossFacilityMessage,
                        });
                        if (cskhInfo && cskhInfo.cskhUserId) {
                            this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                                uuid: uuidLog,
                                data: {
                                    status: HttpStatus.NOT_FOUND,
                                    message: '/his-connector/api/insurance/getDate gặp lỗi'
                                }
                            });
                            return {
                                dataError: dataError?.crossFacilityMessage,
                                status: HttpStatus.NOT_FOUND,
                                req: req.body,
                                message: '/his-connector/api/insurance/getDate gặp lỗi'
                            }
                        }
                        this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                            uuid: uuidLog,
                            data: {
                                status: HttpStatus.NOT_FOUND,
                                message: dataError?.crossFacilityMessage
                            }
                        });
                        throw new HttpException(dataError?.crossFacilityMessage, HttpStatus.NOT_FOUND);
                    }
                    else if (errorDetail) {
                        this.emitService.emit(LOG_SERVICE_EVENT, {
                            name: 'getInsuranceInfoHIS',
                            summary: 'Lấy thông tin bảo hiểm bên HIS',
                            nameParent: 'getInsuranceDate',
                            params,
                            errorBody: dataError,
                            response: {soucre: data},
                            message: errorDetail,
                        });
                        if (cskhInfo && cskhInfo.cskhUserId) {
                            this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                                uuid: uuidLog,
                                data: {
                                    status: HttpStatus.NOT_FOUND,
                                    message: '/his-connector/api/insurance/getDate gặp lỗi'
                                }
                            });
                            return {
                                dataError: errorDetail,
                                status: HttpStatus.NOT_FOUND,
                                req: req.body,
                                message: '/his-connector/api/insurance/getDate gặp lỗi'
                            }
                        }
                        this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                            uuid: uuidLog,
                            data: {
                                status: HttpStatus.NOT_FOUND,
                                message: errorDetail
                            }
                        });
                        throw new HttpException(errorDetail, HttpStatus.NOT_FOUND);
                    }
                }
                if (cskhInfo && cskhInfo.cskhUserId) {
                    this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                        uuid: uuidLog,
                        data: {
                            status: HttpStatus.NOT_FOUND,
                            message: '/his-connector/api/insurance/getDate gặp lỗi'
                        }
                    });
                    return {
                        dataError: dataError?.errorDetail,
                        status: HttpStatus.NOT_FOUND,
                        req: req.body,
                        message: '/his-connector/api/insurance/getDate gặp lỗi'
                    }
                }
                this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                    uuid: uuidLog,
                    data: {
                        status: HttpStatus.NOT_FOUND,
                        message: dataError?.errorDetail || 'Hệ thống không tìm thấy thông tin.'
                    }
                });
                throw new HttpException(dataError?.errorDetail || 'Hệ thống không tìm thấy thông tin.', HttpStatus.NOT_FOUND);
            }
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'getInsuranceInfoHIS',
                summary: 'Lấy thông tin bảo hiểm bên HIS',
                nameParent: 'getInsuranceDate',
                params,
                errorBody: error.message,
                response: {soucre: data},
                message: error.message || 'Hệ thống không tìm thấy thông tin.',
            });
            this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                uuid: uuidLog,
                data: {
                    status: HttpStatus.NOT_FOUND,
                    message: '/his-connector/api/insurance/getDate gặp lỗi'
                }
            });
            throw new HttpException(error.message || 'Hệ thống không tìm thấy thông tin.', HttpStatus.NOT_FOUND);
        }
        /* kiểm tra tính hợp lệ của bảo hiểm */
        // console.log(data);
        const { insuranceToDate, validatedPartner, maDKBD: maDKBDHT, maThe } = data;
        const validPartnerId = new Set(['leloi', 'dalieuhcm', 'bvmathcm']);
        if (!validatedPartner) {
            if (!validPartnerId.has(partnerId)) { // TODO: chỗ này cho thêm config
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'validatedPartner',
                    summary: 'kiểm tra tính hợp lệ của bảo hiểm ',
                    nameParent: 'getInsuranceDate',
                    params,
                    errorBody: null,
                    response: {soucre: data},
                    message: 'Nơi đăng ký khám bảo hiểm ban đầu của bạn không hợp lệ, Vui lòng kiểm tra lại thông tin.',
                });
                throw new HttpException(
                    'Nơi đăng ký khám bảo hiểm ban đầu của bạn không hợp lệ, Vui lòng kiểm tra lại thông tin.', HttpStatus.BAD_REQUEST);
            }
        }
        /* Kiểm tra xem mã bảo hiểm nay có gắn với 1 cái mã số bệnh nhân hay chưa */
        const objPatientCode: any = {};
        // // partnerId !== 'hoanmytd' && partnerId !== 'binhthanhhcm'
        // const newSetPartnerCheck = new Set().add('hoanmytd').add('binhthanhhcm').add('dalieuhcm')
        // if (!newSetPartnerCheck.has(partnerId)) {
        //     try {

        //         const startFindPatientCode = moment().toDate()
        //         this.logger.log(`START Tìm PatientCOde từ InsuranceCode: ${startFindPatientCode}`)

        //         const findMsbnData = (await this.getPatientExtraByInsuranceCodeHIS(partnerId, insuranceForm.insuranceId).toPromise()).data;

        //         const endFindPatientCode = moment().toDate();
        //         this.logger.log(`END Tìm PatientCOde từ InsuranceCode: ${moment(endFindPatientCode).diff(startFindPatientCode, 'milliseconds')} ms`)


        //         if (findMsbnData.length > 0) {
        //             const resultMsbnData: any = first(findMsbnData);
        //             objPatientCode.patientCode = `${resultMsbnData.SoHS}`.trim();
        //         } else {
        //             this.emitService.emit(LOG_SERVICE_EVENT, {
        //                 name: 'getPatientExtraByInsuranceCodeHIS',
        //                 summary: 'Kiểm tra xem mã bảo hiểm nay có gắn với 1 cái mã số bệnh nhân hay chưa',
        //                 nameParent: 'getInsuranceDate',
        //                 params: { partnerId, insuranceId: insuranceForm.insuranceId },
        //                 errorBody: null,
        //                 response: { findMsbnData, soucre: data },
        //                 message: 'Nơi đăng ký khám bảo hiểm ban đầu của bạn không hợp lệ, Vui lòng kiểm tra lại thông tin.',
        //             });
        //         }
        //     } catch (error) {
        //         const { response } = error;
        //         if (response) {
        //             const { data: dataError, status } = response;
        //             this.emitService.emit(LOG_SERVICE_EVENT, {
        //                 name: 'getPatientExtraByInsuranceCodeHIS',
        //                 summary: 'Kiểm tra xem mã bảo hiểm nay có gắn với 1 cái mã số bệnh nhân hay chưa',
        //                 nameParent: 'getInsuranceDate',
        //                 params: { partnerId, insuranceId: insuranceForm.insuranceId },
        //                 errorBody: dataError,
        //                 response: { soucre: data },
        //                 message: 'Nơi đăng ký khám bảo hiểm ban đầu của bạn không hợp lệ, Vui lòng kiểm tra lại thông tin.',
        //             });
        //         }
        //         this.clientSentry.instance().captureException(error);
        //     }
        // }

        /* kiểm tra thông tin hồ sơ bệnh nhân */
        const patientInfo = await this.patientModel.findOne({ id: insuranceForm.patientId }, { id: true }).exec();
        if (!patientInfo) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'partnerInfo',
                summary: 'kiểm tra thông tin hồ sơ bệnh nhân',
                nameParent: 'getInsuranceDate',
                params: {partnerId:  insuranceForm.patientId},
                errorBody: null,
                response: {patientInfo , source: data},
                message: 'Không tìm thấy thông tin hồ sơ bệnh nhân.',
            });
            throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân.', HttpStatus.NOT_FOUND);
        }

        const patientObj = patientInfo.toObject();
        const patientId = patientObj.id;

        /* kiểm tra chính xác thông tin gửi lên */
        // console.log('ngay sinh tren bao hiem', insuranceForm.birthday);
        const updateObj = {
            birthyear: moment(insuranceForm.birthday, 'YYYY-MM-DD').year(),
            birthdate: moment(insuranceForm.birthday, 'YYYY-MM-DD').format('YYYY-MM-DD'),
        };
        // console.log('object update', updateObj);
        /* cập nhật lại thông tin hồ sơ với ngày sinh theo thẻ bảo hiểm */
        await this.patientModel.findByIdAndUpdate({ _id: patientObj._id }, { ...updateObj }, { new: true });
        /* convert sang ngày */
        // console.log('thong tin sau khi cap nhat', aaaa);
        const toDate = moment(insuranceToDate).format('YYYY-MM-DD');
        const bookingDate = moment(params.bookingDate, 'YYYY-MM-DD').format('YYYY-MM-DD');
        const toDateValueOf = moment(toDate).valueOf();
        const bookingDateValueOf = moment(bookingDate).valueOf();
        const insuranceCode = maThe; // insuranceForm.insuranceId;
        if (toDateValueOf >= bookingDateValueOf) { /* còn hiệu lực */
            /* add thông tin bảo hiểm */
            // console.log('bao hiem con hieu luc');
            const findPatientCode = await this.patientCodeModel.findOne({ partnerId, patientId }).exec();
            const qrCodeContent = await this.handleQrcodeBHYTFromHis(data, { ...patientObj }, partnerId);
            /* cập nhật lại thông tin */
            if (!findPatientCode) { /* thêm mới thông tin */
                const info = new this.patientCodeModel({
                    id: `${partnerId}_${patientId}`,
                    createTime: moment().toDate(),
                    patientId,
                    insuranceCode,
                    insuranceExpiredDate: moment(insuranceToDate).toDate(),
                    partnerId,
                    appId,
                    /* patientCode */
                    ...objPatientCode,
                    maDKBDHT,
                    ...(qrCodeContent && { qrCodeContent }),
                });
                const infoObj = await info.save();

                this.emitService.emit(EVENT_FIND_PATIENT_CODE_BY_MSBN, { infoObj, partnerId, insuranceId: insuranceForm.insuranceId, data, insuranceToDate,
                    patientObj, insuranceCode: maThe, maDKBDHT
                });
            } else {
                const patientCodeObj = findPatientCode.toObject();
                if (Object.keys(objPatientCode).length > 0) {
                    const getPatientCode = get(patientCodeObj, 'patientCode', '');
                    if (getPatientCode) {
                        if (patientCodeObj.patientCode !== getPatientCode) {
                            await this.patientCodeModel.findByIdAndUpdate(
                                { _id: patientCodeObj._id },
                                { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...objPatientCode, ...(qrCodeContent && { qrCodeContent }) },
                            ).exec();
                        } else {
                            await this.patientCodeModel.findByIdAndUpdate(
                                { _id: patientCodeObj._id },
                                { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...(qrCodeContent && { qrCodeContent }) },
                            ).exec();
                        }
                    } else {
                        await this.patientCodeModel.findByIdAndUpdate(
                            { _id: patientCodeObj._id },
                            { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...objPatientCode, ...(qrCodeContent && { qrCodeContent }) },
                        ).exec();
                    }

                } else {
                    await this.patientCodeModel.findByIdAndUpdate(
                        { _id: patientCodeObj._id },
                        { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...(qrCodeContent && { qrCodeContent }) },
                    ).exec();
                }
            }
            const infoFull = await this.getPatientInfoAndPatientCodeById(partnerId, appId, patientObj._id);
            // console.log('lay thong tin cuoi cung', infoFull);
            return {
                ...infoFull,
                expired: false,
                isValid: validatedPartner,
                maDKBDHT,
                message: '',
            };
        } else { /* hết hiệu lực */
            const infoFull = await this.getPatientInfoAndPatientCodeById(partnerId, appId, patientObj._id);
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'validTime',
                summary: 'hết hiệu lực',
                params: { toDateValueOf, bookingDateValueOf },
                nameParent: 'getInsuranceDate',
                errorBody: null,
                response: {infoFull, source : data},
                message: 'Thẻ bảo hiểm hết hạn, vui lòng nhập thẻ khác hoặc chọn lại hình thức khám.',
            });
            return {
                ...infoFull,
                req: req.body,
                expired: true,
                isValid: validatedPartner,
                maDKBDHT,
                message: 'Thẻ bảo hiểm hết hạn, vui lòng nhập thẻ khác hoặc chọn lại hình thức khám.',
            };
        }

    }

    async getInsuranceParseAddress(insuranceForm: GetInsuranceParseAddressDTO): Promise<any> {
        try {
            const params = {
                ...insuranceForm,
            };
            const baseUrl = this.urlConfigService.getBookingTreeUrl();
            const url = `${baseUrl}/his-connector/api/parseAddress`;
            return (await this.getInfoParseAddress(url, params).toPromise()).data;
        } catch (error) {
            return error;
            throw new HttpException('Vui lòng thử lại sau.', HttpStatus.EXPECTATION_FAILED);
        }
    }

    async updatePatientHasPatientCode(updatePatientMongoDTO: UpdatePatientMongoDTO, userId: string, partnerId: string): Promise<any> {
        /* kiểm tra xem bệnh nhân có tồn tại trong user hay không */
        const findPatient = await this.patientModel.findOne({ id: updatePatientMongoDTO.id }).exec();
        if (!findPatient) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân', HttpStatus.NOT_FOUND);
        } else {
            const patientObj = findPatient.toObject();
            const checkValue = await this.checkUMCPatientBelongsToUser(userId, patientObj._id);
            if (checkValue) {
                let updateInfo: any = {};
                // if (!!patientObj.patientCode) {
                // tồn tại mã số bệnh nhân
                const { mobile, email, cmnd, profession_id, country_code,
                    dantoc_id, city_id, district_id, ward_id, address,
                } = updatePatientMongoDTO;
                updateInfo = {
                    mobile, cmnd, email, profession_id, country_code,
                    dantoc_id, city_id, district_id, ward_id, address,
                };
                /* lấy lại city, dantoc, district, ward */
                const findNation = await this.nationModel.findOne({ id: dantoc_id }).exec();
                if (findNation) {
                    const nationObj = findNation.toObject();
                    updateInfo.nation = nationObj._id;
                }
                const findCity = await this.cityModel.findOne({ id: city_id }).exec();
                if (findCity) {
                    const cityObject = findCity.toObject();
                    updateInfo.city = cityObject._id;
                }
                const findDistrict = await this.districtModel.findOne({ id: district_id }).exec();
                if (findDistrict) {
                    const districtObj = findDistrict.toObject();
                    updateInfo.district = districtObj._id;
                }
                const findWard = await this.wardModel.findOne({ id: ward_id }).exec();
                if (findWard) {
                    const wardObj = findWard.toObject();
                    updateInfo.ward = wardObj._id;
                }

                if (!!profession_id && profession_id !== '0') {
                    const findProfession = await this.professionModel.findOne({ id: profession_id }).exec();
                    if (findProfession) {
                        const professionObj = findProfession.toObject();
                        updateInfo.profession = professionObj._id;
                    }
                }
                //  else {
                //     updateInfo
                // }

                /* kiểm tra xem hồ sơ này có medpro id chưa. nếu chưa có thì tạo luôn. */
                if (!!patientObj.code === false) {
                    updateInfo.code = this.generateMedproID(6);
                }
                // tiến hành update patient
                const afterUpdate = await this.patientModel.findByIdAndUpdate(
                    { _id: patientObj._id }, { ...updateInfo },
                    { new: true },
                );
                /* tiến hành sao chép sang patient version */
                await this.copyPatient(afterUpdate);
                return this.getPatientInfoById(patientObj._id, partnerId);
                // }
                // throw new HttpException('Thông tin bệnh nhân này chưa có số hồ sơ bệnh nhân.', HttpStatus.BAD_REQUEST);
            }
        }
    }

    // async updatePatientHasPatientCode(updatePatientMongoDTO: UpdatePatientMongoDTO, userId: string): Promise<any> {
    //     /* kiểm tra xem bệnh nhân có tồn tại trong user hay không */
    //     const findPatient = await this.patientModel.findOne({ id: updatePatientMongoDTO.id }).exec();
    //     if (!findPatient) {
    //         throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân', HttpStatus.NOT_FOUND);
    //     } else {
    //         const patientObj = findPatient.toObject();
    //         const checkValue = await this.checkUMCPatientBelongsToUser(userId, patientObj._id);
    //         if (checkValue) {
    //             let updateInfo: any = {};
    //             if (!!patientObj.patientCode) {
    //                 // tồn tại mã số bệnh nhân
    //                 const { mobile, email } = updatePatientMongoDTO;
    //                 updateInfo = { mobile, email };
    //                 /* kiểm tra xem hồ sơ này có medpro id chưa. nếu chưa có thì tạo luôn. */
    //                 if (!!patientObj.code === false) {
    //                     updateInfo.code = this.generateMedproID(6);
    //                 }
    //                 // tiến hành update patient
    //                 await this.patientModel.findByIdAndUpdate({ _id: patientObj._id }, { ...updateInfo });
    //                 return this.getPatientInfoById(patientObj._id);
    //             }
    //             throw new HttpException('Thông tin bệnh nhân này chưa có số hồ sơ bệnh nhân.', HttpStatus.BAD_REQUEST);
    //         }
    //     }
    // }

    async copyPatient(afterUpdate: any): Promise<any> {
        /* tiến hành sao chép sang patient version */
        const { _id: idOld, id: idPatientRef,
            createdAt: ngaycreate, updatedAt: ngayudpate, __v: vCurrent, ...restPatientCopy } = afterUpdate.toObject();
        const newCopy = new this.patientVersionModel({ ...restPatientCopy });
        newCopy.id = uuid.v4().replace(/-/g, '');
        newCopy.patientId = idPatientRef;
        return newCopy.save();
    }

    async checkPatientSyncHISOrBookings(
        appId: string, partnerId: string, user: UserInfoVerifyTokenDTO, patient: IPatient, patientFormData: PatientFormMongoUpdateDTO, isCS = false) {

        // return {}
        /* kiểm tra xem hồ sơ này có link v1 chưa */
        let isUpdateFull = true;
        const idUMCV1 = get(patient, 'patientIdV1UMC', 0);
        const idND1V1 = get(patient, 'patientIdV1', 0);

        const fullNameV2 = `${patient.surname} ${patient.name}`.toUpperCase();
        const birthYearV2 = `${patient.birthyear}`;
        const genderV2 = `${patient.sex}`;
        if (!isCS) {
            if (idUMCV1 > 0) {
                /* kiểm tra xem có booking chưa */
                const getUMCBookingV1 = await this.pkhPatientKnex('booking').where({ patient_id: idUMCV1 }).whereIn('status', [1, 2, -2]);
                if (getUMCBookingV1.length > 0) {
                    isUpdateFull = false;
                    // throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
                }

                /* kiem tra lai xem hồ sơ v2, v1 có giống nhau ko */
                const getPatientV1 = await this.pkhPatientKnex('patient').where({ id: idUMCV1 }).first();
                if (!getPatientV1) {
                    /* un set cái patientIdV1UMC */
                    await this.patientModel.findByIdAndUpdate({ _id: patient._id }, {
                        patientIdV1UMC: 0,
                    }).exec();

                    throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
                }
                /* kiem tra ho va ten */
                const fullnameV1 = `${getPatientV1.surname} ${getPatientV1.name}`.toUpperCase();
                const birthYearV1 = `${getPatientV1.birthyear}`;
                const genderV1 = `${getPatientV1.sex}`;

                const checkUMC = fullNameV2 === fullnameV1 && birthYearV2 === birthYearV1 && genderV2 === genderV1;
                if (!checkUMC) {
                    throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
                }

            }

            if (idND1V1 > 0) {
                /* kiểm tra xem có booking chưa */
                const getND1BookingV1 = await this.pkhPatientKnex('nd1_booking').where({ nd1_patient_id: idND1V1 }).whereIn('status', [1, 2, -2]);
                if (getND1BookingV1.length > 0) {
                    isUpdateFull = false;
                    // throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
                }
                /* kiem tra lai xem hồ sơ v2, v1 có giống nhau ko */
                const getPatientV1 = await this.pkhPatientKnex('nd1_patient').where({ id: idND1V1 }).first();
                if (!getPatientV1) {
                    /* un set cái patientIdV1UMC */
                    await this.patientModel.findByIdAndUpdate({ _id: patient._id }, {
                        patientIdV1: 0,
                    }).exec();

                    throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
                }
                /* kiem tra ho va ten */
                const fullnameV1 = `${getPatientV1.surname} ${getPatientV1.name}`.toUpperCase();
                const birthYearV1 = `${getPatientV1.birthyear}`;
                const genderV1 = `${getPatientV1.sex}`;

                const checkUMC = fullNameV2 === fullnameV1 && birthYearV2 === birthYearV1 && genderV2 === genderV1;
                if (!checkUMC) {
                    throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
                }

            }
        }

        const dataV1: any = { isUpdateFull: true };

        try {

            const config = await this.globalSettingService.findByKeyAndRepoName('CONFIG_CHECK_PATIENT_BOOKING_SUCCESS');
            const configObj = JSON.parse(config);
            if (configObj?.env === 'ON' && !isCS) {
                const [patientCodes, booking, bookingCancel] = await Promise.all([
                    this.getPatientCodeByPatientIdIgnorePartnerId(patient.id),
                    this.bookingModel.find({ status: { $in: [1, 2] }, patientId: patient.id }).limit(1).exec(),
                    this.bookingModel.find({ status: -2, paymentStatus: 2, patientId: patient.id }).limit(1).exec(),
                ]);
                if (patientCodes || (booking.length + bookingCancel.length) > 0) {
                    isUpdateFull =  false;
                }
            }

            if (!isUpdateFull) {
                patientFormData.city_id = patient.city_id;
                patientFormData.dantoc_id = patient.dantoc_id;
            }
            dataV1.isUpdateFull = isUpdateFull;
            if (idUMCV1 > 0) {
                dataV1.umc = await this.updatePatient_Nhidong1('umc', appId, user, patientFormData, isUpdateFull);
            }
            if (idND1V1 > 0) {
                dataV1.nhidong1 = await this.updatePatient_Nhidong1('nhidong1', appId, user, patientFormData, isUpdateFull);
            }
        } catch (error) {
            console.log(error);
            throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
        }

        return dataV1;
    }

    async upatePatientWithoutPatientCode(
        partnerId: string, appId: string, user: UserInfoVerifyTokenDTO, patientFormData: PatientFormMongoUpdateDTO, cskhInfo?: CSKHTokenVerifyDTO,
        locale?: string): Promise<any> {
        if (patientFormData.mobileLocaleIso === 'vi-VN') {
            patientFormData.mobile = patientFormData.mobile.replace(/^\+84/, '0');
        }

        if (patientFormData.relativeMobileLocaleIso === 'vi-VN' && patientFormData.relative_mobile) {
            patientFormData.relative_mobile = patientFormData.relative_mobile.replace(/^\+84/, '0');
        }

        if (!patientFormData?.birthdate) {
            patientFormData.birthdate = null
        }

        const envDisableUpdateDeletePatient = await this.globalSettingService.findByKeyAndRepoName('ENV_UPDATE_DELETE_PATIENT');
        const countBooking = await this.bookingModel.count({ partnerId: 'bvmathcm', patientId: patientFormData.id, status: { $in: [1, 2] } }).exec();

        const booking =  await this.bookingModel.findOne({ id: patientFormData.bookingId }, { patientId: true, partnerId: true }).exec();

        let allowUpdateBvMat = false;
        if (booking?.partnerId === 'bvmathcm') {
            // nếu mà có booking bvmat thì check cùng patientId, cung cho update, k thôi.
            allowUpdateBvMat = booking?.patientId === patientFormData.id;
        }

        const isCS = await this.userService.isCs(cskhInfo?.cskhUserId || user.userMongoId);
        if(isCS){
            allowUpdateBvMat = true;
        }

        if (!allowUpdateBvMat && countBooking >= 1 && envDisableUpdateDeletePatient === 'ON') {
            throw new HttpException('Để bảo mật thông tin bệnh nhân - Vui lòng liên hệ 19002115 để chỉnh sửa hồ sơ', HttpStatus.BAD_REQUEST);
        }

        /* kiểm tra xem bệnh nhân có tồn tại trong user hay không */
        const findPatient = await this.patientModel.findOne({ id: patientFormData.id }).exec();
        let stepInfo: any = {};
        if (!findPatient) {
            throw new HttpException({
                key: 'PATIENT_MESSAGE_ERROR_NOT_FOUND',
                locale,
            }, HttpStatus.NOT_FOUND);
        } else {
            const patientObj = findPatient.toObject();

            /* kiem tra xem */
            const checkValue = await this.checkUMCPatientBelongsToUser(user.userMongoId, patientObj._id);
            if (!checkValue) {
                throw new HttpException('Thao tác không thành công. Vui lòng liên hệ 19002115 để được hỗ trợ.', HttpStatus.NOT_FOUND);
            }

            const patientTrackingData : PatientTrackingData = {
                action: "UPDATE",
                patient: findPatient._id,
                userAction: cskhInfo?.cskhUserId || user.userMongoId,
                userPatient: cskhInfo?.userIdPatient || user.userMongoId,
                dataBefore: cloneDeep(patientObj),
            }

            const { relative_name, relative_email, relative_mobile, relative_type_id, id, bookingId, ...patientInfo } = patientFormData;
            const replacerBirthYear = moment(patientInfo?.birthdate).isValid() ? moment(patientInfo.birthdate).year() : '';
            // const medproId = await this.patientService.generateUMCMedproID();
            const patientUpdate: any = {
                ...patientInfo,
                birthdate: moment(patientInfo.birthdate).isValid() ? moment(patientInfo.birthdate).format('YYYY-MM-DD') : '',
                // code: medproId,
                sourceId: appId,
                partnerId,
                userId: user.userMongoId,
                ...(replacerBirthYear && { birthyear: replacerBirthYear }),
            };

            /* bổ sung cho update thẳng 1 số field */
            const installUpdate: any = {
                address: patientFormData.address,
            };

            const findCity = await this.cityModel.findOne({ id: patientUpdate.city_id }).exec();
            if (findCity) {
                const cityObject = findCity.toObject();
                patientUpdate.city = cityObject._id;
                patientUpdate.city_id = cityObject.id;
            }
            const findDistrict = await this.districtModel.findOne({ id: patientUpdate.district_id }).exec();
            if (findDistrict) {
                const districtObj = findDistrict.toObject();
                patientUpdate.district = districtObj._id;
                patientUpdate.district_id = districtObj.id;
            }
            const findWard = await this.wardModel.findOne({ id: patientUpdate.ward_id }).exec();
            if (findWard) {
                const wardObj = findWard.toObject();
                patientUpdate.ward = wardObj._id;
                patientUpdate.ward_id = wardObj.id;
            }

            if (findPatient.city_id === patientFormData.city_id) {

                installUpdate.district = patientUpdate.district;
                installUpdate.district_id = patientUpdate.district_id;

                installUpdate.ward = patientUpdate.ward;
                installUpdate.ward_id = patientUpdate.ward_id;

                // await this.patientModel.findByIdAndUpdate(
                //     { _id: findPatient._id },
                //     { ...installUpdate },
                // );
            }

            // check update patient
            
            // if (!isCS)
            try {

                if (!!patientUpdate.profession_id && patientUpdate.profession_id !== '0') {
                    const findProfession = await this.professionModel.findOne({ id: patientUpdate.profession_id }).exec();
                    if (findProfession) {
                        const professionObj = findProfession.toObject();
                        patientUpdate.profession = professionObj._id;
                    }
                } else {
                    patientUpdate.profession_id = '';
                }
                /* tìm lại thông tin */
                const findCountry = await this.countryModel.findOne({ code: patientUpdate.country_code }).exec();
                if (findCountry) {
                    const countryObj = findCountry.toObject();
                    patientUpdate.country = countryObj._id;
                    patientUpdate.country_id = countryObj.id;
                }

                const findNation = await this.nationModel.findOne({ id: patientUpdate.dantoc_id }).exec();
                if (findNation) {
                    const nationObj = findNation.toObject();
                    patientUpdate.nation = nationObj._id;
                    patientUpdate.dantoc_id = nationObj.id;
                }

                /* kiểm tra xem hồ sơ này có medpro id chưa. nếu chưa có thì tạo luôn. */
                if (!!patientObj.code === false) {
                    patientUpdate.code = this.generateMedproID(6);
                }
                /* kiểm tra xem có thông tin thân nhân hay ko */
                if (patientFormData.relative_type_id) {
                    patientUpdate.relation = {
                        relative_name: patientFormData.relative_name,
                        relative_email: patientFormData.relative_email,
                        relative_mobile: patientFormData.relative_mobile,
                        relativeMobileLocaleIso: patientFormData.relativeMobileLocaleIso,
                        relative_type_id: patientFormData.relative_type_id,
                    };
                }

                stepInfo = {
                    ...stepInfo,
                    stepName: 'Chuẩn bị thao tác update hồ sơ xuống V1.',
                };

                const v1Info: { isUpdateFull: boolean, umc: {}, nhidong1: {} } =
                    await this.checkPatientSyncHISOrBookings(appId, partnerId, user, findPatient, patientFormData, isCS);
                let updateFinal: any = {};
                if (!v1Info.isUpdateFull) {
                    const { name, surname, sex, birtdate, birtyear, dantoc_id, nation, city_id, city, ...restUpdateFinal } = patientUpdate;
                    updateFinal = restUpdateFinal;
                } else {
                    updateFinal = patientUpdate;
                }
                // let dataPatientV1:any;
                // if (this.listAppId.has(appId)) {
                //     /* tiến hành cập nhật sang v1 */
                //     const newSet = this.utilService.oldHospitalSync();
                //     if (!!partnerId && newSet.has(partnerId)) {
                //         dataPatientV1 = await this.updatePatient_Nhidong1(partnerId, appId, user, patientFormData);
                //     } else {
                //         if (appId === 'medpro') {
                //             const validation = await this.checkBookingND1OfPatient(findPatient._id);
                //             if (validation) {
                //                 // default update for nhidong1
                //                 dataPatientV1 = await this.updatePatient_Nhidong1('nhidong1', appId, user, patientFormData);
                //             }
                //         }
                //     }
                // }
                const trackings = [
                    {
                        name: 'thao tác update hồ sơ xuống V1 thành công',
                    },
                ];
                stepInfo = {
                    ...stepInfo,
                    trackings: [
                        ...trackings,
                    ],
                    stepName: 'Chuẩn bị thao tác update hồ sơ xuống V2.',
                };

                // await this.syncPatientHisAfterUpdate(isCS, user.userMongoId, findPatient?.id, partnerId);

                if (booking?.partnerId === 'bvmathcm') {
                    updateFinal = pick(updateFinal, [
                        'city',
                        'city_id',
                        'district',
                        'district_id',
                        'ward',
                        'ward_id',
                        'address',
                        'cmnd'
                    ])
                }
                const afterUpdate = await this.updatePatientInfo(findPatient._id, updateFinal);

                // Emit event to link pending health data if CMND has changed
                if (patientFormData?.cmnd && patientFormData.cmnd !== patientObj.cmnd) {
                    this.emitService.emit(EVENT_LINK_PENDING_HEALTH_DATA, {
                        cmnd: patientFormData.cmnd,
                        patientId: patientObj.id,
                        userId: user.userMongoId,
                        timestamp: new Date(),
                        requestId: `auto_link_update_${patientObj.id}_${Date.now()}`
                    });
                }

                this.emitService.emit(PATIENT_TRACKING_EVENT, {
                    ...patientTrackingData,
                    dataAfter: afterUpdate.toObject()
                })

                /* tiến hành sao chép sang patient version */
                stepInfo = {
                    ...stepInfo,
                    trackings: [
                        ...stepInfo.trackings,
                        {
                            name: 'thao tác update hồ sơ xuống V2 thành công',
                        },
                    ],
                    stepName: 'Chuẩn bị thao tác copy patient.',
                };

                const patientVersion = await this.copyPatient(afterUpdate);

                // if (partnerId === 'nhidong1' && dataPatientV1 && patientVersion) {
                stepInfo = {
                    ...stepInfo,
                    trackings: [
                        ...stepInfo.trackings,
                        {
                            name: 'thao tác copy patient thành công',
                        },
                    ],
                    stepName: 'Chuẩn bị thao tác update patient version vào trong booking tiếp theo.',
                };
                await this.updatePaitentVersionBooking(findPatient._id, patientVersion._id, patientVersion.id);
                // }

                if (patientFormData?.insuranceId) {
                    await this.insertPatientBHYT(appId, partnerId, afterUpdate, patientFormData.insuranceId);
                }

                if (patientFormData?.relationType && !isEmpty(patientFormData?.relationType)) {
                    const { relationType: id } = patientFormData;
                    const relation = await this.relativeModel.findOne({ id }).exec();
                    const dataRelation = {
                        user: user.userMongoId,
                        // patientId: afterUpdate.id,
                        patient: findPatient._id,
                        relationTypeId: relation.id,
                        relationType: relation._id,
                    };

                    const findPatientRelation = await this.patientRelationModel.findOne({ patient: findPatient._id }).exec();
                    if (findPatientRelation) {
                        await this.patientRelationModel.findByIdAndUpdate(findPatientRelation._id, dataRelation);
                    } else {
                        const newPatientRelation = new this.patientRelationModel(dataRelation);
                        await newPatientRelation.save();
                    }
                }

                return this.getPatientInfoById(patientObj._id, partnerId);

            } catch (error) {
                console.log(error);
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'trackingUpdatePatientError',
                    summary: 'Lỗi khi cập nhật hồ sơ.',
                    nameParent: 'updatePatientNhiDong1',
                    params: {
                        stepInfo,
                        appId,
                        partnerId,
                        user,
                        cskhInfo
                    },
                    errorBody: this.utilService.errorHandler(error),
                    response: null,
                    message: 'Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.',
                });
                throw new HttpException({
                    key: 'BAD_REQUEST_MESSAGE_ERROR',
                    locale,
                }, HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }
    }

    async updatePatient_Nhidong1(
        partnerId: string, appId: string, user: UserInfoVerifyTokenDTO, patientFormData: PatientFormMongoUpdateDTO, isUpdateFull = true): Promise<any> {
        try {
            /* tìm lại user_id và access_token */
            const userMongo = await this.userModel.findById({ _id: user.userMongoId }, { username: true, medproId: true }).exec();
    
            /* tiến hành chuyển đổi */
            const baseUrl = this.urlConfigService.getBookingTreeUrl();
            const urlTransform = `${baseUrl}/his-connector/api/convertPartnerData`;
            const findCountry = await this.countryModel.findOne({ code: patientFormData.country_code }).exec();
            const params: any = {
                fromPartnerId: 'medpro',
                toPartnerId: partnerId,
                countryId: '',
                cityId: `${patientFormData.city_id}`,
                districtId: `${patientFormData.district_id}`,
                wardId: `${patientFormData.ward_id}`,
                nationId: `${patientFormData.dantoc_id}`,
                relationTypeId: `${patientFormData.relative_type_id}`,
            };
            if (findCountry) {
                const objCountry = findCountry.toObject();
                params.countryId = `${objCountry.id}`; // override countryId
            }
            const dataKeys: any = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
            const urlGetPatients = this.oldUrl.NhiDong1UpdatePatients();
            const url = `${urlGetPatients}`;
            /* tìm lại patientIdV1 */
            const findPatient = await this.patientModel.findOne({ id: patientFormData.id }).exec();
            if (!findPatient) {
                throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân', HttpStatus.NOT_FOUND);
            }
            const keyPatientV1 = this.utilService.patientIdV1Key(partnerId);
            /* lấy patient_id của v1 */
            const patientId = findPatient[keyPatientV1] || 0;
    
            if (patientId === 0) {
                return null;
            }
    
            /* tìm lại chính xác hồ sơ - user nao */
            let userV1:any = {};
            const listUserRe = await this.pkhPatientKnex('user')
                .whereIn('username', [`${userMongo.medproId}`, `${userMongo.username}`]);
    
            if (!listUserRe) {
                throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
            }
    
            /* kiểm tra xem patientId này thuộc user nào */
            if (listUserRe.length === 1) {
                const firstUser: any = first(listUserRe);
                userV1 = await this.pkhPatientKnex('user')
                    .where('id', firstUser?.id || 0).first();
    
            } else {
                if (partnerId === 'umc') {
                    const pluckUserId = map(listUserRe, 'id');
                    const getUserPatient = await this.pkhPatientKnex('user_patient')
                        .where({
                            patient_id: patientId,
                        })
                        .whereIn('user_id', pluckUserId).first();
    
                    userV1 = await this.pkhPatientKnex('user')
                        .where('id', getUserPatient?.user_id || 0).first();
                } else if(partnerId === 'nhidong1'){
                    const pluckUserId = map(listUserRe, 'id');
                    const getUserPatient = await this.pkhPatientKnex('nd1_user_patient')
                        .where({
                            nd1_patient_id: patientId,
                        })
                        .whereIn('user_id', pluckUserId).first();
    
                    userV1 = await this.pkhPatientKnex('user')
                        .where('id', getUserPatient?.user_id || 0).first();
                }
            }
    
            if(!userV1){
                return null;
            }
    
            let session: any;
            switch (partnerId) {
                case 'nhidong1':
                    session = await this.sessionService.checkExistsNhiDong1SessionByUserID(userV1.id);
                    break;
                case 'dalieuhcm':
                    session = await this.sessionService.checkExistsSkinSessionByUserID(userV1.id);
                    break;
                case 'ctchhcm':
                    session = await this.sessionService.checkExistsCTCHSessionByUserID(userV1.id);
                    break;
                case 'thuduc':
                    session = await this.sessionService.checkExistsThuDucSessionByUserID(userV1.id);
                    break;
                case 'umc':
                    session = await this.sessionService.checkExistsUMCSessionByUserID(userV1.id);
                    break;
                default:
                    session = {
                        user_id: 0,
                        access_token: '',
                    };
                    break;
            }
    
            const updateParams = {
                user_id: session.user_id,
                access_token: session.access_token,
                patient_id: patientId, // id của patient v1
                app: appId,
                name: patientFormData.name,
                surname: patientFormData.surname,
                sex: patientFormData.sex,
                birthdate: moment(patientFormData.birthdate).isValid() ? moment(patientFormData.birthdate).format('YYYY-MM-DD') : '',
                // tslint:disable-next-line: max-line-length
                birthyear: moment(patientFormData.birthdate).isValid() ? moment(patientFormData.birthdate).get('years') : Number(patientFormData.birthyear),
                cmnd: patientFormData.cmnd,
                mobile: patientFormData.mobile,
                email: patientFormData.email,
                dantoc_id: dataKeys.nationId,
                country_code: dataKeys.countryId,
                district_id: dataKeys.districtId,
                city_id: dataKeys.cityId,
                ward_id: dataKeys.wardId,
                address: patientFormData.address,
                relative_type_id: dataKeys.relationTypeId,
                relative_name: patientFormData.relative_name,
                relative_mobile: patientFormData.relative_mobile,
                relative_email: patientFormData.relative_email,
            };
    
            if (patientFormData.country_code !== 'VIE') {
                updateParams.city_id = partnerId === 'nhidong1' ? 999 : 99;
            }
    
            if (!isUpdateFull) {
                updateParams.name = findPatient.name;
                updateParams.surname = findPatient.surname;
                updateParams.sex = findPatient.sex;
                updateParams.birthdate = findPatient.birthdate;
                updateParams.birthyear = findPatient.birthyear;
                // updateParams.city_id = findPatient.city_id;
                // updateParams
            }
            // console.log('updateParams', updateParams);
            try {
                const resultUpdateV1 = (await this.updatePatientNhiDong1(url, updateParams, partnerId).toPromise()).data;
                // console.log(resultUpdateV1)
                const getErrorMessage = get(resultUpdateV1, 'error_message', '');
                if (!!getErrorMessage) {
    
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: `error_updatePatientV1Failed`,
                        summary: 'Lỗi khi cập nhật xuống v1.',
                        nameParent: 'updatePatientNhiDong1',
                        params: {
                            url, updateParams, partnerId,
                        },
                        errorBody: resultUpdateV1,
                        response: resultUpdateV1,
                        message: `error_mesasge trong response`,
                    });
    
                    throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
                }
                return resultUpdateV1;
            } catch (error) {
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'updatePatientV1Failed',
                    summary: 'Lỗi khi cập nhật xuống v1.',
                    nameParent: 'updatePatientNhiDong1',
                    params: {
                        url, updateParams, partnerId
                    },
                    errorBody: this.utilService.errorHandler(error),
                    response: null,
                    message: 'Catch:',
                });
                throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
            }
        } catch (error) {
            console.log('updatePatient_Nhidong1 err', error);
        }
    }

    async syncPatientNhiDong1(partnerId: string, appId: string, userId: any, patientInfo: any): Promise<any> {
        let data: any = {};
        const params = {
            partnerId,
            appId,
            ...userId,
            ...patientInfo,
        };
        /* tiến hành sync user neu chua co. */
        try {
            const { relation } = patientInfo;
            const obj: any = {};
            if (relation) {
                const { relative_name, relative_mobile, relative_type_id, relative_email } = relation;
                obj.relative_name = relative_name;
                obj.relative_mobile = relative_mobile;
                obj.relative_type_id = relative_type_id;
                obj.relative_email = relative_email;
            }

            if (!!partnerId) {
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'insertPatient_Nhidong1',
                    summary: 'P111',
                    nameParent: 'syncPatientNhiDong1_Params',
                    params: {
                        ...patientInfo,
                        ...obj,
                    },
                    errorBody: null,
                    response: null,
                    message: '',
                });
                const result = await this.insertPatient_Nhidong1(partnerId, appId, {
                    id: 0,
                    userMongoId: userId,
                }, {
                    ...patientInfo,
                    ...obj,
                });
                data = result;
                // console.log('sync thong tin v1', result);
                // console.log('patientFormData đồng bộ', patientInfo);
                const patientIdV1 = result.id || 0;
                const keyPatientV1 = this.utilService.patientIdV1Key(partnerId);
                await this.patientModel.findOneAndUpdate({ id: patientInfo.id }, { [keyPatientV1]: patientIdV1 }).exec();
                return {
                    patientIdV1
                };
            }
        } catch (error) {
            console.log(error);
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'insertPatient_Nhidong1',
                summary: 'Insert Patient Info',
                nameParent: 'syncPatientNhiDong1',
                params,
                errorBody: this.utilService.errorHandler(error),
                response: data,
                message: 'Đồng bộ hồ sơ không thành công',
            });
            throw new HttpException('Đồng bộ hồ sơ không thành công', HttpStatus.BAD_REQUEST);
        }
    }

    async insertPatient_Nhidong1(partnerId: string, appId: string,
                                 user: UserInfoVerifyTokenDTO, patientFormData: PatientFormMongoDTO): Promise<any> {
        // console.log('chay vao insert')
        /* tìm lại user_id và access_token */
        const userMongo = await this.userModel.findById({ _id: user.userMongoId }, { username: true, medproId: true }).exec();
        // console.log('userMongo', userMongo);
        await this.userService.getUserIdAndCreateSessionV1(appId, partnerId, userMongo);
        const userV1 = await this.checkExistsUserByUsername(userMongo.medproId);
        let session: any;
        // await this.userService.forEachOldHospital(userV1.id); /* Tiến hành tạo session */
        switch (partnerId) {
            case 'nhidong1':
                session = await this.sessionService.checkExistsNhiDong1SessionByUserID(userV1.id);
                break;
            case 'dalieuhcm':
                session = await this.sessionService.checkExistsSkinSessionByUserID(userV1.id);
                break;
            case 'ctchhcm':
                session = await this.sessionService.checkExistsCTCHSessionByUserID(userV1.id);
                break;
            case 'thuduc':
                session = await this.sessionService.checkExistsThuDucSessionByUserID(userV1.id);
                break;
            case 'umc':
                session = await this.sessionService.checkExistsUMCSessionByUserID(userV1.id);
                break;
            default:
                session = {
                    user_id: 0,
                    access_token: '',
                };
                break;
        }

        /* tiến hành chuyển đổi */
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const urlTransform = `${baseUrl}/his-connector/api/convertPartnerData`;
        const findCountry = await this.countryModel.findOne({ code: patientFormData.country_code }).exec();
        const params: any = {
            fromPartnerId: 'medpro',
            toPartnerId: partnerId,
            countryId: '',
            cityId: `${patientFormData.city_id}`,
            districtId: `${patientFormData.district_id}`,
            wardId: `${patientFormData.ward_id}`,
            nationId: `${patientFormData.dantoc_id}`,
            relationTypeId: `${patientFormData.relative_type_id}`,
        };
        if (findCountry) {
            const objCountry = findCountry.toObject();
            params.countryId = `${objCountry.id}`;
        }
        let dataKeys: any = {};
        try {
            dataKeys = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
        } catch (error) {
            await this.sentryService.captureError(error,{
                url: urlTransform,
                medthod:"POST",
                user:{
                    id: user?.id,
                    userMongoId: user?.userMongoId
                },
                body: {
                    urlTransform,
                    params,
                    dataKeys,
                }
            }, 'high');
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'insertPatient_Nhidong1',
                summary: 'Insert Patient Info',
                nameParent: 'insertPatient',
                params: {
                    urlTransform,
                    params,
                    dataKeys,
                },
                errorBody: this.utilService.errorHandler(error),
                errorCode: 'insertPatient_TRANSOFMR_01',
                response: null,
                message: error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.',
            });
        }
        const urlGetPatients = this.oldUrl.NhiDong1InsertPatients();
        /* append thêm bv_id đối với trường hợp tạo hồ sơ tái khám của nhidong1 */
        const objTaiKham: any = {};
        const findGatientCode = await this.patientCodeModel.findOne({
            patientId: patientFormData?.id && !isEmpty(patientFormData?.id) ? patientFormData.id : 'NO_ID',
            partnerId,
            appId,
        }).exec();
        if (findGatientCode && findGatientCode?.patientCode) {
            objTaiKham.bv_id = findGatientCode.patientCode;
        }
        // if (patientFormData.patientCode) {
        //     objTaiKham.bv_id = patientFormData.patientCode;
        // }
        const insertParams = {
            user_id: session.user_id,
            access_token: session.access_token,
            app: appId,
            name: patientFormData.name,
            surname: patientFormData.surname,
            sex: patientFormData.sex,
            birthdate: moment(patientFormData.birthdate).isValid() ? moment(patientFormData.birthdate).format('YYYY-MM-DD') : '',
            // tslint:disable-next-line: max-line-length
            birthyear: moment(patientFormData.birthdate).isValid() ? moment(patientFormData.birthdate).get('years') : Number(patientFormData.birthyear),
            cmnd: patientFormData.cmnd,
            mobile: patientFormData.mobile,
            email: patientFormData.email,
            dantoc_id: dataKeys.nationId,
            country_code: dataKeys.countryId,
            district_id: dataKeys.districtId,
            ward_id: dataKeys.wardId,
            city_id: dataKeys.cityId,
            address: patientFormData.address,
            relative_type_id: dataKeys.relationTypeId,
            relative_name: patientFormData.relative_name,
            relative_mobile: patientFormData.relative_mobile,
            relative_email: patientFormData.relative_email,
            ...objTaiKham,
        };
        this.emitService.emit(LOG_SERVICE_EVENT, {
            name: 'insertPatient_Nhidong1',
            summary: 'P22222',
            nameParent: 'insertPatient',
            params: {
                urlTransform,
                insertParams,
                patientFormData,
            },
            errorBody: null,
            errorCode: '',
            response: null,
            message: '',
        });
        try {
            return (await this.insertPatientNhiDong1(urlGetPatients, insertParams, partnerId).toPromise()).data;
            // console.log(resultData);
        } catch (error) {
            await this.sentryService.captureError(error,{
                url: urlGetPatients,
                medthod:"POST",
                user:{
                    id: user?.id,
                    userMongoId: user?.userMongoId
                },
                body: {
                    urlTransform,
                    insertParams,
                    patientFormData,
                }
            }, 'high');
        }
    }

    getPatientByPhoneOrCMNDByHIS(partnerId: string, Phone: string = '', identifyId: string = ''): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/his-connector/api/patient/getByPatientDetail`;
        const params = {
            partnerId,
            Phone,
            identifyId,
        };
        return this.httpService.post(url, params);
    }

    getPatientsByCccdPartnerId(cccd: string, partnerId: string): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/his-connector/api/patient/getByPatientDetailByIdentify`;
        return this.httpService.post(url, {
            identifyId: cccd,
            partnerId,
        });
    }

    async testFindPhoneCmndHIS(partnerId: string, phone: string = '', cmnd: string = ''): Promise<any> {
        try {
            const data = (await this.getPatientByPhoneOrCMNDByHIS(partnerId, phone, cmnd).toPromise()).data;
            return data;
        } catch (error) {
            console.log(error);
        }

    }

    async findPatientByCmndHis(partnerId: string, appId: string = '', body: any): Promise<any> {
        try {
            const { fullName = '', birthday = '', insuranceCode = '', cccd = ''  } = body;
            let data: any = {};
            let addressInfo: any = {};
            let cityName: string = '';
            let districtName: string = '';
            let wardName: string = '';
            const params = {
                fullName,
                cccd,
                birthday: birthday ? moment(birthday, 'DD/MM/YYYY').utc().format('YYYY-MM-DD') : null,
                partnerId,
            };
            try {
                const patientInsuranceCccd = await this.patientInsuranceCccdModel.findOne({ cccd }).exec();
                if (patientInsuranceCccd) {
                    const patientInsuranceCccdObj = patientInsuranceCccd.toObject();
                    const limitDaysConfig = await this.globalSettingService.findByKeyAndRepoName('STAY_IN_CHECK_BHYT_LIMIT');
                    const tenDaysAfterCreatedAt = moment(patientInsuranceCccdObj.createdAt).add(+limitDaysConfig, 'days');
                    const today = moment();
                    const validData = today.isSameOrBefore(tenDaysAfterCreatedAt, 'day');
                    if (validData) {
                        return patientInsuranceCccdObj;
                    }
                }

                const baseUrl = this.urlConfigService.getBookingTreeUrl();
                const url = `${baseUrl}/his-connector/api/insurance/search`;
                const startHisConnector = moment().toDate()
                this.logger.log(`START tra cứu: ${startHisConnector}`)
                data = (await this.getCccdInfoHIS(url, params).toPromise()).data;

                if (data?.code === 1) {
                    const { diaChi, ngaySinh, gioiTinh, hoTen, ...remain } = data;
                    const { name, surname } = this.utilService.splitName(hoTen);
                    const sex = gioiTinh === 'Nam' ? 1 : gioiTinh === 'Nữ' ? 0 : null

                    if (data?.diaChi) {
                        try {
                            addressInfo = await this.getInsuranceParseAddress({ address: data?.diaChi });
                            const diachiList = data?.diaChi?.split(',') || [];
                            cityName = diachiList[3] || '';
                            districtName = diachiList[2] || '';
                            wardName = diachiList[1] || '';
                        } catch (error) {
                            console.log('error', error);
                        }
                    }
    
                    const { districtId = '', cityId = '', wardId = '', address, ...addressRemain } = addressInfo;
    
                    console.log('data', { ...data, ...addressInfo });

                    const newData = {
                        cccd,
                        fullname: hoTen,
                        surname,
                        name,
                        birthdate: ngaySinh,
                        sex,
                        address,
                        ward_id: wardId,
                        district_id: districtId,
                        city_id: cityId,
                        maThe: data.maThe,
                        hanThe: data.hanThe,
                        expiredDate: data.gtTheDen,
                    }
    
                    try {
                        const patientInsuranceCccd = new this.patientInsuranceCccdModel(newData);
                        const result = await patientInsuranceCccd.save();
                        return { ...result.toObject(), ...remain, ...addressRemain, cityName, districtName, wardName };
                    } catch (error) {
                        const patientInsuranceCccd = await this.patientInsuranceCccdModel.findOneAndUpdate({ cccd }, { ...newData }, { new: true }).exec();
                        if (patientInsuranceCccd) {
                            const result = patientInsuranceCccd.toObject();
                            return { ...result, ...remain, ...addressRemain, cityName, districtName, wardName };
                        }
                        throw new HttpException(error.message || 'Hệ thống không tìm thấy thông tin.', HttpStatus.NOT_FOUND);
                    }
                } else {
                    throw new HttpException('Hệ thống không tìm thấy thông tin.', HttpStatus.NOT_FOUND);
                }
            } catch (error) {
                const { response } = error;
                if (response) {
                    const { data: dataError, status } = response;
                    if (status === HttpStatus.NOT_FOUND) {
                        const { errorDetail } = dataError;
                        if (dataError?.checkCode && dataError?.crossFacilityMessage) {
                            this.emitService.emit(LOG_SERVICE_EVENT, {
                                name: 'getInsuranceInfoHIS',
                                summary: 'Lấy thông tin bảo hiểm bên HIS',
                                nameParent: 'getInsuranceDate',
                                params,
                                errorBody: dataError,
                                response: {soucre: data},
                                message: dataError?.crossFacilityMessage,
                            });
                            throw new HttpException(dataError?.crossFacilityMessage, HttpStatus.NOT_FOUND);
                        }
                        else if (errorDetail) {
                            this.emitService.emit(LOG_SERVICE_EVENT, {
                                name: 'getInsuranceInfoHIS',
                                summary: 'Lấy thông tin bảo hiểm bên HIS',
                                nameParent: 'getInsuranceDate',
                                params,
                                errorBody: dataError,
                                response: {soucre: data},
                                message: errorDetail,
                            });
                            throw new HttpException(errorDetail, HttpStatus.NOT_FOUND);
                        }
                    }
                    throw new HttpException(dataError?.errorDetail || 'Hệ thống không tìm thấy thông tin.', HttpStatus.NOT_FOUND);
                }
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'getInsuranceInfoHIS',
                    summary: 'Lấy thông tin bảo hiểm bên HIS',
                    nameParent: 'getInsuranceDate',
                    params,
                    errorBody: error.message,
                    response: {soucre: data},
                    message: error.message || 'Hệ thống không tìm thấy thông tin.',
                });
                throw new HttpException(error.message || 'Hệ thống không tìm thấy thông tin.', HttpStatus.NOT_FOUND);
            }
        } catch (error) {
            console.log(error);
            throw new HttpException(error.message || 'Hệ thống không tìm thấy thông tin.', HttpStatus.NOT_FOUND);
        }

    }

    async insertPatientBHYT_Bk(appId: string, partnerId: string, patientInfoResult: IPatient, insuranceId: string): Promise<any> {
        /* Kiểm tra xem có gởi thông tin mã bảo hiểm yte hay ko */
        let dataBH: any = {};
        try {
            const birthday = moment(patientInfoResult.birthdate).isValid() ? moment(patientInfoResult.birthdate, 'YYYY-MM-DD').format('YYYY-MM-DD') : '';
            const params = {
                fullName: `${patientInfoResult.surname} ${patientInfoResult.name}`,
                insuranceId,
                birthday,
                partnerId,
            };
            const baseUrl = this.urlConfigService.getBookingTreeUrl();
            const url = `${baseUrl}/his-connector/api/insurance/getDate`;
            // console.log('url bh', url);
            // console.log('parms bh', params);
            dataBH = (await this.getInsuranceInfoHIS(url, params).toPromise()).data;
            // console.log('result bh', dataBH);
            /* Tiếp tục kiểm tra  */
            const { insuranceToDate, validatedPartner, maDKBD: maDKBDHT, maThe } = dataBH;
            // if (!validatedPartner) {
            //     if (partnerId !== 'leloi') { // TODO: chỗ này cho thêm config
            //         throw new HttpException(
            //             'Nơi đăng ký khám bảo hiểm ban đầu của bạn không hợp lệ, Vui lòng kiểm tra lại thông tin.', HttpStatus.BAD_REQUEST);
            //     }
            // }
            /* tiếp tục kiểm tra */
            const objPatientCodeBH: any = {};

            const findMsbnData = (await this.getPatientExtraByInsuranceCodeHIS(partnerId, insuranceId).toPromise()).data;
            if (findMsbnData.length > 0) {
                const resultMsbnData: any = first(findMsbnData);
                objPatientCodeBH.patientCode = `${resultMsbnData.SoHS}`.trim();
            }
            // console.log('objPatientCodeBH', objPatientCodeBH);
            const patientId = patientInfoResult.id;
            const insuranceCode = maThe;
            const findPatientCode = await this.patientCodeModel.findOne({ partnerId, patientId }).exec();
            /* cập nhật lại thông tin */
            if (!findPatientCode) { /* thêm mới thông tin */
                // console.log('bh thêm mới patientCode');
                const info = new this.patientCodeModel({
                    id: `${partnerId}_${patientId}`,
                    createTime: moment().toDate(),
                    patientId,
                    insuranceCode,
                    insuranceExpiredDate: moment(insuranceToDate).toDate(),
                    partnerId,
                    appId,
                    /* patientCode */
                    ...objPatientCodeBH,
                    maDKBDHT,
                });
                await info.save();
            } else {
                // console.log('bh cập nhật patientCode');
                const patientCodeObj = findPatientCode.toObject();
                if (Object.keys(objPatientCodeBH).length > 0) {
                    const getPatientCode = get(patientCodeObj, 'patientCode', '');
                    if (getPatientCode) {
                        if (patientCodeObj.patientCode !== getPatientCode) {
                            await this.patientCodeModel.findByIdAndUpdate(
                                { _id: patientCodeObj._id },
                                { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...objPatientCodeBH },
                            ).exec();
                        } else {
                            await this.patientCodeModel.findByIdAndUpdate(
                                { _id: patientCodeObj._id },
                                { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate() },
                            ).exec();
                        }
                    } else {
                        await this.patientCodeModel.findByIdAndUpdate(
                            { _id: patientCodeObj._id },
                            { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...objPatientCodeBH },
                        ).exec();
                    }

                } else {
                    await this.patientCodeModel.findByIdAndUpdate(
                        { _id: patientCodeObj._id },
                        { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate() },
                    ).exec();
                }
            }

        } catch (error) {
            console.log(error);
            return null;
        }
    }

    async insertPatientBHYT(appId: string, partnerId: string, patientInfoResult: IPatient, insuranceId: string): Promise<any> {
        /* Kiểm tra xem có gởi thông tin mã bảo hiểm yte hay ko */
        try {
            const patientId = patientInfoResult.id;
            const insuranceCode = insuranceId;
            const findPatientCode = await this.patientCodeModel.findOne({ partnerId, patientId }).exec();
            /* cập nhật lại thông tin */
            if (!findPatientCode) { /* thêm mới thông tin */
                const info = new this.patientCodeModel({
                    id: `${partnerId}_${patientId}`,
                    createTime: moment().toDate(),
                    patientId,
                    insuranceCode,
                    partnerId,
                    appId,
                });
                await info.save();
            } else {
                const patientCodeObj = findPatientCode.toObject();
                await this.patientCodeModel.findByIdAndUpdate(
                    { _id: patientCodeObj._id },
                    { insuranceCode },
                ).exec();
            }
        } catch (error) {
            console.log(error);
            return null;
        }
    }

    async insertPatient(partnerId: string, appId: string, user: UserInfoVerifyTokenDTO, patientFormData: PatientFormMongoDTO,
                        cskhInfo?: CSKHTokenVerifyDTO, extra?: any): Promise<any> {

        if (patientFormData.mobileLocaleIso === 'vi-VN') {
            patientFormData.mobile = patientFormData.mobile.replace(/^\+84/, '0');
        }

        if (patientFormData.relativeMobileLocaleIso === 'vi-VN' && patientFormData.relative_mobile) {
            patientFormData.relative_mobile = patientFormData.relative_mobile.replace(/^\+84/, '0');
        }

        if (!patientFormData?.birthdate) {
            patientFormData.birthdate = null
        }

        const isCS = await this.userService.isCs(cskhInfo?.cskhUserId || user.userMongoId);
        const maxPatient = appId === 'bvmathcm' ? 5 : this.countPatient;
        if (!isCS) {
            const userWithPatients = await this.getCountPatientInUserPatientUMC(user.userMongoId);
            if (userWithPatients.patients.length >= maxPatient) {
                throw new HttpException(
                    `Số lượng hồ sơ bệnh nhân vượt quá tối đa ${maxPatient} hồ sơ trên mỗi tài khoản. Liên hệ tổng đài đặt khám nhanh 19002115 để được hỗ trợ.`, HttpStatus.FORBIDDEN);
            }
        }

        let data: any = {};
        const params = {
            partnerId,
            appId,
            user,
            ...patientFormData,
        };
        let fullAddress = '';
        /* kiểm tra local xem đã trùng hay chưa */
        /* Lấy thông tin danh sách hồ sơ bệnh nhân ở local */
        const noCheckExists = extra?.noCheckExists || false;
        if (!noCheckExists) {
            const listPatientsCheck = await this.getAllPatientsByUserIdSimple(user.userMongoId);
            if (listPatientsCheck.length > 0) {
                const fullnameCheck = slugify.default(`${patientFormData.surname} ${patientFormData.name}`).toLowerCase();
                const realName = `${patientFormData.surname} ${patientFormData.name}`.trim().toLowerCase();
                const mappCheckList = listPatientsCheck.map(item => {
                    return {
                        ...item.toObject(),
                        fullname: slugify.default(`${item.surname} ${item.name}`).toLowerCase(),
                    };
                });
                const objCheck: any = {};
                if (moment(patientFormData.birthdate).isValid()) {
                    objCheck.birthdate = moment(patientFormData.birthdate, 'YYYY-MM-DD').format('YYYY-MM-DD');
                } else {
                    objCheck.birthyear = Number(patientFormData.birthyear);
                }
                const paramsSearch = {
                    fullname: fullnameCheck,
                    ...objCheck,
                    sex: patientFormData.sex,
                    city_id: patientFormData.city_id, /* thêm city_id nữa */
                };
                const findInfo: any = find(mappCheckList, {
                    ...paramsSearch,
                });
                if (typeof findInfo !== typeof undefined) {
                    if (`${findInfo.surname} ${findInfo.name}`.trim().toLowerCase() === realName) {
                        throw new HttpException('Hồ sơ này đã tồn tại.', HttpStatus.CONFLICT);
                    }
                }
            }
        }

        //bvmat
        if (partnerId === 'bvmathcm') {
            // tìm bên HIS
            try {
                const data = await this.processSearchPatientByCccdPartner(appId, partnerId, user.userMongoId, {
                    mobile: patientFormData.mobile,
                    cmnd: patientFormData.cmnd,
                });
                /* tìm lại thông tin còn thiếu */
                const refObj: any = {};
                const findCountry2 = await this.countryModel.findOne({ code: patientFormData.country_code }).exec();
                if (findCountry2) {
                    refObj.country = findCountry2.toObject();
                }

                const findNation2 = await this.nationModel.findOne({ id: patientFormData.dantoc_id }).exec();
                if (findNation2) {
                    refObj.nation = findNation2.toObject();
                }

                const findCity2 = await this.cityModel.findOne({ id: patientFormData.city_id }).exec();
                if (findCity2) {
                    refObj.city = findCity2.toObject();
                }

                const findDistrict2 = await this.districtModel.findOne({ id: patientFormData.district_id }).exec();
                if (findDistrict2) {
                    refObj.district = findDistrict2.toObject();
                }

                const findWard2 = await this.wardModel.findOne({ id: patientFormData.ward_id }).exec();
                if (findWard2) {
                    refObj.ward = findWard2.toObject();
                }

                return {
                    patientForm: {
                        ...patientFormData,
                        ...refObj,
                        force: true,
                    },
                    recommendedPatientList: data,
                    isRecommended: true,
                    message: 'Hệ thống tìm thấy thông tin hồ sơ cũ thuộc bệnh viện. Bạn có muốn chọn xác nhận lại thông tin.',
                };
            } catch (error) {
                console.log('error', JSON.stringify(error, null, 2))
                this.logger.error('Không có gợi ý phone or cmnd', error);
            }
        }

        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        const { isSearcheBeforeCreateNew = false, isSearchedPhoneCMNDBeforeCreateNew = false } = partnerConfig;
        const searchPatientExtraInfoDTO: SearchPatientExtraInfoMongoDTO = {
            birthYear: patientFormData.birthyear,
            cityId: patientFormData.city_id,
            firstName: patientFormData.name,
            surName: patientFormData.surname,
            gender: patientFormData.sex,
        };
        // console.log(isSearcheBeforeCreateNew);
        if (isSearcheBeforeCreateNew && (patientFormData.force === false || typeof patientFormData.force === typeof undefined)) {
            try {
                // console.log(searchPatientExtraInfoDTO);
                const recommendedPatientList =
                    await this.processSearchPatientByExtraInfo(appId, partnerId, user.userMongoId, searchPatientExtraInfoDTO, cskhInfo);
                if (recommendedPatientList?.length === 0) {
                    throw new Error(`Không có gợi ý`);
                }
                /* tìm lại thông tin còn thiếu */
                const refObj: any = {};
                const findCountry2 = await this.countryModel.findOne({ code: patientFormData.country_code }).exec();
                if (findCountry2) {
                    refObj.country = findCountry2.toObject();
                }

                const findNation2 = await this.nationModel.findOne({ id: patientFormData.dantoc_id }).exec();
                if (findNation2) {
                    refObj.nation = findNation2.toObject();
                }

                const findCity2 = await this.cityModel.findOne({ id: patientFormData.city_id }).exec();
                if (findCity2) {
                    refObj.city = findCity2.toObject();
                }

                const findDistrict2 = await this.districtModel.findOne({ id: patientFormData.district_id }).exec();
                if (findDistrict2) {
                    refObj.district = findDistrict2.toObject();
                }

                const findWard2 = await this.wardModel.findOne({ id: patientFormData.ward_id }).exec();
                if (findWard2) {
                    refObj.ward = findWard2.toObject();
                }

                return {
                    patientForm: {
                        ...patientFormData,
                        ...refObj,
                        force: true,
                    },
                    recommendedPatientList,
                    isRecommended: true,
                    message: 'Hệ thống tìm thấy thông tin hồ sơ cũ thuộc bệnh viện. Bạn có muốn chọn xác nhận lại thông tin.',
                };

            } catch (error) {
                console.log('Không có gợi ý');
            }
        }

        /* Kiểm tra trường hợp tìm kiếm bên HIS cho phone OR Cmnd */
        if (isSearchedPhoneCMNDBeforeCreateNew && (patientFormData.force === false || typeof patientFormData.force === typeof undefined)) {
            // tìm bên HIS
            try {
                const data = await this.processSearchPhoneCMNDPatientByExtraInfo(appId, partnerId, user.userMongoId, {
                    mobile: patientFormData.mobile,
                    cmnd: patientFormData.cmnd,
                });
                /* tìm lại thông tin còn thiếu */
                const refObj: any = {};
                const findCountry2 = await this.countryModel.findOne({ code: patientFormData.country_code }).exec();
                if (findCountry2) {
                    refObj.country = findCountry2.toObject();
                }

                const findNation2 = await this.nationModel.findOne({ id: patientFormData.dantoc_id }).exec();
                if (findNation2) {
                    refObj.nation = findNation2.toObject();
                }

                const findCity2 = await this.cityModel.findOne({ id: patientFormData.city_id }).exec();
                if (findCity2) {
                    refObj.city = findCity2.toObject();
                }

                const findDistrict2 = await this.districtModel.findOne({ id: patientFormData.district_id }).exec();
                if (findDistrict2) {
                    refObj.district = findDistrict2.toObject();
                }

                const findWard2 = await this.wardModel.findOne({ id: patientFormData.ward_id }).exec();
                if (findWard2) {
                    refObj.ward = findWard2.toObject();
                }

                return {
                    patientForm: {
                        ...patientFormData,
                        ...refObj,
                        force: true,
                    },
                    recommendedPatientList: data,
                    isRecommended: true,
                    message: 'Hệ thống tìm thấy thông tin hồ sơ cũ thuộc bệnh viện. Bạn có muốn chọn xác nhận lại thông tin.',
                };
            } catch (error) {
                this.logger.error('Không có gợi ý phone or cmnd', error);
            }

        }

        /* Kiểm tra xem có tìm kiếm trước bên HIS hay ko */
        try {
            const { relative_name, relative_email, relative_mobile, relative_type_id, ...patientInfo } = patientFormData;
            const replacerBirthYear = moment(patientInfo?.birthdate).isValid() ? moment(patientInfo.birthdate).year() : '';
            // const medproId = await this.patientService.generateUMCMedproID();

            const patientInsert = {
                ...patientInfo,
                birthdate: moment(patientInfo.birthdate).isValid() ? moment(patientInfo.birthdate).format('YYYY-MM-DD') : '',
                // code: medproId,
                sourceId: appId,
                partnerId,
                userId: user.userMongoId,
                relation: {
                    relative_name: '',
                    relative_mobile: '',
                    relative_type_id: '',
                    relative_email: '',
                },
                patientIdV1: 0, /* Nhi đồng 1 */
                patientIdV1DaLieu: 0, /* Da liễu */
                patientIdV1CTCH: 0, /* CTCH */
                patientIdV1ThuDuc: 0, /* Thủ Đức */
                patientIdV1UMC: 0, /* UMC */
                /* CSKH */
                cskhUserId: patientFormData?.cskhUserId || null,
                ...(replacerBirthYear && { birthyear: replacerBirthYear }),
            };
            if (!!patientInsert.profession_id && patientInsert.profession_id !== '0') {
                const findProfession = await this.professionModel.findOne({ id: patientInsert.profession_id }).exec();
                if (findProfession) {
                    const professionObj = findProfession.toObject();
                    patientInsert.profession = professionObj._id;
                }
            } else {
                patientInsert.profession_id = '';
            }
            /* tìm lại thông tin */
            const findCountry = await this.countryModel.findOne({ code: patientInsert.country_code }).exec();
            if (findCountry) {
                const countryObj = findCountry.toObject();
                patientInsert.country = countryObj._id;
                patientInsert.country_id = countryObj.id;
            }

            const findNation = await this.nationModel.findOne({ id: patientInsert.dantoc_id }).exec();
            if (findNation) {
                const nationObj = findNation.toObject();
                patientInsert.nation = nationObj._id;
                patientInsert.dantoc_id = nationObj.id;
            }
            if (!!patientInsert.address) {
                fullAddress = `${patientInsert.address}`.trim();
            }
            const findWard = await this.wardModel.findOne({ id: patientInsert.ward_id }).exec();
            if (findWard) {
                const wardObj = findWard.toObject();
                patientInsert.ward = wardObj._id;
                patientInsert.ward_id = wardObj.id;
                fullAddress = `${fullAddress}, ${wardObj.name}`;
            }
            const findDistrict = await this.districtModel.findOne({ id: patientInsert.district_id }).exec();
            if (findDistrict) {
                const districtObj = findDistrict.toObject();
                patientInsert.district = districtObj._id;
                patientInsert.district_id = districtObj.id;
                fullAddress = `${fullAddress}, ${districtObj.name}`;
            }
            const findCity = await this.cityModel.findOne({ id: patientInsert.city_id }).exec();
            if (findCity) {
                const cityObject = findCity.toObject();
                patientInsert.city = cityObject._id;
                patientInsert.city_id = cityObject.id;
                fullAddress = `${fullAddress}, ${cityObject.name}`;
            }
            const uuidv4 = uuid.v4();
            patientInsert.id = uuidv4.replace(/-/g, '');
            patientInsert.code = this.generateMedproID(6);
            /* kiểm tra xem có thông tin thân nhân hay ko */
            if (patientFormData.relative_type_id) {
                patientInsert.relation = {
                    relative_name: patientFormData.relative_name,
                    relative_email: patientFormData.relative_email,
                    relative_mobile: patientFormData.relative_mobile,
                    relative_type_id: patientFormData.relative_type_id,
                };
            }
            if (this.listAppId.has(appId)) {
                const newSet = this.utilService.oldHospitalSync();
                // check rule age for partner nhidong1
                let isAgeValid: boolean = true;
                if (partnerId === 'nhidong1') {
                    const { patientYearOldAccepted = null } = partnerConfig;
                    isAgeValid = this.checkOldAccepted(patientFormData, patientYearOldAccepted);
                }
                if (!!partnerId && newSet.has(partnerId) && isAgeValid) {
                    const dataPatientV1 = await this.insertPatient_Nhidong1(partnerId, appId, user, patientFormData);
                    const getKey = this.utilService.patientIdV1Key(partnerId);
                    patientInsert[getKey] = dataPatientV1.id || 0;
                }
            }
            
            const patientInfoResult: IPatient = await this.insertPatientInfo(patientInsert);
            const trackingData : PatientTrackingData = {
                action: 'CREATE',
                dataAfter: patientInfoResult.toObject(),
                patient: patientInfoResult._id,
                userAction: cskhInfo?.cskhUserId || user.userMongoId,
                userPatient: user.userMongoId,
            }

            this.emitService.emit(PATIENT_TRACKING_EVENT, trackingData);

            await this.insertPatientBHYT(appId, partnerId, patientInfoResult, patientFormData.insuranceId);

            /* TODO:  Cần tạo thêm event cho cskh khi tạo hộ bệnh nhân hay ko */
            /* ----------------TODO:  Cần tạo thêm event cho cskh khi tạo hộ bệnh nhân hay ko-------------- */
            /* kiểm tra xem có patientCode hay ko */
            if (partnerId === 'nhidong1' && this.listAppId.has(appId)) { /* dành cho tái khám */
                if (patientFormData.patientCode) {
                    const patientCodeInfo = new this.patientCodeModel({
                        id: `${partnerId}_${patientInfoResult.id}`,
                        createTime: moment().toDate(),
                        patientId: patientInfoResult.id,
                        patientCode: `${patientFormData.patientCode}`.trim(),
                        partnerId,
                        appId,
                    });
                    await patientCodeInfo.save();
                }
            } else {
                /* Kiểm tra xem có gửi mã số bảo hiểm lên hay không */
            }

            const patientObj = patientInfoResult.toObject();
            // tìm lại thông tin user Mongo
            const userMongo = await this.userModel.findById({ _id: user.userMongoId }).exec();
            userMongo.patients.push(patientObj._id);
            await userMongo.save();

            // Emit event to link pending health data if patient has CMND/CCCD
            if (patientFormData?.cmnd) {
                this.emitService.emit(EVENT_LINK_PENDING_HEALTH_DATA, {
                    cmnd: patientFormData.cmnd,
                    patientId: patientObj.id,
                    userId: user.userMongoId,
                    timestamp: new Date(),
                    requestId: `auto_link_insert_${patientObj.id}_${Date.now()}`
                });
            }
            const { _id, ...rest } = patientObj;

            if (patientFormData?.relationType && !isEmpty(patientFormData?.relationType) ) {
                const { relationType: id } = patientFormData;
                const relation = await this.relativeModel.findOne({ id }).exec();
                const newPatientRelation = new this.patientRelationModel({
                    user: userMongo._id,
                    // patientId: patientObj.id,
                    patient: patientObj._id,
                    relationTypeId: relation.id,
                    relationType: relation._id,
                });
                await newPatientRelation.save();
            }

            return {
                ...rest,
                fullAddress,
            };

        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'insertPatient_Nhidong1',
                summary: 'Insert Patient Info',
                nameParent: 'insertPatient',
                params,
                errorBody: this.utilService.errorHandler(error),
                response: data,
                message: error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.',
            });
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async insertBasicInfo(patientFormData: any, userId: string, partnerId: string, appId: string, platform: string) {
        try {
            let session: any;
            if (['umc', 'nhidong1'].includes(partnerId)) {
                const userMongo = await this.userModel.findById({ _id: userId }, { username: true, medproId: true }).exec();
                await this.userService.getUserIdAndCreateSessionV1(appId, partnerId, userMongo);
                const userV1 = await this.checkExistsUserByUsername(userMongo.medproId);
                // await this.userService.forEachOldHospital(userV1.id); /* Tiến hành tạo session */
                switch (partnerId) {
                    case 'nhidong1':
                        session = await this.sessionService.checkExistsNhiDong1SessionByUserID(userV1.id);
                        break;
                    case 'dalieuhcm':
                        session = await this.sessionService.checkExistsSkinSessionByUserID(userV1.id);
                        break;
                    case 'ctchhcm':
                        session = await this.sessionService.checkExistsCTCHSessionByUserID(userV1.id);
                        break;
                    case 'thuduc':
                        session = await this.sessionService.checkExistsThuDucSessionByUserID(userV1.id);
                        break;
                    case 'umc':
                        session = await this.sessionService.checkExistsUMCSessionByUserID(userV1.id);
                        break;
                    default:
                        session = {
                            user_id: 0,
                            access_token: '',
                        };
                        break;
                }
            }

            //bvmat
            if (partnerId === 'bvmathcm' && patientFormData.cmnd) {
                // tìm bên HIS
                try {
                    console.log('123');
                    
                    const data = await this.processSearchPatientByCccdPartner(appId, partnerId, userId, {
                        mobile: patientFormData.mobile,
                        cmnd: patientFormData.cmnd,
                    });

                    console.log('1', data);
                    
                    /* tìm lại thông tin còn thiếu */
                    const refObj: any = {};
                    const findCountry2 = await this.countryModel.findOne({ code: patientFormData.country_code }).exec();
                    if (findCountry2) {
                        refObj.country = findCountry2.toObject();
                    }

                    const findNation2 = await this.nationModel.findOne({ id: patientFormData.dantoc_id }).exec();
                    if (findNation2) {
                        refObj.nation = findNation2.toObject();
                    }

                    const findCity2 = await this.cityModel.findOne({ id: patientFormData.city_id }).exec();
                    if (findCity2) {
                        refObj.city = findCity2.toObject();
                    }

                    const findDistrict2 = await this.districtModel.findOne({ id: patientFormData.district_id }).exec();
                    if (findDistrict2) {
                        refObj.district = findDistrict2.toObject();
                    }

                    const findWard2 = await this.wardModel.findOne({ id: patientFormData.ward_id }).exec();
                    if (findWard2) {
                        refObj.ward = findWard2.toObject();
                    }

                    console.log('data cccd bvmat', data);
                    console.log('patientForm cccd bvmat', {
                            ...patientFormData,
                            ...refObj,
                            force: true,
                        });
                    

                    return {
                        patientForm: {
                            ...patientFormData,
                            ...refObj,
                            force: true,
                        },
                        recommendedPatientList: data,
                        isRecommended: true,
                        message: 'Hệ thống tìm thấy thông tin hồ sơ cũ thuộc bệnh viện. Bạn có muốn chọn xác nhận lại thông tin.',
                    };
                } catch (error) {
                    console.log('error', JSON.stringify(error, null, 2))
                    this.logger.error('Không có gợi ý phone or cmnd', error);
                }
            }

            const { data } = await this.httpService.post(`${this.urlConfigService.getProxyApiUrl}/patient/insert-basic-info`, {
                ...patientFormData,
                userId,
                session
            }, {
                headers: {
                    appid: appId || '',
                    partnerid: partnerId || '',
                    platform: platform || '',
                    reponame: this.repoConfigService.getRepoName(),
                }
            }).toPromise();

            return data;
        } catch (err) {
            console.log('error', JSON.stringify(err, null, 2))
            if (err.response) {
                throw new HttpException(err.response.data, err.response?.status);
            }

            throw new HttpException(ErrorMessage.BAD_REQUEST, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async insertBasicInfoWhenCreateUser(patientFormData: any, userId: string, partnerId: string, appId: string, platform: string) {
        try {

            const { data } = await this.httpService.post(`${this.urlConfigService.getProxyApiUrl}/patient/user-create/insert-basic-info`, {
                ...patientFormData,
                userId,
            }, {
                headers: {
                    appid: appId || '',
                    partnerid: partnerId || '',
                    platform: platform || '',
                    reponame: this.repoConfigService.getRepoName(),
                }
            }).toPromise();

            return data;
        } catch (err) {
            console.log('error', JSON.stringify(err, null, 2))
            if (err.response) {
                throw new HttpException(err.response.data, err.response?.status);
            }

            throw new HttpException(ErrorMessage.BAD_REQUEST, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    generateMedproID(vLength = 6) {
        const dateFormat = moment().format('YYMMDD');
        const pattern = 'abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789';
        let pass = [];
        const alphaLength = pattern.length - 1;
        for (let i = 0; i < vLength; i++) {
            const n = this.utilService.getRandomInt(0, alphaLength);
            pass = [...pass, pattern[n]];
        }
        const uuidMP = `MP-${dateFormat}${pass.join('')}`.toUpperCase(); // dành cho UMC
        /* kiểm tra xem có trùng hay ko. nếu trùng thì tạo lại. */
        return uuidMP;
    }

    async getAllPatientsByUserIdSimple(userId: string): Promise<any> {
        const userWithPatients = await this.userModel.findById({ _id: userId }, { patients: true }).exec();
        if (userWithPatients.patients) {
            /* tìm danh sách patient */
            return this.patientModel
                .find({ _id: { $in: userWithPatients.patients } })
                .exec();
        }
        return [];
    }

    async getAllPatientsByUserIdSimpleWithPopulate(userId: string): Promise<any> {
        const userWithPatients = await this.userModel.findById({ _id: userId }, { patients: true }).exec();
        if (userWithPatients.patients) {
            /* tìm danh sách patient */
            return this.patientModel
                .find({ _id: { $in: userWithPatients.patients } },
                    { address: true, id: true, name: true, surname: true, birthdate: true, birthyear: true, mobile: true, code: true, sex: true })
                // .populate('profession')
                // .populate('country')
                // .populate('nation')
                .populate({ path: 'city', select: { name: true } })
                .populate({ path: 'district', select: { name: true } })
                .populate({ path: 'ward', select: { name: true } })
                .sort({ createdAt: 'desc' })
                .exec();
        }
        return [];
    }

    async getAllPatientsByUserIdSimpleGetIds(userId: string): Promise<any> {
        const userWithPatients = await this.userModel.findById({ _id: userId }, { patients: true }).exec();
        if (userWithPatients.patients) {
            /* tìm danh sách patient */
            return this.patientModel
                .find({ _id: { $in: userWithPatients.patients } }, { id: true })
                .exec();
        }
        return [];
    }

    async checkExistsUserByUsername(userName: any): Promise<any> {
        return this.pkhPatientKnex('user')
            .where('username', userName)
            .first();
    }

    async testTransformDataPatientV1(): Promise<any> {
        return true;
        // try {
        //     const urlGetPatients = this.oldUrl.NhiDong1GetPatients();
        //     const url = `${urlGetPatients}?user_id=7&access_token=ef3a6fb66d9d4ec73666f0213ec9de5c`;
        //     // const url = `${urlGetPatients}?user_id=${session.user_id}&access_token=${session.access_token}`;
        //     const resultData = (await this.getPatientsByUserIdNhiDong1(url).toPromise()).data;
        //     // return resultData;
        //     /* tiến hành transform data v1 */
        //     if (resultData.length > 0) {
        //         const baseUrl = this.urlConfigService.getBookingTreeUrl();
        //         const urlTransform = `${baseUrl}/his-connector/api/convertPartnerData`;
        //         let dataTransform = [];
        //         for await (const patient of resultData) {
        //             const { relative } = patient;
        //             const objRelative: any = {};
        //             const objRelationDisplay: any = {};
        //             if (typeof relative !== typeof undefined && typeof relative.nd1_relative_type_id !== typeof undefined) {
        //                 objRelative.relationTypeId = `${relative.nd1_relative_type_id}`;
        //                 objRelationDisplay.relation = {
        //                     relative_email: relative.email,
        //                     relative_mobile: relative.mobile,
        //                     relative_name: relative.name,
        //                 };
        //             }
        //             // console.log('chay tio day')
        //             const params = {
        //                 fromPartnerId: 'nhidong1',
        //                 toPartnerId: 'medpro',
        //                 countryId: `${patient.country_code}`,
        //                 cityId: `${patient.city_id}`,
        //                 districtId: `${patient.district_id}`,
        //                 wardId: `${patient.ward_id}`,
        //                 nationId: `${patient.dantoc_id}`,
        //                 ...objRelative,
        //             };
        //             const dataKeys = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
        //             /* tìm lại thông tin danh mục */
        //             console.log(dataKeys);
        //             const keys = Object.entries(dataKeys);
        //             const objDanhMuc: any = {};
        //             for await (const [key, value] of keys) {
        //                 if (value) {
        //                     switch (key) {
        //                         case 'countryId':
        //                             const findCountry = await this.countryModel.findOne({ id: value }).exec();
        //                             if (findCountry) {
        //                                 objDanhMuc.country_code = value;
        //                                 objDanhMuc.country = {
        //                                     ...findCountry.toObject(),
        //                                 };
        //                             }
        //                             break;
        //                         case 'cityId':
        //                             const findCity = await this.cityModel.findOne({ id: value }).exec();
        //                             if (findCity) {
        //                                 objDanhMuc.city_id = value;
        //                                 objDanhMuc.city = {
        //                                     ...findCity.toObject(),
        //                                 };
        //                             }
        //                             break;
        //                         case 'districtId':
        //                             const findDistrict = await this.districtModel.findOne({ id: value }).exec();
        //                             if (findDistrict) {
        //                                 objDanhMuc.district_id = value;
        //                                 objDanhMuc.district = {
        //                                     ...findDistrict.toObject(),
        //                                 };
        //                             }
        //                             break;
        //                         case 'wardId':
        //                             const findWard = await this.wardModel.findOne({ id: value }).exec();
        //                             if (findWard) {
        //                                 objDanhMuc.ward_id = value;
        //                                 objDanhMuc.ward = {
        //                                     ...findWard.toObject(),
        //                                 };
        //                             }
        //                             break;
        //                         case 'nationId':
        //                             const findNation = await this.nationModel.findOne({ id: value }).exec();
        //                             if (findNation) {
        //                                 objDanhMuc.dantoc_id = value;
        //                                 objDanhMuc.nation = {
        //                                     ...findNation.toObject(),
        //                                 };
        //                             }
        //                         case 'relationTypeId':
        //                             const findRelative = await this.relativeModel.findOne({ id: value }).exec();
        //                             if (findRelative) {
        //                                 objDanhMuc.relative_id = value;
        //                                 objDanhMuc.relative = {
        //                                     ...findRelative.toObject(),
        //                                 };
        //                             }
        //                             break;
        //                         default:
        //                             break;
        //                     }
        //                 } else {
        //                     objDanhMuc[key] = null;
        //                 }
        //             }
        //             /* Tiến hành gắn kết vào trong patient */
        //             dataTransform = [...dataTransform, {
        //                 address: patient.address,
        //                 birthdate: patient.birthdate,
        //                 birthyear: patient.birthyear,
        //                 cmnd: patient.cmnd,
        //                 code: patient.medpro_id,
        //                 email: patient.email,
        //                 fullname: `${patient.surname} ${patient.name}`,
        //                 isUpdateFull: false,
        //                 mobile: patient.mobile,
        //                 name: patient.name,
        //                 surname: patient.surname,
        //                 partnerId: 'nhidong1',
        //                 patientCode: patient.bv_id ? patient.bv_id : '',
        //                 sex: patient.sex,
        //                 sourceId: 'medpro',
        //                 userId: 'aaaaa',
        //                 ...objDanhMuc,
        //                 ...objRelationDisplay,
        //             }];
        //         }
        //         return dataTransform;
        //     } else {
        //         return resultData;
        //     }
        // } catch (error) {
        //     throw new HttpException('Không tìm thấy thông tin', HttpStatus.BAD_REQUEST);
        // }
    }

    async getAllPatientsNhiDong1(user: UserInfoVerifyTokenDTO): Promise<any> {
        /* tìm lại user_id và access_token */
        const userMongo = await this.userModel.findById({ _id: user.userMongoId }, { username: true, medproId: true }).exec();
        // console.log('userMongo', userMongo);
        const userV1 = await this.checkExistsUserByUsername(userMongo.medproId);
        const session = await this.sessionService.checkExistsNhiDong1SessionByUserID(userV1.id);
        // console.log('session', session);
        /* gửi thông tin sang proxy */
        // const urlGetPatients = this.oldUrl.NhiDong1GetPatients();
        try {
            const urlGetPatients = this.oldUrl.NhiDong1GetPatients();
            // console.log('urlGetPatients', urlGetPatients);
            // const url = `${urlGetPatients}?user_id=7&access_token=ef3a6fb66d9d4ec73666f0213ec9de5c`;
            const url = `${urlGetPatients}?user_id=${session.user_id}&access_token=${session.access_token}`;
            // console.log('url', url);
            const resultData = (await this.getPatientsByUserIdNhiDong1(url).toPromise()).data;
            /* tiến hành transform data v1 */
            // console.log('resultData', resultData);
            if (resultData.length > 0) {
                const baseUrl = this.urlConfigService.getBookingTreeUrl();
                const urlTransform = `${baseUrl}/his-connector/api/convertPartnerData`;
                let dataTransform = [];
                for await (const patient of resultData) {
                    const { relative } = patient;
                    const objRelative: any = {};
                    const objRelationDisplay: any = {};
                    if (typeof relative !== typeof undefined && typeof relative.nd1_relative_type_id !== typeof undefined) {
                        objRelative.relationTypeId = `${relative.nd1_relative_type_id}`;
                        objRelationDisplay.relation = {
                            relative_email: relative.email,
                            relative_mobile: relative.mobile,
                            relative_name: relative.name,
                        };
                    }
                    // console.log('chay tio day')
                    const params = {
                        fromPartnerId: 'nhidong1',
                        toPartnerId: 'medpro',
                        countryId: `${patient.country_code}`,
                        cityId: `${patient.city_id}`,
                        districtId: `${patient.district_id}`,
                        wardId: `${patient.ward_id}`,
                        nationId: `${patient.dantoc_id}`,
                        ...objRelative,
                    };
                    // console.log('params', params);
                    const dataKeys = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
                    /* tìm lại thông tin danh mục */
                    // console.log(dataKeys);
                    const keys = Object.entries(dataKeys);
                    const objDanhMuc: any = {};
                    for await (const [key, value] of keys) {
                        if (value) {
                            switch (key) {
                                case 'countryId':
                                    const findCountry = await this.countryModel.findOne({ id: value }).exec();
                                    if (findCountry) {
                                        objDanhMuc.country_code = value;
                                        objDanhMuc.country = {
                                            ...findCountry.toObject(),
                                        };
                                    }
                                    break;
                                case 'cityId':
                                    const findCity = await this.cityModel.findOne({ id: value }).exec();
                                    if (findCity) {
                                        objDanhMuc.city_id = value;
                                        objDanhMuc.city = {
                                            ...findCity.toObject(),
                                        };
                                    }
                                    break;
                                case 'districtId':
                                    const findDistrict = await this.districtModel.findOne({ id: value }).exec();
                                    if (findDistrict) {
                                        objDanhMuc.district_id = value;
                                        objDanhMuc.district = {
                                            ...findDistrict.toObject(),
                                        };
                                    }
                                    break;
                                case 'wardId':
                                    const findWard = await this.wardModel.findOne({ id: value }).exec();
                                    if (findWard) {
                                        objDanhMuc.ward_id = value;
                                        objDanhMuc.ward = {
                                            ...findWard.toObject(),
                                        };
                                    }
                                    break;
                                case 'nationId':
                                    const findNation = await this.nationModel.findOne({ id: value }).exec();
                                    if (findNation) {
                                        objDanhMuc.dantoc_id = value;
                                        objDanhMuc.nation = {
                                            ...findNation.toObject(),
                                        };
                                    }
                                case 'relationTypeId':
                                    const findRelative = await this.relativeModel.findOne({ id: value }).exec();
                                    if (findRelative) {
                                        objDanhMuc.relative_id = value;
                                        objDanhMuc.relative = {
                                            ...findRelative.toObject(),
                                        };
                                    }
                                    break;
                                default:
                                    break;
                            }
                        } else {
                            objDanhMuc[key] = null;
                        }
                    }
                    /* Tiến hành gắn kết vào trong patient */
                    dataTransform = [...dataTransform, {
                        id: patient.id,
                        address: patient.address,
                        birthdate: patient.birthdate,
                        birthyear: patient.birthyear,
                        cmnd: patient.cmnd,
                        code: patient.medpro_id,
                        email: patient.email,
                        fullname: `${patient.surname} ${patient.name}`,
                        isUpdateFull: false,
                        mobile: patient.mobile,
                        name: patient.name,
                        surname: patient.surname,
                        partnerId: 'nhidong1',
                        patientCode: patient.bv_id ? patient.bv_id : '',
                        sex: patient.sex,
                        sourceId: 'medpro',
                        userId: 'aaaaa',
                        ...objDanhMuc,
                        ...objRelationDisplay,
                    }];
                }
                return dataTransform;
            } else {
                return resultData;
            }
        } catch (error) {
            throw new HttpException('Không tìm thấy thông tin', HttpStatus.BAD_REQUEST);
        }
    }

    transformDataPatientV1(url: string, data: any): Observable<AxiosResponse<any>> {
        // console.log(url);
        return this.httpService.post(url, {
            ...data,
        });
    }

    async findUserByRefId(userId: string): Promise<any> {
        return this.userModel.findById({
            _id: userId,
        }).exec();
    }

    async verifyCskhToken(cskhToken: string): Promise<CSKHTokenVerifyDTO> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyCSKHJwtOptions();
        let cskhInfo: CSKHTokenVerifyDTO;
        if (typeof cskhToken !== typeof undefined && `${cskhToken}`.trim() !== '') {
            try {
                const jwtVerify: any = jwt.verify(cskhToken, jwtOptions.secret);
                cskhInfo = jwtVerify;
                /* Kiểm tra lại thông tin cskhInfo */
                const { userIdPatient, cskhUserId } = cskhInfo;
                if (cskhUserId) {
                    const cskhUser = await this.findUserByRefId(cskhUserId);
                    if (!cskhUser) {
                        throw new HttpException('Vui lòng kiểm tra lại thông tin user cskh', HttpStatus.UNAUTHORIZED);
                    }
                }

                if (userIdPatient) {
                    const patientUser = await this.findUserByRefId(userIdPatient);
                    if (!patientUser) {
                        throw new HttpException('Vui lòng kiểm tra lại thông tin patient user', HttpStatus.UNAUTHORIZED);
                    }
                }

                return cskhInfo;
            } catch (error) {
                const nameJWTError = !!error.name ? error.name : '';
                if (nameJWTError === 'TokenExpiredError') {
                    throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
                } else if (nameJWTError === 'JsonWebTokenError') {
                    throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
                } else if (nameJWTError === 'NotBeforeError') {
                    throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
                } else {
                    throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
                }
            }
        } else {
            return cskhInfo;
        }
    }

    async getPatientsByUserIdMedpro(appid: string, partnerId: string, user: UserInfoVerifyTokenDTO, formData?: YearOldValidationDto): Promise<any> {

        const { patients, patientCodes } = await this.getAllPatientsByUserId(appid, partnerId, user);
        const [envCheckConstraintPatinet, appAcceptConstraint, repoShowPatientInvalid, patientContrains] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('ENV_CHECK_CONSTRAINS_PATIENT_DETAIL'),
            this.globalSettingService.findByKeyAndRepoName('APP_CHECK_CONSTRAINT_PATIENT'),
            this.globalSettingService.findByKeyAndRepoName('REPO_SHOW_PATIENT_INVALID'),
            this.globalSettingService.findByKeyAndRepoName('PATIENT_CONSTRAINT_BOOKING_DALIEUHCM'),
        ]);

        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: partnerId || appid }, { tim_ho_so_mo_rong_btn: true }).exec();

        let data: any
        if (partnerId === 'bvmathcm') {
            data = patients.map(item => {
                // const json = item;
                const ngaysinh = moment(item.birthdate).isValid() ? moment(item.birthdate, 'YYYY-MM-DD').format('YYYY-MM-DD') : '';
                /* tìm lại trong patientCodes */
                let patientCode = item.code;
                let insuranceCode = item?.insuranceId || '';
                let isUpdateFull = true;
                
                const findPatientCode = patientCodes.find(
                    c => c.partnerId === item.partnerId && c.patientId === item.id && !!c.patientCode
                );
                if (typeof findPatientCode !== typeof undefined) {
                    isUpdateFull = false;
                    const getpatientCode = get(findPatientCode, 'patientCode', null);
                    const getInsuranceCode = get(findPatientCode, 'insuranceCode', null);
                    if (getpatientCode) {
                        patientCode = findPatientCode.patientCode;
                    }
                    if (getInsuranceCode) {
                        insuranceCode = findPatientCode.insuranceCode;
                    }
                }
                return {
                    ...item,
                    birthdate: ngaysinh,
                    fullname: `${item.surname} ${item.name}`,
                    // mobile: isUpdateFull ? item.mobile : this.patientMongoService.secretMobile(item.mobile),
                    patientCode,
                    isUpdateFull: !!insuranceCode ? false : true,
                    insuranceCode,
                };
            });
            const checkListTreeIds = ['TAIKHAMPHACO', 'TAIKHAM'];
            const checkListSubjectIds = ['bvmathcm_TKM'];
            if (checkListTreeIds.includes(formData.treeId) || checkListSubjectIds.includes(formData.subjectId) || formData.serviceId?.endsWith('TK')) {
                data = data.filter(item => {
                    const findPatientCode = patientCodes.find(
                        c => c.patientId === item.id && !!c.patientCode
                    );
                    if (findPatientCode) {
                        const patientCode = get(findPatientCode, 'patientCode', null);
                        return !!patientCode;
                    }
                    return false;
                })
            }
        } else {
            data = patients.map(item => {
                // const json = item;
                const ngaysinh = moment(item.birthdate).isValid() ? moment(item.birthdate, 'YYYY-MM-DD').format('YYYY-MM-DD') : '';
                /* tìm lại trong patientCodes */
                let patientCode = item.code;
                let insuranceCode = item?.insuranceId || '';
                let isUpdateFull = true;
                const isExam = get(item, 'isExam', false);
                
                if (partnerId === 'choray' && !isExam) {
                    return {
                        ...item,
                        birthdate: ngaysinh,
                        fullname: `${item.surname} ${item.name}`,
                        // mobile: isUpdateFull ? item.mobile : this.patientMongoService.secretMobile(item.mobile),
                        patientCode,
                        isUpdateFull: !!insuranceCode ? false : true,
                        insuranceCode,
                    };
                } else {
                    let hasPatientCode = false;
                    const findPatientCode = find(patientCodes, { patientId: item.id });
                    if (typeof findPatientCode !== typeof undefined) {
                        isUpdateFull = false;
                        const getpatientCode = get(findPatientCode, 'patientCode', null);
                        const getInsuranceCode = get(findPatientCode, 'insuranceCode', null);
                        if (getpatientCode) {
                            patientCode = findPatientCode.patientCode;
                            hasPatientCode = true
                        }
                        if (getInsuranceCode) {
                            insuranceCode = findPatientCode.insuranceCode;
                        }
                    }
                    return {
                        ...item,
                        birthdate: ngaysinh,
                        fullname: `${item.surname} ${item.name}`,
                        // mobile: isUpdateFull ? item.mobile : this.patientMongoService.secretMobile(item.mobile),
                        patientCode,
                        isUpdateFull: !!insuranceCode ? false : true,
                        insuranceCode,
                        requiredSearchExtraInfo: !hasPatientCode && partnerConfig?.tim_ho_so_mo_rong_btn
                    };
                }
                
            });
        }

        if (formData?.subjectId || formData?.serviceId) {
            data = await this.checkPatientPartner(data, formData, partnerId, appid);
        }
        // bổ sung patient relation cho patients

        const setAppAcceptConstraint = new Set(appAcceptConstraint.split(','));
        const setRepoShowPatientInvalid = new Set(repoShowPatientInvalid.split(','));

        if (appid === 'medpro' && partnerId === 'choray') {
            data = await this.checkConstrainsPatientDetail(data, { partnerid: partnerId, appid });
        } else if (envCheckConstraintPatinet === 'ON' && setAppAcceptConstraint.has(appid) && setRepoShowPatientInvalid.has(this.repoName)) {
            data = await this.checkConstrainsPatientDetail(data, { partnerid: partnerId, appid });
        }
        const { subjectId = '', serviceId = '', treeId = '', bookingDate } = formData;
        const patientContrainsObj = JSON.parse(patientContrains);
        const keyConstraintConfig = `${treeId.toUpperCase()}|${subjectId}|${serviceId}`;
        const constraintConfig = get(patientContrainsObj?.constraints, keyConstraintConfig);
        
        if (!(partnerId === 'dalieuhcm' && constraintConfig)) {
            data = await this.checkAgePatientAcceptForPartner(data, partnerId);
        }

        
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();

        data = data.map(patient => {
            return {
                ...patient,
                birthdate: moment(patient?.birthdate).isValid() ? moment(patient?.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
                secretPatientId: jwt.sign({ patientId: patient.id }, jwtOptions.secret, jwtOptions.signOptions),
            };
        });

        // data = data.map(item => {
        //     return {
        //         ...item,
        //         birthdate: moment(item?.birthdate).isValid() ? moment(item?.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
        //     };
        // });
        // data = this.sortPatient(data);

        return data;
    }

    checkBirthDate(birthdate: string, ageLimit: number): boolean {
        return Math.floor(moment().diff(moment(new Date(birthdate)), 'years', true)) < +ageLimit;
    }

    checkBirthYear(birthyear: number, ageLimit: number): boolean {
        return Number(moment().format('YYYY')) - birthyear < +ageLimit;
    }

    checkOldAccepted(patient: any, ageLimit: number = null): boolean {
        // get config
        if (!ageLimit) {
            return true;
        }
        return moment(new Date(patient.birthdate)).isValid()
            ? this.checkBirthDate(patient.birthdate, +ageLimit)
            : this.checkBirthYear(patient.birthyear, +ageLimit);
    }

    async getPartnerConfigAgeLimit(partnerId: string): Promise<any> {
        try {
            const ageLimitConfig = await this.globalSettingModel.findOne({ key: this.AGE_LIMIT_CONFIG }).exec();
            const partnerList = ageLimitConfig.value.split(',');
            const partnerExists = partnerList.find(item => item === partnerId);
            if (!partnerExists) {
                return '';
            }
            return true;
        } catch (error) {
            this.logger.error(`Error when exec getPartnerConfigAgeLimit for partnerId: ${partnerId}. Error: ${error.message}`);
            throw error;
        }
    }

    getFullAddress(patient: IPatient): string {
        let fullAddress = '';
        if (!!patient.address) {
            fullAddress = `${patient.address}`.trim();
        }
        const getWardName = get(patient, 'ward.name', '');
        if (getWardName) {
            fullAddress = fullAddress ? `${fullAddress}, ${getWardName}` : getWardName;
        }
        const getDistrict = get(patient, 'district.name', '');
        if (getDistrict) {
            fullAddress = fullAddress ? `${fullAddress}, ${getDistrict}` : getDistrict;
        }
        const getCity = get(patient, 'city.name', '');
        if (getCity) {
            fullAddress = fullAddress ? `${fullAddress}, ${getCity}` : getCity;
        }

        return fullAddress;
    }


    async getAllPatientsByUserId(appid: string, partnerId: string, user: UserInfoVerifyTokenDTO): Promise<any> {
        const userWithPatients = await this.userModel.findById({ _id: user.userMongoId }, { patients: true }).exec();
        if (userWithPatients.patients) {
            /* tìm danh sách patient */
            const patients = await this.patientModel
                .find({ _id: { $in: userWithPatients.patients } })
                .populate({ path: 'profession', select: { name: true } })
                .populate({ path: 'country', select: { name: true } })
                .populate({ path: 'nation', select: { name: true } })
                .populate({ path: 'city', select: { name: true } })
                .populate({ path: 'district', select: { name: true } })
                .populate({ path: 'ward', select: { name: true } })
                .sort({updatedAt: -1})
                .exec();
            /* lấy thông tin relative */
            let listPatients = [];
            for await (const patient of patients) {
                const boj = patient.toObject();
                const { relation } = boj;
                if (relation.relative_type_id) {
                    const findRelative = await this.relativeModel.findOne({ id: relation.relative_type_id }).exec();
                    if (findRelative) {
                        boj.relative = { ...findRelative.toObject() };
                    }
                }
                /* lấy thông tin fullAddress */
                const fullAddress = this.getFullAddress(patient);
                boj.fullAddress = fullAddress;
                listPatients = [...listPatients, boj];
            }
            const mapPatients = map(listPatients, 'id');
            // if (appid !== 'medpro' || partnerId === 'choray') {
            //     const patientCodes = await this.patientCodeModel.find({ partnerId, patientId: { $in: mapPatients } }).exec();
            //     return {
            //         patients: listPatients,
            //         patientCodes,
            //     };
            // } else {
            //     return {
            //         patients: listPatients,
            //         patientCodes: [],
            //     };
            // }
            const patientCodes = await this.getInsuranCodeByPatientId(mapPatients, partnerId, appid);

            return {
                patients: listPatients,
                patientCodes,
            };
        }
        return [];
    }

    async getAllPatientsByUserIdForHospitalFee(appid: string, partnerId: string, user: UserInfoVerifyTokenDTO): Promise<any> {
        const userWithPatients = await this.userModel.findById({ _id: user.userMongoId }, { patients: true }).exec();
        if (userWithPatients.patients) {
            /* tìm danh sách patient */
            const patients = await this.patientModel
                .find({ _id: { $in: userWithPatients.patients } })
                .exec();
            /* lấy thông tin relative */
            const listPatients = patients.map(item => item.toObject());
            const mapPatients = map(listPatients, 'id');
            const patientCodes = await this.patientCodeModel.find({ partnerId, patientId: { $in: mapPatients } }).exec();
            return listPatients.map(item => {
                let patientCode = '';
                if (appid !== 'medpro') {
                    const findPatientCode = find(patientCodes, { patientId: item.id });
                    if (typeof findPatientCode !== typeof undefined) {
                        const getpatientCode = get(findPatientCode, 'patientCode', null);
                        if (getpatientCode) {
                            patientCode = findPatientCode.patientCode;
                        }
                    }
                }
                return {
                    ...item,
                    patientCode,
                };
            }).filter(item => item.patientCode);
        }
        return [];
    }

    async getListPatient(userid: string): Promise<any> {
        const userWithPatients = await this.userModel.findById({ _id: userid }, { patients: true }).exec();
        if (userWithPatients.patients) {
            /* tìm danh sách patient */
            const patients = await this.patientModel
                .find({ _id: { $in: userWithPatients.patients } })
                .populate('profession')
                .populate('country')
                .populate('nation')
                .populate('city')
                .populate('district')
                .populate('ward')
                .exec();
            return patients;
        }
        return [];
    }

    async insertPatientInfo(patientInsert): Promise<any> {
        const patientModel = new this.patientModel(patientInsert);
        return await patientModel.save();
    }

    async updatePatientInfo(id: string, patientInfo: any): Promise<any> {
        return this.patientModel.findByIdAndUpdate({ _id: id }, { ...patientInfo }, { new: true });
    }

    // async getPatientInfo(patientId: string): Promise<any> {
    //     const info = await this.patientModel.findById({ _id: patientId })
    //         .populate('profession')
    //         .populate('country')
    //         .populate('nation')
    //         .populate('city')
    //         .populate('district')
    //         .populate('ward')
    //         .exec();
    //     const json = info.toJSON();
    //     return {
    //         ...json,
    //         birthDate: json.birthdate,
    //         birthYear: json.birthyear,
    //         fullname: `${json.surname} ${json.name}`,
    //     };
    // }

    async getPatientByREf(refId: string): Promise<any> {
        return this.patientModel.findById({ _id: refId }).exec();
    }

    async getPatientInfoById(patientId: string, partnerId: string): Promise<any> {
        const info = await this.patientModel.findById({ _id: patientId })
            .populate('profession')
            .populate('country')
            .populate('nation')
            .populate('city')
            .populate('district')
            .populate('ward')
            .exec();
        const json = info.toObject();
        const fullAddress = this.getFullAddress(info);
        /* lấy thông tin thân nhân */
        const { relation } = json;
        if (relation.relative_type_id) {
            const findRelative = await this.relativeModel.findOne({ id: relation.relative_type_id }).exec();
            if (findRelative) {
                json.relative = { ...findRelative.toObject() };
            }
        }
        const ngaysinh = moment(json.birthdate).isValid() ? moment(json.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '';
        const patientCodes = await this.getInsuranCodeByPatientId([info?.id], partnerId);
        json.patientCode = json?.code
        if (patientCodes.length > 0) {
            const patientCode = get(first(patientCodes), 'patientCode');
            if (patientCode) {
                json.patientCode = patientCode;
            }
            const insuranceCode = get(first(patientCodes), 'insuranceCode');
            if (insuranceCode) {
                json.insuranceCode = insuranceCode;
            }
        }
        return {
            ...json,
            birthdate: ngaysinh,
            fullname: `${json.surname} ${json.name}`,
            fullAddress,
        };
    }

    async getPatientByIdPatient(id: string): Promise<any> {
        return this.patientModel.findOne({ id }).exec();
    }

    async getPatientInfoAndPatientCodeById(partnerId: string, appId: string, patientId: string): Promise<any> {
        const info = await this.patientModel.findById({ _id: patientId })
            .populate('profession')
            .populate('country')
            .populate('nation')
            .populate('city')
            .populate('district')
            .populate('ward')
            .read('primary')
            .exec();
        const json = info.toObject();
        const fullAddress = this.getFullAddress(info);
        /* lấy thông tin thân nhân */
        const { relation } = json;
        if (relation.relative_type_id) {
            const findRelative = await this.relativeModel.findOne({ id: relation.relative_type_id }).exec();
            if (findRelative) {
                json.relative = { ...findRelative.toObject() };
            }
        }
        const ngaysinh = (!!json.birthdate && moment(json.birthdate).isValid()) ? moment(json.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '';
        /* lấy lại thông tin patientCode, insuranceCode */
        const patientCodes = await this.patientCodeModel.findOne({ partnerId, patientId: json.id }).exec();
        let insuranceCode = json.insuranceId || '';
        let patientCode = json.code;
        let isUpdateFull = true;
        if (patientCodes) {
            const codeObj = patientCodes.toObject();
            insuranceCode = codeObj.insuranceCode;
            // if (appId !== 'medpro') {
                isUpdateFull = false;
                patientCode = codeObj.patientCode;
            // }
        }
        return {
            ...json,
            birthdate: ngaysinh,
            fullname: `${json.surname} ${json.name}`,
            insuranceCode,
            isUpdateFull,
            patientCode,
            fullAddress,
        };
    }

    async removeLimitPatientSearch(phone: string): Promise<any> {
        const userPhoneInfo = phone
                .replace(/^[+]84|^0/, '+84')
                .replace(/^84/, '+84')
                .replace(/^9/, '+849')
                .replace(/^3/, '+843');
        const userInfo = await this.userModel.findOne({ username: userPhoneInfo }).exec();
        if (!userInfo) {
            throw new HttpException('Không tìm thấy thông tin user.', HttpStatus.NOT_FOUND);
        }
        return this.patientSearchLogModel.remove({ userId: userInfo._id, date: moment().format('YYYY-MM-DD') }).exec();
    }

    async searchExtraInfo(partnerid: string, appid: string): Promise<any> {
        return this.patientModel
            .find(
                { $text: { $search: 'Nguyễn Dũng' }, sex: 1, birthyear: 1989, city_id: 'aaaaa' },
                { score: { $meta: 'textScore' } },
            )
            .sort({ score: { $meta: 'textScore' } })
            .exec();
    }

    async transformDataSearch(partnerId: string, data: any): Promise<any> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const urlTransform = `${baseUrl}/his-connector/api/convertPartnerData`;
        const { relative } = data;
        const getProfessionId = get(data, 'profession_id', 0);
        const objRelativeType: any = {};
        if (relative && Object.keys(relative).length > 0) {
            objRelativeType.relationTypeId = `${relative.relative_type_id}`;
        }
        const objProfessionId: any = {};
        if (getProfessionId > 0) {
            objProfessionId.professionId = `${getProfessionId}`;
        }
        const params: any = {
            fromPartnerId: partnerId,
            toPartnerId: 'medpro',
            countryId: '',
            cityId: `${data.city_id}`,
            districtId: `${data.district_id}`,
            wardId: `${data.ward_id}`,
            nationId: `${data.dantoc_id}`,
            ...objRelativeType,
            ...objProfessionId,
        };
        try {
            const dataKeys = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
            return dataKeys;
        } catch (error) {
            this.logger.error(`Error when exec transformDataSearch for partnerId: ${partnerId}. Error: ${error.message}`);
            return false;
        }
    }

    async searchPatientByMsbn(
        userId: string, appid: string, partnerId: string, searchPatientDTO: SearchPatientDTO, prefix: boolean = true,
        cskhInfo?: CSKHTokenVerifyDTO, req?: any): Promise<any> {
        // Kiểm tra xem search mấy lần rồi
        if (partnerId === 'nhidong1') {
            return this.searchPatientByMsbnNhidong1(userId, appid, partnerId, searchPatientDTO, prefix, cskhInfo, req)
        }
        let isCS = false;
        if (userId) {
            await this.constraintSearchLog(userId, partnerId, cskhInfo);
            isCS = await this.userService.isCs(cskhInfo?.cskhUserId || userId);
        }
        const uuidLog = uuid.v4().replace(/-/g, '');
        try {
            //event logs
            this.emitService.emit(CREATE_FIND_PATIENT_MSBN_LOGS_EVENT, {
                uuid: uuidLog,
                partnerId,
                appId: appid,
                userId: userId,
                cskhInfo: cskhInfo,
                timestamp: new Date(),
                url: req.url,
                method: req.method,
                headers: req.headers,
                params: req.params,
                body: req.body,
                query: req.query,
                nameRepo: this.repoName,
                action: 'searchPatientByMsbn',
                msbn: searchPatientDTO.msbn,
            });
        } catch (error) {
            
        }

        // const env = this.urlConfigService.getEnv();
        // if (env === 'PRODUCTION') {
        //     if (userId) {
        //         const countValue = await this.patientSearchLogModel.find({ userId, partnerId, date: moment().format('YYYY-MM-DD') }).count();
        //         if (countValue > this.countSearchPatient) {
        //             throw new HttpException(
        //                 'Bạn vượt quá giới hạn tìm kiếm. Cần hỗ trợ vui lòng liên hệ với chúng tôi qua số 19002115', HttpStatus.TOO_MANY_REQUESTS);
        //         }
        //         /* tiến hành insert vào trong db */
        //         const patientSearchLog = new this.patientSearchLogModel({
        //             userId,
        //             partnerId,
        //             date: moment().format('YYYY-MM-DD'),
        //             checkValue: 1,
        //         });
        //         await patientSearchLog.save();
        //     }
        // }
        /* Lấy thông tin partner config */
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        let patientCode = searchPatientDTO.msbn;
        if (partnerId === 'umc') {
            prefix = false;
        }
        if (partnerConfig && prefix === true) {
            const removeSpecialCharacter = `${patientCode}`.replace(/[-]/g, '');
            patientCode = `${partnerConfig.prefixValuePatientCode}${removeSpecialCharacter}`;
        }
        // console.log('patient code origin', patientCode);
        const { isVerifiedByPhone: checkVerifiedPhone = false } = partnerConfig;
        let isVerifiedByPhone = isCS ? false : checkVerifiedPhone;

        if (REPO_NAME_BETA.includes(this.repoName)) {
            isVerifiedByPhone = true;
        }
        // console.log(patientCode);
        /* Kiểm tra bên db mongo medpro xem có tồn tài ko */
        // const findPatient = await this.patientModel
        //     .find({ patientCode })
        //     .populate('profession')
        //     .populate('country')
        //     .populate('nation')
        //     .populate('city')
        //     .populate('district')
        //     .populate('ward')
        //     .limit(1).exec();
        // if (findPatient.length > 0) {
        //     return findPatient.map(item => {
        //         const itemMap = item.toObject();
        //         const secretKeyObj: any = {};
        //         if (!isVerifiedByPhone) {
        //             const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        //             secretKeyObj.secretKey = jwt.sign({ patientId: itemMap.id }, jwtOptions.secret, jwtOptions.signOptions);
        //         }
        //         return {
        //             ...itemMap,
        //             birthdate: moment(itemMap.birthdate).isValid() ? moment(itemMap.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
        //             mobile: this.secretMobile(itemMap.mobile),
        //             isVerifiedByPhone,
        //             ...secretKeyObj,
        //         };
        //     });
        // }
        // if (partnerId === 'medpro') {
        //     return [];
        // }
        /* kiểm tra bên HIS */
        let resultData: any = {};
        let patientV1: any = null;
        try {
            if (this.listAppId.has(appid)) {
                const newSet = this.utilService.oldHospitalSearch();
                if (!!partnerId && newSet.has(partnerId)) {
                    /* tìm lại user_id và access_token */
                    const userMongo = await this.userModel.findById({ _id: userId }, { username: true, medproId: true }).exec();
                    await this.userService.getUserIdAndCreateSessionV1(appid, partnerId, userMongo);
                    const userV1 = await this.checkExistsUserByUsername(userMongo.medproId);
                    let session: any;
                    switch (partnerId) {
                        case 'nhidong1':
                            session = await this.sessionService.checkExistsNhiDong1SessionByUserID(userV1.id);
                            break;
                        case 'dalieuhcm':
                            session = await this.sessionService.checkExistsSkinSessionByUserID(userV1.id);
                            break;
                        case 'ctchhcm':
                            session = await this.sessionService.checkExistsCTCHSessionByUserID(userV1.id);
                            break;
                        case 'thuduc':
                            session = await this.sessionService.checkExistsThuDucSessionByUserID(userV1.id);
                            break;
                        case 'umc':
                            session = await this.sessionService.checkExistsUMCSessionByUserID(userV1.id);
                            break;
                        default:
                            session = {
                                user_id: 0,
                                access_token: '',
                            };
                            break;
                    }
                    // console.log({ msbn: patientCode, ...session }, partnerId);

                    const data = (await this.proxyFindByMsbn({ msbn: patientCode, ...session }, partnerId).toPromise()).data;

                    /* Tiến hành chuyển đổi thông tin */
                    // console.log('data from HIS', data);
                    const dataTransform = await this.transformDataSearch(partnerId, data);

                    // console.log('dataTransform', dataTransform);
                    if (dataTransform) {
                        /* Tiếp tục chuyển đổi */
                        switch (partnerId) {
                            case 'nhidong1':
                                patientV1 = await this.sessionService.getNhiDong1PatientV1(data.id);
                                break;
                            case 'dalieuhcm':
                                patientV1 = await this.sessionService.getDaLieuPatientV1(data.id);
                                break;
                            case 'ctchhcm':
                                patientV1 = await this.sessionService.getCTCHPatientV1(data.id);
                                break;
                            case 'thuduc':
                                patientV1 = await this.sessionService.getThuDucPatientV1(data.id);
                                break;
                            case 'umc':
                                patientV1 = await this.sessionService.getUMCPatientV1(data.id);
                                break;
                            default:
                                break;
                        }

                        // console.log('patient v1 n', patientV1);
                        const override: UMCPatientResponseDTO = {
                            Ten: patientV1.name,
                            Ho: patientV1.surname,
                            SoCMND: patientV1.cmnd,
                            GioiTinh: patientV1.sex === 1,
                            DiDong: patientV1.mobile,
                            DienThoai: patientV1.mobile,
                            IDDanToc: dataTransform.nationId,
                            IDNgheNghiep: dataTransform.professionId,
                            MaQuocGia: 'medpro_VIE',
                            IDTinh: dataTransform.cityId,
                            IDQuanHuyen: dataTransform.districtId,
                            IDPhuongXa: dataTransform.wardId,
                            DiaChi: patientV1.address,
                            NgaySinh: moment(patientV1.birthdate).isValid() ? moment(patientV1.birthdate).format('YYYY-MM-DD') : null,
                            NamSinh: patientV1.birthyear,
                            SoHS: patientCode,
                            NgungSD: false,
                        };
                        resultData = override;
                    } else {
                        throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                    }
                } else {
                    const data = (await this.getPatientByHIS(partnerId, patientCode).toPromise()).data;
                    resultData = data;
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: `resultPatientByHIS_${partnerId}`,
                        summary: 'Kết quả thông tin BN từ HIS nhidong1',
                        nameParent: 'findbymsbn',
                        response: resultData,
                        errorBody: null,
                    });
                }
            } else {
                const checkIsMobile = isMobilePhone(patientCode, 'vi-VN');
                if (checkIsMobile) {
                    const dataMobile = (await this.getPatientExtraByPhoneHIS(partnerId, patientCode).toPromise()).data;
                    if (dataMobile.length === 0) {
                        throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                    } else {
                        resultData = first(dataMobile);
                        patientCode = resultData.SoHS;
                    }
                } else {
                    const data = (await this.getPatientByHIS(partnerId, patientCode).toPromise()).data;
                    resultData = data;
                }
            }
        } catch (error) {
            /* Ưu tiên xử lý tìm theo mã số hồ sơ */
            // console.log('vao day', error)
            const { isSearchedByInsuranceCode = false, isSearcheMedpro = false } = partnerConfig;

            if (isSearchedByInsuranceCode) {
                try {
                    const dataInsuranceCode = (await this.getPatientExtraByInsuranceCodeHIS(partnerId, searchPatientDTO.msbn).toPromise()).data;
                    if (dataInsuranceCode.length === 0) {
                        /* Kiểm tra tiếp xem có config isSearcheMedpro hay không */
                        if (isSearcheMedpro) {
                            /* tìm trong medpro theo parttner id */
                            const medproIdSearch = `${searchPatientDTO.msbn}`.toUpperCase();
                            const findPatients = await this.patientModel
                                .find({ code: medproIdSearch })
                                .populate('profession')
                                .populate('country')
                                .populate('nation')
                                .populate('city')
                                .populate('district')
                                .populate('ward')
                                .limit(1).exec();
                            if (findPatients.length > 0) {
                                const firstPatientFound = first(findPatients);
                                const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, firstPatientFound._id);
                                const secretKeyObj: any = {};
                                if (!isVerifiedByPhone) {
                                    const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                                    secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                                }
                                return [{
                                    ...returnInfo,
                                    mobile: this.secretMobile(returnInfo.mobile),
                                    isVerifiedByPhone,
                                    ...secretKeyObj,
                                }];
                            } else {
                                this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                                    uuid: uuidLog,
                                    data: {
                                        status: HttpStatus.NOT_FOUND,
                                        message: 'Không tìm thấy thông tin bệnh nhân.',
                                    }
                                });
                                throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                            }
                        } else {
                            this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                                uuid: uuidLog,
                                data: {
                                    status: HttpStatus.NOT_FOUND,
                                    message: 'Không tìm thấy thông tin bệnh nhân.',
                                }
                            });
                            throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                        }
                    } else {
                        resultData = first(dataInsuranceCode);
                        patientCode = resultData.SoHS;
                    }
                } catch (error) {
                    // console.log('vao day 22222', error)
                    const { status = 404 } = error;
                    this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                        uuid: uuidLog,
                        data: {
                            status: status,
                        }
                    });
                    switch (status) {
                        case HttpStatus.NOT_FOUND:
                            throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                        default:
                            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
                    }
                }
            } else {
                /* Kiểm tra tiếp xem có config isSearcheMedpro hay không */
                if (isSearcheMedpro) {
                    /* tìm trong medpro theo parttner id */
                    const medproIdSearch = `${searchPatientDTO.msbn}`.toUpperCase();
                    const findPatients = await this.patientModel
                        .find({ code: medproIdSearch })
                        .populate('profession')
                        .populate('country')
                        .populate('nation')
                        .populate('city')
                        .populate('district')
                        .populate('ward')
                        .limit(1).exec();
                    if (findPatients.length > 0) {
                        const firstPatientFound = first(findPatients);
                        const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, firstPatientFound._id);
                        const secretKeyObj: any = {};
                        if (!isVerifiedByPhone) {
                            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                            secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                        }
                        return [{
                            ...returnInfo,
                            mobile: this.secretMobile(returnInfo.mobile),
                            isVerifiedByPhone,
                            ...secretKeyObj,
                        }];
                    } else {
                        this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                            uuid: uuidLog,
                            data: {
                                status: HttpStatus.NOT_FOUND,
                                message: 'Không tìm thấy thông tin bệnh nhân.',
                            }
                        });
                        throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                    }
                } else {
                    this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                        uuid: uuidLog,
                        data: {
                            status: HttpStatus.NOT_FOUND,
                            message: 'Không tìm thấy thông tin bệnh nhân.',
                        }
                    });
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                }
            }
        }
        /* kiểm tra xem bệnh nhân này ngưng sử dụng hay ko */
        const patient: UMCPatientResponseDTO = resultData;
        patientCode = patient?.SoHS || patientCode;
        // console.log('patient 2', patient);
        const NgungSD = patient.NgungSD;
        if (NgungSD) {
            throw new HttpException('Bệnh nhân này ngưng dùng.', HttpStatus.BAD_REQUEST);
        }
        try {
            /* kiểm tra xem thông tin ngày có chưa. nếu có rồi thì update */
            const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, patientCode);

            if (checkExists) {
                // const checkObj = checkExists.toObject();
                // console.log(checkExists);
                const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                    DiDong: patient.DiDong,
                    DienThoai: patient.DienThoai,
                    SoCMND: patient.SoCMND,
                    NgaySinh: patient.NgaySinh,
                    NamSinh: patient.NamSinh,
                    GioiTinh: patient.GioiTinh,
                    relative: {
                        relation: {
                            relative_name: patient?.relative?.fullname || '',
                            relative_email: patient?.relative?.email || '',
                            relative_mobile: patient?.relative?.phone || '',
                            relative_type_id: patient?.relative?.relativeType || ''
                        }
                    }
                });

                const patientObj = updateInfo.toObject();
                const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);

                const secretKeyObj: any = {};
                if (!isVerifiedByPhone) {
                    const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                    secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                }
                return [{
                    ...returnInfo,
                    mobile: this.secretMobile(returnInfo.mobile),
                    isVerifiedByPhone,
                    ...secretKeyObj,
                }];
            } else {
                patient.partnerId = partnerId;
                const patientInfo = await this.insertUMCSyncPatient(patient);
                const patientObj = patientInfo.toObject();
                /* kiểm tra xem có patientV1 hay không */
                if (patientV1) {
                    /* tiến hành cập nhật lại */
                    const patientIdV1 = patientV1.id || 0;
                    const keyPatientV1 = this.utilService.patientIdV1Key(partnerId);
                    await this.patientModel.findByIdAndUpdate({ _id: patientObj._id }, { [keyPatientV1]: patientIdV1 }).exec();
                }
                /* insert thêm vào trong patient_codes */
                const patientCodeInfo = new this.patientCodeModel({
                    id: `${partnerId}_${patientObj.id}`,
                    createTime: moment().toDate(),
                    patientId: patientObj.id,
                    patientCode: `${patientCode}`.trim(),
                    partnerId,
                    appId: appid,
                });
                await patientCodeInfo.save();
                // console.log(aaaa);
                /* kết thúc phần insert vào trong patient codes */
                const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                const secretKeyObj: any = {};
                if (!isVerifiedByPhone) {
                    const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                    secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                }
                this.emitService.emit(UPDATE_RESERVE_LOGS_EVENT, {
                    uuid: uuidLog,
                    data: {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...secretKeyObj,
                    }
                });
                return [{
                    ...returnInfo,
                    mobile: this.secretMobile(returnInfo.mobile),
                    isVerifiedByPhone,
                    ...secretKeyObj,
                }];
            }
        } catch (error) {
            const { status = 404 } = error;
            this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                uuid: uuidLog,
                data: {
                    status: status,
                }
            });
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }

    }

    async searchPatientByMsbnNhidong1(
        userId: string, appid: string, partnerId: string, searchPatientDTO: SearchPatientDTO, prefix: boolean = true,
        cskhInfo?: CSKHTokenVerifyDTO, req?: any): Promise<any> {
        // Kiểm tra xem search mấy lần rồi
        let isCS = false;
        if (userId) {
            await this.constraintSearchLog(userId, partnerId, cskhInfo);
            isCS = await this.userService.isCs(cskhInfo?.cskhUserId || userId);
        }
        const uuidLog = uuid.v4().replace(/-/g, '');
        try {
            //event logs
            this.emitService.emit(CREATE_FIND_PATIENT_MSBN_LOGS_EVENT, {
                uuid: uuidLog,
                partnerId,
                appId: appid,
                userId: userId,
                cskhInfo: cskhInfo,
                timestamp: new Date(),
                url: req.url,
                method: req.method,
                headers: req.headers,
                params: req.params,
                body: req.body,
                query: req.query,
                nameRepo: this.repoName,
                action: 'searchPatientByMsbn',
                msbn: searchPatientDTO.msbn,
            });
        } catch (error) {
            
        }

        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        let patientCode = searchPatientDTO.msbn;
        if (partnerId === 'umc') {
            prefix = false;
        }
        if (partnerConfig && prefix === true) {
            const removeSpecialCharacter = `${patientCode}`.replace(/[-]/g, '');
            patientCode = `${partnerConfig.prefixValuePatientCode}${removeSpecialCharacter}`;
        }
        // console.log('patient code origin', patientCode);
        const { isVerifiedByPhone: checkVerifiedPhone = false } = partnerConfig;
        let isVerifiedByPhone = isCS && partnerId !== 'nhidong1' ? false : checkVerifiedPhone;

        if (REPO_NAME_BETA.includes(this.repoName)) {
            isVerifiedByPhone = true;
        }
        
        /* kiểm tra bên HIS */
        let resultData: any = {};
        let patientV1: any = null;
        try {
            const data = (await this.proxyFindByMsbnNhidong1({ msbn: patientCode }).toPromise()).data;
            if (!data?.DiDong || data?.DiDong === '') {
                throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
            }
            resultData = data;
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: `resultPatientByHIS_${partnerId}`,
                summary: 'Kết quả thông tin BN từ HIS nhidong1',
                nameParent: 'findbymsbn',
                response: resultData,
                errorBody: null,
            });
        } catch (error) {
            this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                uuid: uuidLog,
                data: {
                    status: error?.response?.data?.statusCode,
                    message: error.message || 'Không tìm thấy thông tin bệnh nhân.',
                    endpoint: this.oldUrl.NhiDong1GetByMSBNV2(),
                    error: JSON.stringify(error, null, 2)
                }
            });
            /* Ưu tiên xử lý tìm theo mã số hồ sơ */
            // console.log('vao day', error)
            const { isSearchedByInsuranceCode = false, isSearcheMedpro = false } = partnerConfig;
            
            if (isSearchedByInsuranceCode) {
                try {
                    const dataInsuranceCode = (await this.getPatientExtraByInsuranceCodeHIS(partnerId, searchPatientDTO.msbn).toPromise()).data;
                    if (dataInsuranceCode.length === 0) {
                        /* Kiểm tra tiếp xem có config isSearcheMedpro hay không */
                        if (isSearcheMedpro) {
                            /* tìm trong medpro theo parttner id */
                            const medproIdSearch = `${searchPatientDTO.msbn}`.toUpperCase();
                            const findPatients = await this.patientModel
                                .find({ code: medproIdSearch })
                                .populate('profession')
                                .populate('country')
                                .populate('nation')
                                .populate('city')
                                .populate('district')
                                .populate('ward')
                                .limit(1).exec();

                            console.log('findPatients', findPatients);
                            
                            if (findPatients.length > 0) {
                                const firstPatientFound = first(findPatients);
                                const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, firstPatientFound._id);
                                const secretKeyObj: any = {};
                                if (!isVerifiedByPhone) {
                                    const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                                    secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                                }
                                console.log('returnInfo0', returnInfo);
                                
                                return [{
                                    ...returnInfo,
                                    mobile: this.secretMobile(returnInfo.mobile),
                                    isVerifiedByPhone,
                                    ...secretKeyObj,
                                }];
                            } else {
                                throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                            }
                        } else {
                            throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                        }
                    } else {
                        resultData = first(dataInsuranceCode);
                        patientCode = resultData.SoHS;
                    }
                } catch (error) {
                    console.log('error1', JSON.stringify(error, null, 2));
                    // console.log('vao day 22222', error)
                    const { status = 404 } = error;
                    switch (status) {
                        case HttpStatus.NOT_FOUND:
                            throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                        default:
                            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
                    }
                }
            } else {
                /* Kiểm tra tiếp xem có config isSearcheMedpro hay không */
                if (isSearcheMedpro) {
                    /* tìm trong medpro theo parttner id */
                    const medproIdSearch = `${searchPatientDTO.msbn}`.toUpperCase();
                    const findPatients = await this.patientModel
                        .find({ code: medproIdSearch })
                        .populate('profession')
                        .populate('country')
                        .populate('nation')
                        .populate('city')
                        .populate('district')
                        .populate('ward')
                        .limit(1).exec();
                    if (findPatients.length > 0) {
                        const firstPatientFound = first(findPatients);
                        const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, firstPatientFound._id);
                        const secretKeyObj: any = {};
                        if (!isVerifiedByPhone) {
                            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                            secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                        }
                        return [{
                            ...returnInfo,
                            mobile: this.secretMobile(returnInfo.mobile),
                            isVerifiedByPhone,
                            ...secretKeyObj,
                        }];
                    } else {
                        throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                    }
                } else {
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                }
            }
        }
        /* kiểm tra xem bệnh nhân này ngưng sử dụng hay ko */
        const patient: UMCPatientResponseDTO = resultData;
        patientCode = patient?.SoHS || patientCode;
        // console.log('patient 2', patient);
        const NgungSD = patient.NgungSD;
        if (NgungSD) {
            throw new HttpException('Bệnh nhân này ngưng dùng.', HttpStatus.BAD_REQUEST);
        }
        try {
            /* kiểm tra xem thông tin ngày có chưa. nếu có rồi thì update */
            const info = await this.convertKeyNhiDong1SyncPatient(resultData);

            const objName: any = {};
            const address = 'Chọn thông tin hồ sơ, sau đó xác thực số điện thoại.'
            if (info?.surname && info?.name) {
                objName.fullname = `${info?.surname} ${info?.name}`
                objName.surname = info?.surname
                objName.fullname = info?.fullname
            } else {
                objName.fullname = 'Chờ xác thực số điện thoại'
                objName.surname = 'Chờ'
                objName.name = 'xác thực số điện thoại'
            }

            // console.log('updateInfo', updateInfo);
            
            // const patientObj = updateInfo.toObject();
            // const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
            // console.log('returnInfo1', returnInfo);
            
            const secretKeyObj: any = {};
            // if (!isVerifiedByPhone) {
            //     const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
            //     secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
            // }

            this.emitService.emit(UPDATE_RESERVE_LOGS_EVENT, {
                uuid: uuidLog,
                data: {
                    ...info,
                    ...objName,
                    address,
                    // mobile: secretMobile,
                    patientCode: resultData?.SoHS,
                    isVerifiedByPhone,
                    // ...secretKeyObj,
                }
            });
            return [{
                ...info,
                ...objName,
                address,
                patientCode: resultData?.SoHS,
                isVerifiedByPhone,
                // ...secretKeyObj,
            }];
        } catch (error) {
            console.log('error', JSON.stringify(error, null, 2))
            const { status = 404 } = error;
            this.emitService.emit(UPDATE_ERROR_RESERVE_LOGS_EVENT, {
                uuid: uuidLog,
                data: {
                    status: status,
                }
            });
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }

    }

    async searchPatientByMsbnCard(
        userId: string, appid: string, partnerId: string, searchPatientDTO: SearchPatientDTO, prefix: boolean = true): Promise<any> {
        /* Lấy thông tin partner config */
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        let patientCode = searchPatientDTO.msbn;
        if (partnerId === 'umc') {
            prefix = false;
        }
        if (partnerConfig && prefix === true) {
            const removeSpecialCharacter = `${patientCode}`.replace(/[-]/g, '');
            patientCode = `${partnerConfig.prefixValuePatientCode}${removeSpecialCharacter}`;
        }
        const isVerifiedByPhone = false;
        /* kiểm tra bên HIS */
        let resultData: any = {};
        try {
            const data = (await this.getPatientByHIS(partnerId, patientCode).toPromise()).data;
            resultData = data;
        } catch (error) {
            return {
                message: 'Không tìm thấy hồ sơ theo mã số hồ sơ',
                isFound: false,
            };
        }
        /* kiểm tra xem bệnh nhân này ngưng sử dụng hay ko */
        const patient: UMCPatientResponseDTO = resultData;
        const NgungSD = patient.NgungSD;
        if (NgungSD) {
            return {
                message: 'Hồ sơ ngày ngưng sử dụng. Vui lòng liên hệ bệnh viện.',
                isFound: false,
            };
        }
        try {
            /* kiểm tra xem thông tin ngày có chưa. nếu có rồi thì update */
            const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, patientCode);
            if (checkExists) {
                const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                    DiDong: patient.DiDong,
                    DienThoai: patient.DienThoai,
                    SoCMND: patient.SoCMND,
                    NgaySinh: patient.NgaySinh,
                    NamSinh: patient.NamSinh,
                    GioiTinh: patient.GioiTinh,
                });
                const getPatientByRefId = await this.getPatientByREf(updateInfo._id);
                return {
                    isFound: true,
                    patient: getPatientByRefId,
                    message: 'Cập nhật lại mã hồ sơ thành công.',
                };
            } else {
                patient.partnerId = partnerId;
                const patientInfo = await this.insertUMCSyncPatient(patient);
                const patientObj = patientInfo.toObject();
                /* insert thêm vào trong patient_codes */
                const patientCodeInfo = new this.patientCodeModel({
                    id: `${partnerId}_${patientObj.id}`,
                    createTime: moment().toDate(),
                    patientId: patientObj.id,
                    patientCode: `${patientCode}`.trim(),
                    partnerId,
                    appId: appid,
                });
                await patientCodeInfo.save();
                return {
                    isFound: true,
                    patient: patientInfo,
                    message: 'Tạo mới mã hồ sơ thành công.',
                };
            }
        } catch (error) {
            console.log(error);
            return {
                isFound: false,
                message: 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.',
            };
        }

    }

    postPatientByHIS(url, params): Observable<AxiosResponse<any>> {
        return this.httpService.post(url, params);
    }

    async constraintSearchLog(userId: string, partnerId: string, cskhInfo?: CSKHTokenVerifyDTO): Promise<any> {
        const env = this.urlConfigService.getEnv();
        const isCS = await this.userService.isCs(cskhInfo?.cskhUserId || userId);
        if (env === 'PRODUCTION') {
            if (userId && !isCS) {
                const countValue = await this.patientSearchLogModel.find({ userId, partnerId, date: moment().format('YYYY-MM-DD') }).count();
                const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
                const { isCountSearchConstraints = true } = partnerConfig;
                if (countValue > this.countSearchPatient && isCountSearchConstraints) {
                    throw new HttpException(
                        'Bạn vượt quá giới hạn tìm kiếm. Cần hỗ trợ vui lòng liên hệ với chúng tôi qua số 1900 2115', HttpStatus.TOO_MANY_REQUESTS);
                }
                /* tiến hành insert vào trong db */
                const patientSearchLog = new this.patientSearchLogModel({
                    userId,
                    partnerId,
                    date: moment().format('YYYY-MM-DD'),
                    checkValue: 1,
                });
                await patientSearchLog.save();
            }
        }
    }

    // if (userId) {
    //     const countValue = await this.patientSearchLogModel.find({ userId, partnerId, date: moment().format('YYYY-MM-DD') }).count();
    //     if (countValue > this.countSearchPatient) {
    //         throw new HttpException(
    //             'Bạn vượt quá giới hạn tìm kiếm. Cần hỗ trợ vui lòng liên hệ với chúng tôi qua số 19002115', HttpStatus.TOO_MANY_REQUESTS);
    //     }
    //     /* tiến hành insert vào trong db */
    //     const patientSearchLog = new this.patientSearchLogModel({
    //         userId,
    //         partnerId,
    //         date: moment().format('YYYY-MM-DD'),
    //         checkValue: 1,
    //     });
    //     await patientSearchLog.save();
    // }
    /* Lấy thông tin partner config */

    /* Kiểm tra bên medpro trước */
    // const findPatients = await this.patientModel
    //     .find(
    //         { $text: { $search: `${params.Ho} ${params.Ten}` }, sex: params.GioiTinh, birthyear: params.NamSinh, city_id: params.IDTinh },
    //         { score: { $meta: 'textScore' } },
    //     )
    //     .populate('profession')
    //     .populate('country')
    //     .populate('nation')
    //     .populate('city')
    //     .populate('district')
    //     .populate('ward')
    //     .sort({ score: { $meta: 'textScore' } })
    //     .exec();
    // if (findPatients.length > 0) {
    //     return findPatients.map(item => {
    //         const itemMap = item.toObject();
    //         const secretKeyObj: any = {};
    //         if (!isVerifiedByPhone) {
    //             const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
    //             secretKeyObj.secretKey = jwt.sign({ patientId: itemMap.id }, jwtOptions.secret, jwtOptions.signOptions);
    //         }
    //         return {
    //             ...itemMap,
    //             birthdate: moment(itemMap.birthdate).isValid() ? moment(itemMap.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
    //             mobile: this.secretMobile(itemMap.mobile),
    //             isVerifiedByPhone,
    //             ...secretKeyObj,
    //         };
    //     });
    // }
    // if (partnerId === 'medpro') {
    //     return [];
    // }

    async processSearchPatientByExtraInfo(
        appid: string,
        partnerId: string,
        userId: string,
        searchPatientExtraInfoDTO: SearchPatientExtraInfoMongoDTO,
        cskhInfo?: CSKHTokenVerifyDTO,
    ): Promise<any> {
        const isCS = await this.userService.isCs(cskhInfo?.cskhUserId || userId);
        try {
            const params = {
                Ho: searchPatientExtraInfoDTO.surName,
                Ten: searchPatientExtraInfoDTO.firstName,
                NamSinh: searchPatientExtraInfoDTO.birthYear,
                GioiTinh: searchPatientExtraInfoDTO.gender,
                IDTinh: searchPatientExtraInfoDTO.cityId,
            };
            const data = await(await this.getPatientExtraByHIS(partnerId, params).pipe(timeout(3000)).toPromise()).data;
            const filterData: UMCPatientExtraInfoResponseDTO[] = data.filter((item: UMCPatientExtraInfoResponseDTO) => {
                return !item.NgungSD;
            });
            let resultData = [];
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            const { isVerifiedByPhone: checkVerifiedPhone = false } = partnerConfig;
            const isVerifiedByPhone = isCS ? false : checkVerifiedPhone;
            for await (const item of filterData) {
                const pInfo: UMCPatientResponseDTO = item;
                const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, pInfo.SoHS);
                if (checkExists) {
                    const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                        DiDong: pInfo.DiDong,
                        DienThoai: pInfo.DienThoai,
                        SoCMND: pInfo.SoCMND,
                        NgaySinh: pInfo.NgaySinh,
                        NamSinh: pInfo.NamSinh,
                        GioiTinh: pInfo.GioiTinh,
                    });
                    const patientObj = updateInfo.toObject();
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [
                        ...resultData,
                        {
                            ...returnInfo,
                            mobile: this.secretMobile(returnInfo.mobile),
                            isVerifiedByPhone,
                            ...secretKeyObj,
                        },
                    ];
                } else {
                    pInfo.partnerId = partnerId;
                    const patientInfo = await this.insertUMCSyncPatient(pInfo);
                    const patientObj = patientInfo.toObject();
                    /* insert thêm vào trong patient_codes */
                    const patientCodeInfo = new this.patientCodeModel({
                        id: `${partnerId}_${patientObj.id}`,
                        createTime: moment().toDate(),
                        patientId: patientObj.id,
                        patientCode: pInfo.SoHS,
                        partnerId,
                        appId: appid,
                    });
                    await patientCodeInfo.save();
                    /* kết thúc phần insert vào trong patient codes */
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [
                        ...resultData,
                        {
                            ...returnInfo,
                            mobile: this.secretMobile(returnInfo.mobile),
                            isVerifiedByPhone,
                            ...secretKeyObj,
                        },
                    ];
                }
            }
            return resultData;
        } catch (error) {
            if (error.name === 'TimeoutError') {
                console.log('TimeoutError', JSON.stringify(error, null, 2))
            }
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async processSearchPatientByExtraInfoV2(
        appid: string,
        partnerId: string,
        userId: string,
        searchPatientExtraInfoDTO: SearchPatientExtraInfoMongoDTO,
        cskhInfo?: CSKHTokenVerifyDTO,
    ): Promise<any> {
        try {
            const params = {
                Ho: searchPatientExtraInfoDTO.surName,
                Ten: searchPatientExtraInfoDTO.firstName,
                NamSinh: searchPatientExtraInfoDTO.birthYear,
                GioiTinh: searchPatientExtraInfoDTO.gender,
                IDTinh: searchPatientExtraInfoDTO.cityId,
            };
            return (await this.getPatientExtraByHIS(partnerId, params).toPromise()).data;
        } catch (error) {
            console.log('error', JSON.stringify(error, null, 2))
            return {
                error: JSON.stringify(error, null, 2)
            }
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async processSearchPhoneCMNDPatientByExtraInfo(
        appid: string, partnerId: string, userId: string, searchPatientExtraInfoDTO: SearchPhoneCMNDPatientExtraInfoMongoDTO): Promise<any> {
        const isCS = await this.userService.isCs(userId);
        try {
            const data = (await this.getPatientByPhoneOrCMNDByHIS(
                partnerId, searchPatientExtraInfoDTO.mobile,
                searchPatientExtraInfoDTO.cmnd).pipe(timeout(3000)).toPromise()).data;
            if (data.length === 0) {
                throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
            }
            const filterData: UMCPatientExtraInfoResponseDTO[] = data.filter((item: UMCPatientExtraInfoResponseDTO) => {
                return !item.NgungSD;
            });
            let resultData = [];
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            const { isVerifiedByPhone: checkVerifiedPhone = false } = partnerConfig;
            const isVerifiedByPhone = isCS ? false : checkVerifiedPhone;
            for await (const item of filterData) {
                const pInfo: UMCPatientResponseDTO = item;
                const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, pInfo.SoHS);
                if (checkExists) {
                    const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                        DiDong: pInfo.DiDong,
                        DienThoai: pInfo.DienThoai,
                        SoCMND: pInfo.SoCMND,
                        NgaySinh: pInfo.NgaySinh,
                        NamSinh: pInfo.NamSinh,
                        GioiTinh: pInfo.GioiTinh,
                    });
                    const patientObj = updateInfo.toObject();
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [...resultData, {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...secretKeyObj,
                    }];
                } else {
                    pInfo.partnerId = partnerId;
                    const patientInfo = await this.insertUMCSyncPatient(pInfo);
                    const patientObj = patientInfo.toObject();
                    /* insert thêm vào trong patient_codes */
                    const patientCodeInfo = new this.patientCodeModel({
                        id: `${partnerId}_${patientObj.id}`,
                        createTime: moment().toDate(),
                        patientId: patientObj.id,
                        patientCode: pInfo.SoHS,
                        partnerId,
                        appId: appid,
                    });
                    await patientCodeInfo.save();
                    /* kết thúc phần insert vào trong patient codes */
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [...resultData, {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...secretKeyObj,
                    }];
                }
            }
            return resultData;
        } catch (error) {
            if (error.name === 'TimeoutError') {
                console.log('TimeoutError', JSON.stringify(error, null, 2))
            }
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async processSearchPatientByCccdPartner(
        appid: string, partnerId: string, userId: string, searchPatientExtraInfoDTO: SearchPhoneCMNDPatientExtraInfoMongoDTO): Promise<any> {
        const isCS = await this.userService.isCs(userId);
        try {
            const timeoutConfig = await this.globalSettingService.findByKeyAndRepoName('FIND_PATIENT_HIS_TIMEOUT');
            const timeoutConfigNumber = timeoutConfig ? +timeoutConfig : 3000; // Default to 3 seconds if not set
            const data = (await this.getPatientsByCccdPartnerId(
                searchPatientExtraInfoDTO.cmnd, partnerId).pipe(timeout(timeoutConfigNumber)).toPromise()).data;
            
            if (data.length === 0) {
                throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
            }
            // const filterData: UMCPatientExtraInfoResponseDTO[] = data.filter((item: UMCPatientExtraInfoResponseDTO) => {
            //     return !item.NgungSD;
            // });
            let resultData = [];
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            const { isVerifiedByPhone: checkVerifiedPhone = false } = partnerConfig;
            const isVerifiedByPhone = isCS ? false : checkVerifiedPhone;
            for await (const item of data) {
                const pInfo: any = item;
                const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, pInfo.SoHS);
                if (checkExists) {
                    const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                        DiDong: pInfo.DiDong,
                        DienThoai: pInfo.DienThoai,
                        SoCMND: pInfo.SoCMND,
                        NgaySinh: pInfo.NgaySinh,
                        NamSinh: pInfo.NamSinh,
                        GioiTinh: pInfo.GioiTinh,
                    });
                    const patientObj = updateInfo.toObject();
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [...resultData, {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...secretKeyObj,
                    }];
                } else {
                    pInfo.partnerId = partnerId;
                    const patientInfo = await this.insertUMCSyncPatient(pInfo);
                    const patientObj = patientInfo.toObject();
                    /* insert thêm vào trong patient_codes */
                    const patientCodeInfo = new this.patientCodeModel({
                        id: `${partnerId}_${patientObj.id}`,
                        createTime: moment().toDate(),
                        patientId: patientObj.id,
                        patientCode: pInfo.SoHS,
                        partnerId,
                        appId: appid,
                    });
                    await patientCodeInfo.save();
                    /* kết thúc phần insert vào trong patient codes */
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [...resultData, {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...secretKeyObj,
                    }];
                }
            }
            console.log('resultData', resultData);
            
            return resultData;
        } catch (error) {
            if (error.name === 'TimeoutError') {
                console.log('TimeoutError', JSON.stringify(error, null, 2))
            }
            console.log('error', JSON.stringify(error, null, 2))
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async findUMCPatientByExtraInfo(
        userId: string, appid: string, partnerId: string, searchPatientExtraInfoDTO: SearchPatientExtraInfoMongoDTO,
        cskhInfo?: CSKHTokenVerifyDTO, updateToPatientId?: string): Promise<any> {

        if (partnerId === 'nhidong1') {
            return this.findPatientByExtraInfoNhidong1(userId, appid, partnerId, searchPatientExtraInfoDTO, cskhInfo)
        }
        // Kiểm tra xem search mấy lần rồi
        await this.constraintSearchLog(userId, partnerId, cskhInfo);
        /* tiến hành xử lý */
        const isCS = await this.userService.isCs(cskhInfo?.cskhUserId || userId);
        try {
            let data: any;
            const newSet = this.utilService.oldHospitalSync();
            if (!!partnerId && newSet.has(partnerId)) {
                data = await this.processSearchPatientByExtraInfoV1(userId, appid, partnerId, searchPatientExtraInfoDTO, cskhInfo);
            } else {
                data = await this.processSearchPatientByExtraInfoV2(appid, partnerId, userId, searchPatientExtraInfoDTO, cskhInfo);
            }
            if (data.length === 0 || data.error_code === 1025) {
                throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
            }
            const filterData: UMCPatientExtraInfoResponseDTO[] = data.filter((item: UMCPatientExtraInfoResponseDTO) => {
                return !item.NgungSD;
            });
            let resultData = [];
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            const { isVerifiedByPhone: checkVerifiedPhone = false } = partnerConfig;
            const isVerifiedByPhone = isCS ? false : checkVerifiedPhone;
            for await (const item of filterData) {
                const pInfo: UMCPatientResponseDTO = item;
                const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, pInfo.SoHS);
                if (checkExists) {
                    const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                        DiDong: pInfo.DiDong,
                        DienThoai: pInfo.DienThoai,
                        SoCMND: pInfo.SoCMND,
                        NgaySinh: pInfo.NgaySinh,
                        NamSinh: pInfo.NamSinh,
                        GioiTinh: pInfo.GioiTinh,
                    });
                    const patientObj = updateInfo.toObject();
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id, updateToPatientId: updateToPatientId || '' }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [...resultData, {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...secretKeyObj,
                    }];
                } else {
                    pInfo.partnerId = partnerId;
                    const patientInfo = await this.insertUMCSyncPatient(pInfo);
                    const patientObj = patientInfo.toObject();
                    /* insert thêm vào trong patient_codes */
                    const patientCodeInfo = new this.patientCodeModel({
                        id: `${partnerId}_${patientObj.id}`,
                        createTime: moment().toDate(),
                        patientId: patientObj.id,
                        patientCode: pInfo.SoHS,
                        partnerId,
                        appId: appid,
                    });
                    await patientCodeInfo.save();
                    /* kết thúc phần insert vào trong patient codes */
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id, updateToPatientId: updateToPatientId || '' }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [...resultData, {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...secretKeyObj,
                    }];
                }
            }
            return resultData;
        } catch (error) {
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async findPatientByExtraInfoNhidong1(
        userId: string, appid: string, partnerId: string, searchPatientExtraInfoDTO: SearchPatientExtraInfoMongoDTO,
        cskhInfo?: CSKHTokenVerifyDTO, updateToPatientId?: string): Promise<any> {

        // Kiểm tra xem search mấy lần rồi
        await this.constraintSearchLog(userId, partnerId, cskhInfo);
        /* tiến hành xử lý */
        const isCS = await this.userService.isCs(cskhInfo?.cskhUserId || userId);
        try {
            let data: any;
            data = await this.processSearchPatientByExtraInfoV2(appid, partnerId, userId, searchPatientExtraInfoDTO, cskhInfo);
            console.log('data', data);
            
            if (data.length === 0 || data.error_code === 1025 || data.error) {
                // throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                return data;
            }
            const filterData: UMCPatientExtraInfoResponseDTO[] = data.filter((item: UMCPatientExtraInfoResponseDTO) => {
                return !item.NgungSD;
            });
            console.log('filterData', filterData);
            
            let resultData = [];
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            const { isVerifiedByPhone: checkVerifiedPhone = false } = partnerConfig;
            let isVerifiedByPhone = isCS ? false : checkVerifiedPhone;

            if (REPO_NAME_BETA.includes(this.repoName)) {
                isVerifiedByPhone = true;
            }

            for await (const item of filterData) {
                const pInfo: UMCPatientResponseDTO = item;
                pInfo.partnerId = partnerId;
                const patientObj = await this.convertKeyNhiDong1SyncPatient(pInfo);
                
                resultData = [...resultData, {
                    ...patientObj,
                    patientCode: pInfo?.SoHS,
                    mobile: this.secretMobile(patientObj.mobile),
                    isVerifiedByPhone,
                }];
            }
            return resultData;
        } catch (error) {
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async findPatientHis(userId: string, appid: string, partnerId: string, body: FindPatientHisDto, cskhInfo?: any, locale = 'vi') {

        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        const jwtVerify: any = jwt.verify(body.secretPatientId, jwtOptions.secret);

        const patientId = jwtVerify.patientId;
        const patient = await this.patientModel.findOne({ id: patientId }).exec();

        const patientCode = await this.getPatientCodeByPatientId(patientId, partnerId);

        if (patientCode) {
            throw new HttpException('Hồ sơ này đã có msbn', 409);
        }

        if (!patient) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ', 404);
        }

        const existed = await this.checkUMCPatientBelongsToUser(userId, patient.toObject()._id);
        if (!existed) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ', 404);
        }

        const promise = this.findUMCPatientByExtraInfo(
            userId,
            appid,
            partnerId,
            {
                birthYear: patient.birthyear,
                cityId: patient.city_id,
                firstName: patient.name,
                surName: patient.surname,
                gender: patient.sex,
            },
            cskhInfo,
            patientId,
        );


        try {
            const { findPatientHISTimeout = 2000 } = await this.partnerConfigModel.findOne({ partnerId }, { findPatientHISTimeout: true }).exec();
            const patients: any = await from(promise).pipe(
                timeout(findPatientHISTimeout),
            ).toPromise();

            return patients.map(p => {
                return {
                    ...p,
                    secretPatientId: jwt.sign({ patientId, patientHisId: p.id }, jwtOptions.secret, jwtOptions.signOptions),
                }
            })
        } catch (err) {
            if (err.name === 'TimeoutError') {
                throw new HttpException('Không tìm thấy thông tin bệnh nhân [Timeout]', HttpStatus.REQUEST_TIMEOUT)
            }

            throw err;
        }


    }

    async processSearchPatientByExtraInfoV1(
        userId: string,
        appid: string,
        partnerId: string,
        searchPatientExtraInfoDTO: SearchPatientExtraInfoMongoDTO,
        cskhInfo?: CSKHTokenVerifyDTO,
    ): Promise<any> {
        const params = { userId, appid, partnerId, ...searchPatientExtraInfoDTO, ...cskhInfo };
        try {
            const userMongo = await this.userModel.findById({ _id: userId }, { username: true, medproId: true }).exec();
            const userV1 = await this.checkExistsUserByUsername(userMongo.medproId);
            let session: any = {};
            /* tìm lại thông tin session theo partnerid */
            switch (partnerId) {
                case 'umc':
                    session = await this.sessionService.checkExistsUMCSessionByUserID(userV1.id);
                    break;
                case 'nhidong1':
                    session = await this.sessionService.checkExistsUMCSessionByUserID(userV1.id);
                    break;
                default:
                    break;
            }
            const { user_id, access_token } = session;
            let searchInfo: FindPatientDto = {
                surname: searchPatientExtraInfoDTO.surName,
                name: searchPatientExtraInfoDTO.firstName,
                birthyear: searchPatientExtraInfoDTO.birthYear,
                sex: searchPatientExtraInfoDTO.gender,
                city_id: searchPatientExtraInfoDTO.cityId,
                access_token,
                user_id,
            };
            const sign = jwt.sign({ ...searchInfo }, this.urlConfigService.getKeyNameV1());
            searchInfo = { ...searchInfo, sign };
            return (await this.callGetPatientProfileV1(searchInfo).toPromise()).data;
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'callGetPatientProfileV1',
                summary: 'Lấy thông tin BN từ V1',
                nameParent: 'findUMCPatientByExtraInfo',
                params,
                errorBody: this.utilService.errorHandler(error),
            });
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async verifyUMCPatientByPhone(partnerId: string, appId: string, verifyPhonePatientDTO: VerifyPhonePatientMongoDTO): Promise<any> {

        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();

        let patientHisId;
        let payloadToken: any = {}

        if (verifyPhonePatientDTO.secretPatientId) {
            payloadToken = jwt.verify(verifyPhonePatientDTO.secretPatientId, jwtOptions.secret);
            patientHisId = payloadToken.patientHisId;
        } else {
            patientHisId = verifyPhonePatientDTO.patientId;
        }

        //verify nhidong1
        if (partnerId === 'nhidong1' && !verifyPhonePatientDTO?.secretPatientId && !verifyPhonePatientDTO.patientId) {
            let data: any = {};
            try {
                data = (await this.proxyVerifyPatientNhidong1({
                    partnerId,
                    patientId: verifyPhonePatientDTO.msbn,
                    phone: verifyPhonePatientDTO.phone
                }).toPromise()).data;
                
            } catch (error) {
                console.log('error', JSON.stringify(error, null, 2))
                throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
            }

            const info = await this.convertKeyNhiDong1SyncPatient(data);

            let updatedData: any;
            try {
                const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, verifyPhonePatientDTO.msbn);
                console.log('checkExists', checkExists);
                
                if (checkExists) {
                    updatedData = await this.patientModel.findOneAndUpdate({ id: checkExists.patientId }, { ...info }, { new: true }).exec();
                    console.log('updatedData0', updatedData);
                } else {
                    // patient.partnerId = partnerId;
                    const uuidv4 = uuid.v4();
                    const patientInfoInsert = new this.patientModel({ ...info, id: uuidv4.replace(/-/g, ''),});
                    updatedData = await patientInfoInsert.save();
                    console.log('updatedData1', updatedData);
                    /* kiểm tra xem có patientV1 hay không */
                    // if (patientV1) {
                    //     /* tiến hành cập nhật lại */
                    //     const patientIdV1 = patientV1.id || 0;
                    //     const keyPatientV1 = this.utilService.patientIdV1Key(partnerId);
                    //     await this.patientModel.findByIdAndUpdate({ _id: patientObj._id }, { [keyPatientV1]: patientIdV1 }).exec();
                    // }
                    /* insert thêm vào trong patient_codes */
                    const patientCodeInfo = new this.patientCodeModel({
                        id: `${partnerId}_${updatedData.id}`,
                        createTime: moment().toDate(),
                        patientId: updatedData.id,
                        patientCode: `${verifyPhonePatientDTO.msbn}`.trim(),
                        partnerId,
                        appId,
                    });
                    await patientCodeInfo.save();
                }
            } catch (error) {
                
            }
            
            const infoObj = await this.getPatientInfoAndPatientCodeById(partnerId, appId, updatedData._id);

            const secretKey = jwt.sign({ patientId: infoObj.id, updateToPatientId: payloadToken.patientId || '', phone: verifyPhonePatientDTO.phone }, jwtOptions.secret, jwtOptions.signOptions);
            return {
                patient: { ...infoObj, secretKey },
                secretKey,
            };
        }

        //default
        const infoObj = await this.getUMCPatientShortInfoVerifyPhoneByPatientId(partnerId, appId, patientHisId);

        if (!infoObj) {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }

        console.log('verifyPhonePatientDTO', verifyPhonePatientDTO);
        console.log('infoObj', infoObj);

        if (verifyPhonePatientDTO.msbn === `${infoObj.patientCode}`.trim() && verifyPhonePatientDTO.phone === infoObj.mobile) {
            // console.log(infoObj);
            const secretKey = jwt.sign({ patientId: patientHisId, updateToPatientId: payloadToken.patientId || '', phone: verifyPhonePatientDTO.phone }, jwtOptions.secret, jwtOptions.signOptions);
            // const info = await this.getPatientInfoAndPatientCodeById(partnerId, appId, infoObj._id);
            return {
                patient: { ...infoObj, secretKey },
                secretKey,
            };
        }
        throw new HttpException('Thông tin gửi lên không chính xác. Vui lòng kiểm tra lại!', HttpStatus.NOT_FOUND);
    }

    async verifyPatientByInsuranceCode(partnerId: string, appId: string, formData: VerifyInsuranceCodePatientMongoDTO): Promise<any> {
        const infoObj = await this.getUMCPatientShortInfoVerifyInsuranceCodeByPatientId(partnerId, appId, formData.patientId);
        if (!infoObj) {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }
        if (formData.msbn === `${infoObj.patientCode}`.trim() && formData.insuranceCode === infoObj.insuranceCode) {
            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
            const secretKey = jwt.sign({ patientId: formData.patientId }, jwtOptions.secret, jwtOptions.signOptions);
            return {
                patient: { ...infoObj, secretKey },
                secretKey,
            };
        }
        throw new HttpException('Thông tin gửi lên không chính xác. Vui lòng kiểm tra lại!', HttpStatus.NOT_FOUND);
    }

    async addPatientToUserUMCPatient(partnerId: string, appId: string, addPatientToUserDTO: AddPatientToUserDTO, userId: string,
                                     cskhInfo?: CSKHTokenVerifyDTO): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        let patientId = '';
        let updateToPatientId = '';

        try {
            const jwtVerify: any = jwt.verify(addPatientToUserDTO.secretKey, jwtOptions.secret);
            patientId = jwtVerify.patientId;
            updateToPatientId = jwtVerify.updateToPatientId;
        } catch (error) {
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }
        /* xử lý quá trình add patient to user */
        const isCS = await this.userService.isCs(cskhInfo?.cskhUserId || userId);
        const isExam = addPatientToUserDTO.isExam ? addPatientToUserDTO.isExam : false;
        const maxPatient = appId === 'bvmathcm' ? 5 : this.countPatient;
        if (!isCS && !isExam) {
            const userWithPatients = await this.getCountPatientInUserPatientUMC(userId);
            if (userWithPatients.patients.length >= maxPatient) {
                throw new HttpException(
                    `Số lượng hồ sơ bệnh nhân vượt quá tối đa ${maxPatient} hồ sơ trên mỗi tài khoản. Liên hệ tổng đài đặt khám nhanh 19002115 để được hỗ trợ.`, HttpStatus.FORBIDDEN);
            }
        }

        /* tìm lại thông tin patient _id */
        const findPatient = await this.patientModel.findOne({ id: patientId }).exec();

        if (updateToPatientId) {
            const updateToPatient = await this.patientModel.findOne({ id: updateToPatientId }).read('primary').exec();

            const checkPatientCode = await this.patientCodeModel.findOne({ patientId: updateToPatient.id, partnerId }).read('primary').exec();

            if (checkPatientCode) {
                if (`${updateToPatient.id}` === `${findPatient.id}`) {
                    return this.getPatientInfoById(updateToPatient._id, partnerId);
                } else {
                    throw new HttpException('Hồ sơ này đã có mã Bệnh Nhân', 409);
                }
            } else {
                const patientCodeItem = await this.patientCodeModel.findOne({ patientId: findPatient.id, partnerId }).exec();

                if (patientCodeItem?.patientCode) {
                    await this.patientCodeModel.create({
                        id: `${partnerId}_${updateToPatient.id}`,
                        patientId: updateToPatient.id,
                        partnerId: partnerId,
                        patientCode: patientCodeItem.patientCode,
                        appId,
                    })

                    let updatePatientData : any = {
                        district_id: findPatient.district_id,
                        district: findPatient.district,
                        ward_id: findPatient.ward_id,
                        ward: findPatient.ward,
                        profession_id: findPatient.profession_id,
                        profession: findPatient.profession,
                        dantoc_id: findPatient.dantoc_id,
                        nation: findPatient.nation,
                        cmnd: findPatient.cmnd,
                        email: findPatient.email,
                        address: findPatient.address,
                    }

                    updatePatientData = pickBy(updatePatientData, value => !!value);

                    updateToPatient.set(updatePatientData);
                    await updateToPatient.save();

                    return { isOk: true };
                } else {
                    throw new HttpException('Không tìm thấy mã Bệnh Nhân!', 400);
                }
            }
        }

        if (findPatient) {
            const patientObj = findPatient.toObject();
            /* kiểm tra xem đã có insert rồi hay chưa */
            const checkValue = await this.checkUMCPatientBelongsToUser(userId, patientObj._id);
            if (checkValue) {
                throw new HttpException('Thông tin bệnh nhân này đã được thêm vào user rồi.', HttpStatus.CONFLICT);
            } else {
                /* kiểm tra tiếp xem còn lọt case nào không */
                const checkExists = await this.checkExistsUMCPatientBelongsToUser(userId, findPatient);
                if (checkExists) {
                    throw new HttpException('Thông tin bệnh nhân này đã được thêm vào user rồi.', HttpStatus.CONFLICT);
                }
            }

            try {
                /* tiến hành sync xuống v1 */
                await this.syncUserPatientV1(partnerId, userId, findPatient);
            } catch (error) {
                console.log(error);
            }

            /* tiến hành apply hồ sơ bệnh nhân vào trong user id */
            try {

                await this.addUserPatient(userId, patientObj._id);
                const trackingData : PatientTrackingData = {
                    action: 'ADD_OLD_PATIENT_TO_USER',
                    patient: findPatient._id,
                    userPatient: userId,
                    userAction: cskhInfo?.cskhUserId || userId,
                    dataAfter: patientObj,
                };

                this.emitService.emit(PATIENT_TRACKING_EVENT, trackingData);

                return this.getPatientInfoById(patientObj._id, partnerId);

            } catch (error) {
                throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
            }
        } else {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }

    }

    async syncAddPatientToUser(user: IUser, patient: IPatient): Promise<any> {
        const userId = user._id;
        const checkValue = await this.checkUMCPatientBelongsToUser(userId, patient._id);
        if (checkValue) {
            return null;
        } else {
            /* kiểm tra tiếp xem còn lọt case nào không */
            const checkExists = await this.checkExistsUMCPatientBelongsToUser(userId, patient);
            if (checkExists) {
                return null;
            }
        }
        /* tiến hành apply hồ sơ bệnh nhân vào trong user id */
        try {
            await this.addUserPatient(userId, patient._id);
            return true;
        } catch (error) {
            this.logger.error('Error when sync add patient to user', error);
            return null;
        }
    }

    async syncUserPatientV1(partnerId: string, userId: string, patient: IPatient): Promise<any> {
        const userMongo = await this.userModel.findById({ _id: userId }, { username: true, medproId: true }).exec();
        const userV1 = await this.checkExistsUserByUsername(userMongo.medproId);
        const keyPatientV1 = this.utilService.patientIdV1Key(partnerId);
        try {
            await this.sessionService.addPatientToUserV1(partnerId, userV1.id, patient, keyPatientV1);
        } catch (error) {
            this.logger.error('Error when sync user patient to V1', error);
        }
    }

    async unLinkPatient(id: string, userId: string, cskhInfo: CSKHTokenVerifyDTO): Promise<any> {

        const countBooking = await this.bookingModel.count({ partnerId: 'bvmathcm', patientId: id, status: { $in: [1, 2] } }).exec();
        const envDisableUpdateDeletePatient = await this.globalSettingService.findByKeyAndRepoName('ENV_UPDATE_DELETE_PATIENT');

        if (countBooking >= 1 && envDisableUpdateDeletePatient === 'ON' && !cskhInfo?.cskhUserId) {
            throw new HttpException('Để bảo mật thông tin bệnh nhân - Vui lòng liên hệ 19002115 để xoá hồ sơ', HttpStatus.BAD_REQUEST);
        }

        const userMongoId = cskhInfo?.userIdPatient || userId;
        const findPatient = await this.patientModel.findOne({ id }).exec();
        if (!findPatient) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân', HttpStatus.NOT_FOUND);
        } else {
            const patientObj = findPatient.toObject();
            const checkValue = await this.checkUMCPatientBelongsToUser(userMongoId, patientObj._id);
            if (checkValue) {
                /* tiến hành lấy unlink */
                const userInfoWithPatients = await this.getCountPatientInUserPatientUMC(userMongoId);
                const remainPatients = userInfoWithPatients.patients.filter(item => {
                    return `${item}` !== `${patientObj._id}`;
                });
                const userMongo = await this.userModel.findById({ _id: userMongoId }).exec();
                userMongo.patients = remainPatients;
                await userMongo.save();
                findPatient.userId = null;
                await findPatient.save();

                this.emitService.emit(PATIENT_TRACKING_EVENT, {
                    action: 'UNLINK',
                    patient: findPatient._id,
                    userAction: cskhInfo?.cskhUserId || userMongoId,
                    userPatient: userMongoId,
                });

                return {
                    unlink: true,
                    message: 'Thao tác thành công.',
                };
            } else {
                throw new HttpException('Hồ sơ bệnh nhân này không thuộc user hiện tại.', HttpStatus.BAD_REQUEST);
            }
        }
    }

    async checkPatientHisConstraint(
        partnerId: string, userMongoId: string, patientId: string, relationType?: string, locale?: string,
    ): Promise<any> {
        const result: any = {
            isUpdateFull: true,
        };
        // const user = await this.userModel.findById(userMongoId).exec();
        // if (user && user.isCS === true) {
        //     return result;
        // }
        const config = await this.globalSettingService.findByKeyAndRepoName('CONFIG_CHECK_PATIENT_BOOKING_SUCCESS');
        const configObj = JSON.parse(config);
        if (configObj?.env === 'OFF') {
            return result;
        }
        const [patientCodes, booking, patient, bookingCancel] = await Promise.all([
            this.getPatientCodeByPatientIdIgnorePartnerId(patientId),
            this.bookingModel.find({ status: { $in: [1, 2] }, patientId }).limit(1).exec(),
            this.patientModel.findOne({ id: patientId }).exec(),
            this.bookingModel.find({ status: -2, paymentStatus: 2, patientId }).limit(1).exec(),
        ]);
        result.isUpdateFull = !(patientCodes || (booking.length + bookingCancel.length) > 0);
        return {
            ...result,
            ...(!result.isUpdateFull && { propertyIgnoreUpdate: configObj?.propertyIgnoreUpdate }),
        };
    }

    async getPatientDetailForUpdate(id: string, userId: string, partnerId: string, appId: string, cskhInfo?: CSKHTokenVerifyDTO): Promise<any> {
        const findPatient = await this.patientModel.findOne({ id }).exec();
        if (!findPatient) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân', HttpStatus.NOT_FOUND);
        } else {
            const patientObj = findPatient.toObject();
            const checkValue = await this.checkUMCPatientBelongsToUser(userId, patientObj._id);
            if (checkValue) {
                /* tiến hành lấy thông tin detail */
                const info = await this.getPatientInfoAndPatientCodeById(partnerId, appId, patientObj._id);
                /* lấy thông tin patient Code */
                // const patientCodes = await this.patientCodeModel.findOne({ partnerId, patientId: patientObj.id }).exec();
                // console.log(info);
                // let patientCode = patientObj.code;
                // let isUpdateFull = true;
                // if (appId !== 'medpro') {
                //     if (patientCodes) {
                //         isUpdateFull = false;
                //         patientCode = patientCodes.patientCode;
                //     }
                // }
                const isUpdateFullObj = await this.checkPatientHisConstraint(partnerId, cskhInfo?.cskhUserId || userId, info?.id);
                return {
                    ...info,
                    // patientCode,
                    isUpdateFull: !!info.insuranceCode ? false : true,
                    ...isUpdateFullObj,
                    // mobile: info.isUpdateFull ? info.mobile : this.secretMobile(info.mobile),
                };
            } else {
                throw new HttpException('Hồ sơ bệnh nhân này không thuộc user hiện tại.', HttpStatus.BAD_REQUEST);
            }
        }
    }

    async addUserPatient(userId: string, id: string): Promise<any> {
        /* add thông tin hồ sơ vào trong user */
        const userMongo = await this.userModel.findById({ _id: userId }).exec();
        userMongo.patients.push(id);
        await userMongo.save();
    }

    async checkUMCPatientBelongsToUser(userId: string, patientId: string): Promise<any> {
        /* Kiểm tra xem patient này đã thuộc về user hay chua */
        const userInfoWithPatients = await this.getCountPatientInUserPatientUMC(userId);
        const findIndexValue = userInfoWithPatients.patients.indexOf(patientId);
        if (findIndexValue > -1) {
            return true;
        } else {
            return false;
        }
    }

    // async checkExistsUMCPatientBelongsToUser2(): Promise<any> {
    async checkExistsUMCPatientBelongsToUser(userId: string, patient: IPatient): Promise<any> {
        const getPatientCode = await this.patientCodeModel.findOne({ patientId: patient.id }).exec();
        if (getPatientCode) {
            const patientCode = get(getPatientCode.toObject(), 'patientCode', '');
            if (patientCode) {
                // const userId = '5f9a1512cb9fca001958236c';
                // const patientCode = '200006881111';
                const listPatientsCheck = await this.getAllPatientsByUserIdSimpleGetIds(userId);
                if (listPatientsCheck.length > 0) {
                    const transformList = listPatientsCheck.map(item => item.toObject());
                    const mapIds = map(transformList, 'id');
                    /* tìm lại tất cả patientCode */
                    const listPatientCode = await this.patientCodeModel.find({
                        patientId: { $in: mapIds },
                    }, { patientCode: true }).exec();
                    if (listPatientCode.length > 0) {
                        /* tìm xem patientCode có thuộc trong list này hay ko */
                        const findPatientCode = find(listPatientCode, { patientCode });
                        if (typeof findPatientCode !== typeof undefined) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    async getCountPatientInUserPatientUMC(userId: string): Promise<any> {
        return this.userModel.findById({ _id: userId }, { patients: true }).exec();
    }

    async getUMCPatientShortInfoVerifyPhoneByPatientId(partnerId: string, appId: string, id: string): Promise<any> {
        const getId = await this.patientModel.findOne({ id }, { id: true }).exec();
        return this.getPatientInfoAndPatientCodeById(partnerId, appId, getId._id);
    }

    async getUMCPatientShortInfoVerifyInsuranceCodeByPatientId(partnerId: string, appId: string, id: string): Promise<any> {
        const getId = await this.patientModel.findOne({ id }, { id: true }).exec();
        return this.getPatientInfoAndPatientCodeById(partnerId, appId, getId._id);
    }

    async updateUMCSyncPatient(id: string, patient): Promise<any> {
        const objBirthDate: any = {};
        const { NgaySinh, isExam = false } = patient;
        if (NgaySinh && moment(NgaySinh, 'YYYY-MM-DD').isValid()) {
            objBirthDate.birthdate = moment(NgaySinh, 'YYYY-MM-DD').format('YYYY-MM-DD');
        }

        const objName: any = {};
        const getTen = get(patient, 'Ten', null);
        const getHo = get(patient, 'Ho', null);
        if (getTen) {
            objName.name = `${getTen}`.trim();
        }
        if (getHo) {
            objName.surname = `${getHo}`.trim();
        }

        /* Kiểm tra xem có thông tin insuranceCode */
        const getInsuranceCode = get(patient, 'insuranceCode', null);
        if (getInsuranceCode) {
            /* cập nhật lại thông tin mã bảo hiểm y tế theo */
            const getSoHS = get(patient, 'SoHS', null);
            const getPartnerId = get(patient, 'partnerId', null);
            try {
                await this.patientCodeModel.findOneAndUpdate({
                    patientId: id,
                    patientCode: getSoHS,
                    partnerId: getPartnerId,
                }, {
                    insuranceCode: getInsuranceCode,
                }, { new: true }).exec();
            } catch (error) {
                console.log(error);
            }
        }

        /* kiểm tra relation */
        const getRelation = get(patient, 'relative', {});

        const dataTransformUpdate: any = {
            mobile: (patient.DiDong ? patient.DiDong : patient.DienThoai),
            ...objBirthDate,
            birthyear: patient.NamSinh,
            cmnd: patient.SoCMND,
            sex: patient.GioiTinh,
            ...objName,
            isExam,
            ...getRelation,
        };

        this.emitService.emit(LOG_SERVICE_EVENT, {
            name: `getPatientByHIS_updateDB`,
            summary: 'Lấy thông tin BN từ HIS nhidong1',
            nameParent: 'findbymsbn',
            params: {
                ...dataTransformUpdate,
            },
            errorBody: null,
        });

        try {
            return await this.patientModel
                .findOneAndUpdate({ id }, {
                    ...dataTransformUpdate,
                }, { new: true })
                .exec();
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: `updateUMCSyncPatient_updateDB`,
                summary: 'Update thông tin',
                nameParent: 'findbymsbn',
                errorCode: 'updateUMCSyncPatient',
                params: {
                    ...dataTransformUpdate,
                },
                message: error?.message || 'Lỗi update mongo',
                errorBody: this.utilService.errorHandler(error),
            });
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async convertKeyNhiDong1SyncPatient(data: any): Promise<any> {
        //response from nhidong1
        // {
        //     "SoCMND": "",
        //     "IDDanToc": "medpro_1",
        //     "DiaChiHIS": "29 Bình Phú",
        //     "GioiTinh": false,
        //     "DienThoai": "**********",
        //     "NgaySinh": "2022-10-01T00:00:00",
        //     "DiaChi": "29 Bình Phú",
        //     "id": "464324/23",
        //     "IDTinh": null,
        //     "SoHS": "464324/23",
        //     "IDNgheNghiep": "medpro_952",
        //     "HoVni": "Vương Ngọc",
        //     "IDQuanHuyen": null,
        //     "MaQuocGia": "medpro_VIE",
        //     "Ho": "Vương Ngọc",
        //     "DiaChiVni": "29 Bình Phú",
        //     "TenVni": "Nhi",
        //     "IDPhuongXa": null,
        //     "DiDong": "**********",
        //     "NamSinh": 2022,
        //     "AddressNumber": null,
        //     "DiaChiHISEN": "29 Bình Phú",
        //     "IDInsurance": null,
        //     "Ten": "Nhi",
        //     "relative": {
        //         "fullname": "Nguyễn Thị Thu Thảo",
        //         "phone": null,
        //         "email": null,
        //         "relativeType": "medpro_3",
        //         "relativeName": "Mẹ"
        //     }
        // }
        const { patientId, medproPatientCode, TenVni, HoVni, NgaySinh, NamSinh, MaQuocGia, IDDanToc, IDTinh, IDQuanHuyen,
            IDPhuongXa, IDNgheNghiep, DiDong, SoCMND, GioiTinh, DiaChi, email, insuranceId, relative} = data;
        
        console.log('data', data);

        const objBirthDate: any = {};
        if (NgaySinh && moment(NgaySinh, 'YYYY-MM-DD').isValid()) {
            objBirthDate.birthdate = moment(NgaySinh, 'YYYY-MM-DD').format('YYYY-MM-DD');
        }
        const objName: any = {};
        if (TenVni) {
            objName.name = `${TenVni}`.trim();
        }
        if (HoVni) {
            objName.surname = `${HoVni}`.trim();
        }

        /* kiểm tra relation */
        const getRelation = get(data, 'relative', {});

        const relation = {
            relative_name: getRelation?.fullname,
            relative_mobile: getRelation?.phone,
            // relativeMobileLocaleIso: { type: String, default: '' },
            relative_type_id: getRelation?.relativeType,
            relative_email: getRelation?.email,
        }

        const sex = GioiTinh === true ? 1 : 0;

        /* tìm lại ref */
        const ref: any = {};
        const findCountry = await this.countryModel.findOne({ id: MaQuocGia }).exec();
        if (findCountry) {
            const countryObj = findCountry.toObject();
            ref.country = countryObj._id;
            ref.country_id = countryObj.id;
        }

        const findNation = await this.nationModel.findOne({ id: IDDanToc }).exec();
        if (findNation) {
            const nationObj = findNation.toObject();
            ref.nation = nationObj._id;
        }
        const findCity = await this.cityModel.findOne({ id: IDTinh }).exec();
        if (findCity) {
            const cityObject = findCity.toObject();
            ref.city = cityObject._id;
        }
        const findDistrict = await this.districtModel.findOne({ id: IDQuanHuyen }).exec();
        if (findDistrict) {
            const districtObj = findDistrict.toObject();
            ref.district = districtObj._id;
        }
        const findWard = await this.wardModel.findOne({ id: IDPhuongXa }).exec();
        if (findWard) {
            const wardObj = findWard.toObject();
            ref.ward = wardObj._id;
        }
        if (!!IDNgheNghiep) {
            const findProfession = await this.professionModel.findOne({ id: IDNgheNghiep }).exec();
            if (findProfession) {
                const professionObj = findProfession.toObject();
                ref.profession = professionObj._id;
                ref.profession_id = IDNgheNghiep;
            }
        } else {
            ref.profession_id = '';
        }

        const medproId = this.generateMedproID(6);

        const result: any = {
            mobile: DiDong,
            ...objBirthDate,
            birthyear: NamSinh,
            sex,
            ...objName,
            ...ref,
            dantoc_id: IDDanToc,
            country_code: MaQuocGia,
            city_id: IDTinh,
            district_id: IDQuanHuyen,
            ward_id: IDPhuongXa,
            relation,
            address: DiaChi,
            cmnd: SoCMND,
            code: medproId
        };

        return result;
    }

    secretMobile(mobile: string) {
        try {
            const get2charLast = mobile ? mobile.slice(-3) : '';
            const secretMobile = mobile ? `${mobile.slice(0, -7)}****${get2charLast}` : '';
            return secretMobile;
        } catch (error) {
            return mobile;
        }
    }

    secretMobileCSTool(mobile: string) {
        try {
            const get2charLast = mobile ? mobile.slice(-4) : '';
            const secretMobile = mobile ? `${mobile.slice(0, -8)}xxxx${get2charLast}` : '';
            return secretMobile;
        } catch (error) {
            return mobile
        }
    }

    async insertUMCSyncPatient(patient: UMCPatientResponseDTO): Promise<any> {
        const {
            Ten, Ho, DiDong, DienThoai, DiaChi, GioiTinh, IDDanToc, IDNgheNghiep, IDPhuongXa,
            IDQuanHuyen, IDTinh, MaQuocGia, NamSinh, NgaySinh, SoCMND, SoHS, partnerId, isExam = false,
            relative
        } = patient;
        const medproId = this.generateMedproID(6);
        const uuidv4 = uuid.v4();
        /* tìm lại ref */
        const ref: any = {};
        const findCountry = await this.countryModel.findOne({ id: MaQuocGia }).exec();
        if (findCountry) {
            const countryObj = findCountry.toObject();
            ref.country = countryObj._id;
        }

        const findNation = await this.nationModel.findOne({ id: IDDanToc }).exec();
        if (findNation) {
            const nationObj = findNation.toObject();
            ref.nation = nationObj._id;
        }
        const findCity = await this.cityModel.findOne({ id: IDTinh }).exec();
        if (findCity) {
            const cityObject = findCity.toObject();
            ref.city = cityObject._id;
        }
        const findDistrict = await this.districtModel.findOne({ id: IDQuanHuyen }).exec();
        if (findDistrict) {
            const districtObj = findDistrict.toObject();
            ref.district = districtObj._id;
        }
        const findWard = await this.wardModel.findOne({ id: IDPhuongXa }).exec();
        if (findWard) {
            const wardObj = findWard.toObject();
            ref.ward = wardObj._id;
        }
        if (!!IDNgheNghiep) {
            const findProfession = await this.professionModel.findOne({ id: IDNgheNghiep }).exec();
            if (findProfession) {
                const professionObj = findProfession.toObject();
                ref.profession = professionObj._id;
                ref.profession_id = IDNgheNghiep;
            }
        } else {
            ref.profession_id = '';
        }
        const objBirthDate: any = {};
        if (NgaySinh && moment(NgaySinh, 'YYYY-MM-DD').isValid()) {
            objBirthDate.birthdate = moment(NgaySinh, 'YYYY-MM-DD').format('YYYY-MM-DD');
        }

        let relativeObject: any = {};

        if (typeof relative !== typeof undefined && Object.keys(relative).length > 0) {
            relativeObject = {
                relation: {
                    relative_name: relative?.fullname || '',
                    relative_email: relative?.email || '',
                    relative_mobile: relative.phone || '',
                    relative_type_id: relative.relativeType || ''
                }
            };
        }

        const info = {
            name: `${Ten}`.trim(),
            surname: `${Ho}`.trim(),
            cmnd: SoCMND,
            sex: GioiTinh,
            mobile: (DiDong ? DiDong : DienThoai),
            dantoc_id: IDDanToc,
            // profession_id: IDNgheNghiep,
            country_code: MaQuocGia,
            city_id: IDTinh,
            district_id: IDQuanHuyen,
            ward_id: IDPhuongXa,
            address: DiaChi,
            ...objBirthDate,
            birthyear: NamSinh,
            patientCode: SoHS,
            code: medproId,
            id: uuidv4.replace(/-/g, ''),
            ...ref,
            isExam,
            partnerId,
            ...relativeObject,
        };
        // console.log(info);
        return this.insertPatientInfo(info);
    }

    async checkExistsPatientByMsbnMedproId(partnerId: string, msbn: string): Promise<any> {
        return this.patientCodeModel.findOne({ patientCode: msbn, partnerId }).read('primary').exec();
    }

    getPatientByHIS(partnerId: string, patientCode: string): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const encode = encodeURIComponent(patientCode)
        const url = `${baseUrl}/his-connector/api/patient/getByPatientCode/${partnerId}/${encode}`;
        this.emitService.emit(LOG_SERVICE_EVENT, {
            name: `getPatientByHIS_${partnerId}`,
            summary: 'Lấy thông tin BN từ HIS nhidong1',
            nameParent: 'findbymsbn',
            params: {
                url
            },
            errorBody: null,
        });
        return this.httpService.get(url);
    }

    proxyFindByMsbn(params: any, partnerId: string): Observable<AxiosResponse<any>> {
        const url = this.oldUrl.NhiDong1GetByMSBN();
        return this.httpService.post(url, { ...params }, {
            headers: {
                partnerid: partnerId,
            },
        });
    }

    proxyFindByMsbnNhidong1(params: any): Observable<AxiosResponse<any>> {
        const url = this.oldUrl.NhiDong1GetByMSBNV2();
        console.log('url0', `${url}/${encodeURIComponent(params.msbn)}`);
        return this.httpService.get(`${url}/${encodeURIComponent(params.msbn)}`);
    }

    proxyVerifyPatientNhidong1(params: any): Observable<AxiosResponse<any>> {
        const url = this.oldUrl.NhiDong1VerifyPatient();
        console.log('url1', url);
        console.log('params', params);
        return this.httpService.post(url, { ...params });
    }

    getPatientsByUserIdNhiDong1(url: string): Observable<AxiosResponse<any>> {
        return this.httpService.get(url);
    }

    insertPatientNhiDong1(url: string, params: any, partnerId: string): Observable<AxiosResponse<any>> {
        // console.log(url, params)
        return this.httpService.post(url, { ...params }, {
            headers: {
                partnerid: partnerId,
            },
        });
    }

    updatePatientNhiDong1(url: string, params: any, partnerId: string): Observable<AxiosResponse<any>> {
        // console.log(url, params)
        return this.httpService.post(url, { ...params }, {
            headers: {
                partnerid: partnerId,
            },
        });
    }

    getPatientExtraByHIS(partnerId: string, data: any): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/his-connector/api/patient/getByPatientDetail`;
        const { Ten, Ho, GioiTinh, NamSinh, IDTinh } = data;
        return this.httpService.post(url, {
            partnerId,
            Ten,
            Ho,
            GioiTinh,
            NamSinh,
            IDTinh,
        });
    }

    getPatientExtraByInsuranceCodeHIS(partnerId: string, insuranceId: any): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/his-connector/api/patient/getByPatientDetail`;
        return this.httpService.post(url, {
            partnerId,
            insuranceId,
        });
    }

    getPatientExtraByPhoneHIS(partnerId: string, Phone: string): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/his-connector/api/patient/getByPatientDetail`;
        return this.httpService.post(url, {
            partnerId,
            Phone,
        });
    }

    getInsuranceInfoHIS(url: string, data: any): Observable<AxiosResponse<any>> {
        const { fullName, insuranceId, birthday, partnerId } = data;
        return this.httpService.post(url, {
            fullName,
            insuranceId,
            birthday,
            partnerId,
        });
    }

    getCccdInfoHIS(url: string, data: any): Observable<AxiosResponse<any>> {
        const { fullName, cccd, birthday, partnerId } = data;
        return this.httpService.post(url, {
            fullName,
            cccd,
            birthday,
            partnerId,
        });
    }

    getInfoParseAddress(url: string, data: any): Observable<AxiosResponse<any>> {
        return this.httpService.post(url, {
            ...data,
        });
    }

    async findUserIdByUserPhone(userPhone: string): Promise<any> {
        // Format phone number
        const userPhoneInfo = userPhone
            .replace(/^[+]84|^0/, '+84')
            .replace(/^84/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        try {
            const user = await this.userModel.findOne({ username: userPhoneInfo }).exec();
            if (!user) {
                throw new HttpException(`Not found userId by userPhone: ${userPhone}`, HttpStatus.BAD_REQUEST);
            }
            return user._id;
        } catch (error) {
            Logger.error(`Can't getUserIdByUserPhone() with userPhone: ${userPhone}\nError: ${error.message}`);
            throw error;
        }
    }

    async handleParams(fromData: ConstraintSearchLogDTO): Promise<any> {
        const userId = await this.findUserIdByUserPhone(fromData.userPhone);
        const { partnerId = '' } = fromData;
        let params: any = { userId };
        if (partnerId) {
            params = { ...params, partnerId };
        }
        return params;
    }

    async getTotalRows(fromData: ConstraintSearchLogDTO): Promise<number> {
        const params = await this.handleParams(fromData);
        try {
            return this.patientSearchLogModel.find({  ...params }).countDocuments();
        } catch (err) {
            Logger.error(`Error when exec getTotalRows() with userPhone: ${fromData.userPhone}\nError: ${err.message}`);
            throw err;
        }
    }

    async transferDataConstraintSearchLog(fromData: ConstraintSearchLogDTO, patientSearchLog: IPatientSearchLog[]) {
        const totalRows = await this.getTotalRows(fromData);
        const { pageSize, pageIndex } = fromData;
        // Custom response
        const response = {
            totalRows,
            pageSize,
            pageIndex,
            rows: patientSearchLog,
        };
        return response;
    }

    async getConstraintSearchLog(fromData: ConstraintSearchLogDTO): Promise<any> {
        const { pageIndex, pageSize } = fromData;
        const params = await this.handleParams(fromData);
        try {
            const searchLog = await this.patientSearchLogModel
                .find({ ...params })
                .sort({ createdAt: 'desc' })
                .skip(pageIndex)
                .limit(pageSize)
                .exec();
            return this.transferDataConstraintSearchLog(fromData, searchLog);
        } catch (error) {
            Logger.error(`Can't getConstraintSearchLog() with userPhone: ${fromData.userPhone}\nError: ${error.message}`);
            throw error;
        }
    }

    async deleteConstraintSearchLog(id: []): Promise<any> {
        try {
            const deleteInfo = await this.patientSearchLogModel.deleteMany({ _id: { $in: id } }).exec();
            return { status: 200, message: 'deleted' };
        } catch (error) {
            Logger.error(`Can't deleteConStraintSearchLog() with id: ${id}\nError: ${error.message}`);
            throw error;
        }
    }

    callGetPatientProfileV1(formData: FindPatientDto): Observable<AxiosResponse<any>> {
        const baseUrl = `${this.oldUrl.NhiDong1RestfulAPI()}/api/patient/searchbyinfo`;
        return this.httpService.post(baseUrl, { ...formData });
    }

    async updatePaitentVersionBooking(patientId: string, patientVersion: string, patientVersionId: string): Promise<any> {
        this.logger.log(`Trace for updatePaitentVersionBooking. Payload: ${JSON.stringify({patientId, patientVersion}, null, 2)}`);
        const fromDate = moment().utc().toISOString();
        try {
            return this.bookingModel
                .updateMany(
                    { patient: patientId, date: { $gte: new Date(fromDate) } },
                    { patientVersion, patientVersionId },
                )
                .exec();
        } catch (error) {
            this.logger.error(`Error when exec updatePaitentVersionBooking. Cause: ${error}`);
        }
    }

    async updatePaitentVersionBookingByPartner(patientId: string, patientVersion: string, patientVersionId: string, partnerId: string): Promise<any> {
        this.logger.log(`Trace for updatePaitentVersionBooking. Payload: ${JSON.stringify({patientId, patientVersion}, null, 2)}`);
        const fromDate = moment().utc().toISOString();
        try {
            return this.bookingModel
                .updateMany(
                    { patient: patientId, partnerId, date: { $gte: new Date(fromDate) } },
                    { patientVersion, patientVersionId },
                )
                .exec();
        } catch (error) {
            this.logger.error(`Error when exec updatePaitentVersionBooking. Cause: ${error}`);
        }
    }

    async checkBookingND1OfPatient(patientId: string): Promise<boolean> {
        return this.bookingModel.exists({ patient: patientId, date: { $gte: new Date() } });
    }

    async getPatientsByPhone(mobile: string, patientsUser: string[]): Promise<any> {
        const patients = await this.patientModel
            .find(
                { mobile, id: { $nin: [...patientsUser] } },
                { address: true, id: true, name: true, surname: true, birthdate: true, birthyear: true, mobile: true, code: true, sex: true },
            )
            // .populate('profession')
            // .populate('country')
            // .populate('nation')
            .populate({ path: 'city', select: { name: true } })
            .populate({ path: 'district', select: { name: true } })
            .populate({ path: 'ward', select: { name: true } })
            .sort({ createdAt: 'desc' })
            .exec();
        if (!patients.length) {
            return [];
        }
        const overidePatient = patients.map(patient => {
            const resultPatient = patient ? {...patient.toObject(),  isValid: false, mobile: this.secretMobileCSTool(patient?.mobile || '') } : {};
            return resultPatient;
        });
        return overidePatient;
    }

    async createPatientIntoUserCskh(patientId: string, userMongoId: string, partnerId: string, cskhInfo: CSKHTokenVerifyDTO): Promise<any> {
        /* tìm lại thông tin patient _id */
        const findPatient = await this.patientModel.findOne({ id: patientId }).exec();
        if (findPatient) {
            const patientObj = findPatient.toObject();
            /* kiểm tra xem đã có insert rồi hay chưa */
            const checkValue = await this.checkUMCPatientBelongsToUser(userMongoId, patientObj._id);
            if (checkValue) {
                throw new HttpException('Thông tin bệnh nhân này đã được thêm vào user rồi.', HttpStatus.CONFLICT);
            } else {
                /* kiểm tra tiếp xem còn lọt case nào không */
                const checkExists = await this.checkExistsUMCPatientBelongsToUser(userMongoId, findPatient);
                if (checkExists) {
                    throw new HttpException('Thông tin bệnh nhân này đã được thêm vào user rồi.', HttpStatus.CONFLICT);
                }
            }
            try {
                /* tiến hành sync xuống v1 */
                await this.syncUserPatientV1(findPatient.partnerId, userMongoId, findPatient);
            } catch (error) {
                console.log(error);
            }

            /* tiến hành apply hồ sơ bệnh nhân vào trong user id */
            try {
                await this.addUserPatient(userMongoId, patientObj._id);

                const trackingData : PatientTrackingData = {
                    action: 'ADD_TO_USER',
                    patient: findPatient._id,
                    userPatient: userMongoId,
                    userAction: cskhInfo?.cskhUserId || userMongoId,
                    dataAfter: patientObj,
                    extraInfo: { partnerId },
                }

                this.emitService.emit(PATIENT_TRACKING_EVENT, trackingData);

                return this.getPatientInfoById(patientObj._id, partnerId);
            } catch (error) {
                throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
            }
        } else {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }
    }

    async verifyUserTokenModeCskh(cskhToken: string): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();
        let cskhInfo: any;
        if (typeof cskhToken !== typeof undefined && `${cskhToken}`.trim() !== '') {
            try {
                const jwtVerify: any = jwt.verify(last(cskhToken.split(' ')), jwtOptions.secret);
                /* Kiểm tra lại thông tin cskhInfo */
                const { userMongoId } = jwtVerify;
                if (userMongoId) {
                    const cskhUser = await this.findUserByRefId(userMongoId);
                    if (!cskhUser) {
                        throw new HttpException('Vui lòng kiểm tra lại thông tin user cskh', HttpStatus.UNAUTHORIZED);
                    }
                }
                return {
                    cskhUserId: userMongoId,
                };
            } catch (error) {
                const nameJWTError = !!error.name ? error.name : '';
                if (nameJWTError === 'TokenExpiredError') {
                    throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
                } else if (nameJWTError === 'JsonWebTokenError') {
                    throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
                } else if (nameJWTError === 'NotBeforeError') {
                    throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
                } else {
                    throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
                }
            }
        } else {
            return cskhInfo;
        }
    }

    async handleQrcodeBHYTFromHis(data: DataBhytDto, patient: any, partnerId: string): Promise<string> {
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }, {
            qrCodeConfig: 1,
        }).exec();
        let {
            templateQrCodeContent = '',
            charSeparateQrcodeContent = '|',
            defaultEmptyValue = '-',
        } = partnerConfig?.qrCodeConfig;

        if (templateQrCodeContent) {

            // qui định giới tính từng bệnh viện
            switch (partnerId) {
                case 'hoanmytd':
                case 'hoanmyvp1':
                    patient.sex = `${patient.sex === 0 ? 2 : 1 }`;
                    break;
                default:
                    break;
            }
            // data prepare to fill
            const dataFillTemplate = {
                ...data,
                patient: {
                    ...patient,
                    address: `${this.getFullAddress(patient)}`,
                    birthdate: moment(patient?.birthdate) ? moment(patient.birthdate).format('DD/MM/YYYY') : `${patient.birthyear}`,
                    fullname: `${patient.surname} ${patient.name}`,
                },
                insuranceCode: defaultEmptyValue,
                maKv: `${data?.maKv && !isEmpty(data.maKv) ? data.maKv : '4'}`,
                insuranceFromDate: moment(data.insuranceFromDate).format('DD/MM/YYYY'),
                insuranceToDate: moment(data.insuranceToDate).format('DD/MM/YYYY'),
                ngayCap: moment(data.insuranceFromDate).format('DD/MM/YYYY'),
            };

            const arrTemplates = templateQrCodeContent.split(charSeparateQrcodeContent);
            arrTemplates.pop();
            for (const itemTemplate of arrTemplates) {
                const arrItemTemplate = itemTemplate.split(':');
                const checkEncode = last(arrItemTemplate);
                const keyString = first(arrItemTemplate);
                const prop = get(dataFillTemplate, keyString);

                if (checkEncode === 'hex') {
                    set(dataFillTemplate, keyString, Buffer.from(prop).toString('hex'));
                    const newProp = get(dataFillTemplate, keyString);
                    templateQrCodeContent = templateQrCodeContent
                        .replace(':hex', '')
                        .replace(keyString, newProp && !isEmpty(newProp) ? newProp : defaultEmptyValue);
                } else {
                    templateQrCodeContent = templateQrCodeContent.replace(keyString, (prop && !isNil(prop)) || isNumber(prop) ? prop : defaultEmptyValue);
                }
            }
        }
        return templateQrCodeContent;
    }

    async getPatientCodeByPatientId(patientId: string, partnerId: string): Promise<any> {
        const patientCodes = await this.patientCodeModel.find({
            partnerId,
            patientId,
            patientCode: { $nin: [null, ''] },
        }).exec();

        return first(patientCodes);
    }

    async getPatientCodeByPatientIdIgnorePartnerId(patientId: string): Promise<any> {
        const patientCodes = await this.patientCodeModel.find({
            // partnerId,
            patientId,
            patientCode: { $nin: [null, ''] },
        }).exec();

        return first(patientCodes);
    }

    async handlePatientHasBookingSuccess(partnerId: string, userMongoId: string, patientId: string): Promise<boolean> {
        // const user = await this.userModel.findById(userMongoId).exec();
        // if (user && user.isCS === true) {
        //     return;
        // }
        const config = await this.globalSettingService.findByKeyAndRepoName('CONFIG_CHECK_PATIENT_BOOKING_SUCCESS');
        const configObj = JSON.parse(config);
        if (configObj?.env === 'ON') {
            const [patientCodes, booking, bookingCancel] = await Promise.all([
                this.getPatientCodeByPatientIdIgnorePartnerId(patientId),
                this.bookingModel.find({ status: { $in: [1, 2] }, patientId }).limit(1).exec(),
                this.bookingModel.find({ status: -2, paymentStatus: 2, patientId }).limit(1).exec(),
            ]);
            if (patientCodes || (booking.length + bookingCancel.length) > 0) {
                return true;
                // throw new HttpException({
                //     statusCode: HttpStatus.BAD_REQUEST,
                //     message: configObj?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.',
                // }, HttpStatus.BAD_REQUEST);
            }
            return false;
        }
    }

    async checkPatientPartnerSimmed(patients: any, formData: any, partnerId: string = 'simmed'): Promise<void> {
        const {
            subjectId= '',
            serviceId= '',
            treeId = '',
        } = formData;

        const [config, partner] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('PATIENT_CONSTRAINT_BOOKING_SIMMED'),
            this.hospitalModel.findOne({ partnerId }, { name: true }).exec(),
        ]);
        const configsObj = JSON.parse(config);

        const subjectOrService =
            (serviceId && `gói dịch vụ ${(await this.serviceModel.findOne({ id: serviceId }).exec()).name}`) ||
            (subjectId && `chuyên khoa ${(await this.subjectModel.findOne({ id: subjectId }).exec()).name}`);

        patients = await Promise.all(patients.map(async patient => {
            let warningMessage: string;
            let messageConfig: string;
            const keyConstraintConfig = `${treeId.toUpperCase()}|${subjectId || serviceId}`;
            const constraintConfig = get(configsObj?.constraints, keyConstraintConfig) || get(configsObj?.constraints, `${treeId}|${partnerId}_another`);
            if (!constraintConfig) {
                return patient;
            }
            for (const key of Object.keys(constraintConfig)) {
                switch (key) {
                    case 'age':
                        const minAge = first<number>(constraintConfig[key]?.value);
                        const maxAge = last<number>(constraintConfig[key]?.value);
                        const betweenThan = !this.checkOldAccepted(patient, minAge) && this.checkOldAccepted(patient, maxAge);
                        if (betweenThan) {
                            break;
                        }
                        messageConfig = get(configsObj?.messages, `${constraintConfig[key]?.message}`, '');
                        warningMessage = messageConfig.replace('{LABEL}', subjectOrService).replace('{PARTNER}', partner.name);
                        return {
                            ...patient,
                            warningMessage,
                        };
                    default:
                        const prop = get(patient, key);
                        if (isEmpty(`${prop}`) || prop === constraintConfig[key]) {
                            continue;
                        }
                        messageConfig = get(configsObj?.messages, `${key}_${prop}`, '');
                        warningMessage = messageConfig.replace('{LABEL}', subjectOrService).replace('{PARTNER}', partner.name);
                        return {
                            ...patient,
                            warningMessage,
                        };
                }
            }
            return patient;
        }));

        return patients;
    }

    async getInsuranCodeByPatientId(patientIds: any[], partnerId: string, treeId?: string, appId: string = 'medpro'): Promise<any> {
        let patientCodes = await this.patientCodeModel.find({
            partnerId,
            patientId: { $in: patientIds },
        }).exec();

        if (patientCodes.length === 0) {
            patientCodes = await this.patientCodeModel.find({
                partnerId: appId,
                patientId: { $in: patientIds },
            }).exec();
        }


        if (treeId && treeId === 'CLS') {
            let patientCodeCls = await this.patientCodeModel.find({
                partnerId,
                patientId: { $in: patientIds },
            }).exec();

            if (patientCodeCls.length === 0) {
                patientCodes = await this.patientCodeModel.find({
                    partnerId: appId,
                    patientId: { $in: patientIds },
                }).exec();
            }

            return patientCodeCls || patientCodes;
        }

        return patientCodes;
    }

    async getBookingRuleUrl() {
        // if (REPO_NAME_BETA.includes(this.repoName) || process.env.NODE_ENV === 'development') {
            return this.urlConfigService.getUrlCheckBookingRules();
        // } else {
        //     return this.globalSettingService.findByKeyAndRepoName('BOOKING_RULES_URL');
        // }
    }
    async checkPatientPartnerOld(patients: any, formData: any, partnerId: string): Promise<void> {
        const {
            subjectId= '',
            serviceId= '',
            treeId = '',
        } = formData;

        const [config, partner] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName(`PATIENT_CONSTRAINT_BOOKING_${partnerId}`.toUpperCase()),
            this.hospitalModel.findOne({ partnerId }, { name: true }).exec(),
        ]);

        if (!config) {
            return patients;
        }

        const configsObj = JSON.parse(config);

        let constraintConfig: any;
        let subjectOrService = '';
        // ưu tiên dịch vụ nếu đi chung với chuyên khoa. Cấu hình treeId|chuyenkhoa|dichvu
        const keyConstraintConfig = `${treeId.toUpperCase()}|${subjectId}|${serviceId}`;
        constraintConfig = get(configsObj?.constraints, keyConstraintConfig);
        const [service, subject] = await Promise.all([
            this.serviceModel.findOne({ id: serviceId }, { name: true }).exec(),
            this.subjectModel.findOne({ id: subjectId }, { name: true }).exec(),
        ]);
        if (constraintConfig) {
            subjectOrService =
                (service && `gói dịch vụ ${service?.name}`) ||
                (subject && `chuyên khoa ${subject?.name}`);
        } else {
            const keyConstraintConfig = `${treeId.toUpperCase()}|${subjectId || serviceId}`;
            constraintConfig = get(configsObj?.constraints, keyConstraintConfig) || get(configsObj?.constraints, `${treeId}|${partnerId}_another`);
            subjectOrService =
                (subject && `chuyên khoa ${subject?.name}`) ||
                (service && `gói dịch vụ ${service?.name}`);
        }

        if(!constraintConfig){
            return patients;
        }

        patients = await Promise.all(patients.map(async patient => {
            let warningMessage: string;
            let messageConfig: string;

            for (const key of Object.keys(constraintConfig)) {
                switch (key) {
                    case 'age':
                        const minAge = first<number>(constraintConfig[key]?.value);
                        const maxAge = last<number>(constraintConfig[key]?.value);

                        const lessthan = this.checkAgePatient(patient, minAge, constraintConfig[key]?.unit);
                        const greaterthan = this.checkAgePatient(patient, maxAge, constraintConfig[key]?.unit);

                        const betweenThan = !lessthan && greaterthan;
                        if (betweenThan) {
                            break;
                        }

                        messageConfig = get(configsObj?.messages, `${constraintConfig[key]?.message}`, '');
                        if (lessthan) {
                            messageConfig = get(configsObj?.messages, `age_lt`, messageConfig);
                        } else if (greaterthan) {
                            messageConfig = get(configsObj?.messages, `age_gt`, messageConfig);
                        }
                        warningMessage = messageConfig.replace('{LABEL}', subjectOrService).replace('{PARTNER}', partner.name);
                        return {
                            ...patient,
                            warningMessage,
                        };
                    default:
                        const prop = get(patient, key);
                        if (isEmpty(`${prop}`) || prop === constraintConfig[key]) {
                            continue;
                        }
                        messageConfig = get(configsObj?.messages, `${key}_${prop}`, '');
                        warningMessage = messageConfig.replace('{LABEL}', subjectOrService).replace('{PARTNER}', partner.name);
                        return {
                            ...patient,
                            warningMessage,
                        };
                }
            }
            return patient;
        }));

        return patients;
    }

    async checkPatientPartner(patients: any, formData: any, partnerid: string, appid: string, locale = 'vi'): Promise<any> {
        const baseUrl = await this.getBookingRuleUrl();
        const api = `${baseUrl}/booking-rules/patient/exam`;
        const patientRules = await this.client.post(api, {
            data: formData,
            patientIds: patients.map(patient => patient?.id)
        }, {
            partnerid,
            appid,
            locale,
        });

        const groupPatientRules = groupBy(patientRules, 'id');
        return patients.map(patient => {
            const warningMessage = first<any>(groupPatientRules[patient?.id])?.warningMessage || '';
            return {
                ...patient,
                ...(warningMessage && { warningMessage })
            }
        })
    }

    async syncPatientHisAfterUpdate(isCs: boolean, userMongoId: string, patientId: string, partnerId: string): Promise<any> {

        if (!isCs) {
            return;
        }

        const configPartners = await this.globalSettingService.findByKeyAndRepoName('CONFIG_PARTNER_SYNC_PATIENT_HIS');
        let configPartnerObjs: any
        try {
            configPartnerObjs = JSON.parse(configPartners);
        } catch(error) {
            return;
        }

        const configPartnerObj = get(configPartnerObjs, partnerId, false);
        if (configPartnerObj === false) {
            return;
        }

        const booking = await this.bookingModel.find({ patientId, userId: userMongoId }).limit(1).exec();
        if (booking.length === 0) {
            return;
        }

        const url = `${this.urlConfigService.getBookingTreeUrl()}/his-connector/api/patient/syncPatient`;
        this.emitService.emit(LOG_SERVICE_EVENT, {
            name: 'paramSyncPatientHisAfterUpdate',
            summary: 'CSKH cập nhật hồ sơ đồng bộ his',
            nameParent: 'syncPatientHisAfterUpdate',
            params: {
                partnerId,
                patientId,
                userMongoId,
                url,
            },
        });
        try {
            await this.client.postNoContent(url, {
                partnerId,
                patientId,
            });

        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'syncPatientHisAfterUpdate',
                summary: 'CSKH cập nhật hồ sơ đồng bộ his',
                nameParent: 'syncPatientHisAfterUpdateError',
                params: {
                    partnerId,
                    patientId,
                },
                errorBody: this.utilService.errorHandler(error),
                message: error?.message || error?.response?.message || error?.response?.data?.message,
            });
            throw new HttpException('Loi cap nhat', HttpStatus.BAD_REQUEST)
        }
    }


    async checkConstrainsPatientDetail(patients: any, headers?: HeadersDto, bookingData?: any): Promise<any> {
        try {
            const baseUrl = await this.getBookingRuleUrl();
            const { data = [] } = await this.httpService.post(
                    `${baseUrl}/booking-rules/patient/detail`,
                    {
                        patientIds: patients.map((p) => p.id),
                        bookingData,
                    },
                    {
                        headers: {
                            partnerid: headers.partnerid || '',
                            appid: headers.appid || '',
                            locale: headers.locale || '',
                            platform: headers.platform || '',
                        },
                    },
                ).toPromise();
    
            const patientMap = this.utilService.groupByKey(data, 'id');
    
            patients.forEach((patient) => {
                patient.constraintInfo = patientMap[patient.id]?.constraintInfo;
            });
    
            return patients;
        } catch (error) {
            //TO DO HANDLE ERROR
        }
    }

    checkAgePatient(patient: any, ageLimit?: number, unit: string = 'years'): boolean {
        if (!ageLimit) {
            return true;
        }
        if (moment(new Date(patient?.birthdate)).isValid()) {
            switch(unit) {
                case 'months':
                    return Math.floor(moment().diff(moment(patient?.birthdate, 'YYYY-MM-DD'), 'months', true)) < +ageLimit;
                default:
                    return Math.floor(moment().diff(moment(new Date(patient?.birthdate)), 'years', true)) < +ageLimit;
            }
        } else {
            return Number(moment().format('YYYY')) - patient?.birthyear < +ageLimit;
        }
    }

    async checkAgePatientAcceptForPartnerBooking(patientId: string, partnerId: string): Promise<any> {
        const baseUrl = await this.getBookingRuleUrl();
        const api = `${baseUrl}/booking-rules/patient/age-accept`;
        const result = await this.client.post(api, {
            patientIds: [patientId],
        }, {
            partnerid: partnerId,
        });
        const firstPatient = first<any>(result);
        if (!firstPatient?.warningMessage) {
            return;
        }
        throw new HttpException(firstPatient?.warningMessage, HttpStatus.BAD_REQUEST);
    }

    async checkAgePatientAcceptForPartner(patients: any, partnerId: string): Promise<any> {
        const baseUrl = await this.getBookingRuleUrl();
        const api = `${baseUrl}/booking-rules/patient/age-accept`;
        const result = await this.client.post(api, {
            patientIds: patients.map(patient => patient?.id),
        }, {
            partnerid: partnerId,
        });
        if (result.length === 0) {
            return patients;
        }
        const groupRulePatient = groupBy(result, 'id');
        return patients.map(patient => {
            const rulePatient = first<any>(groupRulePatient[patient?.id])
            return {
                ...patient,
                ...(rulePatient?.warningMessage && { warningMessage: rulePatient?.warningMessage })
            }
        })
    }

    async validateBookingRule(formData: ValidateBookingRuleDto, partnerId: string) {
        const { patientId, subjectId } = formData;

        const patient = await this.patientModel.findOne({id: patientId}).exec();
        if (!patient) {
            throw new HttpException({message: 'Không tìm thấy thông tin bệnh nhân'}, HttpStatus.NOT_FOUND);
        }

        const checkData = await this.checkConfigBookingRulePatientUmc({partnerId, subjectId });

        if (checkData) {
            return this.validateBookingRuleForPatientUmcGroup({
                patient: patient.toObject(),
                ...checkData,
                subjectId,
            })
        } else {
            return patient;
        }
    }

    async insertPatientZalo(formData: UserZaloInfoDto) {
        const { name: fullname, sex, phone, ward_id, district_id, city_id, dateOfBirth, address } = formData;

        if (!phone || !fullname || sex) {
            throw new HttpException("Không đủ thông tin tạo hồ sơ từ Zalopay", HttpStatus.BAD_REQUEST);
        }

        const [surname, name] = this.utilService.splitFullName(fullname);
        const mobile = `${phone}`.replace(/^[+]?84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843').replace('+84', '0');
        const birthdateMM = moment(dateOfBirth);

        let birthdate;
        let birthyear;
        if (birthdateMM.isValid()) {
            birthdate = birthdateMM.format('YYYY-MM-DD');
            birthyear = birthdateMM.get('year');
        }

        const patientInsert: any = {
            id: this.utilService.generateUUID(),
            surname,
            name,
            mobile,
            birthyear,
            birthdate,
            sex,
            address,
            ward_id,
            district_id,
            city_id,
            sourceId: 'zalopay',
            profession_id: 'medpro_934',
            profession: '5edf46d4141d2e5244f9a819',
            dantoc_id: 'medpro_1',
            nation: '5edf3aee60bf731ffc65b533',
        };

        const findNation = await this.nationModel.findOne({ id: patientInsert.dantoc_id }).exec();
        if (findNation) {
            const nationObj = findNation.toObject();
            patientInsert.nation = nationObj._id;
            patientInsert.dantoc_id = nationObj.id;
        }
        if (!!patientInsert.address) {
        }
        const findWard = await this.wardModel.findOne({ id: patientInsert.ward_id }).exec();
        if (findWard) {
            const wardObj = findWard.toObject();
            patientInsert.ward = wardObj._id;
            patientInsert.ward_id = wardObj.id;
        }
        const findDistrict = await this.districtModel.findOne({ id: patientInsert.district_id }).exec();
        if (findDistrict) {
            const districtObj = findDistrict.toObject();
            patientInsert.district = districtObj._id;
            patientInsert.district_id = districtObj.id;
        }
        const findCity = await this.cityModel.findOne({ id: patientInsert.city_id }).exec();
        if (findCity) {
            const cityObject = findCity.toObject();
            patientInsert.city = cityObject._id;
            patientInsert.city_id = cityObject.id;
        }

        const patientNew = new this.patientModel(patientInsert);
        return patientNew.save();
    }

    async checkConfigBookingRulePatientUmc(formData: any) {
        if (!formData?.subjectId) {
            return null;
        }
        const { partnerId } = formData;
        const partnerBranchUmc = await this.globalSettingService.findByKeyAndRepoName('PARTNER_BRANCH_UMC_VALID_AGE');
        const setPartnerBranchUmc = new Set(partnerBranchUmc ? partnerBranchUmc.split(',') : []);
        const [
            subjectAgeLimits, // subject check age umc luồng chính cho khoa nhi và lão
            subjectCheckAgeLimitAnother, // subject umc cho các case khác
            partner,
            subjectNhiGroupUmc, // subject config để nhận biết lão khoa khi các umc có nhiều chi nhánh subject id khác nhau
            subjectLkGroupUmc, // subject config để nhận biết lão khoa khi các umc có nhiều chi nhánh subject id khác nhau
        ] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('UMC_AGE_LIMIT_WITH_EXAM'),
            this.globalSettingService.findByKeyAndRepoName('UMC_AGE_LIMIT_ANOTHER_EXAM'),
            this.hospitalModel.findOne({ partnerId }),
            this.globalSettingService.findByKeyAndRepoName('SUBJECT_NHI_GROUP_UMC'),
            this.globalSettingService.findByKeyAndRepoName('SUBJECT_LAO_GROUP_UMC'),
        ]);
        const subjectAgeLimitObjs = JSON.parse(subjectAgeLimits);
        const ignoreSubject = subjectAgeLimitObjs.find(subject => subject.subjectId === formData?.subjectId);

        if (setPartnerBranchUmc.has(partnerId) && ignoreSubject?.condition !== 'ignore') {
            const subjectCheckAgeLimitAnotherObjs = JSON.parse(subjectCheckAgeLimitAnother);
            const checkSubjectCheckAgeLimitAnotherObj = subjectCheckAgeLimitAnotherObjs.find(subject => formData?.subjectId === subject.subjectId);

            const setSubjectNhiGroupUmc = new Set(subjectNhiGroupUmc.split(',') || []);
            const setSubjectLaoGroupUmc = new Set(subjectLkGroupUmc.split(',') || []);
            const subjectNd = subjectAgeLimitObjs.find(sub => setSubjectNhiGroupUmc.has(sub.subjectId) && formData?.subjectId === sub.subjectId);
            const subjectLk = subjectAgeLimitObjs.find(sub => setSubjectLaoGroupUmc.has(sub.subjectId) && formData?.subjectId === sub.subjectId);
            // const subjectNd = subjectAgeLimitObjs.find(sub => sub.subjectId === 'umc_ND');
            // const subjectLk = subjectAgeLimitObjs.find(sub => sub.subjectId === 'umc_PW');

            return {
                checkSubjectCheckAgeLimitAnotherObj,
                subjectNd,
                subjectLk,
                partner
            };
        } else {
            return null;
        }
    }

    async validateBookingRuleForPatientUmcGroup(formData: any) {
        const {
            patient,
            checkSubjectCheckAgeLimitAnotherObj,
            subjectNd,
            subjectLk,
            partner
        } = formData;

        let newPatient = patient;
        patient.birthyear = moment(patient?.birthdate).isValid() ? moment(patient?.birthdate).get('years') : Number(patient?.birthyear);

        if (!checkSubjectCheckAgeLimitAnotherObj) {
            if (subjectNd?.subjectId === formData.subjectId) {
                // truong hợp 16 <= ho so < 60
                // console.log('subjectNd: ', subjectNd)
                // console.log('patient.birthyear: ', patient.birthyear)
                const { subjectId, patientYear, warningMessage } = subjectNd;
                const subject = await this.subjectModel.findOne({ id: subjectId });
                const checkAgeLt = Number(moment().format('YYYY')) - patient.birthyear >= +patientYear;
                const handleWarningMessage = warningMessage.replace('{PARTNER}', partner.name).replace('{SUBJECT}', subject.name);
                newPatient = {
                    ...patient,
                    ...(checkAgeLt && { warningMessage: handleWarningMessage }),
                };
            } else if (subjectLk?.subjectId === formData.subjectId) {
                // truong hợp 16 <= ho so < 60
                // console.log('subjectLk: ', subjectLk)
                // console.log('patient.birthyear: ', patient.birthyear)
                const { subjectId, patientYear, warningMessage } = subjectLk;
                const checkAgeGt = Number(moment().format('YYYY')) - patient.birthyear < +patientYear;
                const subject = await this.subjectModel.findOne({ id: subjectId });
                const handleWarningMessage = warningMessage.replace('{PARTNER}', partner.name).replace('{SUBJECT}', subject.name);
                newPatient = {
                    ...patient,
                    ...(checkAgeGt && { warningMessage: handleWarningMessage }),
                };
            }
        } else {
            // nhỏ hơn qui định = true
            const { condition, patientYear, warningMessage, subjectId } = checkSubjectCheckAgeLimitAnotherObj;
            const checkAgeLessConfig = await this.checkOldAccepted(patient, patientYear);
            const subject = await this.subjectModel.findOne({ id: subjectId });
            const handleMessage = warningMessage.replace('{PARTNER}', partner.name).replace('{SUBJECT}', subject.name);
            if (condition === 'lt') {
                newPatient = {
                    ...patient,
                    ...(checkAgeLessConfig === true && { warningMessage: handleMessage }),
                };
            } else if (condition === 'gt' && checkAgeLessConfig === false) {
                newPatient = {
                    ...patient,
                    ...(checkAgeLessConfig === false && { warningMessage: handleMessage }),
                };
            }
        }

        return {
            ...newPatient,
            birthdate: moment(patient.birthdate).isValid() ? moment(patient.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
        };

    }

    async getPatientTracking(user: any, query: any) {
        let userId = user.userMongoId ?? user._id;
        const { page = 0, limit = 100, patientId } = query;

        let condition: any = { // get hết theo userAction
            userAction: userId,
        };

        if (patientId) { // có patient thì get theo patient thôi.
            condition = {
                patient: patientId,
            }
        } else if (query.phone) {
            const userPhoneInfo = query.phone
                .replace(/^[+]84|^0/, '+84')
                .replace(/^84/, '+84')
                .replace(/^9/, '+849')
                .replace(/^3/, '+843');
            const userInfo = await this.userModel.findOne({ username: userPhoneInfo, medproId: `mp${userPhoneInfo}` }).exec();

            if (userInfo) { // get hết theo userPatient
                condition = {
                    userPatient: userInfo._id,
                }
            } else {
                throw new HttpException(`Không tìm thấy user ${query.phone}`, HttpStatus.NOT_FOUND);
            }
        }

        const total = await this.patientTrackingModel.countDocuments(condition);
        const [data, userPatient] = await Promise.all([
            this.patientTrackingModel.find(condition)
              .populate('userAction', 'fullname username')
              .populate('patient', 'surname name mobile')
              .skip(page * limit)
              .limit(limit)
              .sort({ _id: -1 })
              .exec(),
            this.userModel.findOne({ _id: userId }, 'fullname username').exec(),
        ]);

        const rows = {
            data: await Promise.all(data.map(async d => {
                const { bookingId, partnerId } = d.extraInfo || {};
                let booking;
                let partner;
                if (d.extraInfo?.bookingId) {
                    const bookingEntity = await this.bookingModel.findOne({ _id: bookingId }, { bookingCode: 1, transactionId: 1, date: 1, status: 1, partnerId: 1 }).exec();
                    booking = bookingEntity?.toObject();
                }

                if (partnerId || booking?.partnerId) {
                    partner = await this.hospitalModel.findOne({ partnerId: partnerId || booking.partnerId }, { name: 1, partnerId: 1 }).exec();
                    partner = partner?.toObject();
                }

                return {
                    ...d.toObject(),
                    action: PatientTrackingActionTextEnum[d.action],
                    booking,
                    partner,
                };
            })),
            userPatient,
        }

        return {
            page: query.page,
            limit: query.limit,
            rows,
            total,
            condition,
        };
    }

    async getConfigBtnUser(appid: string, partnerid: string, user: UserInfoVerifyTokenDTO, formData?: YearOldValidationDto): Promise<any> {
        const partnerConfig = await this.partnerConfigModel
            .findOne(
                { partnerId: partnerid },
                {
                    tao_ho_so_moi_btn: true,
                    da_tung_khamt_btn: true,
                    quet_bhyt_btn: true,
                },
            )
            .exec();
        if (!partnerConfig) {
            throw new HttpException('Không tìm thấy thông tin partner config', HttpStatus.NOT_FOUND);
        }
        let result = {
            tao_ho_so_moi_btn: partnerConfig?.tao_ho_so_moi_btn || false,
            da_tung_khamt_btn: partnerConfig?.da_tung_khamt_btn || false,
            quet_bhyt_btn: partnerConfig?.quet_bhyt_btn || false,
        }
        
        const { treeId, subjectId, serviceId } = formData;
        const checkListTreeIds = ['TAIKHAMPHACO', 'TAIKHAM'];
        const checkListSubjectIds = ['bvmathcm_TKM'];

        if ((appid === 'medpro' || appid === 'bvmathcm') && partnerid === 'bvmathcm' 
            && (checkListTreeIds.includes(treeId) || checkListSubjectIds.includes(subjectId) || formData.serviceId?.endsWith('TK'))
        ) {
            result = {
                tao_ho_so_moi_btn: false,
                da_tung_khamt_btn: true,
                quet_bhyt_btn: false
            }
        }

        if (appid === 'nhidong1' && partnerid === 'nhidong1') {
            result = {
                ...result,
                da_tung_khamt_btn: true,
            }
        }
        
        return result
    }

    @OnEvent(EVENT_FIND_PATIENT_CODE_BY_MSBN)
    async eventFindPatientCodeByMsbn(payload: any) {
        try {
            const objPatientCode: any = {};
            const { infoObj, partnerId, insuranceId, data, insuranceToDate, patientObj, insuranceCode, maDKBDHT } = payload;
            try {
                const startFindPatientCode = moment().toDate()
                this.logger.log(`START Tìm PatientCOde từ InsuranceCode: ${startFindPatientCode}`)

                const findMsbnData = (await this.getPatientExtraByInsuranceCodeHIS(partnerId, insuranceId).pipe(timeout(2000)).toPromise()).data;

                const endFindPatientCode = moment().toDate();
                this.logger.log(`END Tìm PatientCOde từ InsuranceCode: ${moment(endFindPatientCode).diff(startFindPatientCode, 'milliseconds')} ms`)


                if (findMsbnData.length > 0) {
                    const resultMsbnData: any = first(findMsbnData);
                    objPatientCode.patientCode = `${resultMsbnData.SoHS}`.trim();
                } else {
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: 'getPatientExtraByInsuranceCodeHIS',
                        summary: 'Kiểm tra xem mã bảo hiểm nay có gắn với 1 cái mã số bệnh nhân hay chưa',
                        nameParent: 'getInsuranceDate',
                        params: { partnerId, insuranceId: insuranceId },
                        errorBody: null,
                        response: { findMsbnData, soucre: data },
                        message: 'Nơi đăng ký khám bảo hiểm ban đầu của bạn không hợp lệ, Vui lòng kiểm tra lại thông tin.',
                    });
                }
                /* add thông tin bảo hiểm */
                // console.log('bao hiem con hieu luc');
                const findPatientCode = await this.patientCodeModel.findOne({ _id: infoObj._id }).exec();
                const qrCodeContent = await this.handleQrcodeBHYTFromHis(data, { ...patientObj }, partnerId);
                /* cập nhật lại thông tin */
                const patientCodeObj = findPatientCode.toObject();
                await this.patientCodeModel.findByIdAndUpdate(
                    { _id: patientCodeObj._id },
                    { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...(qrCodeContent && { qrCodeContent }) },
                ).exec();
            } catch (error) {
                const { response } = error;
                if (response) {
                    const { data: dataError, status } = response;
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: 'getPatientExtraByInsuranceCodeHIS',
                        summary: 'Kiểm tra xem mã bảo hiểm nay có gắn với 1 cái mã số bệnh nhân hay chưa',
                        nameParent: 'getInsuranceDate',
                        params: { partnerId, insuranceId: insuranceId },
                        errorBody: dataError,
                        response: { soucre: data },
                        message: 'Nơi đăng ký khám bảo hiểm ban đầu của bạn không hợp lệ, Vui lòng kiểm tra lại thông tin.',
                    });
                }
            }
        } catch (error) {
            console.log('message-event/findPatientCodeByMsbn', error)
        }
    }
}
