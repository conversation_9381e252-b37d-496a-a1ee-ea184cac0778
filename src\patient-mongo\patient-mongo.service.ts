import { TranslateService } from './../translate/translate.service';
import { ClientUtilService } from './../config/client-util.service';
import { TopicMailer } from 'src/mail/topic';
import { LOG_SERVICE_EVENT } from './../audit-log/constant';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { HttpException, HttpService, HttpStatus, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import * as moment from 'moment';
import * as uuid from 'uuid';
import { find, first, get, groupBy, isEmpty, isNil, isNumber, last, map, orderBy, pick, pickBy, set, split } from 'lodash';
import { isEmail, isMobilePhone } from 'class-validator';
import * as jwt from 'jsonwebtoken';
// import * as queryString from 'query-string';
import {
    PATIENT_CODE_COLLECTION_NAME,
    PATIENT_COLLECTION_NAME,
    PATIENT_PROFILE_COLLECTION_NAME,
    PATIENT_RELATION_COLLECTION_NAME,
    PATIENT_SEARCH_LOG_COLLECTION_NAME,
    PATIENT_VERSION_COLLECTION_NAME,
    RELATIVE_TYPE_COLLECTION_NAME,
} from './schemas/constants';
import { Model } from 'mongoose';
import { IPatient } from './intefaces/patient.inteface';
import { PatientFormMongoDTO } from './dto/patient-form-mongo.dto';
import { PatientService } from 'src/patient/patient.service';
import { UserInfoVerifyTokenDTO } from './dto/userInfo';
import { SIGNIN_PROVIDER_COLLECTION_NAME, USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { PROFESSION_COLLECTION_NAME } from 'src/profession-mongo/schemas/constants';
import { IProfession } from 'src/profession-mongo/interfaces/profession.interface';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { ICountry } from 'src/country-mongo/interfaces/country.interface';
import { INation } from 'src/nation-mongo/interfaces/nation.interface';
import { NATION_COLLECTION_NAME } from 'src/nation-mongo/schemas/constants';
import { CITY_COLLECTION_NAME } from 'src/city-mongo/schemas/constants';
import { ICity } from 'src/city-mongo/interfaces/city.interface';
import { DISTRICT_COLLECTION_NAME } from 'src/district-mongo/schemas/constants';
import { IDistrict } from 'src/district-mongo/interfaces/district.interface';
import { WARD_COLLECTION_NAME } from 'src/ward-mongo/schemas/constants';
import { IWard } from 'src/ward-mongo/interfaces/ward.interface';
import { UtilService } from 'src/config/util.service';
import { UMCPatientResponseDTO } from 'src/patient/dto/umc-patient-response.dto';
import { UMCPartnerConfigService } from 'src/config/config.umc.partner.service';
import { SearchPatientDTO } from 'src/patient/dto/search-patient.dto';
import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { UMCPatientExtraInfoResponseDTO } from 'src/patient/dto/umc-patient-extra-info-response.dto';
import { JwtModuleOptions } from '@nestjs/jwt';
import { JwtUserConfigService } from 'src/config/config.user.jwt.service';
import { UrlConfigService } from 'src/config/config.url.service';
import { VerifyPhonePatientMongoDTO } from 'src/patient/dto/verify-phone-patient-mongo.dto';
import { SearchPatientExtraInfoMongoDTO } from 'src/patient/dto/search-patient-extra-info-mongo.dto';
import { AddPatientToUserDTO } from 'src/patient/dto/add-patient-to-user.dto';
import { UpdatePatientMongoDTO } from './dto/update-patient-mongo.dto';
import { PatientFormMongoUpdateDTO } from './dto/patient-form-update-mongo.dto';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { IPatientCodes } from './intefaces/patient-codes.inteface';
import { IPatientSearchLog } from './intefaces/patient-search-log.inteface';
import { GetInsuranceInfoDTO } from './dto/get-insurance-info.dto';
import { GetInsuranceDateDTO } from './dto/get-insurance-date.dto';
import { GetInsuranceParseAddressDTO } from './dto/get-insurance-parse-address.dto';
import * as slugify from 'slugify';
import { IPatientVersion } from './intefaces/patient-version.inteface';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { SessionService } from 'src/session/session.service';
import { RestfulAPIOldHospitalConfigService } from 'src/config/config.rest-api.old.hospital';
import { IRelative } from './intefaces/relative.inteface';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import { UserService } from 'src/user/user.service';
import { SearchPhoneCMNDPatientExtraInfoMongoDTO } from 'src/patient/dto/search-phone-cmnd-patient-extra-info-mongo.dto';
import { ChorayPatientResponseDTO } from 'src/booking-gateway/dto/cr-patient-response.dto';
import { CSKHTokenVerifyDTO } from './dto/cskh-token-verify.dto';
import { VerifyInsuranceCodePatientMongoDTO } from 'src/patient/dto/verify-insurance-code-patient-mongo.dto';
import { PatientProfileDTO } from './dto/patient-profile.dto';
import { IPatientProfile } from './intefaces/patient-profile.interface';
import { GetPatientProfileDTO } from './dto/get-patient-profile.dto';
import { ConstraintSearchLogDTO } from './dto/constraint-search-log.dto';
import { YearOldValidationDto } from './dto/year-old-validation.dto';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { FindPatientDto } from './dto/find-patient-profile.dto';
import { SUBJECT_COLLECTION_NAME } from 'src/subject-mongo/schemas/constants';
import { ISubject } from 'src/subject-mongo/interfaces/subject.interface';
import { CREATE_FIND_BHYT_LOGS_EVENT, HANDLE_PATIENT_SORT, REMOVE_PATIENT_V1, UPDATE_RESERVE_LOGS_EVENT, EVENT_PATIENT_CMND_UPDATED } from 'src/message-event/constant';
import { PatientCmndUpdatedDto } from 'src/message-event/dto/patient-cmnd-updated.dto';
import { ISignInProvider } from 'src/user/interfaces/sign-in-provider.interface';
import { INSERT_PATIENT_V1 } from './event/constants';
import { PatientXcDto } from './dto/patient-xc.dto';
import { ReExamQueryDTO } from 'src/re-exam/dto/re-exam-query.dto';
import { BOOKING_COLLECTION_NAME, PATIENT_TRACKING_EVENT } from 'src/booking-gateway/schemas/constants';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { DataBhytDto } from './dto/bhyt-data.dto';
import { IPatientRelation } from './intefaces/patient-relations.interface';
import { ConfigRepoService } from 'src/config/config.repo.service';
import { ErrorMessage } from 'src/common/enums/message-error.enum';
import { SERVICE_COLLECTION_NAME } from 'src/service-mongo/schemas/constants';
import { PatientTrackingData } from './intefaces/patient-tracking.inteface';
import { IService } from 'src/service-mongo/interfaces/service.interface';
import { UserZaloInfoDto } from '../user-zalo/dto/user-zalo-info.dto';
import { ErrorMessageKey } from '../translate/enum/error-message.enum';
import { ValidateBookingRuleDto } from './dto/validate-booking-rule.dto';
import { REPO_NAME_BETA } from '../common/constants';
import { HeadersDto } from '../common/base/headers.dto';
import { FindPatientHisDto } from './dto/find-patient-his.dto';
import { timeout } from 'rxjs/operators';

@Injectable()
export class PatientMongoService {
    private logger = new Logger(PatientMongoService.name);
    private countPatient = 10;
    private countSearchPatient = 4;
    private listAppId: Set<{}>;
    private readonly AGE_LIMIT_CONFIG: string = 'AGE_LIMIT_CONFIG';
    private readonly WARNING_MESSAGE: string = 'WARNING_MESSAGE';
    private readonly repoName: string;
    private readonly oldHospital: Set<{}>;

    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @InjectModel(PATIENT_COLLECTION_NAME) private patientModel: Model<IPatient>,
        @InjectModel(PATIENT_VERSION_COLLECTION_NAME) private patientVersionModel: Model<IPatientVersion>,
        @InjectModel(PATIENT_CODE_COLLECTION_NAME) private patientCodeModel: Model<IPatientCodes>,
        @InjectModel(PATIENT_SEARCH_LOG_COLLECTION_NAME) private patientSearchLogModel: Model<IPatientSearchLog>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(COUNTRY_COLLECTION_NAME) private countryModel: Model<ICountry>,
        @InjectModel(NATION_COLLECTION_NAME) private nationModel: Model<INation>,
        @InjectModel(PROFESSION_COLLECTION_NAME) private professionModel: Model<IProfession>,
        @InjectModel(CITY_COLLECTION_NAME) private cityModel: Model<ICity>,
        @InjectModel(DISTRICT_COLLECTION_NAME) private districtModel: Model<IDistrict>,
        @InjectModel(RELATIVE_TYPE_COLLECTION_NAME) private relativeModel: Model<IRelative>,
        @InjectModel(WARD_COLLECTION_NAME) private wardModel: Model<IWard>,
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(PATIENT_PROFILE_COLLECTION_NAME) private patientProfileModel: Model<IPatientProfile>,
        // @InjectModel(GLOBAL_SETTING_COLLECTION_NAME) private readonly globalSettingModel: Model<IGlobalSetting>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private readonly hospitalModel: Model<IHospital>,
        @InjectModel(SUBJECT_COLLECTION_NAME) private readonly subjectModel: Model<ISubject>,
        @InjectModel(SIGNIN_PROVIDER_COLLECTION_NAME) private readonly signInProviderModel: Model<ISignInProvider>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(PATIENT_RELATION_COLLECTION_NAME) private patientRelationModel: Model<IPatientRelation>,
        @InjectModel(SERVICE_COLLECTION_NAME) private readonly serviceModel: Model<IService>,
        // @InjectModel(PATIENT_XC_COLLECTION_NAME) private readonly patientXncModel: Model<IPatientXnc>,
        @InjectSentry() private readonly clientSentry: SentryService,
        private readonly globalSettingService: GlobalSettingService,
        private patientService: PatientService,
        private utilService: UtilService,
        private readonly uMCPartnerConfigService: UMCPartnerConfigService,
        private readonly httpService: HttpService,
        private readonly jwtUserConfigService: JwtUserConfigService,
        private readonly urlConfigService: UrlConfigService,
        private readonly sessionService: SessionService,
        private readonly oldUrl: RestfulAPIOldHospitalConfigService,
        private userService: UserService,
        private emitService: EventEmitter2,
        private readonly repoConfigService: ConfigRepoService,
        private readonly client: ClientUtilService,
        private readonly translateService: TranslateService,
    ) {
        this.listAppId = this.utilService.listAppId();
        this.repoName = this.repoConfigService.getRepoName();
        this.oldHospital = this.utilService.oldHospitalSync();
    }

    async createPatientProfile(appId: string, partnerId: string, userId: string, formData: PatientProfileDTO): Promise<any> {
        const newPatientProfile = new this.patientProfileModel({
            id: uuid.v4().replace(/-/g, ''),
            ...formData,
            userId,
            appId,
            partnerId,
        });
        try {
            return newPatientProfile.save();
        } catch (error) {
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    async getPatientProfile(appId: string, partnerId: string, userId: string, formData: GetPatientProfileDTO): Promise<any> {
        const list = await this.patientProfileModel.find({
            userId,
            patientId: formData.patientId,
        }).exec();
        return list;
    }

    async insertChoRayPatient(appId: string, partnerId: string, data: ChorayPatientResponseDTO, userId: string, locale = 'vi'): Promise<any> {
        /* Tiến hành constraints search */
        await this.constraintSearchLog(userId, partnerId, undefined, locale);

        const patient: UMCPatientResponseDTO = await this.patientTransformer(data);
        data.mabhyt = last(split(data.msbn, '.'));
        /* insert thêm vào trong patient_codes */
        const objInsurance: any = {};
        if (typeof data.mabhyt !== typeof undefined && `${data.mabhyt}`.trim() !== '') {
            objInsurance.insuranceCode = `${data.mabhyt}`.trim();
        }
        try {
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            const { isVerifiedByPhone: isVerify = false } = partnerConfig;
            let isVerifiedByPhone = isVerify;
            /* Kiểm tra xem có config user-profiles có bật attribute_mode_dev_patner_enable */
            const checkModeDevPartnerEnable = await this.userService.getUserProfileVerifyPhone(partnerId, userId);
            if (checkModeDevPartnerEnable) {
                isVerifiedByPhone = false;
            }
            /* Kiểm tra thêm xem tài khoản đăng nhập có trùng với sdt trong hồ sơ */
            const getUsername: IUser = await this.userService.getUserByMongoUserId(userId);
            /* lấy thông tin mobile để check. nếu không có thì tạo tài khoản luôn */
            let mobile = get(patient, 'DienThoai', '');
            /* Kiểm tra xem mobile có hợp lệ hay ko */
            if (`${mobile}`.length === 9) {
                mobile = `0${mobile}`;
            }
            const checkPhone = isMobilePhone(mobile, 'vi-VN');
            if (checkPhone) {
                const yourphone = `${mobile}`.replace(/^[+]84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843');
                if (yourphone === getUsername.username) {
                    isVerifiedByPhone = false;
                }
            }
            /* kiểm tra xem thông tin ngày có chưa. nếu có rồi thì update */
            const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, patient.SoHS);
            if (checkExists) {
                // Lấy CMND cũ để so sánh
                const oldPatient = await this.patientModel.findOne({ id: checkExists.patientId }).exec();
                const oldCmnd = oldPatient?.cmnd;
                
                const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                    DiDong: patient.DiDong,
                    DienThoai: patient.DienThoai,
                    SoCMND: patient.SoCMND,
                    NgaySinh: patient.NgaySinh,
                    NamSinh: patient.NamSinh,
                    GioiTinh: patient.GioiTinh,
                    Ten: patient.Ten,
                    Ho: patient.Ho,
                    ...objInsurance,
                    isExam: true,
                    SoHS: patient.SoHS,
                    partnerId,
                });
                
                // Emit event nếu có thay đổi CMND
                if (patient.SoCMND && oldCmnd !== patient.SoCMND) {
                    this.emitCmndUpdatedEvent(checkExists.patientId, userId, oldCmnd, patient.SoCMND, 'update');
                }
                const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appId, updateInfo._id);
                const patientCheck = await this.getPatientByIdPatient(checkExists.patientId);
                const checkUserHavePatientCode = await this.checkExistsUMCPatientBelongsToUser(userId, patientCheck);
                const secretKeyObj: any = {};
                if (!checkUserHavePatientCode) {
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                }
                const { insuranceCode = '', ...restPatientInfo } = returnInfo;
                const resultData = [{
                    ...restPatientInfo,
                    mobile: this.secretMobile(returnInfo.mobile),
                    isVerifiedByPhone,
                    ...secretKeyObj,
                    patientCode: `${patient.SoHS}`.trim(),
                }];
                return resultData;
            } else {
                patient.partnerId = partnerId;
                patient.isExam = true;
                const patientInfo = await this.insertUMCSyncPatient(patient);
                const patientObj = patientInfo.toObject();
                
                // Emit event khi tạo mới patient với CMND
                if (patient.SoCMND) {
                    this.emitCmndUpdatedEvent(patientObj.id, userId, null, patient.SoCMND, 'create');
                }
                const patientCodeInfo = new this.patientCodeModel({
                    id: `${partnerId}_${patientObj.id}`,
                    createTime: moment().toDate(),
                    patientId: patientObj.id,
                    patientCode: `${patient.SoHS}`.trim(),
                    ...objInsurance,
                    partnerId,
                    appId,
                });
                await patientCodeInfo.save();
                // console.log(aaaa);
                /* kết thúc phần insert vào trong patient codes */
                const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appId, patientObj._id);
                const secretKeyObj: any = {};
                if (!isVerifiedByPhone) {
                    const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                    secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                }
                const { insuranceCode, ...restPatientInfo } = returnInfo;
                return [{
                    ...restPatientInfo,
                    mobile: this.secretMobile(returnInfo.mobile),
                    isVerifiedByPhone,
                    ...secretKeyObj,
                    patientCode: `${patient.SoHS}`.trim(),
                }];
            }
        } catch (error) {
            console.log(error);
            this.clientSentry.instance().captureException(error);
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async patientTransformer(data: ChorayPatientResponseDTO): Promise<UMCPatientResponseDTO> {
        const patient: UMCPatientResponseDTO = {
            DiDong: data.dienthoai,
            DienThoai: data.dienthoai,
            DiaChi: data.diachi,
            GioiTinh: Number(data.gioitinh) === 1,
            Ho: data.ho,
            Ten: data.ten,
            IDDanToc: null,
            IDNgheNghiep: null,
            IDPhuongXa: null,
            IDQuanHuyen: null,
            IDTinh: null,
            MaQuocGia: null,
            NamSinh: Number(data.namsinh),
            NgaySinh: data.ngaysinh,
            NgungSD: false,
            SoCMND: '',
            SoHS: data.msbn,
        };
        return patient;
    }

    async autoGenerateMedproId(): Promise<any> {
        const patients = await this.patientModel.find({
            code: '',
        });
        if (patients.length > 0) {
            for await (const patient of patients) {
                const medproId = this.generateMedproID(6);
                await this.patientModel.findOneAndUpdate({
                    id: patient.id,
                }, {
                    code: medproId,
                }).exec();
            }
        }
        return patients;
    }

    async getInsuranceInfo(partnerId: string, appId: string, insuranceForm: GetInsuranceInfoDTO): Promise<any> {
        try {
            const params = {
                ...insuranceForm,
                partnerId,
            };
            const baseUrl = this.urlConfigService.getBookingTreeUrl();
            const url = `${baseUrl}/his-connector/api/insurance/getInfo`;
            const data = (await this.getInsuranceInfoHIS(url, params).toPromise()).data;
            /* tiếp tục lấy thông tin thời hạn */
            let dataDate: any = {};
            const urlDate = `${baseUrl}/his-connector/api/insurance/getDate`;
            dataDate = (await this.getInsuranceInfoHIS(url, params).toPromise()).data;
            return { ...data, ...dataDate };
        } catch (error) {
            const { response } = error;
            if (response) {
                const { data, status } = response;
                this.clientSentry.instance().captureException(data);
                if (status === HttpStatus.NOT_FOUND) {
                    const { errorDetail } = data;
                    if (errorDetail) {
                        throw new HttpException(errorDetail, HttpStatus.NOT_FOUND);
                    }
                }
            }
            this.clientSentry.instance().captureException(error);
            throw new HttpException('Hệ thống không tìm thấy thông tin.', HttpStatus.NOT_FOUND);
        }
    }

    // async testInsurance(): Promise<any> {
    //     try {
    //         const data = (await this.getPatientExtraByInsuranceCodeHIS('trungvuong', 'CH4775104005748').toPromise()).data;
    //         return data;
    //     } catch (error) {
    //         console.log(error);
    //     }
    // }

    async getInsuranceDate(partnerId: string, appId: string, insuranceForm: GetInsuranceDateDTO, locale = 'vi', req: any, userId: string): Promise<any> {
        let data: any = {};
        const params = {
            ...insuranceForm,
            partnerId,
        };

        const uuidLog = uuid.v4().replace(/-/g, '');
        try {
            const patientInfo = await this.patientModel.findOne({ id: insuranceForm.patientId }, { id: true }).exec();
            const patientObj = patientInfo.toObject();
            //event logs
            this.emitService.emit(CREATE_FIND_BHYT_LOGS_EVENT, {
                uuid: uuidLog,
                partnerId,
                appId,
                userId: userId,
                timestamp: new Date(),
                url: req.url,
                method: req.method,
                headers: req.headers,
                params: req.params,
                body: req.body,
                query: req.query,
                patientId: patientObj._id,
                nameRepo: this.repoName,
                action: 'getInsuranceDate',
                insuranceId: insuranceForm.insuranceId,
            });
        } catch (error) {
            
        }

        if (!/^[a-zA-Z0-9]*$/.test(params.insuranceId.trim())) {
            const errorMessage = await this.translateService.getCommonErrorMessage(ErrorMessageKey.INVALID_INSURANCE_CODE, locale);
            throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
        }

        try {
            const baseUrl = this.urlConfigService.getBookingTreeUrl();
            const url = `${baseUrl}/his-connector/api/insurance/getDate`;
            data = (await this.getInsuranceInfoHIS(url, params).toPromise()).data;
            
            this.emitService.emit(UPDATE_RESERVE_LOGS_EVENT, {
                uuid: uuidLog,
                data
            });
        } catch (error) {
            const { response } = error;
            if (response) {
                const { data: dataError, status } = response;
                this.clientSentry.instance().captureException(dataError);
                if (status === HttpStatus.NOT_FOUND) {
                    const { errorDetail } = dataError;
                    if (errorDetail) {
                        this.emitService.emit(LOG_SERVICE_EVENT, {
                            name: 'getInsuranceInfoHIS',
                            summary: 'Lấy thông tin bảo hiểm bên HIS',
                            nameParent: 'getInsuranceDate',
                            params,
                            errorBody: dataError,
                            response: {soucre: data},
                            message: errorDetail,
                        });
                        throw new HttpException(errorDetail, HttpStatus.NOT_FOUND);
                    }
                }
            }
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'getInsuranceInfoHIS',
                summary: 'Lấy thông tin bảo hiểm bên HIS',
                nameParent: 'getInsuranceDate',
                params,
                errorBody: error.message,
                response: {soucre: data},
                message: 'Hệ thống không tìm thấy thông tin.',
            });
            this.clientSentry.instance().captureException(error);

            const errorMessage = await this.translateService.getCommonErrorMessage(ErrorMessageKey.NOT_FOUND_INSURANCE_INFO, locale);
            throw new HttpException(errorMessage, HttpStatus.NOT_FOUND);
        }
        /* kiểm tra tính hợp lệ của bảo hiểm */
        this.emitService.emit(LOG_SERVICE_EVENT, {
            name: 'validatedPartner',
            summary: 'Thong tin portal tra ve khi check ma bao hiem',
            nameParent: 'getInsuranceDate',
            params,
            errorBody: null,
            response: data,
            message: 'Thong tin portal tra ve khi check ma bao hiem',
        });
        const { insuranceToDate, validatedPartner, maDKBD: maDKBDHT, maThe } = data;
        const validPartnerId = new Set(['leloi', 'dalieuhcm', 'bvmathcm']);
        if (!validatedPartner) {
            if (!validPartnerId.has(partnerId)) { // TODO: chỗ này cho thêm config
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'validatedPartner',
                    summary: 'kiểm tra tính hợp lệ của bảo hiểm ',
                    nameParent: 'getInsuranceDate',
                    params,
                    errorBody: null,
                    response: {soucre: data},
                    message: 'Nơi đăng ký khám bảo hiểm ban đầu của bạn không hợp lệ, Vui lòng kiểm tra lại thông tin.',
                });

                const errorMessage = await this.translateService.getCommonErrorMessage(ErrorMessageKey.INVALID_INSURANCE_FIRST_REGISTER_PLACE, locale);
                throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
            }
        }
        /* Kiểm tra xem mã bảo hiểm nay có gắn với 1 cái mã số bệnh nhân hay chưa */
        const objPatientCode: any = {};
        const newSetPartnerCheck = new Set().add('hoanmytd').add('binhthanhhcm').add('dalieuhcm').add('bvungbuouct')
        if (!newSetPartnerCheck.has(partnerId)) {
            try {
                const findMsbnData = (await this.getPatientExtraByInsuranceCodeHIS(partnerId, insuranceForm.insuranceId).toPromise()).data;
                if (findMsbnData.length > 0) {
                    const resultMsbnData: any = first(findMsbnData);
                    objPatientCode.patientCode = `${resultMsbnData.SoHS}`.trim();
                } else {
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: 'getPatientExtraByInsuranceCodeHIS',
                        summary: 'Kiểm tra xem mã bảo hiểm nay có gắn với 1 cái mã số bệnh nhân hay chưa',
                        nameParent: 'getInsuranceDate',
                        params: {partnerId, insuranceId: insuranceForm.insuranceId},
                        errorBody: null,
                        response: {findMsbnData, soucre: data},
                        message: 'Nơi đăng ký khám bảo hiểm ban đầu của bạn không hợp lệ, Vui lòng kiểm tra lại thông tin.',
                    });
                }
            } catch (error) {
                const {response} = error;
                if (response) {
                    const { data: dataError, status } = response;
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: 'getPatientExtraByInsuranceCodeHIS',
                        summary: 'Kiểm tra xem mã bảo hiểm nay có gắn với 1 cái mã số bệnh nhân hay chưa',
                        nameParent: 'getInsuranceDate',
                        params: {partnerId, insuranceId: insuranceForm.insuranceId},
                        errorBody: dataError,
                        response: {soucre: data},
                        message: 'Nơi đăng ký khám bảo hiểm ban đầu của bạn không hợp lệ, Vui lòng kiểm tra lại thông tin.',
                    });
                }
                this.clientSentry.instance().captureException(error);
            }
        }
        /* kiểm tra thông tin hồ sơ bệnh nhân */
        const patientInfo = await this.patientModel.findOne({ id: insuranceForm.patientId }).exec();
        if (!patientInfo) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'partnerInfo',
                summary: 'kiểm tra thông tin hồ sơ bệnh nhân',
                nameParent: 'getInsuranceDate',
                params: {partnerId:  insuranceForm.patientId},
                errorBody: null,
                response: {patientInfo , source: data},
                message: 'Không tìm thấy thông tin hồ sơ bệnh nhân.',
            });

            const errorMessage = await this.translateService.getCommonErrorMessage(ErrorMessageKey.VERIFY_INSURANCE_NOT_FOUND_PATIENT, locale);
            throw new HttpException(errorMessage, HttpStatus.NOT_FOUND);
        }
        const patientObj = patientInfo.toObject();
        const patientId = patientObj.id;
        /* kiểm tra chính xác thông tin gửi lên */
        console.log('ngay sinh tren bao hiem', insuranceForm.birthday);
        const updateObj = {
            birthyear: moment(insuranceForm.birthday, 'YYYY-MM-DD').year(),
            birthdate: moment(insuranceForm.birthday, 'YYYY-MM-DD').format('YYYY-MM-DD'),
        };
        console.log('object update', updateObj);
        /* cập nhật lại thông tin hồ sơ với ngày sinh theo thẻ bảo hiểm */
        const aaaa = await this.patientModel.findByIdAndUpdate({ _id: patientObj._id }, { ...updateObj }, { new: true });
        /* convert sang ngày */
        console.log('thong tin sau khi cap nhat', aaaa);
        const toDate = moment(insuranceToDate).format('YYYY-MM-DD');
        const bookingDate = moment(insuranceForm.bookingDate, 'YYYY-MM-DD').format('YYYY-MM-DD');
        const toDateValueOf = moment(toDate).valueOf();
        const bookingDateValueOf = moment(bookingDate).valueOf();
        const insuranceCode = maThe; // insuranceForm.insuranceId;
        if (toDateValueOf >= bookingDateValueOf) { /* còn hiệu lực */
            /* add thông tin bảo hiểm */
            console.log('bao hiem con hieu luc');
            const findPatientCode = await this.patientCodeModel.findOne({ partnerId, patientId }).exec();
            const qrCodeContent = await this.handleQrcodeBHYTFromHis(data, { ...patientObj }, partnerId);
            /* cập nhật lại thông tin */
            if (!findPatientCode) { /* thêm mới thông tin */
                const info = new this.patientCodeModel({
                    id: `${partnerId}_${patientId}`,
                    createTime: moment().toDate(),
                    patientId,
                    insuranceCode,
                    insuranceExpiredDate: moment(insuranceToDate).toDate(),
                    partnerId,
                    appId,
                    /* patientCode */
                    ...objPatientCode,
                    maDKBDHT,
                    ...(qrCodeContent && { qrCodeContent }),
                });
                await info.save();
            } else {
                const patientCodeObj = findPatientCode.toObject();
                if (Object.keys(objPatientCode).length > 0) {
                    const getPatientCode = get(patientCodeObj, 'patientCode', '');
                    if (getPatientCode) {
                        if (patientCodeObj.patientCode !== getPatientCode) {
                            this.clientSentry.instance().captureException(`${partnerId}: patient-codes có _id: ${patientCodeObj._id} old: ${getPatientCode} new: ${patientCodeObj.patientCode}`);
                            await this.patientCodeModel.findByIdAndUpdate(
                                { _id: patientCodeObj._id },
                                { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...objPatientCode, ...(qrCodeContent && { qrCodeContent }) },
                            ).exec();
                        } else {
                            await this.patientCodeModel.findByIdAndUpdate(
                                { _id: patientCodeObj._id },
                                { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...(qrCodeContent && { qrCodeContent }) },
                            ).exec();
                        }
                    } else {
                        await this.patientCodeModel.findByIdAndUpdate(
                            { _id: patientCodeObj._id },
                            { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...objPatientCode, ...(qrCodeContent && { qrCodeContent }) },
                        ).exec();
                    }

                } else {
                    await this.patientCodeModel.findByIdAndUpdate(
                        { _id: patientCodeObj._id },
                        { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...(qrCodeContent && { qrCodeContent }) },
                    ).exec();
                }
            }
            const infoFull = await this.getPatientInfoAndPatientCodeById(partnerId, appId, patientObj._id);
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'validTime',
                summary: 'OK',
                params: { toDateValueOf, bookingDateValueOf },
                nameParent: 'getInsuranceDate',
                errorBody: null,
                response: {
                    ...infoFull,
                    expired: false,
                    isValid: validatedPartner,
                    maDKBDHT,
                    message: '',
                },
                message: '',
            });
            return {
                ...infoFull,
                expired: false,
                isValid: validatedPartner,
                maDKBDHT,
                message: '',
            };
        } else { /* hết hiệu lực */
            const infoFull = await this.getPatientInfoAndPatientCodeById(partnerId, appId, patientObj._id);
            const errorMessage = await this.translateService.getCommonErrorMessage(ErrorMessageKey.INSURANCE_EXPIRED, locale);

            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'validTime',
                summary: 'hết hiệu lực',
                params: { toDateValueOf, bookingDateValueOf },
                nameParent: 'getInsuranceDate',
                errorBody: null,
                response: {
                    ...infoFull,
                    expired: true,
                    isValid: validatedPartner,
                    maDKBDHT,
                    message: errorMessage,
                },
                message: errorMessage,
            });
            return {
                ...infoFull,
                expired: true,
                isValid: validatedPartner,
                maDKBDHT,
                message: errorMessage,
            };
        }

    }

    async getInsuranceParseAddress(insuranceForm: GetInsuranceParseAddressDTO): Promise<any> {
        try {
            const params = {
                ...insuranceForm,
            };
            const baseUrl = this.urlConfigService.getBookingTreeUrl();
            const url = `${baseUrl}/his-connector/api/parseAddress`;
            return (await this.getInfoParseAddress(url, params).toPromise()).data;
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            return error;
            throw new HttpException('Vui lòng thử lại sau.', HttpStatus.EXPECTATION_FAILED);
        }
    }

    async updatePatientHasPatientCode(updatePatientMongoDTO: UpdatePatientMongoDTO, userId: string, partnerId: string, appid: string): Promise<any> {
        /* kiểm tra xem bệnh nhân có tồn tại trong user hay không */
        const findPatient = await this.patientModel.findOne({ id: updatePatientMongoDTO.id }).exec();
        if (!findPatient) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân', HttpStatus.NOT_FOUND);
        } else {
            const patientObj = findPatient.toObject();
            const checkValue = await this.checkUMCPatientBelongsToUser(userId, patientObj._id);
            if (checkValue) {
                let updateInfo: any = {};
                // if (!!patientObj.patientCode) {
                // tồn tại mã số bệnh nhân
                const { mobile, email, cmnd, profession_id, country_code,
                    dantoc_id, city_id, district_id, ward_id, address,
                } = updatePatientMongoDTO;
                updateInfo = {
                    mobile, cmnd, email, profession_id, country_code,
                    dantoc_id, city_id, district_id, ward_id, address,
                };
                /* lấy lại city, dantoc, district, ward */
                const findNation = await this.nationModel.findOne({ id: dantoc_id }).exec();
                if (findNation) {
                    const nationObj = findNation.toObject();
                    updateInfo.nation = nationObj._id;
                }
                const findCity = await this.cityModel.findOne({ id: city_id }).exec();
                if (findCity) {
                    const cityObject = findCity.toObject();
                    updateInfo.city = cityObject._id;
                }
                const findDistrict = await this.districtModel.findOne({ id: district_id }).exec();
                if (findDistrict) {
                    const districtObj = findDistrict.toObject();
                    updateInfo.district = districtObj._id;
                }
                const findWard = await this.wardModel.findOne({ id: ward_id }).exec();
                if (findWard) {
                    const wardObj = findWard.toObject();
                    updateInfo.ward = wardObj._id;
                }

                if (!!profession_id && profession_id !== '0') {
                    const findProfession = await this.professionModel.findOne({ id: profession_id }).exec();
                    if (findProfession) {
                        const professionObj = findProfession.toObject();
                        updateInfo.profession = professionObj._id;
                    }
                }
                //  else {
                //     updateInfo
                // }

                /* kiểm tra xem hồ sơ này có medpro id chưa. nếu chưa có thì tạo luôn. */
                if (!!patientObj.code === false) {
                    updateInfo.code = this.generateMedproID(6);
                }
                // tiến hành update patient
                const afterUpdate = await this.patientModel.findByIdAndUpdate(
                    { _id: patientObj._id }, { ...updateInfo },
                    { new: true },
                );
                /* tiến hành sao chép sang patient version */
                await this.copyPatient(afterUpdate);
                return this.getPatientInfoById(patientObj._id, partnerId, appid);
                // }
                // throw new HttpException('Thông tin bệnh nhân này chưa có số hồ sơ bệnh nhân.', HttpStatus.BAD_REQUEST);
            }
        }
    }

    // async updatePatientHasPatientCode(updatePatientMongoDTO: UpdatePatientMongoDTO, userId: string): Promise<any> {
    //     /* kiểm tra xem bệnh nhân có tồn tại trong user hay không */
    //     const findPatient = await this.patientModel.findOne({ id: updatePatientMongoDTO.id }).exec();
    //     if (!findPatient) {
    //         throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân', HttpStatus.NOT_FOUND);
    //     } else {
    //         const patientObj = findPatient.toObject();
    //         const checkValue = await this.checkUMCPatientBelongsToUser(userId, patientObj._id);
    //         if (checkValue) {
    //             let updateInfo: any = {};
    //             if (!!patientObj.patientCode) {
    //                 // tồn tại mã số bệnh nhân
    //                 const { mobile, email } = updatePatientMongoDTO;
    //                 updateInfo = { mobile, email };
    //                 /* kiểm tra xem hồ sơ này có medpro id chưa. nếu chưa có thì tạo luôn. */
    //                 if (!!patientObj.code === false) {
    //                     updateInfo.code = this.generateMedproID(6);
    //                 }
    //                 // tiến hành update patient
    //                 await this.patientModel.findByIdAndUpdate({ _id: patientObj._id }, { ...updateInfo });
    //                 return this.getPatientInfoById(patientObj._id);
    //             }
    //             throw new HttpException('Thông tin bệnh nhân này chưa có số hồ sơ bệnh nhân.', HttpStatus.BAD_REQUEST);
    //         }
    //     }
    // }

    async copyPatient(afterUpdate: any): Promise<any> {
        /* tiến hành sao chép sang patient version */
        const { _id: idOld, id: idPatientRef,
            createdAt: ngaycreate, updatedAt: ngayudpate, __v: vCurrent, ...restPatientCopy } = afterUpdate.toObject();
        const newCopy = new this.patientVersionModel({ ...restPatientCopy });
        newCopy.id = uuid.v4().replace(/-/g, '');
        newCopy.patientId = idPatientRef;
        return newCopy.save();
    }

    async checkPatientSyncHISOrBookings(
        appId: string, partnerId: string, user: UserInfoVerifyTokenDTO, patient: IPatient, patientFormData: PatientFormMongoUpdateDTO) {

        /* kiểm tra xem hồ sơ này có link v1 chưa */
        let isUpdateFull = true;
        const idUMCV1 = get(patient, 'patientIdV1UMC', 0);
        const idND1V1 = get(patient, 'patientIdV1', 0);

        const fullNameV2 = `${patient.surname} ${patient.name}`.toUpperCase();
        const birthYearV2 = `${patient.birthyear}`;
        const genderV2 = `${patient.sex}`;
        if (idUMCV1 > 0) {
            /* kiểm tra xem có booking chưa */
            const getUMCBookingV1 = await this.pkhPatientKnex('booking').where({ patient_id: idUMCV1 }).whereIn('status', [1, 2, -2]);
            if (getUMCBookingV1.length > 0) {
                isUpdateFull = false;
                // throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
            }

            /* kiem tra lai xem hồ sơ v2, v1 có giống nhau ko */
            const getPatientV1 = await this.pkhPatientKnex('patient').where({ id: idUMCV1 }).first();
            if (!getPatientV1) {
                /* un set cái patientIdV1UMC */
                await this.patientModel.findByIdAndUpdate({ _id: patient._id }, {
                    patientIdV1UMC: 0,
                }).exec();

                throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
            }
            /* kiem tra ho va ten */
            const fullnameV1 = `${getPatientV1.surname} ${getPatientV1.name}`.toUpperCase();
            const birthYearV1 = `${getPatientV1.birthyear}`;
            const genderV1 = `${getPatientV1.sex}`;

            const checkUMC = fullNameV2 === fullnameV1 && birthYearV2 === birthYearV1 && genderV2 === genderV1;
            if (!checkUMC) {
                throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
            }

        }

        if (idND1V1 > 0) {
            /* kiểm tra xem có booking chưa */
            const getND1BookingV1 = await this.pkhPatientKnex('nd1_booking').where({ nd1_patient_id: idND1V1 }).whereIn('status', [1, 2, -2]);
            if (getND1BookingV1.length > 0) {
                isUpdateFull = false;
                // throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
            }
            /* kiem tra lai xem hồ sơ v2, v1 có giống nhau ko */
            const getPatientV1 = await this.pkhPatientKnex('nd1_patient').where({ id: idND1V1 }).first();
            if (!getPatientV1) {
                /* un set cái patientIdV1UMC */
                await this.patientModel.findByIdAndUpdate({ _id: patient._id }, {
                    patientIdV1: 0,
                }).exec();

                throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
            }
            /* kiem tra ho va ten */
            const fullnameV1 = `${getPatientV1.surname} ${getPatientV1.name}`.toUpperCase();
            const birthYearV1 = `${getPatientV1.birthyear}`;
            const genderV1 = `${getPatientV1.sex}`;

            const checkUMC = fullNameV2 === fullnameV1 && birthYearV2 === birthYearV1 && genderV2 === genderV1;
            if (!checkUMC) {
                throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
            }

        }
        const dataV1: any = { isUpdateFull: true };

        try {

            const config = await this.globalSettingService.findByKeyAndRepoName('CONFIG_CHECK_PATIENT_BOOKING_SUCCESS');
            const configObj = JSON.parse(config);
            if (configObj?.env === 'ON') {
                const [patientCodes, booking, bookingCancel] = await Promise.all([
                    this.getPatientCodeByPatientIdIgnorePartnerId(patient.id),
                    this.bookingModel.find({ status: { $in: [1, 2] }, patientId: patient.id }).limit(1).exec(),
                    this.bookingModel.find({ status: -2, paymentStatus: 2, patientId: patient.id }).limit(1).exec(),
                ]);
                if (patientCodes || (booking.length + bookingCancel.length) > 0) {
                    isUpdateFull =  false;
                }
            }

            if (!isUpdateFull) {
                patientFormData.city_id = patient.city_id;
                patientFormData.dantoc_id = patient.dantoc_id;
            }
            dataV1.isUpdateFull = isUpdateFull;
            if (idUMCV1 > 0) {
                dataV1.umc = await this.updatePatient_Nhidong1('umc', appId, user, patientFormData, isUpdateFull);
            }
            if (idND1V1 > 0) {
                dataV1.nhidong1 = await this.updatePatient_Nhidong1('nhidong1', appId, user, patientFormData, isUpdateFull);
            }
        } catch (error) {
            console.log(error);
            throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
        }

        return dataV1;
    }

    async proxyUpatePatientWithoutPatientCode(
        partnerId: string, appId: string, patientFormData: PatientFormMongoUpdateDTO, authorization: string,
        userId?: string, version?: string, locale?: string, platform?: string
    ): Promise<any> {
        const url = `${this.urlConfigService.getUrlApiWeb}/mongo/patient/update-without-msbn`;
        
        // Lấy thông tin patient cũ để so sánh CMND (nếu có userId và patientId)
        let oldCmnd: string = null;
        if (userId && patientFormData.id) {
            try {
                const oldPatient = await this.patientModel.findOne({ id: patientFormData.id }).exec();
                oldCmnd = oldPatient?.cmnd;
            } catch (error) {
                // Ignore error khi không tìm thấy patient cũ
                this.logger.warn(`Could not find old patient data for CMND comparison: ${error.message}`);
            }
        }
        
        try {
            const { data } = await this.httpService.post(
                url,
                patientFormData,
                { headers: { appid: appId, partnerid: partnerId, authorization, platform, version: version || '', locale: locale || 'vi'} },
            ).toPromise();
            
            // Emit event nếu có thay đổi CMND
            if (userId && patientFormData.id && patientFormData.cmnd && oldCmnd !== patientFormData.cmnd) {
                this.emitCmndUpdatedEvent(patientFormData.id, userId, oldCmnd, patientFormData.cmnd, 'update');
            }
            
            return data;
        } catch (error) {
            const message = error?.message || error?.response.data.message
            this.logger.error(`Error when exec proxyUpatePatientWithoutPatientCode. Cause: ${message}`);
            throw new HttpException(message, HttpStatus.BAD_REQUEST)
        }
    }

    async upatePatientWithoutPatientCode(
        partnerId: string, appId: string, user: UserInfoVerifyTokenDTO, patientFormData: PatientFormMongoUpdateDTO,
        cskhInfo?: CSKHTokenVerifyDTO, locale?: string,
    ): Promise<any> {
        if (patientFormData.mobileLocaleIso === 'vi-VN') {
            patientFormData.mobile = patientFormData.mobile.replace(/^\+84/, '0');
        }

        if (patientFormData.relativeMobileLocaleIso === 'vi-VN' && patientFormData.relative_mobile) {
            patientFormData.relative_mobile = patientFormData.relative_mobile.replace(/^\+84/, '0');
        }

        const envDisableUpdateDeletePatient = await this.globalSettingService.findByKeyAndRepoName('ENV_UPDATE_DELETE_PATIENT');
        const countBooking = await this.bookingModel.count({ partnerId: 'bvmathcm', patientId: patientFormData.id, status: { $in: [1, 2] } }).exec();

        const booking =  await this.bookingModel.findOne({ id: patientFormData.bookingId }, { patientId: true, partnerId: true }).exec();

        let allowUpdateBvMat = false;
        if (booking?.partnerId === 'bvmathcm') {
            // nếu mà có booking bvmat thì check cùng patientId, cung cho update, k thôi.
            allowUpdateBvMat = booking?.patientId === patientFormData.id;
        }

        if (!allowUpdateBvMat && countBooking >= 1 && envDisableUpdateDeletePatient === 'ON') {
            throw new HttpException('Để bảo mật thông tin bệnh nhân - Vui lòng liên hệ 19002115 để chỉnh sửa hồ sơ', HttpStatus.BAD_REQUEST);
        }

        /* kiểm tra xem bệnh nhân có tồn tại trong user hay không */
        const findPatient = await this.patientModel.findOne({ id: patientFormData.id }).exec();
        let stepInfo: any = {};
        if (!findPatient) {
            throw new HttpException({
                key: 'PATIENT_MESSAGE_ERROR_NOT_FOUND',
                locale,
            }, HttpStatus.NOT_FOUND);
        } else {
            const patientObj = findPatient.toObject();

            /* kiem tra xem */
            const checkValue = await this.checkUMCPatientBelongsToUser(user.userMongoId, patientObj._id);
            if (!checkValue) {
                throw new HttpException('Thao tác không thành công. Vui lòng liên hệ 19002115 để được hỗ trợ.', HttpStatus.NOT_FOUND);
            }

            const patientTrackingData : PatientTrackingData = {
                action: "UPDATE",
                patient: findPatient._id,
                userAction: user.userMongoId,
                userPatient: user.userMongoId,
                dataBefore: patientObj,
            }

            const { relative_name, relative_email, relative_mobile, relative_type_id, id, bookingId, ...patientInfo } = patientFormData;
            const replacerBirthYear = moment(patientInfo?.birthdate).isValid() ? moment(patientInfo.birthdate).year() : '';
            // const medproId = await this.patientService.generateUMCMedproID();
            const patientUpdate: any = {
                ...patientInfo,
                birthdate: moment(patientInfo.birthdate).isValid() ? moment(patientInfo.birthdate).format('YYYY-MM-DD') : '',
                // code: medproId,
                sourceId: appId,
                partnerId,
                userId: user.userMongoId,
                ...(replacerBirthYear && { birthyear: replacerBirthYear }),
            };

            /* bổ sung cho update thẳng 1 số field */
            const installUpdate: any = {
                address: patientFormData.address,
            };

            const findCity = await this.cityModel.findOne({ id: patientUpdate.city_id }).exec();
            if (findCity) {
                const cityObject = findCity.toObject();
                patientUpdate.city = cityObject._id;
                patientUpdate.city_id = cityObject.id;
            }
            const findDistrict = await this.districtModel.findOne({ id: patientUpdate.district_id }).exec();
            if (findDistrict) {
                const districtObj = findDistrict.toObject();
                patientUpdate.district = districtObj._id;
                patientUpdate.district_id = districtObj.id;
            }
            const findWard = await this.wardModel.findOne({ id: patientUpdate.ward_id }).exec();
            if (findWard) {
                const wardObj = findWard.toObject();
                patientUpdate.ward = wardObj._id;
                patientUpdate.ward_id = wardObj.id;
            }

            if (findPatient.city_id === patientFormData.city_id) {

                installUpdate.district = patientUpdate.district;
                installUpdate.district_id = patientUpdate.district_id;

                installUpdate.ward = patientUpdate.ward;
                installUpdate.ward_id = patientUpdate.ward_id;

                // await this.patientModel.findByIdAndUpdate(
                //     { _id: findPatient._id },
                //     { ...installUpdate },
                // );
            }

            // check update patient
            // const isCS = await this.userService.isCs(cskhInfo?.cskhUserId || user.userMongoId);
            // if (!isCS)
            try {

                if (!!patientUpdate.profession_id && patientUpdate.profession_id !== '0') {
                    const findProfession = await this.professionModel.findOne({ id: patientUpdate.profession_id }).exec();
                    if (findProfession) {
                        const professionObj = findProfession.toObject();
                        patientUpdate.profession = professionObj._id;
                    }
                } else {
                    patientUpdate.profession_id = '';
                }
                /* tìm lại thông tin */
                const findCountry = await this.countryModel.findOne({ code: patientUpdate.country_code }).exec();
                if (findCountry) {
                    const countryObj = findCountry.toObject();
                    patientUpdate.country = countryObj._id;
                    patientUpdate.country_id = countryObj.id;
                }

                const findNation = await this.nationModel.findOne({ id: patientUpdate.dantoc_id }).exec();
                if (findNation) {
                    const nationObj = findNation.toObject();
                    patientUpdate.nation = nationObj._id;
                    patientUpdate.dantoc_id = nationObj.id;
                }

                /* kiểm tra xem hồ sơ này có medpro id chưa. nếu chưa có thì tạo luôn. */
                if (!!patientObj.code === false) {
                    patientUpdate.code = this.generateMedproID(6);
                }
                /* kiểm tra xem có thông tin thân nhân hay ko */
                if (patientFormData.relative_type_id) {
                    patientUpdate.relation = {
                        relative_name: patientFormData.relative_name,
                        relative_email: patientFormData.relative_email,
                        relative_mobile: patientFormData.relative_mobile,
                        relativeMobileLocaleIso: patientFormData.relativeMobileLocaleIso,
                        relative_type_id: patientFormData.relative_type_id,
                    };
                }

                stepInfo = {
                    ...stepInfo,
                    stepName: 'Chuẩn bị thao tác update hồ sơ xuống V1.',
                };

                const v1Info: { isUpdateFull: boolean, umc: {}, nhidong1: {} } =
                    await this.checkPatientSyncHISOrBookings(appId, partnerId, user, findPatient, patientFormData);
                let updateFinal: any = {};
                if (!v1Info.isUpdateFull) {
                    const { name, surname, sex, birtdate, birtyear, dantoc_id, nation, city_id, city, ...restUpdateFinal } = patientUpdate;
                    updateFinal = restUpdateFinal;
                } else {
                    updateFinal = patientUpdate;
                }
                // let dataPatientV1:any;
                // if (this.listAppId.has(appId)) {
                //     /* tiến hành cập nhật sang v1 */
                //     const newSet = this.utilService.oldHospitalSync();
                //     if (!!partnerId && newSet.has(partnerId)) {
                //         dataPatientV1 = await this.updatePatient_Nhidong1(partnerId, appId, user, patientFormData);
                //     } else {
                //         if (appId === 'medpro') {
                //             const validation = await this.checkBookingND1OfPatient(findPatient._id);
                //             if (validation) {
                //                 // default update for nhidong1
                //                 dataPatientV1 = await this.updatePatient_Nhidong1('nhidong1', appId, user, patientFormData);
                //             }
                //         }
                //     }
                // }
                const trackings = [
                    {
                        name: 'thao tác update hồ sơ xuống V1 thành công',
                    },
                ];
                stepInfo = {
                    ...stepInfo,
                    trackings: [
                        ...trackings,
                    ],
                    stepName: 'Chuẩn bị thao tác update hồ sơ xuống V2.',
                };

                // await this.syncPatientHisAfterUpdate(isCS, user.userMongoId, findPatient?.id, partnerId);

                if (booking?.partnerId === 'bvmathcm') {
                    updateFinal = pick(updateFinal, [
                        'city',
                        'city_id',
                        'district',
                        'district_id',
                        'ward',
                        'ward_id',
                        'address',
                        'cmnd'
                    ])
                }
                
                // Lưu CMND cũ để so sánh
                const oldCmnd = findPatient.cmnd;
                
                const afterUpdate = await this.updatePatientInfo(findPatient._id, updateFinal);

                this.emitService.emit(PATIENT_TRACKING_EVENT, {
                    ...patientTrackingData,
                    dataAfter: afterUpdate.toObject()
                })

                // Emit event nếu có thay đổi CMND
                const newCmnd = updateFinal.cmnd;
                if (newCmnd && oldCmnd !== newCmnd) {
                    this.emitCmndUpdatedEvent(findPatient.id, user.userMongoId, oldCmnd, newCmnd, 'update');
                }

                /* tiến hành sao chép sang patient version */
                stepInfo = {
                    ...stepInfo,
                    trackings: [
                        ...stepInfo.trackings,
                        {
                            name: 'thao tác update hồ sơ xuống V2 thành công',
                        },
                    ],
                    stepName: 'Chuẩn bị thao tác copy patient.',
                };

                const patientVersion = await this.copyPatient(afterUpdate);

                // if (partnerId === 'nhidong1' && dataPatientV1 && patientVersion) {
                stepInfo = {
                    ...stepInfo,
                    trackings: [
                        ...stepInfo.trackings,
                        {
                            name: 'thao tác copy patient thành công',
                        },
                    ],
                    stepName: 'Chuẩn bị thao tác update patient version vào trong booking tiếp theo.',
                };
                await this.updatePaitentVersionBooking(findPatient._id, patientVersion._id, patientVersion.id);
                // }

                if (patientFormData?.insuranceId) {
                    await this.insertPatientBHYT(appId, partnerId, afterUpdate, patientFormData.insuranceId);
                }

                return this.getPatientInfoById(patientObj._id, partnerId, appId);

            } catch (error) {
                console.log(error);
                // this.clientSentry.instance().captureException(error);
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'trackingUpdatePatientError',
                    summary: 'Lỗi khi cập nhật hồ sơ.',
                    nameParent: 'updatePatientNhiDong1',
                    params: {
                        stepInfo,
                        appId,
                        partnerId,
                        user,
                        cskhInfo
                    },
                    errorBody: this.utilService.errorHandler(error),
                    response: null,
                    message: 'Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.',
                });
                throw new HttpException({
                    key: 'BAD_REQUEST_MESSAGE_ERROR',
                    locale,
                }, HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }
    }

    async updatePatient_Nhidong1(
        partnerId: string, appId: string, user: UserInfoVerifyTokenDTO, patientFormData: PatientFormMongoUpdateDTO,
        isUpdateFull = true, locale?: string): Promise<boolean> {
        /* tìm lại user_id và access_token */
        const userMongo = await this.userModel.findById({ _id: user.userMongoId }, { username: true, medproId: true }).exec();

        /* tiến hành chuyển đổi */
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const urlTransform = `${baseUrl}/his-connector/api/convertPartnerData`;
        const findCountry = await this.countryModel.findOne({ code: patientFormData.country_code }).exec();
        const params: any = {
            fromPartnerId: 'medpro',
            toPartnerId: partnerId,
            countryId: '',
            cityId: `${patientFormData.city_id}`,
            districtId: `${patientFormData.district_id}`,
            wardId: `${patientFormData.ward_id}`,
            nationId: `${patientFormData.dantoc_id}`,
            relationTypeId: `${patientFormData.relative_type_id}`,
        };
        if (findCountry) {
            const objCountry = findCountry.toObject();
            params.countryId = `${objCountry.id}`; // override countryId
        }
        const dataKeys: any = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
        const urlGetPatients = this.oldUrl.NhiDong1UpdatePatients();
        const url = `${urlGetPatients}`;
        /* tìm lại patientIdV1 */
        const findPatient = await this.patientModel.findOne({ id: patientFormData.id }).exec();
        if (!findPatient) {
            throw new HttpException({
                key: 'PATIENT_MESSAGE_ERROR_NOT_FOUND',
                locale,
            }, HttpStatus.NOT_FOUND);
        }
        const keyPatientV1 = this.utilService.patientIdV1Key(partnerId);
        /* lấy patient_id của v1 */
        const patientId = findPatient[keyPatientV1] || 0;

        if (patientId === 0) {
            return null;
        }

        /* tìm lại chính xác hồ sơ - user nao */
        let userV1:any = {};
        const listUserRe = await this.pkhPatientKnex('user')
            .whereIn('username', [`${userMongo.medproId}`, `${userMongo.username}`]);

        if (!listUserRe) {
            throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
        }

        /* kiểm tra xem patientId này thuộc user nào */
        if (listUserRe.length === 1) {
            const firstUser: any = first(listUserRe);
            userV1 = await this.pkhPatientKnex('user')
                .where('id', firstUser?.id || 0).first();

        } else {
            if (partnerId === 'umc') {
                const pluckUserId = map(listUserRe, 'id');
                const getUserPatient = await this.pkhPatientKnex('user_patient')
                    .where({
                        patient_id: patientId,
                    })
                    .whereIn('user_id', pluckUserId).first();

                userV1 = await this.pkhPatientKnex('user')
                    .where('id', getUserPatient?.user_id || 0).first();
            } else if(partnerId === 'nhidong1'){
                const pluckUserId = map(listUserRe, 'id');
                const getUserPatient = await this.pkhPatientKnex('nd1_user_patient')
                    .where({
                        nd1_patient_id: patientId,
                    })
                    .whereIn('user_id', pluckUserId).first();

                userV1 = await this.pkhPatientKnex('user')
                    .where('id', getUserPatient?.user_id || 0).first();
            }
        }

        if(!userV1){
            return null;
        }

        let session: any;
        switch (partnerId) {
            case 'nhidong1':
                session = await this.sessionService.checkExistsNhiDong1SessionByUserID(userV1.id);
                break;
            case 'dalieuhcm':
                session = await this.sessionService.checkExistsSkinSessionByUserID(userV1.id);
                break;
            case 'ctchhcm':
                session = await this.sessionService.checkExistsCTCHSessionByUserID(userV1.id);
                break;
            case 'thuduc':
                session = await this.sessionService.checkExistsThuDucSessionByUserID(userV1.id);
                break;
            case 'umc':
                session = await this.sessionService.checkExistsUMCSessionByUserID(userV1.id);
                break;
            default:
                session = {
                    user_id: 0,
                    access_token: '',
                };
                break;
        }

        const updateParams = {
            user_id: session.user_id,
            access_token: session.access_token,
            patient_id: patientId, // id của patient v1
            app: appId,
            name: patientFormData.name,
            surname: patientFormData.surname,
            sex: patientFormData.sex,
            birthdate: moment(patientFormData.birthdate).isValid() ? moment(patientFormData.birthdate).format('YYYY-MM-DD') : '',
            // tslint:disable-next-line: max-line-length
            birthyear: moment(patientFormData.birthdate).isValid() ? moment(patientFormData.birthdate).get('years') : Number(patientFormData.birthyear),
            cmnd: patientFormData.cmnd,
            mobile: patientFormData.mobile,
            email: patientFormData.email,
            dantoc_id: dataKeys.nationId,
            country_code: dataKeys.countryId,
            district_id: dataKeys.districtId,
            city_id: dataKeys.cityId,
            ward_id: dataKeys.wardId,
            address: patientFormData.address,
            relative_type_id: dataKeys.relationTypeId,
            relative_name: patientFormData.relative_name,
            relative_mobile: patientFormData.relative_mobile,
            relative_email: patientFormData.relative_email,
        };
        if (!isUpdateFull) {
            updateParams.name = findPatient.name;
            updateParams.surname = findPatient.surname;
            updateParams.sex = findPatient.sex;
            updateParams.birthdate = findPatient.birthdate;
            updateParams.birthyear = findPatient.birthyear;
            // updateParams.city_id = findPatient.city_id;
            // updateParams
        }
        console.log('updateParams', updateParams);
        try {
            const resultUpdateV1 = (await this.updatePatientNhiDong1(url, updateParams, partnerId).toPromise()).data;
            console.log(resultUpdateV1)
            const getErrorMessage = get(resultUpdateV1, 'error_message', '');
            if (!!getErrorMessage) {

                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: `error_updatePatientV1Failed`,
                    summary: 'Lỗi khi cập nhật xuống v1.',
                    nameParent: 'updatePatientNhiDong1',
                    params: {
                        url, updateParams, partnerId,
                    },
                    errorBody: resultUpdateV1,
                    response: resultUpdateV1,
                    message: `error_mesasge trong response`,
                });

                throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
            }
            return resultUpdateV1;
        } catch (error) {
            // this.clientSentry.instance().captureException(error);
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'updatePatientV1Failed',
                summary: 'Lỗi khi cập nhật xuống v1.',
                nameParent: 'updatePatientNhiDong1',
                params: {
                    url, updateParams, partnerId
                },
                errorBody: this.utilService.errorHandler(error),
                response: null,
                message: 'Catch:',
            });
            throw new HttpException('Thao tác không thành công. Vui lòng gọi 19002115 để được hỗ trợ.', HttpStatus.BAD_REQUEST);
        }
    }

    async syncPatientNhiDong1(partnerId: string, appId: string, userId: any, patientInfo: any, locale: string = 'vi'): Promise<any> {
        let data: any = {};
        const params = {
            partnerId,
            appId,
            ...userId,
            ...patientInfo,
        };
        /* tiến hành sync user neu chua co. */
        try {
            const { relation } = patientInfo;
            const obj: any = {};
            if (relation) {
                const { relative_name, relative_mobile, relative_type_id, relative_email } = relation;
                obj.relative_name = relative_name;
                obj.relative_mobile = relative_mobile;
                obj.relative_type_id = relative_type_id;
                obj.relative_email = relative_email;
            }
            if (!!partnerId) {
                const result = await this.insertPatient_Nhidong1(partnerId, appId, {
                    id: 0,
                    userMongoId: userId,
                }, {
                    ...patientInfo,
                    ...obj,
                });
                data = result;
                console.log('sync thong tin v1', result);
                console.log('patientFormData đồng bộ', patientInfo);
                const patientIdV1 = result.id || 0;
                const keyPatientV1 = this.utilService.patientIdV1Key(partnerId);
                await this.patientModel.findOneAndUpdate({ id: patientInfo.id }, { [keyPatientV1]: patientIdV1 }).exec();

                return {
                    patientIdV1
                };
            }
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'insertPatient_Nhidong1',
                summary: 'Insert Patient Info',
                nameParent: 'syncPatientNhiDong1',
                params,
                errorBody: this.utilService.errorHandler(error),
                response: data,
                message: error?.message || 'Đồng bộ hồ sơ không thành công',
            });
            // throw new HttpException('Đồng bộ hồ sơ không thành công', HttpStatus.BAD_REQUEST);
            const message = await this.globalSettingService.findByKeyAndRepoName('ERROR_SYNC_PATIENT', null, locale);
            throw new HttpException(message, HttpStatus.BAD_REQUEST)
        }
    }

    async insertPatient_Nhidong1(partnerId: string, appId: string, user: UserInfoVerifyTokenDTO, patientFormData: PatientFormMongoDTO, locale: string = 'vi'): Promise<any> {
        // console.log('chay vao insert')
        /* tìm lại user_id và access_token */
        const userMongo = await this.userModel.findById({ _id: user.userMongoId }, { username: true, medproId: true }).exec();

        await this.userService.getUserIdAndCreateSessionV1(appId, partnerId, userMongo);
        // console.log('userMongo', userMongo);
        const userV1 = await this.checkExistsUserByUsername(userMongo.username);
        // console.log('userV1', userV1);
        let session: any;
        switch (partnerId) {
            case 'nhidong1':
                session = await this.sessionService.checkExistsNhiDong1SessionByUserID(userV1.id);
                break;
            case 'dalieuhcm':
                session = await this.sessionService.checkExistsSkinSessionByUserID(userV1.id);
                break;
            case 'ctchhcm':
                session = await this.sessionService.checkExistsCTCHSessionByUserID(userV1.id);
                break;
            case 'thuduc':
                session = await this.sessionService.checkExistsThuDucSessionByUserID(userV1.id);
                break;
            case 'umc':
                session = await this.sessionService.checkExistsUMCSessionByUserID(userV1.id);
                break;
            default:
                session = {
                    user_id: 0,
                    access_token: '',
                };
                break;
        }

        /* tiến hành chuyển đổi */
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const urlTransform = `${baseUrl}/his-connector/api/convertPartnerData`;
        const findCountry = await this.countryModel.findOne({ code: patientFormData.country_code }).exec();
        const params: any = {
            fromPartnerId: 'medpro',
            toPartnerId: partnerId,
            countryId: '',
            cityId: `${patientFormData.city_id}`,
            districtId: `${patientFormData.district_id}`,
            wardId: `${patientFormData.ward_id}`,
            nationId: `${patientFormData.dantoc_id}`,
            relationTypeId: `${patientFormData.relative_type_id}`,
        };
        if (findCountry) {
            const objCountry = findCountry.toObject();
            params.countryId = `${objCountry.id}`;
        }
        let dataKeys: any = {};
        try {
            dataKeys = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'insertPatient_Nhidong1',
                summary: 'Insert Patient Info',
                nameParent: 'insertPatient',
                params: {
                    urlTransform,
                    params,
                    dataKeys,
                },
                errorBody: this.utilService.errorHandler(error),
                errorCode: 'insertPatient_TRANSOFMR_01',
                response: null,
                message: error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.',
            });
        }

        const urlGetPatients = this.oldUrl.NhiDong1InsertPatients();
        /* append thêm bv_id đối với trường hợp tạo hồ sơ tái khám của nhidong1 */
        const objTaiKham: any = {};
        const findGatientCode = await this.patientCodeModel.findOne({
            patientId: patientFormData?.id && !isEmpty(patientFormData?.id) ? patientFormData.id : 'NO_ID',
            partnerId,
            appId,
        }).exec();
        if (findGatientCode && findGatientCode?.patientCode) {
            objTaiKham.bv_id = findGatientCode.patientCode;
        }
        const insertParams = {
            user_id: session.user_id,
            access_token: session.access_token,
            app: appId,
            name: patientFormData.name,
            surname: patientFormData.surname,
            sex: patientFormData.sex,
            birthdate: moment(patientFormData.birthdate).isValid() ? moment(patientFormData.birthdate).format('YYYY-MM-DD') : '',
            // tslint:disable-next-line: max-line-length
            birthyear: moment(patientFormData.birthdate).isValid() ? moment(patientFormData.birthdate).get('years') : Number(patientFormData.birthyear),
            cmnd: patientFormData.cmnd,
            mobile: patientFormData.mobile,
            email: patientFormData.email,
            dantoc_id: dataKeys.nationId,
            country_code: dataKeys.countryId,
            district_id: dataKeys.districtId,
            ward_id: dataKeys.wardId,
            city_id: dataKeys.cityId,
            address: patientFormData.address,
            relative_type_id: dataKeys.relationTypeId,
            relative_name: patientFormData.relative_name,
            relative_mobile: patientFormData.relative_mobile,
            relative_email: patientFormData.relative_email,
            ...objTaiKham,
        };
        // console.log(insertParams);
        try {
            const resultDataInsertV1 = (await this.insertPatientNhiDong1(urlGetPatients, insertParams, partnerId).toPromise()).data;
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'insertPatient_Nhidong1',
                summary: 'Insert Patient Info',
                nameParent: 'insertPatient',
                params: {
                    urlGetPatients,
                    insertParams,
                    partnerId,
                },
                errorBody: null,
                errorCode: 'insertPatient_PROXY_01',
                response: resultDataInsertV1,
                message: 'Ket qua insert patient nhi dong1.',
            });
            // check user related to this user
            let userRelated: any = [];
            // if signInProvider of user have email then this have userV1 of gmail
            const signInProviderV1 = await this.signInProviderModel.find({ user: userMongo._id, email: { $ne: null } }).exec();
            await Promise.all(
                signInProviderV1.map(async element => {
                    const dataEmail = await this.checkExistsUserByUsername(element?.email);
                    // only social
                    if (isEmail(element?.email) && dataEmail?.id) {
                        userRelated = [...userRelated, dataEmail.id];
                    }
                }),
            );
            if (userRelated?.length) {
                const uniqueUserV1 = [...new Set([...userRelated])];
                this.logger.log(`UniqueUserV1 to insert: ${uniqueUserV1}`);
                uniqueUserV1.map(element => {
                    if (element !== userV1?.id) {
                        this.logger.debug(`Push event with userV1: ${element}`, 'Push Event- Insert Patient V1');
                        this.emitService.emit(INSERT_PATIENT_V1, { partnerId, appId, userV1: element, patientId: resultDataInsertV1?.id });
                    }
                });
            }
            return resultDataInsertV1;
            // console.log(resultData);
        } catch (error) {
            console.log('Error insertPatient_PROXY_01:::: ', error);
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'insertPatient_Nhidong1',
                summary: 'Insert Patient Info',
                nameParent: 'insertPatient',
                params: {
                    urlGetPatients,
                    insertParams,
                    partnerId,
                },
                errorBody: this.utilService.errorHandler(error),
                errorCode: 'insertPatient_PROXY_01',
                response: null,
                message: error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.',
            });
            let message = error?.response?.data?.message || error?.message || ErrorMessage.BAD_REQUEST;
            switch (message) {
                case 'Số lượng hồ sơ bệnh nhân đạt tối đa!':
                    message = await this.globalSettingService.findByKeyAndRepoName('PATIENT_V1_EXIST', null, locale);
                    break;
                default:
            }
            throw new HttpException(message, HttpStatus.BAD_REQUEST);
        }
    }

    getPatientByPhoneOrCMNDByHIS(partnerId: string, Phone: string = '', identifyId: string = ''): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/his-connector/api/patient/getByPatientDetail`;
        const params = {
            partnerId,
            Phone,
            identifyId,
        };
        this.clientSentry.instance().captureMessage(`find phone-cmnd ${JSON.stringify(params, null, 2)}`);
        return this.httpService.post(url, params);
    }

    getPatientsByCccdPartnerId(cccd: string, partnerId: string): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/his-connector/api/patient/getByPatientDetailByIdentify`;
        return this.httpService.post(url, {
            identifyId: cccd,
            partnerId,
        });
    }

    async testFindPhoneCmndHIS(partnerId: string, phone: string = '', cmnd: string = ''): Promise<any> {
        try {
            const data = (await this.getPatientByPhoneOrCMNDByHIS(partnerId, phone, cmnd).toPromise()).data;
            return data;
        } catch (error) {
            console.log(error);
        }

    }

    async insertPatientBHYT_Bk(appId: string, partnerId: string, patientInfoResult: IPatient, insuranceId: string): Promise<any> {
        /* Kiểm tra xem có gởi thông tin mã bảo hiểm yte hay ko */
        let dataBH: any = {};
        try {
            const birthday = moment(patientInfoResult.birthdate).isValid() ? moment(patientInfoResult.birthdate, 'YYYY-MM-DD').format('YYYY-MM-DD') : '';
            const params = {
                fullName: `${patientInfoResult.surname} ${patientInfoResult.name}`,
                insuranceId,
                birthday,
                partnerId,
            };
            const baseUrl = this.urlConfigService.getBookingTreeUrl();
            const url = `${baseUrl}/his-connector/api/insurance/getDate`;
            console.log('url bh', url);
            console.log('parms bh', params);
            dataBH = (await this.getInsuranceInfoHIS(url, params).toPromise()).data;
            console.log('result bh', dataBH);
            /* Tiếp tục kiểm tra  */
            const { insuranceToDate, validatedPartner, maDKBD: maDKBDHT, maThe } = dataBH;
            // if (!validatedPartner) {
            //     if (partnerId !== 'leloi') { // TODO: chỗ này cho thêm config
            //         throw new HttpException(
            //             'Nơi đăng ký khám bảo hiểm ban đầu của bạn không hợp lệ, Vui lòng kiểm tra lại thông tin.', HttpStatus.BAD_REQUEST);
            //     }
            // }
            /* tiếp tục kiểm tra */
            const objPatientCodeBH: any = {};

            const findMsbnData = (await this.getPatientExtraByInsuranceCodeHIS(partnerId, insuranceId).toPromise()).data;
            if (findMsbnData.length > 0) {
                const resultMsbnData: any = first(findMsbnData);
                objPatientCodeBH.patientCode = `${resultMsbnData.SoHS}`.trim();
            }
            console.log('objPatientCodeBH', objPatientCodeBH);
            const patientId = patientInfoResult.id;
            const insuranceCode = maThe;
            const findPatientCode = await this.patientCodeModel.findOne({ partnerId, patientId }).exec();
            /* cập nhật lại thông tin */
            if (!findPatientCode) { /* thêm mới thông tin */
                console.log('bh thêm mới patientCode');
                const info = new this.patientCodeModel({
                    id: `${partnerId}_${patientId}`,
                    createTime: moment().toDate(),
                    patientId,
                    insuranceCode,
                    insuranceExpiredDate: moment(insuranceToDate).toDate(),
                    partnerId,
                    appId,
                    /* patientCode */
                    ...objPatientCodeBH,
                    maDKBDHT,
                });
                await info.save();
            } else {
                console.log('bh cập nhật patientCode');
                const patientCodeObj = findPatientCode.toObject();
                if (Object.keys(objPatientCodeBH).length > 0) {
                    const getPatientCode = get(patientCodeObj, 'patientCode', '');
                    if (getPatientCode) {
                        if (patientCodeObj.patientCode !== getPatientCode) {
                            this.clientSentry.instance().captureException(`${partnerId}: patient-codes có _id: ${patientCodeObj._id} old: ${getPatientCode} new: ${patientCodeObj.patientCode}`);
                            await this.patientCodeModel.findByIdAndUpdate(
                                { _id: patientCodeObj._id },
                                { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...objPatientCodeBH },
                            ).exec();
                        } else {
                            await this.patientCodeModel.findByIdAndUpdate(
                                { _id: patientCodeObj._id },
                                { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate() },
                            ).exec();
                        }
                    } else {
                        await this.patientCodeModel.findByIdAndUpdate(
                            { _id: patientCodeObj._id },
                            { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate(), ...objPatientCodeBH },
                        ).exec();
                    }

                } else {
                    await this.patientCodeModel.findByIdAndUpdate(
                        { _id: patientCodeObj._id },
                        { insuranceCode, maDKBDHT, insuranceExpiredDate: moment(insuranceToDate).toDate() },
                    ).exec();
                }
            }

        } catch (error) {
            console.log(error);
            this.clientSentry.instance().captureException(error);
            return null;
        }
    }

    async insertPatientBHYT(appId: string, partnerId: string, patientInfoResult: IPatient, insuranceId: string): Promise<any> {
        /* Kiểm tra xem có gởi thông tin mã bảo hiểm yte hay ko */
        try {
            const patientId = patientInfoResult.id;
            const insuranceCode = insuranceId;
            const findPatientCode = await this.patientCodeModel.findOne({ partnerId, patientId }).exec();
            /* cập nhật lại thông tin */
            if (!findPatientCode) { /* thêm mới thông tin */
                const info = new this.patientCodeModel({
                    id: `${partnerId}_${patientId}`,
                    createTime: moment().toDate(),
                    patientId,
                    insuranceCode,
                    partnerId,
                    appId,
                });
                await info.save();
            } else {
                const patientCodeObj = findPatientCode.toObject();
                await this.patientCodeModel.findByIdAndUpdate(
                    { _id: patientCodeObj._id },
                    { insuranceCode },
                ).exec();
            }
        } catch (error) {
            console.log(error);
            this.clientSentry.instance().captureException(error);
            return null;
        }
    }

    async insertPatient(partnerId: string, appId: string, user: UserInfoVerifyTokenDTO, patientFormData: PatientFormMongoDTO,
                        cskhInfo?: CSKHTokenVerifyDTO, reExamQuery?: ReExamQueryDTO, locale?: string): Promise<any> {

        if (patientFormData.mobileLocaleIso === 'vi-VN') {
            patientFormData.mobile = patientFormData.mobile.replace(/^\+84/, '0');
        }

        if (patientFormData.relativeMobileLocaleIso === 'vi-VN' && patientFormData.relative_mobile) {
            patientFormData.relative_mobile = patientFormData.relative_mobile.replace(/^\+84/, '0');
        }

        let data: any = {};
        const params = {
            partnerId,
            appId,
            user,
            ...patientFormData,
        };
        let fullAddress = '';
        /* kiểm tra local xem đã trùng hay chưa */
        /* Lấy thông tin danh sách hồ sơ bệnh nhân ở local */
        const listPatientsCheck = await this.getAllPatientsByUserIdSimple(user.userMongoId);
        if (listPatientsCheck.length > 0) {
            const fullnameCheck = slugify.default(`${patientFormData.surname} ${patientFormData.name}`).toLowerCase();
            const realName = `${patientFormData.surname} ${patientFormData.name}`.trim().toLowerCase();
            const mappCheckList = listPatientsCheck.map(item => {
                return {
                    ...item.toObject(),
                    fullname: slugify.default(`${item.surname} ${item.name}`).toLowerCase(),
                };
            });
            const objCheck: any = {};
            if (moment(patientFormData.birthdate).isValid()) {
                objCheck.birthdate = moment(patientFormData.birthdate, 'YYYY-MM-DD').format('YYYY-MM-DD');
            } else {
                objCheck.birthyear = Number(patientFormData.birthyear);
            }
            const paramsSearch = {
                fullname: fullnameCheck,
                ...objCheck,
                sex: patientFormData.sex,
                city_id: patientFormData.city_id, /* thêm city_id nữa */
            };
            const findInfo = find<any>(mappCheckList, {
                ...paramsSearch,
            });
            if (typeof findInfo !== typeof undefined) {
                // luồng tái khám nhanh
                if (reExamQuery) {
                    return this.getPatientForReExams(findInfo.id);
                }

                const errorMessage = await this.translateService.getCommonErrorMessage(ErrorMessageKey.PATIENT_ALREADY_EXIST, locale, 'Hồ sơ này đã tồn tại.');
                // throw new HttpException('Hồ sơ này đã tồn tại.', HttpStatus.CONFLICT);
                if (`${findInfo.surname} ${findInfo.name}`.trim().toLowerCase() === realName) {
                    throw new HttpException(errorMessage, HttpStatus.CONFLICT);
                }
            }
        }

        //bvmat
        if (partnerId === 'bvmathcm') {
            // tìm bên HIS
            try {
                console.log('123');
                
                const data = await this.processSearchPatientByCccdPartner(appId, partnerId, user.userMongoId, {
                    mobile: patientFormData.mobile,
                    cmnd: patientFormData.cmnd,
                });

                console.log('1', data);
                
                /* tìm lại thông tin còn thiếu */
                const refObj: any = {};
                const findCountry2 = await this.countryModel.findOne({ code: patientFormData.country_code }).exec();
                if (findCountry2) {
                    refObj.country = findCountry2.toObject();
                }

                const findNation2 = await this.nationModel.findOne({ id: patientFormData.dantoc_id }).exec();
                if (findNation2) {
                    refObj.nation = findNation2.toObject();
                }

                const findCity2 = await this.cityModel.findOne({ id: patientFormData.city_id }).exec();
                if (findCity2) {
                    refObj.city = findCity2.toObject();
                }

                const findDistrict2 = await this.districtModel.findOne({ id: patientFormData.district_id }).exec();
                if (findDistrict2) {
                    refObj.district = findDistrict2.toObject();
                }

                const findWard2 = await this.wardModel.findOne({ id: patientFormData.ward_id }).exec();
                if (findWard2) {
                    refObj.ward = findWard2.toObject();
                }

                console.log('data cccd bvmat', data);
                console.log('patientForm cccd bvmat', {
                        ...patientFormData,
                        ...refObj,
                        force: true,
                    });
                

                return {
                    patientForm: {
                        ...patientFormData,
                        ...refObj,
                        force: true,
                    },
                    recommendedPatientList: data,
                    isRecommended: true,
                    message: 'Hệ thống tìm thấy thông tin hồ sơ cũ thuộc bệnh viện. Bạn có muốn chọn xác nhận lại thông tin.',
                };
            } catch (error) {
                console.log('error', JSON.stringify(error, null, 2))
                this.logger.error('Không có gợi ý phone or cmnd', error);
            }
        }

        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        const { isSearcheBeforeCreateNew = false, isSearchedPhoneCMNDBeforeCreateNew = false } = partnerConfig;
        const searchPatientExtraInfoDTO: SearchPatientExtraInfoMongoDTO = {
            birthYear: patientFormData.birthyear,
            cityId: patientFormData.city_id,
            firstName: patientFormData.name,
            surName: patientFormData.surname,
            gender: patientFormData.sex,
        };
        // console.log(isSearcheBeforeCreateNew);
        if (isSearcheBeforeCreateNew && (patientFormData.force === false || typeof patientFormData.force === typeof undefined)) {
            try {
                // console.log(searchPatientExtraInfoDTO);
                const recommendedPatientList =
                    await this.processSearchPatientByExtraInfo(appId, partnerId, user.userMongoId, searchPatientExtraInfoDTO, cskhInfo);
                if (recommendedPatientList?.length === 0) {
                    throw new Error(`Không có gợi ý`);
                }
                /* tìm lại thông tin còn thiếu */
                const refObj: any = {};
                const findCountry2 = await this.countryModel.findOne({ code: patientFormData.country_code }).exec();
                if (findCountry2) {
                    refObj.country = findCountry2.toObject();
                }

                const findNation2 = await this.nationModel.findOne({ id: patientFormData.dantoc_id }).exec();
                if (findNation2) {
                    refObj.nation = findNation2.toObject();
                }

                const findCity2 = await this.cityModel.findOne({ id: patientFormData.city_id }).exec();
                if (findCity2) {
                    refObj.city = findCity2.toObject();
                }

                const findDistrict2 = await this.districtModel.findOne({ id: patientFormData.district_id }).exec();
                if (findDistrict2) {
                    refObj.district = findDistrict2.toObject();
                }

                const findWard2 = await this.wardModel.findOne({ id: patientFormData.ward_id }).exec();
                if (findWard2) {
                    refObj.ward = findWard2.toObject();
                }

                const message = await this.globalSettingService.findByKeyAndRepoName('PATIENT_MESSAGE_ERROR_OLD', null, locale);
                return {
                    patientForm: {
                        ...patientFormData,
                        ...refObj,
                        force: true,
                    },
                    recommendedPatientList,
                    isRecommended: true,
                    // message: 'Hệ thống tìm thấy thông tin hồ sơ cũ thuộc bệnh viện. Bạn có muốn chọn xác nhận lại thông tin.',
                    message,
                };

            } catch (error) {
                const message = await this.globalSettingService.findByKeyAndRepoName('BAD_REQUEST_MESSAGE_ERROR', null, locale);
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'insertPatient_Nhidong1',
                    summary: 'Insert Patient Info',
                    nameParent: 'insertPatient',
                    params: {
                        condition: `if (isSearcheBeforeCreateNew && (patientFormData.force === false || typeof patientFormData.force === typeof undefined))`,
                        appId, partnerId, userMongoId: user.userMongoId, searchPatientExtraInfoDTO, cskhInfo,
                    },
                    errorBody: this.utilService.errorHandler(error),
                    errorCode: 'insertPatient_01',
                    response: data,
                    // message: error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.',
                    message: error?.message || message,
                });
                // console.log('Không có gợi ý');
            }
        }

        /* Kiểm tra trường hợp tìm kiếm bên HIS cho phone OR Cmnd */
        if (isSearchedPhoneCMNDBeforeCreateNew && (patientFormData.force === false || typeof patientFormData.force === typeof undefined)) {
            // tìm bên HIS
            try {
                const data = await this.processSearchPhoneCMNDPatientByExtraInfo(appId, partnerId, user.userMongoId, {
                    mobile: patientFormData.mobile,
                    cmnd: patientFormData.cmnd,
                });
                /* tìm lại thông tin còn thiếu */
                const refObj: any = {};
                const findCountry2 = await this.countryModel.findOne({ code: patientFormData.country_code }).exec();
                if (findCountry2) {
                    refObj.country = findCountry2.toObject();
                }

                const findNation2 = await this.nationModel.findOne({ id: patientFormData.dantoc_id }).exec();
                if (findNation2) {
                    refObj.nation = findNation2.toObject();
                }

                const findCity2 = await this.cityModel.findOne({ id: patientFormData.city_id }).exec();
                if (findCity2) {
                    refObj.city = findCity2.toObject();
                }

                const findDistrict2 = await this.districtModel.findOne({ id: patientFormData.district_id }).exec();
                if (findDistrict2) {
                    refObj.district = findDistrict2.toObject();
                }

                const findWard2 = await this.wardModel.findOne({ id: patientFormData.ward_id }).exec();
                if (findWard2) {
                    refObj.ward = findWard2.toObject();
                }

                const message = await this.globalSettingService.findByKeyAndRepoName('PATIENT_MESSAGE_ERROR_OLD', null, locale);
                return {
                    patientForm: {
                        ...patientFormData,
                        ...refObj,
                        force: true,
                    },
                    recommendedPatientList: data,
                    isRecommended: true,
                    // message: 'Hệ thống tìm thấy thông tin hồ sơ cũ thuộc bệnh viện. Bạn có muốn chọn xác nhận lại thông tin.',
                    message,
                };
            } catch (error) {
                const message = await this.globalSettingService.findByKeyAndRepoName('BAD_REQUEST_MESSAGE_ERROR', null, locale);
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'insertPatient_Nhidong1',
                    summary: 'Insert Patient Info',
                    nameParent: 'insertPatient',
                    params: {
                        condition: `if (isSearchedPhoneCMNDBeforeCreateNew && (patientFormData.force === false || typeof patientFormData.force === typeof undefined))`,
                        appId, partnerId, userMongoId: user.userMongoId, patientInfo: {
                            mobile: patientFormData.mobile,
                            cmnd: patientFormData.cmnd,
                        },
                    },
                    errorBody: this.utilService.errorHandler(error),
                    errorCode: 'insertPatient_02',
                    response: data,
                    // message: error?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.',
                    message: error?.message || message,
                });
            }

        }

        /* Kiểm tra xem có tìm kiếm trước bên HIS hay ko */
        try {
            const { relative_name, relative_email, relative_mobile, relative_type_id, ...patientInfo } = patientFormData;
            const replacerBirthYear = moment(patientInfo?.birthdate).isValid() ? moment(patientInfo.birthdate).year() : '';
            // const medproId = await this.patientSereice.generateUMCMedproID();
            const patientInsert = {
                ...patientInfo,
                birthdate: moment(patientInfo.birthdate).isValid() ? moment(patientInfo.birthdate).format('YYYY-MM-DD') : '',
                // code: medproId,
                sourceId: appId,
                partnerId,
                userId: user.userMongoId,
                relation: {
                    relative_name: '',
                    relative_mobile: '',
                    relativeMobileLocaleIso: '',
                    relative_type_id: '',
                    relative_email: '',
                },
                patientIdV1: 0, /* Nhi đồng 1 */
                patientIdV1DaLieu: 0, /* Da liễu */
                patientIdV1CTCH: 0, /* CTCH */
                patientIdV1ThuDuc: 0, /* Thủ Đức */
                patientIdV1UMC: 0, /* UMC */
                /* CSKH */
                cskhUserId: patientFormData?.cskhUserId || null,
                ...(replacerBirthYear && { birthyear: replacerBirthYear }),
            };
            if (!!patientInsert.profession_id && patientInsert.profession_id !== '0') {
                const findProfession = await this.professionModel.findOne({ id: patientInsert.profession_id }).exec();
                if (findProfession) {
                    const professionObj = findProfession.toObject();
                    patientInsert.profession = professionObj._id;
                }
            } else {
                patientInsert.profession_id = '';
            }
            /* tìm lại thông tin */
            const findCountry = await this.countryModel.findOne({ code: patientInsert.country_code }).exec();
            if (findCountry) {
                const countryObj = findCountry.toObject();
                patientInsert.country = countryObj._id;
                patientInsert.country_id = countryObj.id;
            }

            const findNation = await this.nationModel.findOne({ id: patientInsert.dantoc_id }).exec();
            if (findNation) {
                const nationObj = findNation.toObject();
                patientInsert.nation = nationObj._id;
                patientInsert.dantoc_id = nationObj.id;
            }
            if (!!patientInsert.address) {
                fullAddress = `${patientInsert.address}`.trim();
            }
            const findWard = await this.wardModel.findOne({ id: patientInsert.ward_id }).exec();
            if (findWard) {
                const wardObj = findWard.toObject();
                patientInsert.ward = wardObj._id;
                patientInsert.ward_id = wardObj.id;
                fullAddress = `${fullAddress}, ${wardObj.name}`;
            }
            const findDistrict = await this.districtModel.findOne({ id: patientInsert.district_id }).exec();
            if (findDistrict) {
                const districtObj = findDistrict.toObject();
                patientInsert.district = districtObj._id;
                patientInsert.district_id = districtObj.id;
                fullAddress = `${fullAddress}, ${districtObj.name}`;
            }
            const findCity = await this.cityModel.findOne({ id: patientInsert.city_id }).exec();
            if (findCity) {
                const cityObject = findCity.toObject();
                patientInsert.city = cityObject._id;
                patientInsert.city_id = cityObject.id;
                fullAddress = `${fullAddress}, ${cityObject.name}`;
            }

            if (`5ecb3b014ae1165edc747c5b` !== `${findCountry._id}`) {
                fullAddress = '';
            }

            const uuidv4 = uuid.v4();
            patientInsert.id = uuidv4.replace(/-/g, '');
            patientInsert.code = this.generateMedproID(6);
            /* kiểm tra xem có thông tin thân nhân hay ko */
            if (patientFormData.relative_type_id) {
                patientInsert.relation = {
                    relative_name: patientFormData.relative_name,
                    relative_email: patientFormData.relative_email,
                    relativeMobileLocaleIso: patientFormData.relativeMobileLocaleIso,
                    relative_mobile: patientFormData.relative_mobile,
                    relative_type_id: patientFormData.relative_type_id,
                };
            }
            /* kiểm tra xem appId vs partnerId */
            if (this.listAppId.has(appId)) {
                const newSet = this.utilService.oldHospitalSync();
                // check rule age for partner nhidong1
                let isAgeValid: boolean = true;
                if (partnerId === 'nhidong1') {
                    const { patientYearOldAccepted = null } = partnerConfig;
                    isAgeValid = this.checkOldAccepted(patientFormData, patientYearOldAccepted);
                }
                if (!!partnerId && newSet.has(partnerId) && isAgeValid) {
                    const dataPatientV1 = await this.insertPatient_Nhidong1(partnerId, appId, user, patientFormData, locale);
                    const getKey = this.utilService.patientIdV1Key(partnerId);
                    patientInsert[getKey] = dataPatientV1.id || 0;
                }
            }
            const patientInfoResult: IPatient = await this.insertPatientInfo(patientInsert);
            const trackingData : PatientTrackingData = {
                action: 'CREATE',
                dataAfter: patientInfoResult.toObject(),
                patient: patientInfoResult._id,
                userAction: user.userMongoId,
                userPatient: user.userMongoId,
            }

            this.emitService.emit(PATIENT_TRACKING_EVENT, trackingData);

            // Emit event khi tạo mới patient với CMND
            if (patientFormData.cmnd) {
                this.emitCmndUpdatedEvent(patientInfoResult.id, user.userMongoId, null, patientFormData.cmnd, 'create');
            }

            await this.insertPatientBHYT(appId, partnerId, patientInfoResult, patientFormData.insuranceId);

            /* TODO:  Cần tạo thêm event cho cskh khi tạo hộ bệnh nhân hay ko */
            /* ----------------TODO:  Cần tạo thêm event cho cskh khi tạo hộ bệnh nhân hay ko-------------- */
            /* kiểm tra xem có patientCode hay ko */
            if (partnerId === 'nhidong1' && this.listAppId.has(appId)) { /* dành cho tái khám */
                if (patientFormData.patientCode) {
                    const patientCodeInfo = new this.patientCodeModel({
                        id: `${partnerId}_${patientInfoResult.id}`,
                        createTime: moment().toDate(),
                        patientId: patientInfoResult.id,
                        patientCode: `${patientFormData.patientCode}`.trim(),
                        partnerId,
                        appId,
                    });
                    await patientCodeInfo.save();
                }
            } else {
                /* Kiểm tra xem có gửi mã số bảo hiểm lên hay không */
            }

            const patientObj = patientInfoResult.toObject();
            // tìm lại thông tin user Mongo
            const userMongo = await this.userModel.findById({ _id: user.userMongoId }).exec();
            userMongo.patients.push(patientObj._id);
            await userMongo.save();
            // const { _id, ...rest } = patientObj;

            if (patientFormData?.relationType && !isEmpty(patientFormData?.relationType) ) {
                const { relationType: id } = patientFormData;
                const relation = await this.relativeModel.findOne({ id }).exec();
                const newPatientRelation = new this.patientRelationModel({
                    user: userMongo._id,
                    // patientId: patientObj.id,
                    patient: patientObj._id,
                    relationTypeId: relation.id,
                    relationType: relation._id,
                });
                await newPatientRelation.save();
            }

            this.emitService.emit(HANDLE_PATIENT_SORT, {
                patientId: patientObj.id,
                createdPatient: patientObj.createdAt,
            });

            this.emitService.emit(TopicMailer.User.USER_CREATE_PATIENT_SUCCESS, {
                partnerId,
                appId,
                userId: userMongo._id,
                patientId: patientObj.id,
            });

            this.emitService.emit(TopicMailer.Patient.PATIENT_CREATE_SUCCESS, {
                partnerId,
                appId,
                userId: userMongo._id,
                patientId: patientObj.id,
            });

            const patientConstraintObj = await this.checkPatientPartner([patientObj], patientFormData.yearOldValidationDto, partnerId, appId);
            const { _id, ...rest } = first<any>(patientConstraintObj);

            return {
                ...rest,
                fullAddress,
            };

        } catch (error) {
            // const message = error?.message || ErrorMessage.BAD_REQUEST;
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'insertPatient_Nhidong1',
                summary: 'Insert Patient Info',
                nameParent: 'insertPatient',
                params,
                errorBody: this.utilService.errorHandler(error),
                errorCode: 'insertPatient_03',
                response: data,
                message: error?.message,
            });
            throw new HttpException({
                key: 'BAD_REQUEST_MESSAGE_ERROR',
                locale,
            }, HttpStatus.BAD_REQUEST);
        }
    }

    async insertBasicInfo(patientFormData: any, userId: string, partnerId: string, appId: string, platform: string, locale) {
        try {
            let session: any;
            if (['umc', 'nhidong1'].includes(partnerId)) {
                const userMongo = await this.userModel.findById({ _id: userId }, { username: true, medproId: true }).exec();
                await this.userService.getUserIdAndCreateSessionV1(appId, partnerId, userMongo);
                const userV1 = await this.checkExistsUserByUsername(userMongo.username);
                // await this.userService.forEachOldHospital(userV1.id); /* Tiến hành tạo session */
                switch (partnerId) {
                    case 'nhidong1':
                        session = await this.sessionService.checkExistsNhiDong1SessionByUserID(userV1.id);
                        break;
                    case 'dalieuhcm':
                        session = await this.sessionService.checkExistsSkinSessionByUserID(userV1.id);
                        break;
                    case 'ctchhcm':
                        session = await this.sessionService.checkExistsCTCHSessionByUserID(userV1.id);
                        break;
                    case 'thuduc':
                        session = await this.sessionService.checkExistsThuDucSessionByUserID(userV1.id);
                        break;
                    case 'umc':
                        session = await this.sessionService.checkExistsUMCSessionByUserID(userV1.id);
                        break;
                    default:
                        session = {
                            user_id: 0,
                            access_token: '',
                        };
                        break;
                }
            }

            //bvmat
            if (partnerId === 'bvmathcm' && patientFormData.cmnd) {
                // tìm bên HIS
                try {
                    console.log('123');
                    
                    const data = await this.processSearchPatientByCccdPartner(appId, partnerId, userId, {
                        mobile: patientFormData.mobile,
                        cmnd: patientFormData.cmnd,
                    });

                    console.log('1', data);
                    
                    /* tìm lại thông tin còn thiếu */
                    const refObj: any = {};
                    const findCountry2 = await this.countryModel.findOne({ code: patientFormData.country_code }).exec();
                    if (findCountry2) {
                        refObj.country = findCountry2.toObject();
                    }

                    const findNation2 = await this.nationModel.findOne({ id: patientFormData.dantoc_id }).exec();
                    if (findNation2) {
                        refObj.nation = findNation2.toObject();
                    }

                    const findCity2 = await this.cityModel.findOne({ id: patientFormData.city_id }).exec();
                    if (findCity2) {
                        refObj.city = findCity2.toObject();
                    }

                    const findDistrict2 = await this.districtModel.findOne({ id: patientFormData.district_id }).exec();
                    if (findDistrict2) {
                        refObj.district = findDistrict2.toObject();
                    }

                    const findWard2 = await this.wardModel.findOne({ id: patientFormData.ward_id }).exec();
                    if (findWard2) {
                        refObj.ward = findWard2.toObject();
                    }

                    console.log('data cccd bvmat', data);
                    console.log('patientForm cccd bvmat', {
                            ...patientFormData,
                            ...refObj,
                            force: true,
                        });
                    let constraintInfo = {
                        isValid: false,
                        color: '',
                        errors: [],
                    };

                    return {
                        patientForm: {
                            ...patientFormData,
                            ...refObj,
                            force: true,
                        },
                        constraintInfo,
                        recommendedPatientList: data,
                        isRecommended: true,
                        message: 'Hệ thống tìm thấy thông tin hồ sơ cũ thuộc bệnh viện. Bạn có muốn chọn xác nhận lại thông tin.',
                    };
                } catch (error) {
                    console.log('error', JSON.stringify(error, null, 2))
                    this.logger.error('Không có gợi ý phone or cmnd', error);
                }
            }

            const { data } = await this.httpService.post(`${this.urlConfigService.getProxyApiUrl}/patient/insert-basic-info`, {
                ...patientFormData,
                userId,
                session
            }, {
                headers: {
                    appid: appId || '',
                    partnerid: partnerId || '',
                    platform: platform || '',
                    locale: locale || '',
                    reponame: this.repoConfigService.getRepoName(),
                }
            }).toPromise();

            return data;
        } catch (err) {
            if (err.response) {
                throw new HttpException(err.response.data, err.response?.status);
            }

            console.log('err', err);
            throw new HttpException(ErrorMessage.BAD_REQUEST, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    async insertBasicInfoWhenCreateUser(patientFormData: any, userId: string, partnerId: string, appId: string, platform: string) {
        try {

            const { data } = await this.httpService.post(`${this.urlConfigService.getProxyApiUrl}/patient/user-create/insert-basic-info`, {
                ...patientFormData,
                userId,
            }, {
                headers: {
                    appid: appId || '',
                    partnerid: partnerId || '',
                    platform: platform || '',
                    reponame: this.repoConfigService.getRepoName(),
                }
            }).toPromise();

            return data;
        } catch (err) {
            console.log('error', JSON.stringify(err, null, 2))
            if (err.response) {
                throw new HttpException(err.response.data, err.response?.status);
            }

            throw new HttpException(ErrorMessage.BAD_REQUEST, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    generateMedproID(vLength = 6) {
        const dateFormat = moment().format('YYMMDD');
        const pattern = 'abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789';
        let pass = [];
        const alphaLength = pattern.length - 1;
        for (let i = 0; i < vLength; i++) {
            const n = this.utilService.getRandomInt(0, alphaLength);
            pass = [...pass, pattern[n]];
        }
        const uuidMP = `MP-${dateFormat}${pass.join('')}`.toUpperCase(); // dành cho UMC
        /* kiểm tra xem có trùng hay ko. nếu trùng thì tạo lại. */
        return uuidMP;
    }

    async getAllPatientsByUserIdSimple(userId: string): Promise<any> {
        const userWithPatients = await this.userModel.findById({ _id: userId }, { patients: true }).exec();
        if (userWithPatients.patients) {
            /* tìm danh sách patient */
            return this.patientModel
                .find({ _id: { $in: userWithPatients.patients } })
                .exec();
        }
        return [];
    }

    async getAllPatientsByUserIdSimpleWithPopulate(userId: string): Promise<any> {
        const userWithPatients = await this.userModel.findById({ _id: userId }, { patients: true }).exec();
        if (userWithPatients.patients) {
            /* tìm danh sách patient */
            return this.patientModel
                .find({ _id: { $in: userWithPatients.patients } },
                    { address: true, id: true, name: true, surname: true, birthdate: true, birthyear: true, mobile: true, code: true, sex: true })
                // .populate('profession')
                // .populate('country')
                // .populate('nation')
                .populate({ path: 'city', select: { name: true } })
                .populate({ path: 'district', select: { name: true } })
                .populate({ path: 'ward', select: { name: true } })
                .sort({ createdAt: 'desc' })
                .exec();
        }
        return [];
    }

    async getAllPatientsByUserIdSimpleGetIds(userId: string): Promise<any> {
        const userWithPatients = await this.userModel.findById({ _id: userId }, { patients: true }).exec();
        if (userWithPatients.patients) {
            /* tìm danh sách patient */
            return this.patientModel
                .find({ _id: { $in: userWithPatients.patients } }, { id: true })
                .exec();
        }
        return [];
    }

    async checkExistsUserByUsername(userName: any): Promise<any> {
        return this.pkhPatientKnex('user')
            .where('username', userName)
            .first();
    }

    async testTransformDataPatientV1(): Promise<any> {
        return true;
        // try {
        //     const urlGetPatients = this.oldUrl.NhiDong1GetPatients();
        //     const url = `${urlGetPatients}?user_id=7&access_token=ef3a6fb66d9d4ec73666f0213ec9de5c`;
        //     // const url = `${urlGetPatients}?user_id=${session.user_id}&access_token=${session.access_token}`;
        //     const resultData = (await this.getPatientsByUserIdNhiDong1(url).toPromise()).data;
        //     // return resultData;
        //     /* tiến hành transform data v1 */
        //     if (resultData.length > 0) {
        //         const baseUrl = this.urlConfigService.getBookingTreeUrl();
        //         const urlTransform = `${baseUrl}/his-connector/api/convertPartnerData`;
        //         let dataTransform = [];
        //         for await (const patient of resultData) {
        //             const { relative } = patient;
        //             const objRelative: any = {};
        //             const objRelationDisplay: any = {};
        //             if (typeof relative !== typeof undefined && typeof relative.nd1_relative_type_id !== typeof undefined) {
        //                 objRelative.relationTypeId = `${relative.nd1_relative_type_id}`;
        //                 objRelationDisplay.relation = {
        //                     relative_email: relative.email,
        //                     relative_mobile: relative.mobile,
        //                     relative_name: relative.name,
        //                 };
        //             }
        //             // console.log('chay tio day')
        //             const params = {
        //                 fromPartnerId: 'nhidong1',
        //                 toPartnerId: 'medpro',
        //                 countryId: `${patient.country_code}`,
        //                 cityId: `${patient.city_id}`,
        //                 districtId: `${patient.district_id}`,
        //                 wardId: `${patient.ward_id}`,
        //                 nationId: `${patient.dantoc_id}`,
        //                 ...objRelative,
        //             };
        //             const dataKeys = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
        //             /* tìm lại thông tin danh mục */
        //             console.log(dataKeys);
        //             const keys = Object.entries(dataKeys);
        //             const objDanhMuc: any = {};
        //             for await (const [key, value] of keys) {
        //                 if (value) {
        //                     switch (key) {
        //                         case 'countryId':
        //                             const findCountry = await this.countryModel.findOne({ id: value }).exec();
        //                             if (findCountry) {
        //                                 objDanhMuc.country_code = value;
        //                                 objDanhMuc.country = {
        //                                     ...findCountry.toObject(),
        //                                 };
        //                             }
        //                             break;
        //                         case 'cityId':
        //                             const findCity = await this.cityModel.findOne({ id: value }).exec();
        //                             if (findCity) {
        //                                 objDanhMuc.city_id = value;
        //                                 objDanhMuc.city = {
        //                                     ...findCity.toObject(),
        //                                 };
        //                             }
        //                             break;
        //                         case 'districtId':
        //                             const findDistrict = await this.districtModel.findOne({ id: value }).exec();
        //                             if (findDistrict) {
        //                                 objDanhMuc.district_id = value;
        //                                 objDanhMuc.district = {
        //                                     ...findDistrict.toObject(),
        //                                 };
        //                             }
        //                             break;
        //                         case 'wardId':
        //                             const findWard = await this.wardModel.findOne({ id: value }).exec();
        //                             if (findWard) {
        //                                 objDanhMuc.ward_id = value;
        //                                 objDanhMuc.ward = {
        //                                     ...findWard.toObject(),
        //                                 };
        //                             }
        //                             break;
        //                         case 'nationId':
        //                             const findNation = await this.nationModel.findOne({ id: value }).exec();
        //                             if (findNation) {
        //                                 objDanhMuc.dantoc_id = value;
        //                                 objDanhMuc.nation = {
        //                                     ...findNation.toObject(),
        //                                 };
        //                             }
        //                         case 'relationTypeId':
        //                             const findRelative = await this.relativeModel.findOne({ id: value }).exec();
        //                             if (findRelative) {
        //                                 objDanhMuc.relative_id = value;
        //                                 objDanhMuc.relative = {
        //                                     ...findRelative.toObject(),
        //                                 };
        //                             }
        //                             break;
        //                         default:
        //                             break;
        //                     }
        //                 } else {
        //                     objDanhMuc[key] = null;
        //                 }
        //             }
        //             /* Tiến hành gắn kết vào trong patient */
        //             dataTransform = [...dataTransform, {
        //                 address: patient.address,
        //                 birthdate: patient.birthdate,
        //                 birthyear: patient.birthyear,
        //                 cmnd: patient.cmnd,
        //                 code: patient.medpro_id,
        //                 email: patient.email,
        //                 fullname: `${patient.surname} ${patient.name}`,
        //                 isUpdateFull: false,
        //                 mobile: patient.mobile,
        //                 name: patient.name,
        //                 surname: patient.surname,
        //                 partnerId: 'nhidong1',
        //                 patientCode: patient.bv_id ? patient.bv_id : '',
        //                 sex: patient.sex,
        //                 sourceId: 'medpro',
        //                 userId: 'aaaaa',
        //                 ...objDanhMuc,
        //                 ...objRelationDisplay,
        //             }];
        //         }
        //         return dataTransform;
        //     } else {
        //         return resultData;
        //     }
        // } catch (error) {
        //     throw new HttpException('Không tìm thấy thông tin', HttpStatus.BAD_REQUEST);
        // }
    }

    async getAllPatientsNhiDong1(user: UserInfoVerifyTokenDTO): Promise<any> {
        /* tìm lại user_id và access_token */
        const userMongo = await this.userModel.findById({ _id: user.userMongoId }, { username: true, medproId: true }).exec();
        // console.log('userMongo', userMongo);
        const userV1 = await this.checkExistsUserByUsername(userMongo.username);
        // console.log('userV1', userV1);
        const session = await this.sessionService.checkExistsNhiDong1SessionByUserID(userV1.id);
        // console.log('session', session);
        /* gửi thông tin sang proxy */
        // const urlGetPatients = this.oldUrl.NhiDong1GetPatients();
        try {
            const urlGetPatients = this.oldUrl.NhiDong1GetPatients();
            // console.log('urlGetPatients', urlGetPatients);
            // const url = `${urlGetPatients}?user_id=7&access_token=ef3a6fb66d9d4ec73666f0213ec9de5c`;
            const url = `${urlGetPatients}?user_id=${session.user_id}&access_token=${session.access_token}`;
            // console.log('url', url);
            const resultData = (await this.getPatientsByUserIdNhiDong1(url).toPromise()).data;
            /* tiến hành transform data v1 */
            // console.log('resultData', resultData);
            if (resultData.length > 0) {
                const baseUrl = this.urlConfigService.getBookingTreeUrl();
                const urlTransform = `${baseUrl}/his-connector/api/convertPartnerData`;
                let dataTransform = [];
                for await (const patient of resultData) {
                    const { relative } = patient;
                    const objRelative: any = {};
                    const objRelationDisplay: any = {};
                    if (typeof relative !== typeof undefined && typeof relative.nd1_relative_type_id !== typeof undefined) {
                        objRelative.relationTypeId = `${relative.nd1_relative_type_id}`;
                        objRelationDisplay.relation = {
                            relative_email: relative.email,
                            relative_mobile: relative.mobile,
                            relative_name: relative.name,
                        };
                    }
                    // console.log('chay tio day')
                    const params = {
                        fromPartnerId: 'nhidong1',
                        toPartnerId: 'medpro',
                        countryId: `${patient.country_code}`,
                        cityId: `${patient.city_id}`,
                        districtId: `${patient.district_id}`,
                        wardId: `${patient.ward_id}`,
                        nationId: `${patient.dantoc_id}`,
                        ...objRelative,
                    };
                    // console.log('params', params);
                    const dataKeys = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
                    /* tìm lại thông tin danh mục */
                    // console.log(dataKeys);
                    const keys = Object.entries(dataKeys);
                    const objDanhMuc: any = {};
                    for await (const [key, value] of keys) {
                        if (value) {
                            switch (key) {
                                case 'countryId':
                                    const findCountry = await this.countryModel.findOne({ id: value }).exec();
                                    if (findCountry) {
                                        objDanhMuc.country_code = value;
                                        objDanhMuc.country = {
                                            ...findCountry.toObject(),
                                        };
                                    }
                                    break;
                                case 'cityId':
                                    const findCity = await this.cityModel.findOne({ id: value }).exec();
                                    if (findCity) {
                                        objDanhMuc.city_id = value;
                                        objDanhMuc.city = {
                                            ...findCity.toObject(),
                                        };
                                    }
                                    break;
                                case 'districtId':
                                    const findDistrict = await this.districtModel.findOne({ id: value }).exec();
                                    if (findDistrict) {
                                        objDanhMuc.district_id = value;
                                        objDanhMuc.district = {
                                            ...findDistrict.toObject(),
                                        };
                                    }
                                    break;
                                case 'wardId':
                                    const findWard = await this.wardModel.findOne({ id: value }).exec();
                                    if (findWard) {
                                        objDanhMuc.ward_id = value;
                                        objDanhMuc.ward = {
                                            ...findWard.toObject(),
                                        };
                                    }
                                    break;
                                case 'nationId':
                                    const findNation = await this.nationModel.findOne({ id: value }).exec();
                                    if (findNation) {
                                        objDanhMuc.dantoc_id = value;
                                        objDanhMuc.nation = {
                                            ...findNation.toObject(),
                                        };
                                    }
                                case 'relationTypeId':
                                    const findRelative = await this.relativeModel.findOne({ id: value }).exec();
                                    if (findRelative) {
                                        objDanhMuc.relative_id = value;
                                        objDanhMuc.relative = {
                                            ...findRelative.toObject(),
                                        };
                                    }
                                    break;
                                default:
                                    break;
                            }
                        } else {
                            objDanhMuc[key] = null;
                        }
                    }
                    /* Tiến hành gắn kết vào trong patient */
                    dataTransform = [...dataTransform, {
                        id: patient.id,
                        address: patient.address,
                        birthdate: patient.birthdate,
                        birthyear: patient.birthyear,
                        cmnd: patient.cmnd,
                        code: patient.medpro_id,
                        email: patient.email,
                        fullname: `${patient.surname} ${patient.name}`,
                        isUpdateFull: false,
                        mobile: patient.mobile,
                        name: patient.name,
                        surname: patient.surname,
                        partnerId: 'nhidong1',
                        patientCode: patient.bv_id ? patient.bv_id : '',
                        sex: patient.sex,
                        sourceId: 'medpro',
                        userId: 'aaaaa',
                        ...objDanhMuc,
                        ...objRelationDisplay,
                    }];
                }
                return dataTransform;
            } else {
                return resultData;
            }
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            throw new HttpException('Không tìm thấy thông tin', HttpStatus.BAD_REQUEST);
        }
    }

    transformDataPatientV1(url: string, data: any): Observable<AxiosResponse<any>> {
        // console.log(url);
        return this.httpService.post(url, {
            ...data,
        });
    }

    async findUserByRefId(userId: string): Promise<any> {
        return this.userModel.findById({
            _id: userId,
        }).exec();
    }

    async verifyCskhToken(cskhToken: string): Promise<CSKHTokenVerifyDTO> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyCSKHJwtOptions();
        let cskhInfo: CSKHTokenVerifyDTO;
        if (typeof cskhToken !== typeof undefined && `${cskhToken}`.trim() !== '') {
            try {
                const jwtVerify: any = jwt.verify(cskhToken, jwtOptions.secret);
                cskhInfo = jwtVerify;
                /* Kiểm tra lại thông tin cskhInfo */
                const { userIdPatient, cskhUserId } = cskhInfo;
                if (cskhUserId) {
                    const cskhUser = await this.findUserByRefId(cskhUserId);
                    if (!cskhUser) {
                        throw new HttpException('Vui lòng kiểm tra lại thông tin user cskh', HttpStatus.UNAUTHORIZED);
                    }
                }

                if (userIdPatient) {
                    const patientUser = await this.findUserByRefId(userIdPatient);
                    if (!patientUser) {
                        throw new HttpException('Vui lòng kiểm tra lại thông tin patient user', HttpStatus.UNAUTHORIZED);
                    }
                }

                return cskhInfo;
            } catch (error) {
                this.clientSentry.instance().captureException(error);
                const nameJWTError = !!error.name ? error.name : '';
                if (nameJWTError === 'TokenExpiredError') {
                    throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
                } else if (nameJWTError === 'JsonWebTokenError') {
                    throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
                } else if (nameJWTError === 'NotBeforeError') {
                    throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
                } else {
                    throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
                }
            }
        } else {
            return cskhInfo;
        }
    }

    async getPatientsByUserIdMedpro(appid: string, partnerId: string, user: UserInfoVerifyTokenDTO, formData?: YearOldValidationDto, locale: string = 'vi'): Promise<any> {

        const { patients, patientCodes } = await this.getAllPatientsByUserId(appid, partnerId, user);

        const [envCheckConstraintPatinet, appAcceptConstraint, repoShowPatientInvalid] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('ENV_CHECK_CONSTRAINS_PATIENT_DETAIL'),
            this.globalSettingService.findByKeyAndRepoName('APP_CHECK_CONSTRAINT_PATIENT'),
            this.globalSettingService.findByKeyAndRepoName('REPO_SHOW_PATIENT_INVALID'),
        ]);

        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId: partnerId || appid }, { tim_ho_so_mo_rong_btn: true }).exec();

        let data: any
        if (partnerId === 'bvmathcm') {
            data = patients.map(item => {
                // const json = item;
                const ngaysinh = moment(item.birthdate).isValid() ? moment(item.birthdate, 'YYYY-MM-DD').format('YYYY-MM-DD') : '';
                /* tìm lại trong patientCodes */
                let patientCode = item.code;
                let insuranceCode = item?.insuranceId || '';
                let isUpdateFull = true;
                let hasPatientCode = false;
                
                const findPatientCode = patientCodes.find(
                    c => c.partnerId === item.partnerId && c.patientId === item.id && !!c.patientCode
                );
                if (typeof findPatientCode !== typeof undefined) {
                    isUpdateFull = false;
                    const getpatientCode = get(findPatientCode, 'patientCode', null);
                    const getInsuranceCode = get(findPatientCode, 'insuranceCode', null);
                    if (getpatientCode) {
                        patientCode = findPatientCode.patientCode;
                        hasPatientCode = true
                    }
                    if (getInsuranceCode) {
                        insuranceCode = findPatientCode.insuranceCode;
                    }
                }
                return {
                    ...item,
                    birthdate: ngaysinh,
                    fullname: `${item.surname} ${item.name}`,
                    // mobile: isUpdateFull ? item.mobile : this.patientMongoService.secretMobile(item.mobile),
                    patientCode,
                    isUpdateFull: !!insuranceCode ? false : true,
                    insuranceCode,
                    requiredSearchExtraInfo: !hasPatientCode && partnerConfig?.tim_ho_so_mo_rong_btn
                };
            });
            const checkListTreeIds = ['TAIKHAMPHACO', 'TAIKHAM'];
            const checkListSubjectIds = ['bvmathcm_TKM'];
            if (checkListTreeIds.includes(formData.treeId) || checkListSubjectIds.includes(formData.subjectId) || formData.serviceId.endsWith('TK')) {
                data = data.filter(item => {
                    const findPatientCode = patientCodes.find(
                        c => c.patientId === item.id && !!c.patientCode
                    );
                    if (findPatientCode) {
                        const patientCode = get(findPatientCode, 'patientCode', null);
                        return !!patientCode;
                    }
                    return false;
                })
            }
        } else {
            data = patients.map(item => {
                // const json = item;
                const ngaysinh = moment(item.birthdate).isValid() ? moment(item.birthdate, 'YYYY-MM-DD').format('YYYY-MM-DD') : '';
                /* tìm lại trong patientCodes */
                let patientCode = item.code;
                let insuranceCode = item?.insuranceId || '';
                let isUpdateFull = true;
                const isExam = get(item, 'isExam', false);
    
                if (partnerId === 'choray' && !isExam) {
                    return {
                        ...item,
                        birthdate: ngaysinh,
                        fullname: `${item.surname} ${item.name}`,
                        // mobile: isUpdateFull ? item.mobile : this.patientMongoService.secretMobile(item.mobile),
                        patientCode,
                        isUpdateFull: !!insuranceCode ? false : true,
                        insuranceCode,
                    };
                } else {
                    let hasPatientCode = false;
                    const findPatientCode = find(patientCodes, { patientId: item.id });
                    if (typeof findPatientCode !== typeof undefined) {
                        isUpdateFull = false;
                        const getpatientCode = get(findPatientCode, 'patientCode', null);
                        const getInsuranceCode = get(findPatientCode, 'insuranceCode', null);
                        if (getpatientCode) {
                            patientCode = findPatientCode.patientCode;
                            hasPatientCode = true
                        }
                        if (getInsuranceCode) {
                            insuranceCode = findPatientCode.insuranceCode;
                        }
                    }
                    return {
                        ...item,
                        birthdate: ngaysinh,
                        fullname: `${item.surname} ${item.name}`,
                        // mobile: isUpdateFull ? item.mobile : this.patientMongoService.secretMobile(item.mobile),
                        patientCode,
                        isUpdateFull: !!insuranceCode ? false : true,
                        insuranceCode,
                        requiredSearchExtraInfo: !hasPatientCode && partnerConfig?.tim_ho_so_mo_rong_btn
                    };
                }
    
            });
        }

        // let data = patients.map(item => {
        //     // const json = item;
        //     const ngaysinh = moment(item.birthdate).isValid() ? moment(item.birthdate, 'YYYY-MM-DD').format('YYYY-MM-DD') : '';
        //     /* tìm lại trong patientCodes */
        //     let patientCode = item.code;
        //     let insuranceCode = '';
        //     let isUpdateFull = true;
        //     const isExam = get(item, 'isExam', false);

        //     if (partnerId === 'choray' && !isExam) {
        //         return {
        //             ...item,
        //             birthdate: ngaysinh,
        //             fullname: `${item.surname} ${item.name}`,
        //             // mobile: isUpdateFull ? item.mobile : this.patientMongoService.secretMobile(item.mobile),
        //             patientCode,
        //             isUpdateFull: !!insuranceCode ? false : true,
        //             insuranceCode,
        //         };
        //     } else {
        //         let hasPatientCode = false;
        //         const findPatientCode = find(patientCodes, { patientId: item.id });
        //         if (typeof findPatientCode !== typeof undefined) {
        //             isUpdateFull = false;
        //             const getpatientCode = get(findPatientCode, 'patientCode', null);
        //             const getInsuranceCode = get(findPatientCode, 'insuranceCode', null);
        //             if (getpatientCode) {
        //                 patientCode = findPatientCode.patientCode;
        //                 hasPatientCode = true
        //             }
        //             if (getInsuranceCode) {
        //                 insuranceCode = findPatientCode.insuranceCode;
        //             }
        //         }
        //         return {
        //             ...item,
        //             birthdate: ngaysinh,
        //             fullname: `${item.surname} ${item.name}`,
        //             // mobile: isUpdateFull ? item.mobile : this.patientMongoService.secretMobile(item.mobile),
        //             patientCode,
        //             isUpdateFull: !!insuranceCode ? false : true,
        //             insuranceCode,
        //             requiredSearchExtraInfo: !hasPatientCode && partnerConfig?.tim_ho_so_mo_rong_btn
        //         };
        //     }

        // });

        if (formData?.subjectId || formData?.serviceId) {
            data = await this.checkPatientPartner(data, formData, partnerId, appid, locale);
        }
        // bổ sung patient relation cho patients

        const setAppAcceptConstraint = new Set(appAcceptConstraint.split(','));
        const setRepoShowPatientInvalid = new Set(repoShowPatientInvalid.split(','));

        if (appid === 'medpro' && partnerId === 'choray') {
            data = await this.checkConstrainsPatientDetail(data, { partnerid: partnerId, appid, locale });
        } else if (envCheckConstraintPatinet === 'ON' && setAppAcceptConstraint.has(appid) && setRepoShowPatientInvalid.has(this.repoName)) {
            data = await this.checkConstrainsPatientDetail(data, { partnerid: partnerId, appid, locale });
        }

        data = await this.checkAgePatientAcceptForPartner(data, partnerId);

        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        data = data.map(patient => {
            return {
                ...patient,
                birthdate: moment(patient?.birthdate).isValid() ? moment(patient?.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
                secretPatientId: jwt.sign({ patientId: patient.id }, jwtOptions.secret, jwtOptions.signOptions),
            };
        });

        // data = data.map(item => {
        //     return {
        //         ...item,
        //         birthdate: moment(item?.birthdate).isValid() ? moment(item?.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
        //     };
        // });
        // data = this.sortPatient(data);

        return data;
    }

    async getPatientsByUserIdMedproOptimize(appid: string, partnerId: string, user: UserInfoVerifyTokenDTO, formData?: YearOldValidationDto, locale: string = 'vi',): Promise<any> {

        const StartGetListPatient = moment().toDate();

        const { patients, patientCodes } = await this.getAllPatientsByUserId(appid, partnerId, user);

        const [envCheckConstraintPatinet, appAcceptConstraint, repoShowPatientInvalid] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('ENV_CHECK_CONSTRAINS_PATIENT_DETAIL'),
            this.globalSettingService.findByKeyAndRepoName('APP_CHECK_CONSTRAINT_PATIENT'),
            this.globalSettingService.findByKeyAndRepoName('REPO_SHOW_PATIENT_INVALID'),
        ]);

        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).lean().exec();

        const partner = await this.hospitalModel.findOne({partnerId}).exec();

        let data = patients.map(item => {
            // const json = item;
            const ngaysinh = moment(item.birthdate).isValid() ? moment(item.birthdate, 'YYYY-MM-DD').format('YYYY-MM-DD') : '';
            /* tìm lại trong patientCodes */
            let patientCode = item.code;
            let insuranceCode = '';
            let isUpdateFull = true;
            const isExam = get(item, 'isExam', false);

            if (partnerId === 'choray' && !isExam) {
                return {
                    ...item,
                    birthdate: ngaysinh,
                    fullname: `${item.surname} ${item.name}`,
                    // mobile: isUpdateFull ? item.mobile : this.patientMongoService.secretMobile(item.mobile),
                    patientCode,
                    isUpdateFull: !!insuranceCode ? false : true,
                    insuranceCode,
                };
            } else {
                const findPatientCode = find(patientCodes, { patientId: item.id });
                if (typeof findPatientCode !== typeof undefined) {
                    isUpdateFull = false;
                    const getpatientCode = get(findPatientCode, 'patientCode', null);
                    const getInsuranceCode = get(findPatientCode, 'insuranceCode', null);
                    if (getpatientCode) {
                        patientCode = findPatientCode.patientCode;
                    }
                    if (getInsuranceCode) {
                        insuranceCode = findPatientCode.insuranceCode;
                    }
                }
                return {
                    ...item,
                    birthdate: ngaysinh,
                    fullname: `${item.surname} ${item.name}`,
                    // mobile: isUpdateFull ? item.mobile : this.patientMongoService.secretMobile(item.mobile),
                    patientCode,
                    isUpdateFull: !!insuranceCode ? false : true,
                    insuranceCode,
                };
            }

        });

        if (formData?.subjectId || formData?.serviceId) {
            data = await this.checkPatientPartner(data, formData, partnerId, locale);
        }
        // bổ sung patient relation cho patients

        data = await this.getRelationPatient(data);

        const setAppAcceptConstraint = new Set(appAcceptConstraint.split(','));
        const setRepoShowPatientInvalid = new Set(repoShowPatientInvalid.split(','));
        if (envCheckConstraintPatinet === 'ON' && setAppAcceptConstraint.has(appid) && setRepoShowPatientInvalid.has(this.repoName)) {
            data = await this.checkConstrainsPatientDetail(data, {
                locale: locale,
                partnerid: partnerId,
                appid: appid,
            });
        }

        data = await this.checkAgePatientAcceptForPartner(data, partnerId);

        // rule tuổi đặt khám
        if (partnerConfig?.yearOldAccepted) {
            const { conditon, patientYear, warningMessage } = partnerConfig.yearOldAccepted;

            const mapDatas = await Promise.all(data.map( async patient => {
                const checkAgePatient = this.checkOldAccepted(patient, patientYear);
                // const checkAgePatient = conditon === 'lt' ?
                // Number(moment().format('YYYY')) - patient.birthyear < +patientYear : // < tuổi qui định
                // Number(moment().format('YYYY')) - patient.birthyear > +patientYear; // > tuổi qui định
                const handleMessageWarning = warningMessage
                    .replace('{PARTNER}', partner.name)
                    .replace('{CONDITION}', conditon === 'lt' ? 'không đủ tuổi' : 'đã quá tuổi') || '';
                return {
                    ...patient,
                    birthdate: moment(patient.birthdate).isValid() ? moment(patient.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
                    ...(checkAgePatient && { warningMessage: handleMessageWarning }),
                };
            }));
            return mapDatas;
        }

        const partnerBranchUmc = await this.globalSettingService.findByKeyAndRepoName('PARTNER_BRANCH_UMC_VALID_AGE');
        const setPartnerBranchUmc = new Set(partnerBranchUmc ? partnerBranchUmc.split(',') : []);
        const [
            subjectAgeLimits, // subject check age umc luồng chính cho khoa nhi và lão
            subjectCheckAgeLimitAnother, // subject umc cho các case khác
            // partner,
            subjectNhiGroupUmc, // subject config để nhận biết lão khoa khi các umc có nhiều chi nhánh subject id khác nhau
            subjectLkGroupUmc,
            warningMessagePatient, // subject config để nhận biết lão khoa khi các umc có nhiều chi nhánh subject id khác nhau
        ] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('UMC_AGE_LIMIT_WITH_EXAM'),
            this.globalSettingService.findByKeyAndRepoName('UMC_AGE_LIMIT_ANOTHER_EXAM'),
            // this.hospitalModel.findOne({ partnerId }),
            this.globalSettingService.findByKeyAndRepoName('SUBJECT_NHI_GROUP_UMC'),
            this.globalSettingService.findByKeyAndRepoName('SUBJECT_LAO_GROUP_UMC'),
            this.globalSettingService.findByKeyAndRepoName('WARNING_ADDRESS_PATIENT')
        ]);
        const subjectAgeLimitObjs = JSON.parse(subjectAgeLimits);
        const ignoreSubject = subjectAgeLimitObjs.find(subject => subject.subjectId === formData?.subjectId);
        if (!!formData?.subjectId && setPartnerBranchUmc.has(partnerId) && ignoreSubject?.condition !== 'ignore') {
            const subjectCheckAgeLimitAnotherObjs = JSON.parse(subjectCheckAgeLimitAnother);
            const checkSubjectCheckAgeLimitAnotherObj = subjectCheckAgeLimitAnotherObjs.find(subject => formData?.subjectId === subject.subjectId);

            const setSubjectNhiGroupUmc = new Set(subjectNhiGroupUmc.split(',') || []);
            const setSubjectLaoGroupUmc = new Set(subjectLkGroupUmc.split(',') || []);
            const subjectNd = subjectAgeLimitObjs.find(sub => setSubjectNhiGroupUmc.has(sub.subjectId));
            const subjectLk = subjectAgeLimitObjs.find(sub => setSubjectLaoGroupUmc.has(sub.subjectId));
            // const subjectNd = subjectAgeLimitObjs.find(sub => sub.subjectId === 'umc_ND');
            // const subjectLk = subjectAgeLimitObjs.find(sub => sub.subjectId === 'umc_PW');

            const mapDatas = await Promise.all(
                data.map(async patient => {
                    let newPatient = patient;
                    patient.birthyear = moment(patient?.birthdate).isValid()
                    ? moment(patient?.birthdate).get('years')
                    : Number(patient?.birthyear);
                    if (!checkSubjectCheckAgeLimitAnotherObj) {
                        if (subjectNd.subjectId === formData.subjectId) {
                            // truong hợp 16 <= ho so < 60
                            const { subjectId, patientYear, warningMessage } = subjectNd;
                            const subject = await this.subjectModel.findOne({ id: subjectId }).exec();
                            const checkAgeLt = Number(moment().format('YYYY')) - patient.birthyear >= +patientYear;
                            const handleWarningMessage = warningMessage.replace('{PARTNER}', partner.name).replace('{SUBJECT}', subject.name);
                            newPatient = {
                                ...patient,
                                ...(checkAgeLt && { warningMessage: handleWarningMessage }),
                            };
                        } else if (subjectLk.subjectId === formData.subjectId) {
                            // truong hợp 16 <= ho so < 60
                            const { subjectId, patientYear, warningMessage } = subjectLk;
                            const checkAgeGt = Number(moment().format('YYYY')) - patient.birthyear < +patientYear;
                            const subject = await this.subjectModel.findOne({ id: subjectId }).exec();
                            const handleWarningMessage = warningMessage.replace('{PARTNER}', partner.name).replace('{SUBJECT}', subject.name);
                            newPatient = {
                                ...patient,
                                ...(checkAgeGt && { warningMessage: handleWarningMessage }),
                            };
                        }
                    } else {
                        // nhỏ hơn qui định = true
                        const { condition, patientYear, warningMessage, subjectId } = checkSubjectCheckAgeLimitAnotherObj;
                        const checkAgeLessConfig = await this.checkOldAccepted(patient, patientYear);
                        const subject = await this.subjectModel.findOne({ id: subjectId }).exec();
                        const handleMessage = warningMessage.replace('{PARTNER}', partner.name).replace('{SUBJECT}', subject.name);
                        if (condition === 'lt') {
                            newPatient = {
                                ...patient,
                                ...(
                                    checkAgeLessConfig === true &&
                                    { warningMessage: handleMessage }
                                ),
                            };
                        } else if (condition === 'gt' && checkAgeLessConfig === false) {
                            newPatient = {
                                ...patient,
                                ...(
                                    checkAgeLessConfig === false &&
                                    { warningMessage: handleMessage  }
                                ),
                            };
                            throw new HttpException(handleMessage, HttpStatus.BAD_REQUEST);
                        }
                    }

                    return {
                        ...newPatient,
                        birthdate: moment(patient.birthdate).isValid() ? moment(patient.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
                    };
                }),
            );
            return mapDatas;
        }

        // rule tuổi
        const partnerHaveAgeConfig = await this.getPartnerConfigAgeLimit(partnerId);
        if (formData?.version === 2 && partnerHaveAgeConfig) {
            const { patientYearOldAccepted } = partnerConfig;
            // const warningMessageConfig = await this.globalSettingModel.findOne({ key: this.WARNING_MESSAGE }).exec();
            // const partner = await this.hospitalModel.findOne({ partnerId }).exec();
            // const warningMessage = warningMessageConfig?.value.replace('{PARTNER}', partner.name) || '';
            const warningMessageConfig = await this.globalSettingService.findByKeyAndRepoName(this.WARNING_MESSAGE);
            // const partner = await this.hospitalModel.findOne({ partnerId }).exec();
            const warningMessage = warningMessageConfig.replace('{PARTNER}', partner.name) || '';
            return data.map(item => {
                const checkAge = this.checkOldAccepted(item, patientYearOldAccepted);
                return {
                    ...item,
                    ...(!checkAge && { warningMessage }),
                    birthdate: moment(item.birthdate).isValid() ? moment(item.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
                };
            });
        }

        // rule dia chi

        data = data.map(patient => {
            if (!patient.address) {
                return {
                    ...patient,
                    birthdate: moment(patient?.birthdate).isValid() ? moment(patient?.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
                    ...(warningMessagePatient && { warningMessage: warningMessagePatient }),
                };
            } else {
                return {
                    ...patient,
                    birthdate: moment(patient?.birthdate).isValid() ? moment(patient?.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
                };
            }
        });

        // data = data.map(item => {
        //     return {
        //         ...item,
        //         birthdate: moment(item?.birthdate).isValid() ? moment(item?.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
        //     };
        // });
        // data = this.sortPatient(data);

        data = await this.translatePatient(data, locale);

        const EndGetListPatient = moment().toDate();
        const diff = moment(EndGetListPatient).diff(StartGetListPatient, 'milliseconds');
        this.emitService.emit(LOG_SERVICE_EVENT, {
            name: 'getPatientList',
            summary: `REPO: ${this.repoName}: Thời gian lấy ds hồ sơ: ${diff}`,
            nameParent: 'getPatientsByUserIdMedpro_Optimize',
            params: {
                appid, partnerId, user, formData, diff,
            },
            errorBody: null,
            response: {},
            message: '',
        });
        return data;
    }

    async checkAgePatientAcceptForPartner(patients: any, partnerId: string): Promise<any> {
        const baseUrl = await this.getBookingRuleUrl();
        const api = `${baseUrl}/booking-rules/patient/age-accept`;

        const result = await this.client.post(api, {
            patientIds: patients.map(patient => patient?.id),
        }, {
            partnerid: partnerId,
        });

        if (result.length === 0) {
            return patients;
        }

        const groupRulePatient = groupBy(result, 'id');
        return patients.map(patient => {
            const rulePatient = first<any>(groupRulePatient[patient?.id])
            return {
                ...patient,
                ...(rulePatient?.warningMessage && { warningMessage: rulePatient?.warningMessage })
            }
        })
    }

    checkBirthDate(birthdate: string, ageLimit: number): boolean {
        return Math.floor(moment().diff(moment(new Date(birthdate)), 'years', true)) < +ageLimit;
    }

    checkBirthYear(birthyear: number, ageLimit: number): boolean {
        return Number(moment().format('YYYY')) - birthyear < +ageLimit;
    }

    checkOldAccepted(patient: any, ageLimit: number = null): boolean {
        // get config
        if (!ageLimit) {
            return true;
        }
        return moment(new Date(patient?.birthdate)).isValid()
            ? this.checkBirthDate(patient.birthdate, +ageLimit)
            : this.checkBirthYear(patient?.birthyear, +ageLimit);
    }

    async getPartnerConfigAgeLimit(partnerId: string): Promise<any> {
        try {
            // const ageLimitConfig = await this.globalSettingModel.findOne({ key: this.AGE_LIMIT_CONFIG }).exec();
            // const partnerList = ageLimitConfig.value.split(',');
            const ageLimitConfig = await this.globalSettingService.findByKeyAndRepoName(this.AGE_LIMIT_CONFIG);
            const partnerList = ageLimitConfig.split(',');
            const partnerExists = partnerList.find(item => item === partnerId);
            if (!partnerExists) {
                return '';
            }
            return true;
        } catch (error) {
            this.logger.error(`Error when exec getPartnerConfigAgeLimit for partnerId: ${partnerId}. Error: ${error.message}`);
            throw error;
        }
    }

    getFullAddress(patient: IPatient): string {
        let fullAddress = '';
        if (!!patient.address) {
            fullAddress = `${patient.address}`.trim();
        }
        const getCountryObjId = get(patient, 'country._id', '');
        if (`5ecb3b014ae1165edc747c5b` !== `${getCountryObjId}`) {
           return fullAddress;
        }
        const getWardName = get(patient, 'ward.name', '');
        if (getWardName) {
            fullAddress = fullAddress ? `${fullAddress}, ${getWardName}` : getWardName;
        }
        const getDistrict = get(patient, 'district.name', '');
        if (getDistrict) {
            fullAddress = fullAddress ? `${fullAddress}, ${getDistrict}` : getDistrict;
        }
        const getCity = get(patient, 'city.name', '');
        if (getCity) {
            fullAddress = fullAddress ? `${fullAddress}, ${getCity}` : getCity;
        }

        return fullAddress;
    }

    async getAllPatientsByUserId(appid: string, partnerId: string, user: UserInfoVerifyTokenDTO): Promise<any> {

        const trackingLogs:any = {};
        const StartuserWithPatients = moment().toDate();
        const userWithPatients = await this.userModel.findById({ _id: user.userMongoId }, { patients: true }).exec();
        const EnduserWithPatients = moment().toDate();
        trackingLogs.userWithPatients = `diff: ${moment(EnduserWithPatients).diff(StartuserWithPatients, 'milliseconds')} ms`;
        if (userWithPatients.patients) {
            /* tìm danh sách patient */
            const Startpatients = moment().toDate();
            const patients = await this.patientModel
                .find({ _id: { $in: userWithPatients.patients } })
                .populate({ path: 'profession', select: { name: true, id: true } })
                .populate({ path: 'country', select: { name: true } })
                .populate({ path: 'nation', select: { name: true } })
                .populate({ path: 'city', select: { name: true } })
                .populate({ path: 'district', select: { name: true } })
                .populate({ path: 'ward', select: { name: true } })
                // .populate('patientSort')
                .sort({createdAt: -1})
                .exec();
            const Endpatients = moment().toDate();
            trackingLogs.patients = `diff: ${moment(Endpatients).diff(Startpatients, 'milliseconds')} ms`;
            /* lấy thông tin relative */
            let listPatients = [];
            const StartTransformPatient = moment().toDate();
            for await (const patient of patients) {
                const boj = patient.toObject();
                const { relation } = boj;
                if (relation.relative_type_id) {
                    const findRelative = await this.relativeModel.findOne({ id: relation.relative_type_id }).exec();
                    if (findRelative) {
                        boj.relative = { ...findRelative.toObject() };
                    }
                }
                /* lấy thông tin fullAddress */
                const fullAddress = this.getFullAddress(patient);
                boj.fullAddress = fullAddress;
                listPatients = [...listPatients, boj];
            }
            const EndTransformPatient = moment().toDate();
            trackingLogs.transformPatientWithRelative = `diff: ${moment(EndTransformPatient).diff(StartTransformPatient, 'milliseconds')} ms`;
            const mapPatients = map(listPatients, 'id');

            const StartPatientCodes = moment().toDate();
            const patientCodes = await this.getInsuranCodeByPatientId(mapPatients, partnerId);
            const EndPatientCodes = moment().toDate();
            trackingLogs.patientCodes = `diff: ${moment(EndPatientCodes).diff(StartPatientCodes, 'milliseconds')} ms`;

            return {
                patients: listPatients,
                patientCodes,
                trackingLogs,
            };
        }
        return [];
    }

    async getAllPatientsByUserIdForHospitalFee(appid: string, partnerId: string, user: UserInfoVerifyTokenDTO): Promise<any> {
        const userWithPatients = await this.userModel.findById({ _id: user.userMongoId }, { patients: true }).exec();
        if (userWithPatients.patients) {
            /* tìm danh sách patient */
            const patients = await this.patientModel
                .find({ _id: { $in: userWithPatients.patients } })
                .exec();
            /* lấy thông tin relative */
            const listPatients = patients.map(item => item.toObject());
            const mapPatients = map(listPatients, 'id');
            const patientCodes = await this.patientCodeModel.find({ partnerId, patientId: { $in: mapPatients } }).exec();
            return listPatients.map(item => {
                let patientCode = '';
                if (appid !== 'medpro') {
                    const findPatientCode = find(patientCodes, { patientId: item.id });
                    if (typeof findPatientCode !== typeof undefined) {
                        const getpatientCode = get(findPatientCode, 'patientCode', null);
                        if (getpatientCode) {
                            patientCode = findPatientCode.patientCode;
                        }
                    }
                }
                return {
                    ...item,
                    patientCode,
                };
            }).filter(item => item.patientCode);
        }
        return [];
    }

    async getListPatient(userid: string): Promise<any> {
        const userWithPatients = await this.userModel.findById({ _id: userid }, { patients: true }).exec();
        if (userWithPatients.patients) {
            /* tìm danh sách patient */
            const patients = await this.patientModel
                .find({ _id: { $in: userWithPatients.patients } })
                .populate('profession')
                .populate('country')
                .populate('nation')
                .populate('city')
                .populate('district')
                .populate('ward')
                .exec();
            return patients;
        }
        return [];
    }

    async insertPatientInfo(patientInsert): Promise<any> {
        const patientModel = new this.patientModel(patientInsert);
        return await patientModel.save();
    }

    async updatePatientInfo(id: string, patientInfo: any): Promise<any> {
        const { id: idHehe, ...rest } = patientInfo;
        return this.patientModel.findByIdAndUpdate({ _id: id }, { ...rest }, { new: true });
    }

    // async getPatientInfo(patientId: string): Promise<any> {
    //     const info = await this.patientModel.findById({ _id: patientId })
    //         .populate('profession')
    //         .populate('country')
    //         .populate('nation')
    //         .populate('city')
    //         .populate('district')
    //         .populate('ward')
    //         .exec();
    //     const json = info.toJSON();
    //     return {
    //         ...json,
    //         birthDate: json.birthdate,
    //         birthYear: json.birthyear,
    //         fullname: `${json.surname} ${json.name}`,
    //     };
    // }

    async getPatientByREf(refId: string): Promise<any> {
        return this.patientModel.findById({ _id: refId }).exec();
    }

    async getPatientInfoById(patientId: string, partnerId: string, appid: string, yearOldValidationDto?: YearOldValidationDto): Promise<any> {
        const info = await this.patientModel.findById({ _id: patientId })
            .populate('profession')
            .populate('country')
            .populate('nation')
            .populate('city')
            .populate('district')
            .populate('ward')
            .exec();
        let json = info.toObject();
        const fullAddress = this.getFullAddress(info);
        /* lấy thông tin thân nhân */
        const { relation } = json;
        if (relation.relative_type_id) {
            const findRelative = await this.relativeModel.findOne({ id: relation.relative_type_id }).exec();
            if (findRelative) {
                json.relative = { ...findRelative.toObject() };
            }
        }
        const ngaysinh = moment(json.birthdate).isValid() ? moment(json.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '';

        const patientCodes = await this.getInsuranCodeByPatientId([info?.id], partnerId);
        json.patientCode = json?.code
        if (patientCodes.length > 0) {
            const patientCode = get(first(patientCodes), 'patientCode');
            if (patientCode) {
                json.patientCode = patientCode;
            }
            const insuranceCode = get(first(patientCodes), 'insuranceCode');
            if (insuranceCode) {
                json.insuranceCode = insuranceCode;
            }
        }

        if (yearOldValidationDto) {
            const patientConstraintObj = await this.checkPatientPartner([json], yearOldValidationDto, appid, partnerId);
            json = first<any>(patientConstraintObj)?.warningMessage ?
                {
                    ...json,
                    warningMessage: first<any>(patientConstraintObj).warningMessage
                } : json;
        }


        return {
            ...json,
            birthdate: ngaysinh,
            fullname: `${json.surname} ${json.name}`,
            fullAddress,
        };
    }

    async getPatientByIdPatient(id: string): Promise<any> {
        return this.patientModel.findOne({ id }).exec();
    }

    async getPatientInfoAndPatientCodeById(partnerId: string, appId: string, patientId: string): Promise<any> {
        const info = await this.patientModel.findById({ _id: patientId })
            .populate('profession')
            .populate('country')
            .populate('nation')
            .populate('city')
            .populate('district')
            .populate('ward')
            .read('primary')
            .exec();
        const json: any = info.toObject();
        const checkCountry = info?.country || null;
        if(!checkCountry){
            /* tim lai country theo VN */
            const findCountry = await this.countryModel.findById('5ecb3b014ae1165edc747c5b').exec();
            json.country_code = findCountry.code;
            json.country_id = findCountry.id;
            json.country = findCountry.toObject();
            this.patientModel
                .findByIdAndUpdate({ _id: patientId }, { country_code: findCountry.code, country_id: findCountry.id, country: findCountry._id })
                .exec()
                .catch(error => error);
        }
        const fullAddress = this.getFullAddress(info);
        /* lấy thông tin thân nhân */
        const { relation } = json;
        if (relation.relative_type_id) {
            const findRelative = await this.relativeModel.findOne({ id: relation.relative_type_id }).exec();
            if (findRelative) {
                json.relative = { ...findRelative.toObject() };
            }
        }
        const ngaysinh = !!json.birthdate && moment(json.birthdate).isValid() ? moment(json.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '';
        /* lấy lại thông tin patientCode, insuranceCode */
        const patientCodes = await this.patientCodeModel.findOne({ partnerId, patientId: json.id }).exec();
        let insuranceCode = json?.insuranceId || '';
        let patientCode = json.code;
        let isUpdateFull = true;
        if (patientCodes) {
            const codeObj = patientCodes.toObject();
            insuranceCode = codeObj.insuranceCode;
            // if (appId !== 'medpro') {
                isUpdateFull = false;
                patientCode = codeObj.patientCode;
            // }
        }
        return {
            ...json,
            birthdate: ngaysinh,
            fullname: `${json.surname} ${json.name}`,
            insuranceCode,
            isUpdateFull,
            patientCode,
            fullAddress,
        };
    }

    async removeLimitPatientSearch(partnerId: string, userId: string): Promise<any> {
        return this.patientSearchLogModel.remove({ userId, partnerId, date: moment().format('YYYY-MM-DD') }).exec();
    }

    async searchExtraInfo(partnerid: string, appid: string): Promise<any> {
        return this.patientModel
            .find(
                { $text: { $search: 'Nguyễn Dũng' }, sex: 1, birthyear: 1989, city_id: 'aaaaa' },
                { score: { $meta: 'textScore' } },
            )
            .sort({ score: { $meta: 'textScore' } })
            .exec();
    }

    async transformDataSearch(partnerId: string, data: any): Promise<any> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const urlTransform = `${baseUrl}/his-connector/api/convertPartnerData`;
        const { relative } = data;
        const getProfessionId = get(data, 'profession_id', 0);
        const objRelativeType: any = {};
        if (relative && Object.keys(relative).length > 0) {
            objRelativeType.relationTypeId = `${relative.relative_type_id}`;
        }
        const objProfessionId: any = {};
        if (getProfessionId > 0) {
            objProfessionId.professionId = `${getProfessionId}`;
        }
        const params: any = {
            fromPartnerId: partnerId,
            toPartnerId: 'medpro',
            // countryId: '',
            cityId: `${data.city_id}`,
            districtId: `${data.district_id}`,
            wardId: `${data.ward_id}`,
            nationId: `${data.dantoc_id}`,
            ...objRelativeType,
            ...objProfessionId,
            countryId: get(data, 'country.code', 'VIE'),
        };
        try {
            const dataKeys = (await this.transformDataPatientV1(urlTransform, params).toPromise()).data;
            return dataKeys;
        } catch (error) {
            this.clientSentry.instance().captureMessage(`Chuyển đổi Tìm kiếm ${partnerId} theo msbn failed`);
            this.clientSentry.instance().captureException(error);
            return false;
        }
    }

    async searchPatientByMsbn(
        userId: string, appid: string, partnerId: string, searchPatientDTO: SearchPatientDTO, prefix: boolean = true,
        cskhInfo?: CSKHTokenVerifyDTO, locale: string = 'vi'): Promise<any> {
        const [warningMessageGlobal, hospital, errorPatientNotFound] = await Promise.all([
            // this.globalSettingModel.findOne({ key: 'UMC_V2_CREATE_PATIENT_OLD' }),
            this.globalSettingService.findByKeyAndRepoName('UMC_V2_CREATE_PATIENT_OLD'),
            this.hospitalModel.findOne({ partnerId }),
            this.globalSettingService.findByKeyAndRepoName('PATIENT_MESSAGE_ERROR_NOT_FOUND', null, locale),
        ]);
        // const warningMessage = warningMessageGlobal.value.replace('{PARTNER}', hospital.name);
        const warningMessage = warningMessageGlobal.replace('{PARTNER}', hospital.name);
        // Kiểm tra xem search mấy lần rồi
        let isCS = false;
        if (userId) {
            await this.constraintSearchLog(userId, partnerId, cskhInfo, locale);
            isCS = await this.userService.isCs(cskhInfo?.cskhUserId || userId);
        }
        // const env = this.urlConfigService.getEnv();
        // if (env === 'PRODUCTION') {
        //     if (userId) {
        //         const countValue = await this.patientSearchLogModel.find({ userId, partnerId, date: moment().format('YYYY-MM-DD') }).count();
        //         if (countValue > this.countSearchPatient) {
        //             throw new HttpException(
        //                 'Bạn vượt quá giới hạn tìm kiếm. Cần hỗ trợ vui lòng liên hệ với chúng tôi qua số 19002115', HttpStatus.TOO_MANY_REQUESTS);
        //         }
        //         /* tiến hành insert vào trong db */
        //         const patientSearchLog = new this.patientSearchLogModel({
        //             userId,
        //             partnerId,
        //             date: moment().format('YYYY-MM-DD'),
        //             checkValue: 1,
        //         });
        //         await patientSearchLog.save();
        //     }
        // }
        /* Lấy thông tin partner config */
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        let patientCode = searchPatientDTO.msbn;
        if (partnerId === 'umc') {
            prefix = false;
        }
        if (partnerConfig && prefix === true) {
            const removeSpecialCharacter = `${patientCode}`.replace(/[-]/g, '');
            patientCode = `${partnerConfig.prefixValuePatientCode}${removeSpecialCharacter}`;
        }
        // console.log('patient code origin', patientCode);
        const { isVerifiedByPhone: checkVerifiedPhone = false } = partnerConfig;
        const isVerifiedByPhone = isCS ? false : checkVerifiedPhone;

        // console.log(patientCode);
        /* Kiểm tra bên db mongo medpro xem có tồn tài ko */
        // const findPatient = await this.patientModel
        //     .find({ patientCode })
        //     .populate('profession')
        //     .populate('country')
        //     .populate('nation')
        //     .populate('city')
        //     .populate('district')
        //     .populate('ward')
        //     .limit(1).exec();
        // if (findPatient.length > 0) {
        //     return findPatient.map(item => {
        //         const itemMap = item.toObject();
        //         const secretKeyObj: any = {};
        //         if (!isVerifiedByPhone) {
        //             const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        //             secretKeyObj.secretKey = jwt.sign({ patientId: itemMap.id }, jwtOptions.secret, jwtOptions.signOptions);
        //         }
        //         return {
        //             ...itemMap,
        //             birthdate: moment(itemMap.birthdate).isValid() ? moment(itemMap.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
        //             mobile: this.secretMobile(itemMap.mobile),
        //             isVerifiedByPhone,
        //             ...secretKeyObj,
        //         };
        //     });
        // }
        // if (partnerId === 'medpro') {
        //     return [];
        // }
        /* kiểm tra bên HIS */
        let resultData: any = {};
        let patientV1: any = null;
        try {
            if (this.listAppId.has(appid)) {
                const newSet = this.utilService.oldHospitalSearch();
                if (!!partnerId && newSet.has(partnerId)) {
                    /* tìm lại user_id và access_token */
                    const userMongo = await this.userModel.findById({ _id: userId }, { username: true, medproId: true }).exec();
                    const userV1 = await this.checkExistsUserByUsername(userMongo.username);
                    // this.logger.debug(
                    //     `partnerId: ${partnerId}, appId: ${appid}, userMongo: ${userMongo}, userV1: ${JSON.stringify(userV1, null, 2)}`,
                    //     'CallV1 - Get Patient Info',
                    // );
                    let session: any;
                    switch (partnerId) {
                        case 'nhidong1':
                            session = await this.sessionService.checkExistsNhiDong1SessionByUserID(userV1.id);
                            break;
                        case 'dalieuhcm':
                            session = await this.sessionService.checkExistsSkinSessionByUserID(userV1.id);
                            break;
                        case 'ctchhcm':
                            session = await this.sessionService.checkExistsCTCHSessionByUserID(userV1.id);
                            break;
                        case 'thuduc':
                            session = await this.sessionService.checkExistsThuDucSessionByUserID(userV1.id);
                            break;
                        case 'umc':
                            session = await this.sessionService.checkExistsUMCSessionByUserID(userV1.id);
                            break;
                        default:
                            session = {
                                user_id: 0,
                                access_token: '',
                            };
                            break;
                    }
                    // console.log({ msbn: patientCode, ...session }, partnerId);
                    const data: any = (await this.proxyFindByMsbn({ msbn: patientCode, ...session }, partnerId).toPromise()).data;
                    /* Tiến hành chuyển đổi thông tin */
                    const dataTransform = await this.transformDataSearch(partnerId, data);
                    // console.log('dataTransform', dataTransform);
                    if (dataTransform) {
                        /* Tiếp tục chuyển đổi */
                        switch (partnerId) {
                            case 'nhidong1':
                                patientV1 = await this.sessionService.getNhiDong1PatientV1(data.id);
                                break;
                            case 'dalieuhcm':
                                patientV1 = await this.sessionService.getDaLieuPatientV1(data.id);
                                break;
                            case 'ctchhcm':
                                patientV1 = await this.sessionService.getCTCHPatientV1(data.id);
                                break;
                            case 'thuduc':
                                patientV1 = await this.sessionService.getThuDucPatientV1(data.id);
                                break;
                            case 'umc':
                                patientV1 = await this.sessionService.getUMCPatientV1(data.id);
                                break;
                            default:
                                break;
                        }
                        // console.log('patient v1 n', patientV1);
                        const override: UMCPatientResponseDTO = {
                            Ten: patientV1.name,
                            Ho: patientV1.surname,
                            SoCMND: patientV1.cmnd,
                            GioiTinh: patientV1.sex === 1,
                            DiDong: patientV1.mobile,
                            DienThoai: patientV1.mobile,
                            IDDanToc: dataTransform.nationId,
                            IDNgheNghiep: dataTransform.professionId,
                            MaQuocGia: dataTransform?.countryId || 'VIE',
                            IDTinh: dataTransform.cityId,
                            IDQuanHuyen: dataTransform.districtId,
                            IDPhuongXa: dataTransform.wardId,
                            DiaChi: patientV1.address,
                            NgaySinh: moment(patientV1.birthdate).isValid() ? moment(patientV1.birthdate).format('YYYY-MM-DD') : null,
                            NamSinh: patientV1.birthyear,
                            SoHS: patientCode,
                            NgungSD: false,
                        };
                        resultData = override;
                    } else {
                        this.emitService.emit(LOG_SERVICE_EVENT, {
                            name: 'transformDataSearch',
                            nameParent: 'searchPatientByMsbn',
                            params: { partnerId, data },
                            response: dataTransform,
                            errorCode: 'searchPatientByMsbn_01',
                        });
                        throw new HttpException(errorPatientNotFound, HttpStatus.NOT_FOUND);
                    }
                } else {
                    const data = (await this.getPatientByHIS(partnerId, patientCode).toPromise()).data;
                    resultData = data;
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: `resultPatientByHIS_${partnerId}`,
                        summary: 'Kết quả thông tin BN từ HIS nhidong1',
                        nameParent: 'findbymsbn',
                        response: resultData,
                        errorBody: null,
                    });
                }
            } else {
                const checkIsMobile = isMobilePhone(patientCode, 'vi-VN');
                if (checkIsMobile) {
                    const dataMobile = (await this.getPatientExtraByPhoneHIS(partnerId, patientCode).toPromise()).data;
                    if (dataMobile.length === 0) {
                        this.emitService.emit(LOG_SERVICE_EVENT, {
                            name: 'getPatientExtraByPhoneHIS',
                            nameParent: 'searchPatientByMsbn',
                            params: { partnerId, patientCode },
                            response: dataMobile,
                            errorCode: 'searchPatientByMsbn_02',
                        });
                        throw new HttpException(errorPatientNotFound, HttpStatus.NOT_FOUND);
                    } else {
                        resultData = first(dataMobile);
                        patientCode = resultData.SoHS;
                    }
                } else {
                    const data = (await this.getPatientByHIS(partnerId, patientCode).toPromise()).data;
                    resultData = data;
                }
            }
        } catch (error) {
            /* Ưu tiên xử lý tìm theo mã số hồ sơ */
            // console.log('vao day', error)
            // this.clientSentry.instance().captureException(error);
            const { isSearchedByInsuranceCode = false, isSearcheMedpro = false } = partnerConfig;
            if (isSearchedByInsuranceCode) {
                try {
                    const dataInsuranceCode = (await this.getPatientExtraByInsuranceCodeHIS(partnerId, searchPatientDTO.msbn).toPromise()).data;
                    if (dataInsuranceCode.length === 0) {
                        /* Kiểm tra tiếp xem có config isSearcheMedpro hay không */
                        if (isSearcheMedpro) {
                            /* tìm trong medpro theo parttner id */
                            const medproIdSearch = `${searchPatientDTO.msbn}`.toUpperCase();
                            const findPatients = await this.patientModel
                                .find({ code: medproIdSearch })
                                .populate('profession')
                                .populate('country')
                                .populate('nation')
                                .populate('city')
                                .populate('district')
                                .populate('ward')
                                .limit(1).exec();
                            if (findPatients.length > 0) {
                                const firstPatientFound = first(findPatients);
                                const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, firstPatientFound._id);
                                const secretKeyObj: any = {};
                                if (!isVerifiedByPhone) {
                                    const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                                    secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                                }
                                let patientsData = [{
                                    ...returnInfo,
                                    mobile: this.secretMobile(returnInfo.mobile),
                                    isVerifiedByPhone,
                                    ...(!isMobilePhone(returnInfo.mobile, 'vi-VN') && { warningMessage }),
                                    ...secretKeyObj,
                                }];
                                patientsData = await this.getRelationPatient(patientsData);
                                const [envCheckConstraintPatinet, appAcceptConstraint, repoShowPatientInvalid] = await Promise.all([
                                    this.globalSettingService.findByKeyAndRepoName('ENV_CHECK_CONSTRAINS_PATIENT_DETAIL'),
                                    this.globalSettingService.findByKeyAndRepoName('APP_CHECK_CONSTRAINT_PATIENT'),
                                    this.globalSettingService.findByKeyAndRepoName('REPO_SHOW_PATIENT_INVALID'),
                                ]);
                                const setAppAcceptConstraint = new Set(appAcceptConstraint.split(','));
                                const setRepoShowPatientInvalid = new Set(repoShowPatientInvalid.split(','));
                                if (
                                    envCheckConstraintPatinet === 'ON' &&
                                    setAppAcceptConstraint.has(appid) &&
                                    setRepoShowPatientInvalid.has(this.repoName)
                                ) {
                                    patientsData = await this.checkConstrainsPatientDetail(patientsData, {
                                        locale: locale,
                                        partnerid: partnerId,
                                        appid: appid,
                                    });
                                }
                                return patientsData;
                            } else {
                                this.emitService.emit(LOG_SERVICE_EVENT, {
                                    name: 'findPatients',
                                    nameParent: 'searchPatientByMsbn',
                                    params: { code: medproIdSearch },
                                    response: findPatients,
                                    errorCode: 'searchPatientByMsbn_03',
                                });
                                throw new HttpException(errorPatientNotFound, HttpStatus.NOT_FOUND);
                            }
                        } else {
                            this.emitService.emit(LOG_SERVICE_EVENT, {
                                name: 'isSearcheMedpro',
                                nameParent: 'searchPatientByMsbn',
                                params: isSearcheMedpro,
                                errorCode: 'searchPatientByMsbn_04',
                            });
                            throw new HttpException(errorPatientNotFound, HttpStatus.NOT_FOUND);
                        }
                    } else {
                        resultData = first(dataInsuranceCode);
                        patientCode = resultData.SoHS;
                    }
                } catch (error) {
                    // console.log('vao day 22222', error)
                    // this.clientSentry.instance().captureException(error);
                    const { status = 404 } = error;
                    switch (status) {
                        case HttpStatus.NOT_FOUND:
                            this.emitService.emit(LOG_SERVICE_EVENT, {
                                name: 'searchPatientByMsbn',
                                nameParent: 'searchPatientByMsbn',
                                params: { userId, appid, partnerId, searchPatientDTO, prefix },
                                errorCode: 'searchPatientByMsbn_05',
                            });
                            throw new HttpException(errorPatientNotFound, HttpStatus.NOT_FOUND);
                        default:
                            this.emitService.emit(LOG_SERVICE_EVENT, {
                                name: 'searchPatientByMsbn',
                                nameParent: 'searchPatientByMsbn',
                                params: { userId, appid, partnerId, searchPatientDTO, prefix },
                                errorCode: 'searchPatientByMsbn_06',
                            });
                            throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
                    }
                }
            } else {
                /* Kiểm tra tiếp xem có config isSearcheMedpro hay không */
                if (isSearcheMedpro) {
                    /* tìm trong medpro theo parttner id */
                    const medproIdSearch = `${searchPatientDTO.msbn}`.toUpperCase();
                    const findPatients = await this.patientModel
                        .find({ code: medproIdSearch })
                        .populate('profession')
                        .populate('country')
                        .populate('nation')
                        .populate('city')
                        .populate('district')
                        .populate('ward')
                        .limit(1).exec();
                    if (findPatients.length > 0) {
                        const firstPatientFound = first(findPatients);
                        const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, firstPatientFound._id);
                        const secretKeyObj: any = {};
                        if (!isVerifiedByPhone) {
                            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                            secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                        }
                        // bổ sung patient relation cho patients
                        let patientsData = [{
                            ...returnInfo,
                            mobile: this.secretMobile(returnInfo.mobile),
                            isVerifiedByPhone,
                            ...(!isMobilePhone(returnInfo.mobile, 'vi-VN') && { warningMessage }),
                            ...secretKeyObj,
                        }];
                        patientsData = await this.getRelationPatient(patientsData);
                        const [envCheckConstraintPatinet, appAcceptConstraint, repoShowPatientInvalid] = await Promise.all([
                            this.globalSettingService.findByKeyAndRepoName('ENV_CHECK_CONSTRAINS_PATIENT_DETAIL'),
                            this.globalSettingService.findByKeyAndRepoName('APP_CHECK_CONSTRAINT_PATIENT'),
                            this.globalSettingService.findByKeyAndRepoName('REPO_SHOW_PATIENT_INVALID'),
                        ]);
                        const setAppAcceptConstraint = new Set(appAcceptConstraint.split(','));
                        const setRepoShowPatientInvalid = new Set(repoShowPatientInvalid.split(','));
                        if (envCheckConstraintPatinet === 'ON' && setAppAcceptConstraint.has(appid) && setRepoShowPatientInvalid.has(this.repoName)) {
                            patientsData = await this.checkConstrainsPatientDetail(patientsData, {
                                locale: locale,
                                partnerid: partnerId,
                                appid: appid,
                            });
                        }
                        return patientsData;
                    } else {
                        this.emitService.emit(LOG_SERVICE_EVENT, {
                            name: 'findPatients',
                            nameParent: 'searchPatientByMsbn',
                            params: { code: medproIdSearch },
                            errorCode: 'searchPatientByMsbn_07',
                        });
                        throw new HttpException(errorPatientNotFound, HttpStatus.NOT_FOUND);
                    }
                } else {
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: 'isSearcheMedpro',
                        nameParent: 'searchPatientByMsbn',
                        params: isSearcheMedpro,
                        errorCode: 'searchPatientByMsbn_08',
                    });
                    throw new HttpException(errorPatientNotFound, HttpStatus.NOT_FOUND);
                }
            }
        }
        /* kiểm tra xem bệnh nhân này ngưng sử dụng hay ko */
        const patient: UMCPatientResponseDTO = resultData;
        // console.log('patient 2', patient);
        patientCode = patient?.SoHS || patientCode;
        let mobile = patient?.DiDong;
        if (mobile?.length === 9) {
            // check mobile
            mobile = `0${mobile}`;
            const checkPhone = isMobilePhone(mobile, 'vi-VN');
            if (checkPhone) {
                patient.DiDong = mobile;
            }
        }
        let phone = patient?.DienThoai;
        if (phone?.length === 9) {
            // check phone
            phone = `0${phone}`;
            const checkPhone = isMobilePhone(phone, 'vi-VN');
            if (checkPhone) {
                patient.DienThoai = phone;
            }
        }
        const NgungSD = patient.NgungSD;
        if (NgungSD) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'searchPatientByMsbn',
                nameParent: 'searchPatientByMsbn',
                params: patient,
                response: NgungSD,
                errorCode: 'searchPatientByMsbn_09',
            });
            throw new HttpException('Bệnh nhân này ngưng dùng.', HttpStatus.BAD_REQUEST);
        }
        try {
            /* kiểm tra xem thông tin ngày có chưa. nếu có rồi thì update */
            const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, patientCode, appid);
            if (checkExists) {
                // const checkObj = checkExists.toObject();
                // console.log(checkExists);
                const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                    DiDong: patient.DiDong,
                    DienThoai: patient.DienThoai,
                    SoCMND: patient.SoCMND,
                    NgaySinh: patient.NgaySinh,
                    NamSinh: patient.NamSinh,
                    GioiTinh: patient.GioiTinh,
                    relative: {
                        relation: {
                            relative_name: patient?.relative?.fullname || '',
                            relative_email: patient?.relative?.email || '',
                            relative_mobile: patient?.relative?.phone || '',
                            relative_type_id: patient?.relative?.relativeType || ''
                        }
                    }
                });
                const patientObj = updateInfo.toObject();
                const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                const secretKeyObj: any = {};
                if (!isVerifiedByPhone) {
                    const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                    secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                }
                let patientsData = [{
                    ...returnInfo,
                    mobile: this.secretMobile(returnInfo.mobile),
                    isVerifiedByPhone,
                    ...(!isMobilePhone(returnInfo.mobile, 'vi-VN') && { warningMessage }),
                    ...secretKeyObj,
                }];

                // bổ sung patient relation cho patients
                patientsData = await this.getRelationPatient(patientsData);
                const [envCheckConstraintPatinet, appAcceptConstraint, repoShowPatientInvalid] = await Promise.all([
                    this.globalSettingService.findByKeyAndRepoName('ENV_CHECK_CONSTRAINS_PATIENT_DETAIL'),
                    this.globalSettingService.findByKeyAndRepoName('APP_CHECK_CONSTRAINT_PATIENT'),
                    this.globalSettingService.findByKeyAndRepoName('REPO_SHOW_PATIENT_INVALID'),
                ]);
                const setAppAcceptConstraint = new Set(appAcceptConstraint.split(','));
                const setRepoShowPatientInvalid = new Set(repoShowPatientInvalid.split(','));
                if (envCheckConstraintPatinet === 'ON' && setAppAcceptConstraint.has(appid) && setRepoShowPatientInvalid.has(this.repoName)) {
                    patientsData = await this.checkConstrainsPatientDetail(patientsData, {
                        locale: locale,
                        partnerid: partnerId,
                        appid: appid
                    });
                }
                return patientsData;
            } else {
                patient.partnerId = partnerId;
                const patientInfo = await this.insertUMCSyncPatient(patient);
                const patientObj = patientInfo.toObject();
                /* kiểm tra xem có patientV1 hay không */
                if (patientV1) {
                    /* tiến hành cập nhật lại */
                    const patientIdV1 = patientV1.id || 0;
                    const keyPatientV1 = this.utilService.patientIdV1Key(partnerId);
                    await this.patientModel.findByIdAndUpdate({ _id: patientObj._id }, { [keyPatientV1]: patientIdV1 }).exec();
                }
                /* insert thêm vào trong patient_codes */
                const patientCodeInfo = new this.patientCodeModel({
                    id: `${partnerId}_${patientObj.id}`,
                    createTime: moment().toDate(),
                    patientId: patientObj.id,
                    patientCode: `${patientCode}`.trim(),
                    partnerId,
                    appId: appid,
                });
                await patientCodeInfo.save();
                // console.log(aaaa);
                /* kết thúc phần insert vào trong patient codes */
                const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                const secretKeyObj: any = {};
                if (!isVerifiedByPhone) {
                    const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                    secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                }
                let patientsData = [{
                    ...returnInfo,
                    mobile: this.secretMobile(returnInfo.mobile),
                    isVerifiedByPhone,
                    ...(!isMobilePhone(returnInfo.mobile, 'vi-VN') && { warningMessage }),
                    ...secretKeyObj,
                }];
                // bổ sung patient relation cho patients
                patientsData = await this.getRelationPatient(patientsData);
                const [envCheckConstraintPatinet, appAcceptConstraint, repoShowPatientInvalid] = await Promise.all([
                    this.globalSettingService.findByKeyAndRepoName('ENV_CHECK_CONSTRAINS_PATIENT_DETAIL'),
                    this.globalSettingService.findByKeyAndRepoName('APP_CHECK_CONSTRAINT_PATIENT'),
                    this.globalSettingService.findByKeyAndRepoName('REPO_SHOW_PATIENT_INVALID'),
                ]);
                const setAppAcceptConstraint = new Set(appAcceptConstraint.split(','));
                const setRepoShowPatientInvalid = new Set(repoShowPatientInvalid.split(','));
                if (envCheckConstraintPatinet === 'ON' && setAppAcceptConstraint.has(appid) && setRepoShowPatientInvalid.has(this.repoName)) {
                    patientsData = await this.checkConstrainsPatientDetail(patientsData, {
                        locale: locale,
                        partnerid: partnerId,
                        appid: appid,
                    });
                }
                return patientsData;
            }
        } catch (error) {
            // this.clientSentry.instance().captureException(error);
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: 'searchPatientByMsbn',
                        nameParent: 'searchPatientByMsbn',
                        params: { userId, appid, partnerId, searchPatientDTO, prefix, patient },
                        errorCode: 'searchPatientByMsbn_10',
                    });
                    throw new HttpException(errorPatientNotFound, HttpStatus.NOT_FOUND);
                default:
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: 'searchPatientByMsbn',
                        nameParent: 'searchPatientByMsbn',
                        params: { userId, appid, partnerId, searchPatientDTO, prefix, patient },
                        errorCode: 'searchPatientByMsbn_11',
                    });
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }

    }

    async searchPatientByMsbnCard(
        userId: string, appid: string, partnerId: string, searchPatientDTO: SearchPatientDTO, prefix: boolean = true): Promise<any> {
        /* Lấy thông tin partner config */
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
        let patientCode = searchPatientDTO.msbn;
        if (partnerId === 'umc') {
            prefix = false;
        }
        if (partnerConfig && prefix === true) {
            const removeSpecialCharacter = `${patientCode}`.replace(/[-]/g, '');
            patientCode = `${partnerConfig.prefixValuePatientCode}${removeSpecialCharacter}`;
        }
        const isVerifiedByPhone = false;
        /* kiểm tra bên HIS */
        let resultData: any = {};
        try {
            const data = (await this.getPatientByHIS(partnerId, patientCode).toPromise()).data;
            resultData = data;
        } catch (error) {
            return {
                message: 'Không tìm thấy hồ sơ theo mã số hồ sơ',
                isFound: false,
            };
        }
        /* kiểm tra xem bệnh nhân này ngưng sử dụng hay ko */
        const patient: UMCPatientResponseDTO = resultData;
        const NgungSD = patient.NgungSD;
        if (NgungSD) {
            return {
                message: 'Hồ sơ ngày ngưng sử dụng. Vui lòng liên hệ bệnh viện.',
                isFound: false,
            };
        }
        try {
            /* kiểm tra xem thông tin ngày có chưa. nếu có rồi thì update */
            const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, patientCode);
            if (checkExists) {
                const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                    DiDong: patient.DiDong,
                    DienThoai: patient.DienThoai,
                    SoCMND: patient.SoCMND,
                    NgaySinh: patient.NgaySinh,
                    NamSinh: patient.NamSinh,
                    GioiTinh: patient.GioiTinh,
                });
                const getPatientByRefId = await this.getPatientByREf(updateInfo._id);
                return {
                    isFound: true,
                    patient: getPatientByRefId,
                    message: 'Cập nhật lại mã hồ sơ thành công.',
                };
            } else {
                patient.partnerId = partnerId;
                const patientInfo = await this.insertUMCSyncPatient(patient);
                const patientObj = patientInfo.toObject();
                /* insert thêm vào trong patient_codes */
                const patientCodeInfo = new this.patientCodeModel({
                    id: `${partnerId}_${patientObj.id}`,
                    createTime: moment().toDate(),
                    patientId: patientObj.id,
                    patientCode: `${patientCode}`.trim(),
                    partnerId,
                    appId: appid,
                });
                await patientCodeInfo.save();
                return {
                    isFound: true,
                    patient: patientInfo,
                    message: 'Tạo mới mã hồ sơ thành công.',
                };
            }
        } catch (error) {
            console.log(error);
            return {
                isFound: false,
                message: 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.',
            };
        }

    }

    postPatientByHIS(url, params): Observable<AxiosResponse<any>> {
        return this.httpService.post(url, params);
    }

    async constraintSearchLog(userId: string, partnerId: string, cskhInfo?: CSKHTokenVerifyDTO, locale: string = 'vi'): Promise<any> {
        const env = this.urlConfigService.getEnv();
        const validEnvTracking = new Set(['PRODUCTION', 'TESTING_V2', 'TESTING_V3']);
        if (validEnvTracking.has(env)) {
            const isCS = await this.userService.isCs(cskhInfo?.cskhUserId || userId);
            const ignoreUserList = await this.globalSettingService.findByKeyAndRepoName('IGNORE_USER_EXAM_RESULT_SEARCH')
            const ignoreUserSets = ignoreUserList ? new Set(ignoreUserList.split(',')) : new Set();
            let checkUser = false;
            if (isCS) {
                checkUser = true;
            } else {
                // tìm lại username */
                const getUserName = await this.userModel.findById(userId, { username: true }).exec();
                if (getUserName && ignoreUserSets.has(getUserName)) {
                    checkUser = true;
                }
            }

            if (userId && !checkUser) {
                const countValue = await this.patientSearchLogModel.find({ userId, partnerId, date: moment().format('YYYY-MM-DD') }).count();
                const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
                const { isCountSearchConstraints = true } = partnerConfig;
                if (countValue > this.countSearchPatient && isCountSearchConstraints) {
                    const message = await this.globalSettingService.findByKeyAndRepoName('ERROR_PATIENT_SEARCH_OVER', null, locale)
                    throw new HttpException(message, HttpStatus.TOO_MANY_REQUESTS);
                }
                /* tiến hành insert vào trong db */
                const patientSearchLog = new this.patientSearchLogModel({
                    userId,
                    partnerId,
                    date: moment().format('YYYY-MM-DD'),
                    checkValue: 1,
                });
                await patientSearchLog.save();
            }
        }
    }

    // if (userId) {
    //     const countValue = await this.patientSearchLogModel.find({ userId, partnerId, date: moment().format('YYYY-MM-DD') }).count();
    //     if (countValue > this.countSearchPatient) {
    //         throw new HttpException(
    //             'Bạn vượt quá giới hạn tìm kiếm. Cần hỗ trợ vui lòng liên hệ với chúng tôi qua số 19002115', HttpStatus.TOO_MANY_REQUESTS);
    //     }
    //     /* tiến hành insert vào trong db */
    //     const patientSearchLog = new this.patientSearchLogModel({
    //         userId,
    //         partnerId,
    //         date: moment().format('YYYY-MM-DD'),
    //         checkValue: 1,
    //     });
    //     await patientSearchLog.save();
    // }
    /* Lấy thông tin partner config */

    /* Kiểm tra bên medpro trước */
    // const findPatients = await this.patientModel
    //     .find(
    //         { $text: { $search: `${params.Ho} ${params.Ten}` }, sex: params.GioiTinh, birthyear: params.NamSinh, city_id: params.IDTinh },
    //         { score: { $meta: 'textScore' } },
    //     )
    //     .populate('profession')
    //     .populate('country')
    //     .populate('nation')
    //     .populate('city')
    //     .populate('district')
    //     .populate('ward')
    //     .sort({ score: { $meta: 'textScore' } })
    //     .exec();
    // if (findPatients.length > 0) {
    //     return findPatients.map(item => {
    //         const itemMap = item.toObject();
    //         const secretKeyObj: any = {};
    //         if (!isVerifiedByPhone) {
    //             const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
    //             secretKeyObj.secretKey = jwt.sign({ patientId: itemMap.id }, jwtOptions.secret, jwtOptions.signOptions);
    //         }
    //         return {
    //             ...itemMap,
    //             birthdate: moment(itemMap.birthdate).isValid() ? moment(itemMap.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
    //             mobile: this.secretMobile(itemMap.mobile),
    //             isVerifiedByPhone,
    //             ...secretKeyObj,
    //         };
    //     });
    // }
    // if (partnerId === 'medpro') {
    //     return [];
    // }

    async processSearchPatientByExtraInfo(
        appid: string,
        partnerId: string,
        userId: string,
        searchPatientExtraInfoDTO: SearchPatientExtraInfoMongoDTO,
        cskhInfo?: CSKHTokenVerifyDTO,
    ): Promise<any> {
        const isCS = await this.userService.isCs(cskhInfo?.cskhUserId || userId);
        try {
            const params = {
                Ho: searchPatientExtraInfoDTO.surName,
                Ten: searchPatientExtraInfoDTO.firstName,
                NamSinh: searchPatientExtraInfoDTO.birthYear,
                GioiTinh: searchPatientExtraInfoDTO.gender,
                IDTinh: searchPatientExtraInfoDTO.cityId,
            };
            const data = await(await this.getPatientExtraByHIS(partnerId, params).toPromise()).data;
            const filterData: UMCPatientExtraInfoResponseDTO[] = data.filter((item: UMCPatientExtraInfoResponseDTO) => {
                return !item.NgungSD;
            });
            let resultData = [];
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            const { isVerifiedByPhone: checkVerifiedPhone = false } = partnerConfig;
            const isVerifiedByPhone = isCS ? false : checkVerifiedPhone;
            for await (const item of filterData) {
                const pInfo: UMCPatientResponseDTO = item;
                const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, pInfo.SoHS);
                if (checkExists) {
                    const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                        DiDong: pInfo.DiDong,
                        DienThoai: pInfo.DienThoai,
                        SoCMND: pInfo.SoCMND,
                        NgaySinh: pInfo.NgaySinh,
                        NamSinh: pInfo.NamSinh,
                        GioiTinh: pInfo.GioiTinh,
                    });
                    const patientObj = updateInfo.toObject();
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [
                        ...resultData,
                        {
                            ...returnInfo,
                            mobile: this.secretMobile(returnInfo.mobile),
                            isVerifiedByPhone,
                            ...secretKeyObj,
                        },
                    ];
                } else {
                    pInfo.partnerId = partnerId;
                    const patientInfo = await this.insertUMCSyncPatient(pInfo);
                    const patientObj = patientInfo.toObject();
                    /* insert thêm vào trong patient_codes */
                    const patientCodeInfo = new this.patientCodeModel({
                        id: `${partnerId}_${patientObj.id}`,
                        createTime: moment().toDate(),
                        patientId: patientObj.id,
                        patientCode: pInfo.SoHS,
                        partnerId,
                        appId: appid,
                    });
                    await patientCodeInfo.save();
                    /* kết thúc phần insert vào trong patient codes */
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [
                        ...resultData,
                        {
                            ...returnInfo,
                            mobile: this.secretMobile(returnInfo.mobile),
                            isVerifiedByPhone,
                            ...secretKeyObj,
                        },
                    ];
                }
            }
            return resultData;
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async processSearchPatientByExtraInfoV2(
        appid: string,
        partnerId: string,
        userId: string,
        searchPatientExtraInfoDTO: SearchPatientExtraInfoMongoDTO,
        cskhInfo?: CSKHTokenVerifyDTO,
    ): Promise<any> {
        try {
            const params = {
                Ho: searchPatientExtraInfoDTO.surName,
                Ten: searchPatientExtraInfoDTO.firstName,
                NamSinh: searchPatientExtraInfoDTO.birthYear,
                GioiTinh: searchPatientExtraInfoDTO.gender,
                IDTinh: searchPatientExtraInfoDTO.cityId,
            };
            return (await this.getPatientExtraByHIS(partnerId, params).toPromise()).data;
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async processSearchPhoneCMNDPatientByExtraInfo(
        appid: string, partnerId: string, userId: string, searchPatientExtraInfoDTO: SearchPhoneCMNDPatientExtraInfoMongoDTO): Promise<any> {
        const isCS = await this.userService.isCs(userId);
        try {
            const data = (await this.getPatientByPhoneOrCMNDByHIS(
                partnerId, searchPatientExtraInfoDTO.mobile,
                searchPatientExtraInfoDTO.cmnd).toPromise()).data;
            if (data.length === 0) {
                throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
            }
            const filterData: UMCPatientExtraInfoResponseDTO[] = data.filter((item: UMCPatientExtraInfoResponseDTO) => {
                return !item.NgungSD;
            });
            let resultData = [];
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            const { isVerifiedByPhone: checkVerifiedPhone = false } = partnerConfig;
            const isVerifiedByPhone = isCS ? false : checkVerifiedPhone;
            for await (const item of filterData) {
                const pInfo: UMCPatientResponseDTO = item;
                const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, pInfo.SoHS);
                if (checkExists) {
                    const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                        DiDong: pInfo.DiDong,
                        DienThoai: pInfo.DienThoai,
                        SoCMND: pInfo.SoCMND,
                        NgaySinh: pInfo.NgaySinh,
                        NamSinh: pInfo.NamSinh,
                        GioiTinh: pInfo.GioiTinh,
                    });
                    const patientObj = updateInfo.toObject();
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [...resultData, {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...secretKeyObj,
                    }];
                } else {
                    pInfo.partnerId = partnerId;
                    const patientInfo = await this.insertUMCSyncPatient(pInfo);
                    const patientObj = patientInfo.toObject();
                    /* insert thêm vào trong patient_codes */
                    const patientCodeInfo = new this.patientCodeModel({
                        id: `${partnerId}_${patientObj.id}`,
                        createTime: moment().toDate(),
                        patientId: patientObj.id,
                        patientCode: pInfo.SoHS,
                        partnerId,
                        appId: appid,
                    });
                    await patientCodeInfo.save();
                    /* kết thúc phần insert vào trong patient codes */
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [...resultData, {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...secretKeyObj,
                    }];
                }
            }
            return resultData;
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async processSearchPatientByCccdPartner(
        appid: string, partnerId: string, userId: string, searchPatientExtraInfoDTO: SearchPhoneCMNDPatientExtraInfoMongoDTO): Promise<any> {
        const isCS = await this.userService.isCs(userId);
        try {
            const timeoutConfig = await this.globalSettingService.findByKeyAndRepoName('FIND_PATIENT_HIS_TIMEOUT');
            const timeoutConfigNumber = timeoutConfig ? +timeoutConfig : 3000; // Default to 3 seconds if not set
            const data = (await this.getPatientsByCccdPartnerId(
                searchPatientExtraInfoDTO.cmnd, partnerId).pipe(timeout(timeoutConfigNumber)).toPromise()).data;
            
            if (data.length === 0) {
                throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
            }
            // const filterData: UMCPatientExtraInfoResponseDTO[] = data.filter((item: UMCPatientExtraInfoResponseDTO) => {
            //     return !item.NgungSD;
            // });
            let resultData = [];
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            const { isVerifiedByPhone: checkVerifiedPhone = false } = partnerConfig;
            const isVerifiedByPhone = isCS ? false : checkVerifiedPhone;
            for await (const item of data) {
                const pInfo: any = item;
                const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, pInfo.SoHS);
                if (checkExists) {
                    const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                        DiDong: pInfo.DiDong,
                        DienThoai: pInfo.DienThoai,
                        SoCMND: pInfo.SoCMND,
                        NgaySinh: pInfo.NgaySinh,
                        NamSinh: pInfo.NamSinh,
                        GioiTinh: pInfo.GioiTinh,
                    });
                    const patientObj = updateInfo.toObject();
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [...resultData, {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...secretKeyObj,
                    }];
                } else {
                    pInfo.partnerId = partnerId;
                    const patientInfo = await this.insertUMCSyncPatient(pInfo);
                    const patientObj = patientInfo.toObject();
                    /* insert thêm vào trong patient_codes */
                    const patientCodeInfo = new this.patientCodeModel({
                        id: `${partnerId}_${patientObj.id}`,
                        createTime: moment().toDate(),
                        patientId: patientObj.id,
                        patientCode: pInfo.SoHS,
                        partnerId,
                        appId: appid,
                    });
                    await patientCodeInfo.save();
                    /* kết thúc phần insert vào trong patient codes */
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [...resultData, {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...secretKeyObj,
                    }];
                }
            }
            console.log('resultData', resultData);
            
            return resultData;
        } catch (error) {
            if (error.name === 'TimeoutError') {
                console.log('TimeoutError', JSON.stringify(error, null, 2))
            }
            console.log('error', JSON.stringify(error, null, 2))
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async findUMCPatientByExtraInfo(
        userId: string,
        appid: string,
        partnerId: string,
        searchPatientExtraInfoDTO: SearchPatientExtraInfoMongoDTO,
        cskhInfo?: CSKHTokenVerifyDTO,
        updateToPatientId?: string,
        locale: string = 'vi',
    ): Promise<any> {
        const [warningMessageGlobal, hospital, errorPatientNotFoundMesage] = await Promise.all([
            // this.globalSettingModel.findOne({key: 'UMC_V2_CREATE_PATIENT_OLD'}),
            this.globalSettingService.findByKeyAndRepoName('UMC_V2_CREATE_PATIENT_OLD'),
            this.hospitalModel.findOne({partnerId}),
            this.globalSettingService.findByKeyAndRepoName('PATIENT_MESSAGE_ERROR_NOT_FOUND', null, locale),
        ]);
        // const warningMessage = warningMessageGlobal.value.replace('{PARTNER}', hospital.name);
        const warningMessage = warningMessageGlobal.replace('{PARTNER}', hospital.name);

        // Kiểm tra xem search mấy lần rồi
        await this.constraintSearchLog(userId, partnerId, cskhInfo, locale);
        /* tiến hành xử lý */
        const isCS = await this.userService.isCs(cskhInfo?.cskhUserId || userId);
        try {
            let data: any = [];
            const newSet = this.utilService.oldHospitalSync();
            if (!!partnerId && newSet.has(partnerId)) {
                let patientV1: any;
                const dataV1 = await this.processSearchPatientByExtraInfoV1(userId, appid, partnerId, searchPatientExtraInfoDTO, cskhInfo);
                // map data v1
                await Promise.all(dataV1.map(async element => {
                    const dataTransform = await this.transformDataSearch(partnerId, element);
                    if (dataTransform) {
                        /* Tiếp tục chuyển đổi */
                        switch (partnerId) {
                            case 'nhidong1':
                                patientV1 = await this.sessionService.getNhiDong1PatientV1(element.id);
                                break;
                            case 'dalieuhcm':
                                patientV1 = await this.sessionService.getDaLieuPatientV1(element.id);
                                break;
                            case 'ctchhcm':
                                patientV1 = await this.sessionService.getCTCHPatientV1(element.id);
                                break;
                            case 'thuduc':
                                patientV1 = await this.sessionService.getThuDucPatientV1(element.id);
                                break;
                            case 'umc':
                                patientV1 = await this.sessionService.getUMCPatientV1(element.id);
                                break;
                            default:
                                break;
                        }
                        const override: UMCPatientResponseDTO = {
                            Ten: patientV1.name,
                            Ho: patientV1.surname,
                            SoCMND: patientV1.cmnd,
                            GioiTinh: patientV1.sex === 1,
                            DiDong: patientV1.mobile,
                            DienThoai: patientV1.mobile,
                            IDDanToc: dataTransform.nationId,
                            IDNgheNghiep: dataTransform.professionId,
                            MaQuocGia: 'VIE',
                            IDTinh: dataTransform.cityId,
                            IDQuanHuyen: dataTransform.districtId,
                            IDPhuongXa: dataTransform.wardId,
                            DiaChi: patientV1.address,
                            NgaySinh: moment(patientV1.birthdate).isValid() ? moment(patientV1.birthdate).format('YYYY-MM-DD') : null,
                            NamSinh: patientV1.birthyear,
                            SoHS: patientV1.bvdhyd_msbn,
                            NgungSD: false,
                        };
                        data = [...data, override];
                    } else {
                        this.emitService.emit(LOG_SERVICE_EVENT, {
                            name: 'transformDataSearch',
                            nameParent: 'searchPatientByMsbn',
                            params: { partnerId, data },
                            response: dataTransform,
                            errorCode: 'searchPatientByMsbn_01',
                        });
                        throw new HttpException(errorPatientNotFoundMesage, HttpStatus.NOT_FOUND);
                    }
                }));
            }  else {
                data = await this.processSearchPatientByExtraInfoV2(appid, partnerId, userId, searchPatientExtraInfoDTO, cskhInfo);
            }
            if (data.length === 0 || data.error_code === 1025) {
                this.emitService.emit(LOG_SERVICE_EVENT, {
                    name: 'processSearchPatientByExtraInfoV2',
                    nameParent: 'findUMCPatientByExtraInfo',
                    params: { userId, appid, partnerId, searchPatientExtraInfoDTO, cskhInfo },
                    response: data,
                    errorCode: 'findUMCPatientByExtraInfo_01',
                });
                throw new HttpException(errorPatientNotFoundMesage, HttpStatus.NOT_FOUND);
            }
            const filterData: UMCPatientExtraInfoResponseDTO[] = data.filter((item: UMCPatientExtraInfoResponseDTO) => {
                return !item.NgungSD;
            });
            let resultData = [];
            const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }).exec();
            const { isVerifiedByPhone: checkVerifiedPhone = false } = partnerConfig;
            const isVerifiedByPhone = isCS ? false : checkVerifiedPhone;
            for await (const item of filterData) {
                const pInfo: UMCPatientResponseDTO = item;
                let mobile = pInfo?.DiDong;
                if (mobile?.length === 9) {
                    // check mobile
                    mobile = `0${mobile}`;
                    const checkPhone = isMobilePhone(mobile, 'vi-VN');
                    if (checkPhone) {
                        pInfo.DiDong = mobile;
                    }
                }
                let phone = pInfo?.DienThoai;
                if (phone?.length === 9) {
                    // check phone
                    phone = `0${phone}`;
                    const checkPhone = isMobilePhone(phone, 'vi-VN');
                    if (checkPhone) {
                        pInfo.DienThoai = phone;
                    }
                }
                const checkExists = await this.checkExistsPatientByMsbnMedproId(partnerId, pInfo.SoHS);
                if (checkExists) {
                    const updateInfo = await this.updateUMCSyncPatient(checkExists.patientId, {
                        DiDong: pInfo.DiDong,
                        DienThoai: pInfo.DienThoai,
                        SoCMND: pInfo.SoCMND,
                        NgaySinh: pInfo.NgaySinh,
                        NamSinh: pInfo.NamSinh,
                        GioiTinh: pInfo.GioiTinh,
                    });
                    const patientObj = updateInfo.toObject();
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id, updateToPatientId: updateToPatientId || '' }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [...resultData, {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...(!isMobilePhone(returnInfo.mobile, 'vi-VN') && { warningMessage }),
                        ...secretKeyObj,
                    }];
                } else {
                    pInfo.partnerId = partnerId;
                    const patientInfo = await this.insertUMCSyncPatient(pInfo);
                    const patientObj = patientInfo.toObject();
                    /* insert thêm vào trong patient_codes */
                    const patientCodeInfo = new this.patientCodeModel({
                        id: `${partnerId}_${patientObj.id}`,
                        createTime: moment().toDate(),
                        patientId: patientObj.id,
                        patientCode: pInfo.SoHS,
                        partnerId,
                        appId: appid,
                    });
                    await patientCodeInfo.save();
                    /* kết thúc phần insert vào trong patient codes */
                    const returnInfo = await this.getPatientInfoAndPatientCodeById(partnerId, appid, patientObj._id);
                    const secretKeyObj: any = {};
                    if (!isVerifiedByPhone) {
                        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
                        secretKeyObj.secretKey = jwt.sign({ patientId: returnInfo.id, updateToPatientId: updateToPatientId || '' }, jwtOptions.secret, jwtOptions.signOptions);
                    }
                    resultData = [...resultData, {
                        ...returnInfo,
                        mobile: this.secretMobile(returnInfo.mobile),
                        isVerifiedByPhone,
                        ...(!isMobilePhone(returnInfo.mobile, 'vi-VN') && { warningMessage }),
                        ...secretKeyObj,
                    }];
                }
            }

            // bổ sung patient relation cho patients
            resultData = await this.getRelationPatient(resultData);
            const [envCheckConstraintPatinet, appAcceptConstraint, repoShowPatientInvalid] = await Promise.all([
                this.globalSettingService.findByKeyAndRepoName('ENV_CHECK_CONSTRAINS_PATIENT_DETAIL'),
                this.globalSettingService.findByKeyAndRepoName('APP_CHECK_CONSTRAINT_PATIENT'),
                this.globalSettingService.findByKeyAndRepoName('REPO_SHOW_PATIENT_INVALID'),
            ]);
            const setAppAcceptConstraint = new Set(appAcceptConstraint.split(','));
            const setRepoShowPatientInvalid = new Set(repoShowPatientInvalid.split(','));
            if (envCheckConstraintPatinet === 'ON' && setAppAcceptConstraint.has(appid) && setRepoShowPatientInvalid.has(this.repoName)) {
                resultData = await this.checkConstrainsPatientDetail(resultData, {
                    locale: locale,
                    partnerid: partnerId,
                    appid: appid,
                });
            }
            return resultData;
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: 'findUMCPatientByExtraInfo',
                        nameParent: 'findUMCPatientByExtraInfo',
                        params: { userId, appid, partnerId, searchPatientExtraInfoDTO, cskhInfo },
                        errorCode: 'findUMCPatientByExtraInfo_02',
                    });
                    throw new HttpException(errorPatientNotFoundMesage, HttpStatus.NOT_FOUND);
                default:
                    this.emitService.emit(LOG_SERVICE_EVENT, {
                        name: 'findUMCPatientByExtraInfo',
                        nameParent: 'findUMCPatientByExtraInfo',
                        params: { userId, appid, partnerId, searchPatientExtraInfoDTO, cskhInfo },
                        errorCode: 'findUMCPatientByExtraInfo_03',
                    });
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async findPatientHis(userId: string, appid: string, partnerId: string, body: FindPatientHisDto, cskhInfo?: any, locale = 'vi') {

        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        const jwtVerify: any = jwt.verify(body.secretPatientId, jwtOptions.secret);

        const patientId = jwtVerify.patientId;
        const patient = await this.patientModel.findOne({ id: patientId }).exec();

        const patientCode = await this.getPatientCodeByPatientId(patientId, partnerId);

        if (patientCode) {
            throw new HttpException('Hồ sơ này đã có msbn', 409);
        }

        if (!patient) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ', 404);
        }

        const existed = await this.checkUMCPatientBelongsToUser(userId, patient.toObject()._id);
        if (!existed) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ', 404);
        }

        const patients = await this.findUMCPatientByExtraInfo(
            userId,
            appid,
            partnerId,
            {
                birthYear: patient.birthyear,
                cityId: patient.city_id,
                firstName: patient.name,
                surName: patient.surname,
                gender: patient.sex,
            },
            cskhInfo,
            patientId,
            locale,
        );

        const res = patients.map(p => {

            return {
                ...p,
                secretPatientId: jwt.sign({ patientId, patientHisId: p.id }, jwtOptions.secret, jwtOptions.signOptions),
            };
        }).filter(p => !!p.mobile);

        if (res.length === 0) {
            throw new HttpException('Không tìm thấy hồ sơ phù hợp!', 404);
        }

        return res;
    }

    async processSearchPatientByExtraInfoV1(
        userId: string,
        appid: string,
        partnerId: string,
        searchPatientExtraInfoDTO: SearchPatientExtraInfoMongoDTO,
        cskhInfo?: CSKHTokenVerifyDTO,
    ): Promise<any> {
        const params = { userId, appid, partnerId, ...searchPatientExtraInfoDTO, ...cskhInfo };
        try {
            const userMongo = await this.userModel.findById({ _id: userId }, { username: true, medproId: true }).exec();
            const userV1 = await this.checkExistsUserByUsername(userMongo.username);
            let session: any = {};
            /* tìm lại thông tin session theo partnerid */
            switch (partnerId) {
                case 'umc':
                    session = await this.sessionService.checkExistsUMCSessionByUserID(userV1.id);
                    break;
                case 'nhidong1':
                    session = await this.sessionService.checkExistsUMCSessionByUserID(userV1.id);
                    break;
                default:
                    break;
            }
            const { user_id, access_token } = session;
            /* tiến hành chuyển đổi */
            const baseUrl = this.urlConfigService.getBookingTreeUrl();
            const urlTransform = `${baseUrl}/his-connector/api/convertPartnerData`;
            const paramsTransform = {
                fromPartnerId: 'medpro',
                toPartnerId: partnerId,
                ...searchPatientExtraInfoDTO,
            };
            const dataKeys = (await this.transformDataPatientV1(urlTransform, paramsTransform).toPromise()).data;
            const searchInfo: FindPatientDto = {
                surname: searchPatientExtraInfoDTO.surName,
                name: searchPatientExtraInfoDTO.firstName,
                birthyear: searchPatientExtraInfoDTO.birthYear,
                sex: searchPatientExtraInfoDTO.gender,
                city_id: dataKeys.cityId,
                access_token,
                user_id,
            };
            return (await this.callGetPatientProfileV1(searchInfo, partnerId).toPromise()).data;
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'callGetPatientProfileV1',
                summary: 'Lấy thông tin BN từ V1',
                nameParent: 'findUMCPatientByExtraInfo',
                params,
                errorBody: this.utilService.errorHandler(error),
            });
            const { status = 404 } = error;
            switch (status) {
                case HttpStatus.NOT_FOUND:
                    throw new HttpException('Không tìm thấy thông tin bệnh nhân.', HttpStatus.NOT_FOUND);
                default:
                    throw new HttpException('Hiện tại hệ thống không xử lý được yêu cầu này. Vui lòng thử lại sau.', HttpStatus.FORBIDDEN);
            }
        }
    }

    async verifyUMCPatientByPhone(partnerId: string, appId: string, verifyPhonePatientDTO: VerifyPhonePatientMongoDTO): Promise<any> {

        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();

        let patientHisId;
        let payloadToken: any = {}

        if (verifyPhonePatientDTO.secretPatientId) {
            payloadToken = jwt.verify(verifyPhonePatientDTO.secretPatientId, jwtOptions.secret);
            patientHisId = payloadToken.patientHisId;
        } else {
            patientHisId = verifyPhonePatientDTO.patientId;
        }

        const infoObj = await this.getUMCPatientShortInfoVerifyPhoneByPatientId(partnerId, appId, patientHisId);

        if (!infoObj) {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }

        if (verifyPhonePatientDTO.msbn === `${infoObj.patientCode}`.trim() && verifyPhonePatientDTO.phone === infoObj.mobile) {
            // console.log(infoObj);
            const secretKey = jwt.sign({ patientId: patientHisId, updateToPatientId: payloadToken.patientId || '', phone: verifyPhonePatientDTO.phone }, jwtOptions.secret, jwtOptions.signOptions);
            // const info = await this.getPatientInfoAndPatientCodeById(partnerId, appId, infoObj._id);
            return {
                patient: { ...infoObj, secretKey },
                secretKey,
            };
        }
        throw new HttpException('Thông tin gửi lên không chính xác. Vui lòng kiểm tra lại!', HttpStatus.NOT_FOUND);
    }

    async verifyPatientByInsuranceCode(partnerId: string, appId: string, formData: VerifyInsuranceCodePatientMongoDTO, locale = 'vi'): Promise<any> {
        const infoObj = await this.getUMCPatientShortInfoVerifyInsuranceCodeByPatientId(partnerId, appId, formData.patientId);
        if (!infoObj) {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }
        if (formData.msbn === `${infoObj.patientCode}`.trim() && formData.insuranceCode === infoObj.insuranceCode) {
            const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
            const secretKey = jwt.sign({ patientId: formData.patientId }, jwtOptions.secret, jwtOptions.signOptions);
            return {
                patient: { ...infoObj, secretKey },
                secretKey,
            };
        }
        throw new HttpException('Thông tin gửi lên không chính xác. Vui lòng kiểm tra lại!', HttpStatus.NOT_FOUND);
    }

    async addPatientToUserUMCPatient(partnerId: string, appId: string, addPatientToUserDTO: AddPatientToUserDTO, userId: string,
                                     cskhInfo?: CSKHTokenVerifyDTO, locale: string = 'vi'): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.verifyPhoneJwtOptions();
        let patientId = '';
        let updateToPatientId = '';

        try {
            const jwtVerify: any = jwt.verify(addPatientToUserDTO.secretKey, jwtOptions.secret);
            patientId = jwtVerify.patientId;
            updateToPatientId = jwtVerify.updateToPatientId;
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            const nameJWTError = !!error.name ? error.name : '';
            if (nameJWTError === 'TokenExpiredError') {
                throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'JsonWebTokenError') {
                throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
            } else if (nameJWTError === 'NotBeforeError') {
                throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
            } else {
                throw new HttpException('Có lỗi xảy ra. Vui lòng thử lại', HttpStatus.BAD_REQUEST);
            }
        }
        /* xử lý quá trình add patient to user */
        const isCS = await this.userService.isCs(cskhInfo?.cskhUserId || userId);
        const isExam = addPatientToUserDTO.isExam ? addPatientToUserDTO.isExam : false;
        if (!isCS && !isExam) {
            const userWithPatients = await this.getCountPatientInUserPatientUMC(userId);
            if (userWithPatients.patients.length >= this.countPatient) {
                const message = await this.globalSettingService.findByKeyAndRepoName('PATIENT_V1_EXIST', null, locale);
                throw new HttpException(message, HttpStatus.FORBIDDEN);
            }
        }

        /* tìm lại thông tin patient _id */
        const findPatient = await this.patientModel.findOne({ id: patientId }).exec();

        if (updateToPatientId) {
            const updateToPatient = await this.patientModel.findOne({ id: updateToPatientId }).exec();

            const checkPatientCode = await this.patientCodeModel.findOne({ patientId: updateToPatient.id, partnerId }).exec();

            if (checkPatientCode) {
                throw new HttpException('Hồ sơ này đã có mã Bệnh Nhân', 409);
            } else {
                const patientCodeItem = await this.patientCodeModel.findOne({ patientId: findPatient.id, partnerId }).exec();

                if (patientCodeItem?.patientCode) {
                    await this.patientCodeModel.create({
                        id: `${partnerId}_${updateToPatient.id}`,
                        patientId: updateToPatient.id,
                        partnerId: partnerId,
                        patientCode: patientCodeItem.patientCode,
                        appId,
                    })

                    let updatePatientData : any = {
                        district_id: findPatient.district_id,
                        district: findPatient.district,
                        ward_id: findPatient.ward_id,
                        ward: findPatient.ward,
                        profession_id: findPatient.profession_id,
                        profession: findPatient.profession,
                        dantoc_id: findPatient.dantoc_id,
                        nation: findPatient.nation,
                        cmnd: findPatient.cmnd,
                        email: findPatient.email,
                        address: findPatient.address,
                    }

                    updatePatientData = pickBy(updatePatientData, value => !!value);

                    updateToPatient.set(updatePatientData);
                    await updateToPatient.save();

                    return this.getPatientInfoById(updateToPatient._id, partnerId, appId);
                } else {
                    throw new HttpException('Không tìm thấy mã Bệnh Nhân!', 400);
                }
            }
        }

        if (findPatient) {
            const patientObj = findPatient.toObject();
            /* kiểm tra xem đã có insert rồi hay chưa */
            const checkValue = await this.checkUMCPatientBelongsToUser(userId, patientObj._id);
            if (checkValue) {
                const message = await this.translateService.getCommonErrorMessage(ErrorMessageKey.PATIENT_ALREADY_ADD_TO_USER, locale)
                throw new HttpException(message, HttpStatus.CONFLICT);
            } else {
                /* kiểm tra tiếp xem còn lọt case nào không */
                const checkExists = await this.checkExistsUMCPatientBelongsToUser(userId, findPatient);
                if (checkExists) {
                    const message = await this.translateService.getCommonErrorMessage(ErrorMessageKey.PATIENT_ALREADY_ADD_TO_USER, locale)
                    throw new HttpException(message, HttpStatus.CONFLICT);
                }
            }

            try {
                /* tiến hành sync xuống v1 */
                await this.syncUserPatientV1(partnerId, userId, findPatient);
            } catch (error) {
                console.log(error);
                this.clientSentry.instance().captureException(error);
            }

            if (addPatientToUserDTO?.relationType && !isEmpty(addPatientToUserDTO?.relationType) ) {
                const { relationType: id } = addPatientToUserDTO;
                const relation = await this.relativeModel.findOne({ id }).exec();
                const dataRelation = {
                    user: userId,
                    // patientId: afterUpdate.id,
                    patient: findPatient._id,
                    relationTypeId: relation?.id,
                    relationType: relation?._id,
                };
                const findPatientRelation = await this.patientRelationModel.findOne({ patient: findPatient._id }).exec();
                if (findPatientRelation) {
                    await this.patientRelationModel.findByIdAndUpdate(findPatientRelation._id, dataRelation);
                } else {
                    const newPatientRelation = new this.patientRelationModel(dataRelation);
                    await newPatientRelation.save();
                }

                // const { relationType: id } = addPatientToUserDTO;
                // const relation = await this.relativeModel.findOne({ id }).exec();
                // const newPatientRelation = new this.patientRelationModel({
                //     user: userId,
                //     patientId: patientObj.id,
                //     relationTypeId: relation?.id,
                //     relationType: relation?._id,
                // });
                // await newPatientRelation.save();
            }

            /* tiến hành apply hồ sơ bệnh nhân vào trong user id */
            try {
                await this.addUserPatient(userId, patientObj._id);
                const trackingData : PatientTrackingData = {
                    action: 'ADD_OLD_PATIENT_TO_USER',
                    patient: findPatient._id,
                    userPatient: userId,
                    userAction: userId,
                    dataAfter: patientObj,
                };

                this.emitService.emit(PATIENT_TRACKING_EVENT, trackingData);

                return this.getPatientInfoById(patientObj._id, partnerId, appId);
            } catch (error) {
                this.clientSentry.instance().captureException(error);
                throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
            }
        } else {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }

    }

    async syncAddPatientToUser(user: IUser, patient: IPatient): Promise<any> {
        const userId = user._id;
        const checkValue = await this.checkUMCPatientBelongsToUser(userId, patient._id);
        if (checkValue) {
            return null;
        } else {
            /* kiểm tra tiếp xem còn lọt case nào không */
            const checkExists = await this.checkExistsUMCPatientBelongsToUser(userId, patient);
            if (checkExists) {
                return null;
            }
        }
        /* tiến hành apply hồ sơ bệnh nhân vào trong user id */
        try {
            await this.addUserPatient(userId, patient._id);
            return true;
        } catch (error) {
            this.clientSentry.instance().captureException(error);
            return null;
        }
    }

    async syncUserPatientV1(partnerId: string, userId: string, patient: IPatient): Promise<any> {
        const userMongo = await this.userModel.findById({ _id: userId }, { username: true, medproId: true }).exec();
        const userV1 = await this.checkExistsUserByUsername(userMongo.username);
        const keyPatientV1 = this.utilService.patientIdV1Key(partnerId);
        try {
            await this.sessionService.addPatientToUserV1(partnerId, userV1.id, patient, keyPatientV1);
        } catch (error) {
            this.clientSentry.instance().captureMessage(`Sync user cho phần addPatientToUserV1 failed  ${partnerId} - ${userId} - ${patient.id}`);
            this.clientSentry.instance().captureException(error);
        }
    }

    async unLinkPatient(id: string, userId: string): Promise<any> {
        const countBooking = await this.bookingModel.count({ partnerId: 'bvmathcm', patientId: id, status: { $in: [1, 2] } }).exec();
        const envDisableUpdateDeletePatient = await this.globalSettingService.findByKeyAndRepoName('ENV_UPDATE_DELETE_PATIENT');

        if (countBooking >= 1 && envDisableUpdateDeletePatient === 'ON') {
            throw new HttpException('Để bảo mật thông tin bệnh nhân - Vui lòng liên hệ 19002115 để xoá hồ sơ', HttpStatus.BAD_REQUEST);
        }

        const findPatient = await this.patientModel.findOne({ id }).exec();
        if (!findPatient) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân', HttpStatus.NOT_FOUND);
        } else {
            const patientObj = findPatient.toObject();
            const checkValue = await this.checkUMCPatientBelongsToUser(userId, patientObj._id);
            if (checkValue) {
                /* tiến hành lấy unlink */
                const userInfoWithPatients = await this.getCountPatientInUserPatientUMC(userId);
                const remainPatients = userInfoWithPatients.patients.filter(item => {
                    return `${item}` !== `${patientObj._id}`;
                });

                // await this.unlinkPatientV1(userId, patientObj);

                const userMongo = await this.userModel.findById({ _id: userId }).exec();
                userMongo.patients = remainPatients;
                await userMongo.save();
                findPatient.userId = null;
                await findPatient.save();
                this.emitService.emit(REMOVE_PATIENT_V1, { patientObj, userMongo, partnerId: findPatient.partnerId });

                this.emitService.emit(PATIENT_TRACKING_EVENT, {
                    action: 'UNLINK',
                    patient: findPatient._id,
                    userAction: userId,
                    userPatient: userId,
                });

                return {
                    unlink: true,
                    message: 'Thao tác thành công.',
                };
            } else {
                throw new HttpException('Hồ sơ bệnh nhân này không thuộc user hiện tại.', HttpStatus.BAD_REQUEST);
            }
        }
    }

    async getPatientDetailForUpdate(id: string, userId: string, partnerId: string, appId: string, cskhInfo?: CSKHTokenVerifyDTO): Promise<any> {
        const findPatient = await this.patientModel.findOne({ id }).exec();
        if (!findPatient) {
            throw new HttpException('Không tìm thấy thông tin hồ sơ bệnh nhân', HttpStatus.NOT_FOUND);
        } else {
            const patientObj = findPatient.toObject();
            const checkValue = await this.checkUMCPatientBelongsToUser(userId, patientObj._id);
            if (checkValue) {
                /* tiến hành lấy thông tin detail */
                const info = await this.getPatientInfoAndPatientCodeById(partnerId, appId, patientObj._id);
                /* lấy thông tin patient Code */
                // const patientCodes = await this.patientCodeModel.findOne({ partnerId, patientId: patientObj.id }).exec();
                // console.log(info);
                // let patientCode = patientObj.code;
                // let isUpdateFull = true;
                // if (appId !== 'medpro') {
                //     if (patientCodes) {
                //         isUpdateFull = false;
                //         patientCode = patientCodes.patientCode;
                //     }
                // }
                const patientsWithRelation = await this.getRelationPatient([info]);
                const isUpdateFullObj = await this.checkPatientHisConstraint(partnerId, cskhInfo?.cskhUserId || userId, info?.id);

                let defaultPhoneLocaleObject: any;
                if (info.mobile?.startsWith('0') || info.relation?.relative_mobile?.startsWith('0')) {
                    defaultPhoneLocaleObject = await this.getPatientPhoneLocaleObject('vi-VN');
                }

                return {
                    ...first(patientsWithRelation),
                    // ...info,
                    // patientCode,
                    isUpdateFull: !!info.insuranceCode ? false : true,
                    ...isUpdateFullObj,
                    // mobile: info.isUpdateFull ? info.mobile : this.secretMobile(info.mobile),
                    mobileLocaleConfig: info.mobile?.startsWith('0') ? defaultPhoneLocaleObject : await this.getPatientPhoneLocaleObject(info.mobileLocaleIso),
                    relativeMobileLocaleConfig: info.relation?.relative_mobile?.startsWith('0') ? defaultPhoneLocaleObject : await this.getPatientPhoneLocaleObject(info.relation?.relativeMobileLocaleIso),
                };
            } else {
                throw new HttpException('Hồ sơ bệnh nhân này không thuộc user hiện tại.', HttpStatus.BAD_REQUEST);
            }
        }
    }

    async getPatientPhoneLocaleObject(iso: string) {
        if (!iso) {
            return null;
        }

        const [languageCode, countryCode] = iso.split('-');
        const cfs = JSON.parse(await this.globalSettingService.findByKeyAndRepoName('CONFIG_PHONE_USER_COUNTRY_PATIENT'));
        return cfs[countryCode]
    }

    async addUserPatient(userId: string, id: string): Promise<any> {
        /* add thông tin hồ sơ vào trong user */
        const userMongo = await this.userModel.findById({ _id: userId }).exec();
        userMongo.patients.push(id);
        await userMongo.save();
    }

    async checkUMCPatientBelongsToUser(userId: string, patientId: string): Promise<any> {
        /* Kiểm tra xem patient này đã thuộc về user hay chua */
        const userInfoWithPatients = await this.getCountPatientInUserPatientUMC(userId);
        const findIndexValue = userInfoWithPatients.patients.indexOf(patientId);
        if (findIndexValue > -1) {
            return true;
        } else {
            return false;
        }
    }

    // async checkExistsUMCPatientBelongsToUser2(): Promise<any> {
    async checkExistsUMCPatientBelongsToUser(userId: string, patient: IPatient): Promise<any> {
        const getPatientCode = await this.patientCodeModel.findOne({ patientId: patient.id }).exec();
        if (getPatientCode) {
            const patientCode = get(getPatientCode.toObject(), 'patientCode', '');
            if (patientCode) {
                // const userId = '5f9a1512cb9fca001958236c';
                // const patientCode = '200006881111';
                const listPatientsCheck = await this.getAllPatientsByUserIdSimpleGetIds(userId);
                if (listPatientsCheck.length > 0) {
                    const transformList = listPatientsCheck.map(item => item.toObject());
                    const mapIds = map(transformList, 'id');
                    /* tìm lại tất cả patientCode */
                    const listPatientCode = await this.patientCodeModel.find({
                        patientId: { $in: mapIds },
                    }, { patientCode: true }).exec();
                    if (listPatientCode.length > 0) {
                        /* tìm xem patientCode có thuộc trong list này hay ko */
                        const findPatientCode = find(listPatientCode, { patientCode });
                        if (typeof findPatientCode !== typeof undefined) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    async getCountPatientInUserPatientUMC(userId: string): Promise<any> {
        return this.userModel.findById({ _id: userId }, { patients: true }).exec();
    }

    async getUMCPatientShortInfoVerifyPhoneByPatientId(partnerId: string, appId: string, id: string): Promise<any> {
        const getId = await this.patientModel.findOne({ id }, { id: true }).exec();
        return this.getPatientInfoAndPatientCodeById(partnerId, appId, getId._id);
    }

    async getUMCPatientShortInfoVerifyInsuranceCodeByPatientId(partnerId: string, appId: string, id: string): Promise<any> {
        const getId = await this.patientModel.findOne({ id }, { id: true }).exec();
        return this.getPatientInfoAndPatientCodeById(partnerId, appId, getId._id);
    }

    async updateUMCSyncPatient(id: string, patient): Promise<any> {
        const objBirthDate: any = {};
        const { NgaySinh, isExam = false } = patient;
        if (NgaySinh && moment(NgaySinh, 'YYYY-MM-DD').isValid()) {
            objBirthDate.birthdate = moment(NgaySinh, 'YYYY-MM-DD').format('YYYY-MM-DD');
        }

        const objName: any = {};
        const getTen = get(patient, 'Ten', null);
        const getHo = get(patient, 'Ho', null);
        if (getTen) {
            objName.name = `${getTen}`.trim();
        }
        if (getHo) {
            objName.surname = `${getHo}`.trim();
        }

        /* Kiểm tra xem có thông tin insuranceCode */
        const getInsuranceCode = get(patient, 'insuranceCode', null);
        if (getInsuranceCode) {
            /* cập nhật lại thông tin mã bảo hiểm y tế theo */
            const getSoHS = get(patient, 'SoHS', null);
            const getPartnerId = get(patient, 'partnerId', null);
            try {
                await this.patientCodeModel.findOneAndUpdate({
                    patientId: id,
                    patientCode: getSoHS,
                    partnerId: getPartnerId,
                }, {
                    insuranceCode: getInsuranceCode,
                }, { new: true }).exec();
            } catch (error) {
                console.log(error);
                this.clientSentry.instance().captureException(error);
            }
        }

        /* kiểm tra relation */
        const getRelation = get(patient, 'relative', {});

        const dataTransformUpdate: any = {
            mobile: (patient.DiDong ? patient.DiDong : patient.DienThoai),
            ...objBirthDate,
            birthyear: patient.NamSinh,
            cmnd: patient.SoCMND,
            sex: patient.GioiTinh,
            ...objName,
            isExam,
            ...getRelation
        };


        this.emitService.emit(LOG_SERVICE_EVENT, {
            name: `getPatientByHIS_updateDB`,
            summary: 'Lấy thông tin BN từ HIS nhidong1',
            nameParent: 'findbymsbn',
            params: {
                ...dataTransformUpdate
            },
            errorBody: null,
        });

        try {
            return await this.patientModel
                .findOneAndUpdate({ id }, { ...dataTransformUpdate }, { new: true })
                .exec();
        } catch (error) {
            console.log(error);
            // this.clientSentry.instance().captureException(error);
            throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
        }
    }

    secretMobile(mobile: string) {
        try {
            const get2charLast = mobile ? mobile.slice(-3) : '';
            const secretMobile = mobile ? `${mobile.slice(0, -7)}xxxx${get2charLast}` : '';
            return secretMobile;
        } catch (error) {
            return mobile;
        }
    }

    secretMobileCSTool(mobile: string) {
        try {
            const get2charLast = mobile ? mobile.slice(-4) : '';
            const secretMobile = mobile ? `${mobile.slice(0, -8)}xxxx${get2charLast}` : '';
            return secretMobile;
        } catch (error) {
            return mobile
        }
    }

    async insertUMCSyncPatient(patient: UMCPatientResponseDTO): Promise<any> {
        const {
            Ten, Ho, DiDong, DienThoai, DiaChi, GioiTinh, IDDanToc, IDNgheNghiep, IDPhuongXa,
            IDQuanHuyen, IDTinh, MaQuocGia, NamSinh, NgaySinh, SoCMND, SoHS, partnerId, isExam = false,
            relative
        } = patient;
        const medproId = this.generateMedproID(6);
        const uuidv4 = uuid.v4();
        /* tìm lại ref */
        const ref: any = {};
        const findCountry = await this.countryModel.findOne({ code: MaQuocGia, partnerId: 'medpro' }).exec();
        if (findCountry) {
            const countryObj = findCountry.toObject();
            ref.country = countryObj._id;
        }

        const findNation = await this.nationModel.findOne({ id: IDDanToc }).exec();
        if (findNation) {
            const nationObj = findNation.toObject();
            ref.nation = nationObj._id;
        }
        const findCity = await this.cityModel.findOne({ id: IDTinh }).exec();
        if (findCity) {
            const cityObject = findCity.toObject();
            ref.city = cityObject._id;
        }
        const findDistrict = await this.districtModel.findOne({ id: IDQuanHuyen }).exec();
        if (findDistrict) {
            const districtObj = findDistrict.toObject();
            ref.district = districtObj._id;
        }
        const findWard = await this.wardModel.findOne({ id: IDPhuongXa }).exec();
        if (findWard) {
            const wardObj = findWard.toObject();
            ref.ward = wardObj._id;
        }
        if (!!IDNgheNghiep) {
            const findProfession = await this.professionModel.findOne({ id: IDNgheNghiep }).exec();
            if (findProfession) {
                const professionObj = findProfession.toObject();
                ref.profession = professionObj._id;
                ref.profession_id = IDNgheNghiep;
            }
        } else {
            ref.profession_id = '';
        }
        const objBirthDate: any = {};
        if (NgaySinh && moment(NgaySinh, 'YYYY-MM-DD').isValid()) {
            objBirthDate.birthdate = moment(NgaySinh, 'YYYY-MM-DD').format('YYYY-MM-DD');
        }

        let relativeObject:any = {};

        if (typeof relative !== typeof undefined && Object.keys(relative).length > 0) {
            relativeObject = {
                relation: {
                    relative_name: relative?.fullname || '',
                    relative_email: relative?.email || '',
                    relative_mobile: relative.phone || '',
                    relative_type_id: relative.relativeType || ''
                }
            };
        }

        const info = {
            name: `${Ten}`.trim(),
            surname: `${Ho}`.trim(),
            cmnd: SoCMND,
            sex: GioiTinh,
            mobile: (DiDong ? DiDong : DienThoai),
            dantoc_id: IDDanToc,
            // profession_id: IDNgheNghiep,
            country_code: MaQuocGia,
            city_id: IDTinh,
            district_id: IDQuanHuyen,
            ward_id: IDPhuongXa,
            address: DiaChi,
            ...objBirthDate,
            birthyear: NamSinh,
            patientCode: SoHS,
            code: medproId,
            id: uuidv4.replace(/-/g, ''),
            ...ref,
            isExam,
            partnerId,
            ...relativeObject
        };
        // console.log(info);
        return this.insertPatientInfo(info);
    }

    async checkExistsPatientByMsbnMedproId(partnerId: string, msbn: string, appId?: string): Promise<any> {
        return this.patientCodeModel.findOne({ ...(appId && { appId }), patientCode: msbn, partnerId }).read('primary').exec();
    }

    getPatientByHIS(partnerId: string, patientCode: string): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const encode = encodeURIComponent(patientCode)
        const url = `${baseUrl}/his-connector/api/patient/getByPatientCode/${partnerId}/${encode}`;
        return this.httpService.get(url);
    }

    proxyFindByMsbn(params: any, partnerId: string): Observable<AxiosResponse<any>> {
        const url = this.oldUrl.NhiDong1GetByMSBN();
        return this.httpService.post(url, { ...params }, {
            headers: {
                partnerid: partnerId,
            },
        });
    }

    getPatientsByUserIdNhiDong1(url: string): Observable<AxiosResponse<any>> {
        return this.httpService.get(url);
    }

    insertPatientNhiDong1(url: string, params: any, partnerId: string): Observable<AxiosResponse<any>> {
        // console.log(url, params)
        return this.httpService.post(url, { ...params }, {
            headers: {
                partnerid: partnerId,
            },
        });
    }

    updatePatientNhiDong1(url: string, params: any, partnerId: string): Observable<AxiosResponse<any>> {
        // console.log(url, params)
        return this.httpService.post(url, { ...params }, {
            headers: {
                partnerid: partnerId,
            },
        });
    }

    getPatientExtraByHIS(partnerId: string, data: any): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/his-connector/api/patient/getByPatientDetail`;
        const { Ten, Ho, GioiTinh, NamSinh, IDTinh } = data;
        return this.httpService.post(url, {
            partnerId,
            Ten,
            Ho,
            GioiTinh,
            NamSinh,
            IDTinh,
        });
    }

    getPatientExtraByInsuranceCodeHIS(partnerId: string, insuranceId: any): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/his-connector/api/patient/getByPatientDetail`;
        return this.httpService.post(url, {
            partnerId,
            insuranceId,
        });
    }

    getPatientExtraByPhoneHIS(partnerId: string, Phone: string): Observable<AxiosResponse<any>> {
        const baseUrl = this.urlConfigService.getBookingTreeUrl();
        const url = `${baseUrl}/his-connector/api/patient/getByPatientDetail`;
        return this.httpService.post(url, {
            partnerId,
            Phone,
        });
    }

    getInsuranceInfoHIS(url: string, data: any): Observable<AxiosResponse<any>> {
        const { fullName, insuranceId, birthday, partnerId } = data;
        return this.httpService.post(url, {
            fullName,
            insuranceId,
            birthday,
            partnerId,
        });
    }

    getInfoParseAddress(url: string, data: any): Observable<AxiosResponse<any>> {
        return this.httpService.post(url, {
            ...data,
        });
    }

    async findUserIdByUserPhone(userPhone: string): Promise<any> {
        // Format phone number
        const userPhoneInfo = userPhone
            .replace(/^[+]84|^0/, '+84')
            .replace(/^84/, '+84')
            .replace(/^9/, '+849')
            .replace(/^3/, '+843');
        try {
            const user = await this.userModel.findOne({ username: userPhoneInfo }).exec();
            if (!user) {
                throw new HttpException(`Not found userId by userPhone: ${userPhone}`, HttpStatus.BAD_REQUEST);
            }
            return user._id;
        } catch (error) {
            Logger.error(`Can't getUserIdByUserPhone() with userPhone: ${userPhone}\nError: ${error.message}`);
            throw error;
        }
    }

    async handleParams(fromData: ConstraintSearchLogDTO): Promise<any> {
        const userId = await this.findUserIdByUserPhone(fromData.userPhone);
        const { partnerId = '' } = fromData;
        let params: any = { userId };
        if (partnerId) {
            params = { ...params, partnerId };
        }
        return params;
    }

    async getTotalRows(fromData: ConstraintSearchLogDTO): Promise<number> {
        const params = await this.handleParams(fromData);
        try {
            return this.patientSearchLogModel.find({  ...params }).countDocuments();
        } catch (err) {
            Logger.error(`Error when exec getTotalRows() with userPhone: ${fromData.userPhone}\nError: ${err.message}`);
            throw err;
        }
    }

    async transferDataConstraintSearchLog(fromData: ConstraintSearchLogDTO, patientSearchLog: IPatientSearchLog[]) {
        const totalRows = await this.getTotalRows(fromData);
        const { pageSize, pageIndex } = fromData;
        // Custom response
        const response = {
            totalRows,
            pageSize,
            pageIndex,
            rows: patientSearchLog,
        };
        return response;
    }

    async getConstraintSearchLog(fromData: ConstraintSearchLogDTO): Promise<any> {
        const { pageIndex, pageSize } = fromData;
        const params = await this.handleParams(fromData);
        try {
            const searchLog = await this.patientSearchLogModel
                .find({ ...params })
                .sort({ createdAt: 'desc' })
                .skip(pageIndex)
                .limit(pageSize)
                .exec();
            return this.transferDataConstraintSearchLog(fromData, searchLog);
        } catch (error) {
            Logger.error(`Can't getConstraintSearchLog() with userPhone: ${fromData.userPhone}\nError: ${error.message}`);
            throw error;
        }
    }

    async deleteConstraintSearchLog(id: []): Promise<any> {
        try {
            const deleteInfo = await this.patientSearchLogModel.deleteMany({ _id: { $in: id } }).exec();
            return { status: 200, message: 'deleted' };
        } catch (error) {
            Logger.error(`Can't deleteConStraintSearchLog() with id: ${id}\nError: ${error.message}`);
            throw error;
        }
    }

    callGetPatientProfileV1(formData: FindPatientDto, partnerId: string): Observable<AxiosResponse<any>> {
        const baseUrl = `${this.oldUrl.NhiDong1RestfulAPI()}/patient/searchbyinfo`;
        return this.httpService.post(baseUrl, { ...formData }, { headers: { partnerid: partnerId } });
    }

    async addPatientXnc(userId: string, formData: PatientXcDto): Promise<any> {
        // try {
        //     const { countryCode } = formData;
        //     const user = await this.getUser(userId);
        //     const patient = await this.patientModel.findOne({ id: formData.patientId }).exec();
        //     if (!patient) {
        //         throw new HttpException('Bệnh nhân không tồn tại', HttpStatus.NOT_FOUND);
        //     }
        //     const isHavePatient = user?.patients.filter((element: any) => element === patient.id);
        //     if (!isHavePatient) {
        //         throw new HttpException('Bệnh nhân không thuộc user', HttpStatus.BAD_REQUEST);
        //     }
        //     const country = await this.countryModel.findOne({ code: countryCode, partnerId: 'medpro' }).exec();
        //     if (!country) {
        //         throw new HttpException('Không tìm thấy thông tin quốc gia', HttpStatus.NOT_FOUND);
        //     }
        //     // create patient xnc
        //     const patientXnc = await this.patientXncModel.create({ ...formData, country: country._id });
        //     return this.patientModel
        //         .findByIdAndUpdate(patient._id, { patientXnc: patientXnc._id })
        //         .populate({ path: 'patientXnc' })
        //         .exec();
        // } catch (error) {
        //     this.logger.error(`Error when exec addPatientXnc. Cause: ${error.message}`);
        //     throw error;
        // }
    }

    async getUser(userId: string): Promise<any> {
        return this.userModel.findById(userId).exec();
    }

    async getPatientForReExams(patientId: string): Promise<any> {
        const patient = await this.patientModel.findOne({ id: patientId }).exec();
        const { _id, ...rest } = patient.toObject();
        let fullAddress = '';
        const [findWard, findDistrict, findCity ] = await Promise.all([
            this.wardModel.findOne({ id: patient.ward_id }).exec(),
            this.districtModel.findOne({ id: patient.district_id }).exec(),
            this.cityModel.findOne({ id: patient.city_id }).exec(),
        ]);
        // address
        fullAddress = !!patient.address ? `${patient.address}`.trim() : fullAddress;
        // ward
        fullAddress = findWard ? `${fullAddress}, ${findWard.toObject().name}` : fullAddress;
        // district
        fullAddress = findDistrict ? `${fullAddress}, ${findDistrict.toObject().name}` : fullAddress;
        // city
        fullAddress = findCity ? `${fullAddress}, ${findCity.toObject().name}` : fullAddress;
        return {
            ...rest,
            fullAddress,
        };
    }

    async getPatientsByPhone(mobile: string, patientsUser: string[]): Promise<any> {
        const patients = await this.patientModel
            .find(
                { mobile, id: { $nin: [...patientsUser] } },
                { address: true, id: true, name: true, surname: true, birthdate: true, birthyear: true, mobile: true, code: true, sex: true },
            )
            // .populate('profession')
            // .populate('country')
            // .populate('nation')
            .populate({ path: 'city', select: { name: true } })
            .populate({ path: 'district', select: { name: true } })
            .populate({ path: 'ward', select: { name: true } })
            .sort({ createdAt: 'desc' })
            .exec();
        if (!patients.length) {
            return [];
        }
        const overidePatient = patients.map(patient => {
            const resultPatient = patient ? {...patient.toObject(),  isValid: false, mobile: this.secretMobileCSTool(patient?.mobile || '') } : {};
            return resultPatient;
        });
        return overidePatient;
    }

    async updatePaitentVersionBooking(patientId: string, patientVersion: string, patientVersionId: string): Promise<any> {
        this.logger.log(`Trace for updatePaitentVersionBooking. Payload: ${JSON.stringify({patientId, patientVersion}, null, 2)}`);

        const fromDate = moment().set({ hours: 0, minutes: 0, seconds: 0 }).toISOString();

        try {
            return this.bookingModel
                .updateMany(
                    { patient: patientId, date: { $gte: new Date(fromDate) } },
                    { patientVersion, patientVersionId },
                )
                .exec();
        } catch (error) {
            this.logger.error(`Error when exec updatePaitentVersionBooking. Cause: ${error}`);
        }
    }

    async createPatientIntoUserCskh(patientId: string, userMongoId: string, partnerId: string, appid: string, locale = 'vi'): Promise<any> {
        /* tìm lại thông tin patient _id */
        const findPatient = await this.patientModel.findOne({ id: patientId }).exec();
        if (findPatient) {
            const patientObj = findPatient.toObject();
            /* kiểm tra xem đã có insert rồi hay chưa */
            const checkValue = await this.checkUMCPatientBelongsToUser(userMongoId, patientObj._id);
            if (checkValue) {
                const message = await this.translateService.getCommonErrorMessage(ErrorMessageKey.PATIENT_ALREADY_ADD_TO_USER, locale)
                throw new HttpException(message, HttpStatus.CONFLICT);
            } else {
                /* kiểm tra tiếp xem còn lọt case nào không */
                const checkExists = await this.checkExistsUMCPatientBelongsToUser(userMongoId, findPatient);
                if (checkExists) {
                    const message = await this.translateService.getCommonErrorMessage(ErrorMessageKey.PATIENT_ALREADY_ADD_TO_USER, locale)
                    throw new HttpException(message, HttpStatus.CONFLICT);
                }
            }
            try {
                /* tiến hành sync xuống v1 */
                await this.syncUserPatientV1(findPatient.partnerId, userMongoId, findPatient);
            } catch (error) {
                console.log(error);
                this.clientSentry.instance().captureException(error);
            }
            /* tiến hành apply hồ sơ bệnh nhân vào trong user id */
            try {
                await this.addUserPatient(userMongoId, patientObj._id);
                return this.getPatientInfoById(patientObj._id, partnerId, appid);
            } catch (error) {
                this.clientSentry.instance().captureException(error);
                throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
            }
        } else {
            throw new HttpException('Không tìm thấy thông tin bệnh nhân', HttpStatus.NOT_FOUND);
        }
    }

    async checkBookingND1OfPatient(patientId: string): Promise<boolean> {
        return this.bookingModel.exists({ patient: patientId, date: { $gte: new Date() } });
    }

    async verifyUserTokenModeCskh(cskhToken: string): Promise<any> {
        const jwtOptions: JwtModuleOptions = this.jwtUserConfigService.createJwtOptions();
        let cskhInfo: any;
        if (typeof cskhToken !== typeof undefined && `${cskhToken}`.trim() !== '') {
            try {
                const jwtVerify: any = jwt.verify(last(cskhToken.split(' ')), jwtOptions.secret);
                /* Kiểm tra lại thông tin cskhInfo */
                const { userMongoId } = jwtVerify;
                if (userMongoId) {
                    const cskhUser = await this.findUserByRefId(userMongoId);
                    if (!cskhUser) {
                        throw new HttpException('Vui lòng kiểm tra lại thông tin user cskh', HttpStatus.UNAUTHORIZED);
                    }
                }
                return {
                    cskhUserId: userMongoId,
                };
            } catch (error) {
                const nameJWTError = !!error.name ? error.name : '';
                if (nameJWTError === 'TokenExpiredError') {
                    throw new HttpException('secret key hết hạn', HttpStatus.BAD_REQUEST);
                } else if (nameJWTError === 'JsonWebTokenError') {
                    throw new HttpException('secret key không đúng ', HttpStatus.BAD_REQUEST);
                } else if (nameJWTError === 'NotBeforeError') {
                    throw new HttpException('secret key chưa active', HttpStatus.BAD_REQUEST);
                } else {
                    throw new HttpException('Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại.', HttpStatus.BAD_REQUEST);
                }
            }
        } else {
            return cskhInfo;
        }
    }

    async handleQrcodeBHYTFromHis(data: DataBhytDto, patient: any, partnerId: string): Promise<string> {
        const partnerConfig = await this.partnerConfigModel.findOne({ partnerId }, {
            qrCodeConfig: 1,
        }).exec();
        let {
            templateQrCodeContent = '',
            charSeparateQrcodeContent = '|',
            defaultEmptyValue = '-',
        } = partnerConfig?.qrCodeConfig;

        if (templateQrCodeContent) {

            // qui định giới tính từng bệnh viện
            switch (partnerId) {
                case 'hoanmytd':
                case 'hoanmyvp1':
                    patient.sex = `${patient.sex === 0 ? 2 : 1 }`;
                    break;
                default:
                    break;
            }
            // data prepare to fill
            const dataFillTemplate = {
                ...data,
                patient: {
                    ...patient,
                    address: `${this.getFullAddress(patient)}`,
                    birthdate: moment(patient?.birthdate) ? moment(patient.birthdate).format('DD/MM/YYYY') : `${patient.birthyear}`,
                    fullname: `${patient.surname} ${patient.name}`,
                },
                insuranceCode: defaultEmptyValue,
                maKv: `${data?.maKv && !isEmpty(data.maKv) ? data.maKv : '4'}`,
                insuranceFromDate: moment(data.insuranceFromDate).format('DD/MM/YYYY'),
                insuranceToDate: moment(data.insuranceToDate).format('DD/MM/YYYY'),
                ngayCap: moment(data.insuranceFromDate).format('DD/MM/YYYY'),
            };

            const arrTemplates = templateQrCodeContent.split(charSeparateQrcodeContent);
            arrTemplates.pop();
            for (const itemTemplate of arrTemplates) {
                const arrItemTemplate = itemTemplate.split(':');
                const checkEncode = last(arrItemTemplate);
                const keyString = first(arrItemTemplate);
                const prop = get(dataFillTemplate, keyString);

                if (checkEncode === 'hex') {
                    set(dataFillTemplate, keyString, Buffer.from(prop).toString('hex'));
                    const newProp = get(dataFillTemplate, keyString);
                    templateQrCodeContent = templateQrCodeContent
                        .replace(':hex', '')
                        .replace(keyString, newProp && !isEmpty(newProp) ? newProp : defaultEmptyValue);
                } else {
                    templateQrCodeContent = templateQrCodeContent.replace(keyString, (prop && !isNil(prop)) || isNumber(prop) ? prop : defaultEmptyValue);
                }
            }
        }
        return templateQrCodeContent;
    }

    async testQrCodeBhytHis(): Promise<any> {
        const patient = await this.patientModel.findById('62412371e38d36001aaad7c1');
        const data: any = {
            insuranceToDate: 1672505999000,
            insuranceFromDate: 1640970000000,
            maThe: "DN4796020112660",
            lastCheckInHospital: null,
            maCqBHXH: '',
            nameParent: '',
            maKtCqBHXH: '',
            ngayCap: 1672505999000,
        };
        return this.handleQrcodeBHYTFromHis(data, { ...patient.toObject() }, 'hoanmytd' );
    }

    async getRelationPatient(patients: any): Promise<any> {
        const list = patients.map(patient => patient._id)
        const [patientRelations, anotherRelationJson] = await Promise.all([
            this.patientRelationModel
                .find({
                    patient: { $in: [...list] },
                })
                .populate({
                    path: 'relationType',
                    select: { id: true, name: true, image: true },
                })
                .exec(),
            this.globalSettingService.findByKeyAndRepoName('PATIENT_RELATION_DEFAULT'),
        ]);
        const groupPatient = groupBy(patientRelations, 'patient');
        const anotherRelation = anotherRelationJson ? JSON.parse(anotherRelationJson) : null;
        return patients.map(patient => {
            const patientRelation = first(groupPatient[patient._id]);
            if (patientRelation) {
                const patientRelationObj = patientRelation.toObject();
                if (patientRelationObj?.relationType) {
                    const {
                        id = anotherRelation?.id,
                        name = anotherRelation?.name,
                        image = anotherRelation?.image,
                    } = patientRelationObj.relationType;
                    return {
                        ...patient,
                        patientRelation: { id, name, image },
                    };
                } else {
                    return {
                        ...patient,
                        patientRelation: anotherRelation,
                    };
                }
            } else {
                return {
                    ...patient,
                    patientRelation: anotherRelation,
                };
            }
        });
    }

    // async checkConstrainsPatientDetail(patients: any, locale?: string, partnerId?: string): Promise<any> {
    //     const [colorConstraint, patientReltaionDefault, ignorePatientWithoutVie, partnerCheckRelative, partnerConfig] = await Promise.all([
    //         this.globalSettingService.findByKeyAndRepoName('CONSTRAINS_COLOR'),
    //         this.globalSettingService.findByKeyAndRepoName('PATIENT_RELATION_DEFAULT', null, locale),
    //         this.globalSettingService.findByKeyAndRepoName('IGNORE_PATIENT_WITHOUT_VIE'),
    //         this.globalSettingService.findByKeyAndRepoName('PARTNER_PATIENT_CONSTRAINT_RELATIVE'),
    //         this.partnerConfigModel.findOne({ partnerId }, { patientYearOldAccepted: true }).exec(),
    //     ]);

    //     const constraintPatientGlobalName = `CONSTRAINS_PATIENT_DETAIL${partnerId ? `_${partnerId}`.toUpperCase() : ''}`;
    //     let constraintPatientDetailStr = await this.globalSettingService.findByKeyAndRepoName(constraintPatientGlobalName, null, locale);
    //     if (!constraintPatientDetailStr) {
    //         constraintPatientDetailStr = await this.globalSettingService.findByKeyAndRepoName('CONSTRAINS_PATIENT_DETAIL', null, locale);
    //     }

    //     const constraintPatientDetails = JSON.parse(constraintPatientDetailStr);
    //     const groupConstraint = groupBy(constraintPatientDetails, 'key');
    //     const patientReltaionDefaultObj = JSON.parse(patientReltaionDefault);
    //     const ignorePatientWithoutVieObj = JSON.parse(ignorePatientWithoutVie);
    //     if (constraintPatientDetails && constraintPatientDetails.length > 0) {
    //         patients = patients.map(patient => {
    //             let constraintPatientDetailObj = this.utilService.tranformArrToObj(constraintPatientDetails);
    //             // xu lý case relative cho nd1
    //             if (partnerConfig?.patientYearOldAccepted) {
    //                 const checkAge = this.checkAgePatient(patient, partnerConfig?.patientYearOldAccepted);
    //                 // trên patientYearOldAccepted thì bỏ qua case valid thân nhân
    //                 if ((new Set(partnerCheckRelative.split(','))).has(partnerId) && checkAge) {
    //                     patient = {
    //                         ...patient,
    //                         ...patient?.relation,
    //                     }
    //                 } else {
    //                     constraintPatientDetailObj = this.utilService.omit(constraintPatientDetailObj, Object.keys(patient?.relation))
    //                 }
    //             }

    //             let constraintInfo = {
    //                 isValid: true,
    //                 color: '',
    //                 errors: [],
    //             };
    //             let errors: any = [];
    //             const keys = Object.keys(constraintPatientDetailObj);
    //             for (const key of keys) {
    //                 let checkConstraint = get(patient, key);
    //                 if (key === 'patientRelation') {
    //                     if (checkConstraint?.id === patientReltaionDefaultObj?.id) {
    //                         checkConstraint = null;
    //                     }
    //                 }
    //                 // if (!patient?.country_code.includes('VIE') && key === 'address') {
    //                 if ('5ecb3b014ae1165edc747c5b' !== `${patient?.country?._id}` && get(ignorePatientWithoutVieObj, key, false) === true) {
    //                     continue;
    //                 }
    //                 if (key === 'birthdate' && !moment(patient?.birthdate).isValid() && patient?.birthyear) {
    //                     continue;
    //                 }
    //                 if (isNil(checkConstraint) || isEmpty(`${checkConstraint}`)) {
    //                     errors = [...errors, ...groupConstraint[key]];
    //                 }
    //             }
    //             if (errors.length > 0) {
    //                 constraintInfo = {
    //                     ...constraintInfo,
    //                     isValid: false,
    //                     errors,
    //                     color: colorConstraint,
    //                 };
    //             }

    //             patient = this.utilService.omit(patient, Object.keys(patient?.relation));

    //             return { ...patient, constraintInfo };
    //         });
    //     }

    //     return patients;
    // }

    sortPatient(patients: any): Promise<any> {
        patients = patients.map(patient => {
            const { patientSort, ...data } = patient;
            return {
                ...data,
                createdBooking: patientSort?.createdBooking || 0,
                createdPatient: patientSort?.createdPatient || 0,
            };
        });
        patients = orderBy(patients, ['createdBooking', 'createdPatient'], ['desc', 'desc']);
        return patients;
    }

    async testPatientSort(): Promise<any> {
        this.emitService.emit(HANDLE_PATIENT_SORT, {
            patientId: '56b4e1499ab64bfe822ba9d588451794',
            createdBooking: moment().toDate().getTime(),
            createdPatient: moment().toDate().getTime(),
        });
        this.emitService.emit(HANDLE_PATIENT_SORT, {
            patientId: '56b4e1499ab64bfe822ba9d588451794',
            createdPatient: moment().toDate().getTime(),
        });
        this.emitService.emit(HANDLE_PATIENT_SORT, {
            patientId: '0b61fe62ed014f8795449cf7d7bf27f4',
            createdPatient: moment().toDate().getTime(),
            createdBooking: moment().toDate().getTime(),
        });
    }

    async getPatientCodeByPatientId(patientId: string, partnerId: string): Promise<any> {
        const patientCodes = await this.patientCodeModel.find({
            partnerId,
            patientId,
            patientCode: { $nin: [null, ''] },
        }).exec();

        return first(patientCodes);
    }

    async getPatientCodeByPatientIdIgnorePartnerId(patientId: string): Promise<any> {
        const patientCodes = await this.patientCodeModel.find({
            // partnerId,
            patientId,
            patientCode: { $nin: [null, ''] },
        }).exec();

        return first(patientCodes);
    }

    async handlePatientHasBookingSuccess(
        partnerId: string, userMongoId: string, patientId: string, relationType?: string, locale?: string,
    ): Promise<any> {
        const user = await this.userModel.findById(userMongoId).exec();
        if (user && user.isCS === true) {
            return;
        }
        const config = await this.globalSettingService.findByKeyAndRepoName('CONFIG_CHECK_PATIENT_BOOKING_SUCCESS', null, locale);
        const configObj = JSON.parse(config);
        if (configObj?.env === 'ON') {
            const [patientCodes, booking, patient] = await Promise.all([
                this.getPatientCodeByPatientIdIgnorePartnerId(patientId),
                this.bookingModel.find({status: { $in: [1, 2, -2]} , patientId, userId: userMongoId }).limit(1).exec(),
                this.patientModel.findOne({ id: patientId }).exec(),
            ]);
            if (patientCodes || booking.length > 0) {
                if (relationType && !isEmpty(relationType)) {
                    await this.updatePatientRelation(relationType, user, patient);
                }
                throw new HttpException({
                    statusCode: HttpStatus.BAD_REQUEST,
                    message: configObj?.message || 'Hệ thống chưa xử lý được thao tác này. Vui lòng thử lại sau.',
                }, HttpStatus.BAD_REQUEST);
            }
        }
    }

    async unlinkPatientV1(userId: string, patient: any): Promise<void> {
        const user = await this.userModel.findById(userId).exec();
        const partnerId = patient.partnerId;
        if (this.oldHospital.has(partnerId) && user?.userIdV1) {
            const userIdV1 = +user?.userIdV1;
            const table = {
                umc: { patient: 'patient', userPatient: 'user_patient'},
                nhidong1: { patient: 'nd1_patient', userPatient: 'nd1_user_patient' },
                ctchhcm: { patient: 'ctch_patient', userPatient: 'ctch_user_patient' },
            };

            let patientIdV1: number;
            switch (partnerId) {
                case 'umc':
                    patientIdV1 = patient.patientIdV1UMC; break;
                case 'ctchhcm':
                    patientIdV1 = patient.patientIdV1CTCH; break;
                case 'nhidong1':
                    patientIdV1 = patient.patientIdV1; break;

            }

            const findPatientV1s = await this.pkhPatientKnex(table[partnerId].userPatient)
                .select(
                    `${table[partnerId].userPatient}.id`,
                )
                .innerJoin(table[partnerId].patient, `${table[partnerId].patient}.id`, `${table[partnerId].userPatient}.patient_id`)
                .where('user_id', +userIdV1)
                .andWhere('patient_id', +patientIdV1)
                .first();

            if (findPatientV1s) {
                try {
                    await this.pkhPatientKnex(table[partnerId].userPatient)
                                .where('id', +findPatientV1s.id)
                                .del();
                } catch (error) {
                    this.logger.log(error?.message);
                    throw new HttpException(`Xử lý xóa hồ sơ v1 bị lỗi!`, HttpStatus.BAD_REQUEST);
                }
            }
        }
    }

    async updatePatientRelation(relationType: string, user: any, patient: any): Promise<any> {
        const id = relationType;
        const relation = await this.relativeModel.findOne({ id }).exec();
        const dataRelation = {
            user: user.userMongoId,
            patient: patient._id,
            relationTypeId: relation.id,
            relationType: relation._id,
        };
        const findPatientRelation = await this.patientRelationModel.findOne({ patient: patient._id }).exec();
        if (findPatientRelation) {
            await this.patientRelationModel.findByIdAndUpdate(findPatientRelation._id, dataRelation);
        } else {
            const newPatientRelation = new this.patientRelationModel(dataRelation);
            await newPatientRelation.save();
        }
    }

    async checkPatientHisConstraint(
        partnerId: string, userMongoId: string, patientId: string, relationType?: string, locale?: string,
    ): Promise<any> {
        const result: any = {
            isUpdateFull: true,
        };
        const user = await this.userModel.findById(userMongoId).exec();
        if (user && user.isCS === true) {
            return result;
        }
        const config = await this.globalSettingService.findByKeyAndRepoName('CONFIG_CHECK_PATIENT_BOOKING_SUCCESS', null, locale);
        const configObj = JSON.parse(config);
        if (configObj?.env === 'OFF') {
            return result;
        }
        const [patientCodes, booking, patient] = await Promise.all([
            this.getPatientCodeByPatientIdIgnorePartnerId(patientId),
            this.bookingModel.find({status: { $in: [1, 2, -2]} , patientId, userId: userMongoId }).limit(1).exec(),
            this.patientModel.findOne({ id: patientId }).exec(),
        ]);
        result.isUpdateFull = !(patientCodes || booking.length > 0);
        return {
            ...result,
            ...(!result.isUpdateFull && { propertyIgnoreUpdate: configObj?.propertyIgnoreUpdate }),
        };
    }

    // async checkPatientPartnerSimmed(patients: any, formData: any, partnerId: string = 'simmed'): Promise<void> {
    //     const {
    //         subjectId= '',
    //         serviceId= '',
    //         treeId = '',
    //     } = formData;

    //     const [config, partner] = await Promise.all([
    //         this.globalSettingService.findByKeyAndRepoName('PATIENT_CONSTRAINT_BOOKING_SIMMED'),
    //         this.hospitalModel.findOne({ partnerId }, { name: true }).exec(),
    //     ]);
    //     const configsObj = JSON.parse(config);

    //     const subjectOrService =
    //         (serviceId && `gói dịch vụ ${(await this.serviceModel.findOne({ id: serviceId }).exec()).name}`) ||
    //         (subjectId && `chuyên khoa ${(await this.subjectModel.findOne({ id: subjectId }).exec()).name}`);

    //     patients = await Promise.all(patients.map(async patient => {
    //         let warningMessage: string;
    //         let messageConfig: string;
    //         const keyConstraintConfig = `${treeId.toUpperCase()}|${subjectId || serviceId}`;
    //         const constraintConfig = get(configsObj?.constraints, keyConstraintConfig) || get(configsObj?.constraints, `${treeId}|${partnerId}_another`);
    //         if (!constraintConfig) {
    //             return patient;
    //         }
    //         for (const key of Object.keys(constraintConfig)) {
    //             switch (key) {
    //                 case 'age':
    //                     const minAge = first<number>(constraintConfig[key]?.value);
    //                     const maxAge = last<number>(constraintConfig[key]?.value);
    //                     const betweenThan = !this.checkOldAccepted(patient, minAge) && this.checkOldAccepted(patient, maxAge);
    //                     if (betweenThan) {
    //                         break;
    //                     }
    //                     messageConfig = get(configsObj?.messages, `${constraintConfig[key]?.message}`, '');
    //                     warningMessage = messageConfig.replace('{LABEL}', subjectOrService).replace('{PARTNER}', partner.name);
    //                     return {
    //                         ...patient,
    //                         warningMessage,
    //                     };
    //                 default:
    //                     const prop = get(patient, key);
    //                     if (isEmpty(`${prop}`) || prop === constraintConfig[key]) {
    //                         continue;
    //                     }
    //                     messageConfig = get(configsObj?.messages, `${key}_${prop}`, '');
    //                     warningMessage = messageConfig.replace('{LABEL}', subjectOrService).replace('{PARTNER}', partner.name);
    //                     return {
    //                         ...patient,
    //                         warningMessage,
    //                     };
    //             }
    //         }
    //         return patient;
    //     }));

    //     return patients;
    // }

    async getInsuranCodeByPatientId(patientIds: any[], partnerId: string, appId: string = 'medpro', treeId?: string): Promise<any> {
        let patientCodes = await this.patientCodeModel.find({
            partnerId,
            patientId: { $in: patientIds },
        }).exec();

        if (patientCodes.length === 0) {
            patientCodes = await this.patientCodeModel.find({
                partnerId: appId,
                patientId: { $in: patientIds },
            }).exec();
        }

        if (treeId && treeId === 'CLS') {
            const patientCodeCls = await this.patientCodeModel.find({
                partnerId,
                patientId: { $in: patientIds },
            }).exec();

            if (patientCodeCls.length === 0) {
                patientCodes = await this.patientCodeModel.find({
                    partnerId: appId,
                    patientId: { $in: patientIds },
                }).exec();
            }

            return patientCodeCls || patientCodes;
        }

        return patientCodes;
    }

    async getBookingRuleUrl () {
        return this.urlConfigService.getUrlCheckBookingRules();
        // let baseUrl = '';
        // const bookingRulesENV = this.urlConfigService.getUrlCheckBookingRules() || '';
        // if ((REPO_NAME_BETA.includes(this.repoName) && bookingRulesENV) || process.env.NODE_ENV === 'development') {
        //     baseUrl = bookingRulesENV
        // } else {
        //     baseUrl = await this.globalSettingService.findByKeyAndRepoName('BOOKING_RULES_URL_122');
        // }
        //
        // return baseUrl;
    }

    async checkPatientPartner(patients: any, formData: any, partnerid: string, appid: string, locale = 'vi'): Promise<any> {
        const baseUrl = await this.getBookingRuleUrl();
        const api = `${baseUrl}/booking-rules/patient/exam`;
        const patientRules = await this.client.post(api, {
            data: formData,
            patientIds: patients.map(patient => patient?.id)
        }, {
            partnerid,
            appid,
            locale,
        });

        const groupPatientRules = groupBy(patientRules, 'id');
        return patients.map(patient => {
            const warningMessage = first<any>(groupPatientRules[patient?.id])?.warningMessage || '';
            return {
                ...patient,
                ...(warningMessage && { warningMessage })
            }
        })
    }

    async checkConstrainsPatientDetail(patients: any, headers?: HeadersDto, bookingData?: any): Promise<any> {
        const baseUrl = await this.getBookingRuleUrl();
        const { data = [] } = await this.httpService.post(
            `${baseUrl}/booking-rules/patient/detail`,
            {
                patientIds: patients.map((p) => p.id),
                bookingData,
            },
            {
                headers: {
                    partnerid: headers.partnerid || '',
                    appid: headers.appid || '',
                    locale: headers.locale || '',
                    platform: headers.platform || '',
                },
            },
        ).toPromise();

        const patientMap = this.utilService.groupByKey(data, 'id');

        patients.forEach((patient) => {
            patient.constraintInfo = patientMap[patient.id]?.constraintInfo;
        });

        return patients;
    }

    // async checkPatientPartner(patients: any, formData: any, partnerId: string): Promise<any> {
    //     const {
    //         subjectId= '',
    //         serviceId= '',
    //         treeId = '',
    //     } = formData;

    //     const [config, partner] = await Promise.all([
    //         this.globalSettingService.findByKeyAndRepoName(`PATIENT_CONSTRAINT_BOOKING_${partnerId}`.toUpperCase()),
    //         this.hospitalModel.findOne({ partnerId }, { name: true }).exec(),
    //     ]);

    //     if (!config) {
    //         return patients;
    //     }

    //     const configsObj = JSON.parse(config);

    //     let constraintConfig: any;
    //     let subjectOrService = '';
    //     // ưu tiên dịch vụ nếu đi chung với chuyên khoa. Cấu hình treeId|chuyenkhoa|dichvu
    //     const keyConstraintConfig = `${treeId.toUpperCase()}|${subjectId}|${serviceId}`;
    //     constraintConfig = get(configsObj?.constraints, keyConstraintConfig);
    //     const [service, subject] = await Promise.all([
    //         this.serviceModel.findOne({ id: serviceId }, { name: true }).exec(),
    //         this.subjectModel.findOne({ id: subjectId }, { name: true }).exec(),
    //     ]);
    //     if (constraintConfig) {
    //         subjectOrService =
    //             (service && `gói dịch vụ ${service?.name}`) ||
    //             (subject && `chuyên khoa ${subject?.name}`);
    //     } else {
    //         const keyConstraintConfig = `${treeId.toUpperCase()}|${subjectId || serviceId}`;
    //         constraintConfig = get(configsObj?.constraints, keyConstraintConfig) || get(configsObj?.constraints, `${treeId}|${partnerId}_another`);
    //         subjectOrService =
    //             (subject && `chuyên khoa ${subject?.name}`) ||
    //             (service && `gói dịch vụ ${service?.name}`);
    //     }

    //     if (!constraintConfig) {
    //         return patients;
    //     }

    //     patients = await Promise.all(patients.map(async patient => {
    //         let warningMessage: string;
    //         let messageConfig: string;

    //         for (const key of Object.keys(constraintConfig)) {
    //             switch (key) {
    //                 case 'age':
    //                     const minAge = first<number>(constraintConfig[key]?.value);
    //                     const maxAge = last<number>(constraintConfig[key]?.value);

    //                     const lessthan = this.checkAgePatient(patient, minAge, constraintConfig[key]?.unit);
    //                     const greaterthan = this.checkAgePatient(patient, maxAge, constraintConfig[key]?.unit);

    //                     const betweenThan = !lessthan && greaterthan;
    //                     if (betweenThan) {
    //                         break;
    //                     }

    //                     messageConfig = get(configsObj?.messages, `${constraintConfig[key]?.message}`, '');
    //                     if (lessthan) {
    //                         messageConfig = get(configsObj?.messages, `age_lt`, messageConfig);
    //                     } else if (greaterthan) {
    //                         messageConfig = get(configsObj?.messages, `age_gt`, messageConfig);
    //                     }
    //                     warningMessage = messageConfig.replace('{LABEL}', subjectOrService).replace('{PARTNER}', partner.name);
    //                     return {
    //                         ...patient,
    //                         warningMessage,
    //                     };
    //                 default:
    //                     const prop = get(patient, key);
    //                     if (isEmpty(`${prop}`) || prop === constraintConfig[key]) {
    //                         continue;
    //                     }
    //                     messageConfig = get(configsObj?.messages, `${key}_${prop}`, '');
    //                     warningMessage = messageConfig.replace('{LABEL}', subjectOrService).replace('{PARTNER}', partner.name);
    //                     return {
    //                         ...patient,
    //                         warningMessage,
    //                     };
    //             }
    //         }
    //         return patient;
    //     }));

    //     return patients;
    // }

    async syncPatientHisAfterUpdate(isCs: boolean, userMongoId: string, patientId: string, partnerId: string): Promise<any> {

        if (!isCs) {
            return;
        }

        const configPartners = await this.globalSettingService.findByKeyAndRepoName('CONFIG_PARTNER_SYNC_PATIENT_HIS');
        let configPartnerObjs: any
        try {
            configPartnerObjs = JSON.parse(configPartners);
        } catch(error) {
            return;
        }

        const configPartnerObj = get(configPartnerObjs, partnerId, false);
        if (configPartnerObj === false) {
            return;
        }

        const booking = await this.bookingModel.find({ patientId, userId: userMongoId }).limit(1).exec();
        if (booking.length === 0) {
            return;
        }

        const url = `${this.urlConfigService.getBookingTreeUrl()}/his-connector/api/patient/syncPatient`;
        this.emitService.emit(LOG_SERVICE_EVENT, {
            name: 'paramSyncPatientHisAfterUpdate',
            summary: 'CSKH cập nhật hồ sơ đồng bộ his',
            nameParent: 'syncPatientHisAfterUpdate',
            params: {
                partnerId,
                patientId,
                userMongoId,
                url,
            },
        });
        try {
            await this.client.postNoContent(url, {
                partnerId,
                patientId,
            });
        } catch (error) {
            this.emitService.emit(LOG_SERVICE_EVENT, {
                name: 'syncPatientHisAfterUpdate',
                summary: 'CSKH cập nhật hồ sơ đồng bộ his',
                nameParent: 'syncPatientHisAfterUpdateError',
                params: {
                    partnerId,
                    patientId,
                },
                errorBody: this.utilService.errorHandler(error),
                message: error?.message || error?.response?.message || error?.response?.data?.message,
            });
        }

        const patientAfterUpdate = await this.patientModel.findOne({ id: patientId }).exec();
        try {
            const patientVersion = await this.copyPatient(patientAfterUpdate.toObject());
            await this.updatePaitentVersionBooking(patientAfterUpdate?.id, patientVersion?._id, patientVersion?.id)
        } catch (error) {
            this.logger.error(error?.message);
            throw new HttpException(ErrorMessage.BAD_REQUEST, HttpStatus.BAD_REQUEST)
        }


    }

    checkAgePatient(patient: any, ageLimit?: number, unit: string = 'years'): boolean {
        if (!ageLimit) {
            return true;
        }
        if (moment(new Date(patient?.birthdate)).isValid()) {
            switch(unit) {
                case 'months':
                    return Math.floor(moment().diff(moment(patient?.birthdate, 'YYYY-MM-DD'), 'months', true)) < +ageLimit;
                default:
                    return Math.floor(moment().diff(moment(new Date(patient?.birthdate)), 'years', true)) < +ageLimit;
            }
        } else {
            return Number(moment().format('YYYY')) - patient?.birthyear < +ageLimit;
        }
    }

    async checkAgePatientAcceptForPartnerBooking(patientId: string, partnerId: string): Promise<any> {
        const baseUrl = this.urlConfigService.getUrlCheckBookingRules();
        const api = `${baseUrl}/booking-rules/patient/age-accept`;
        const result = await this.client.post(api, {
            patientIds: [patientId],
        }, {
            partnerid: partnerId,
        });
        const firstPatient = first<any>(result);
        if (!firstPatient?.warningMessage) {
            return;
        }
        throw new HttpException(firstPatient?.warningMessage, HttpStatus.BAD_REQUEST);
    }

    translatePatient(patients: any, locale: string = 'vi'): Promise<any> {
        if (!locale || locale === 'vi') {
            return patients;
        }

        return Promise.all(patients.map(async patient => {
            const promise = [
                patient?.patientRelation ?
                    this.translateService.getByBundle('medpro', 'relation_types', patient?.patientRelation?.id, patient?.patientRelation || {}, null, locale) :
                    Promise.resolve({}),
                patient.profession?.id ?
                    this.translateService.getByBundle('medpro', 'professions', patient.profession?.id, {}, null, locale) :
                    Promise.resolve({}),
            ]

            const [patientRelationTranslate, professionTl] = await Promise.all(promise);
            return {
                ...patient,
                patientRelation: {
                    ...patient?.patientRelation,
                    ...patientRelationTranslate,
                },
                profession: {
                    ...patient.profession,
                    ...professionTl,
                }
            }
        }))
    }

    async validateBookingRule(formData: ValidateBookingRuleDto, partnerId: string) {
        const { patientId, subjectId } = formData;

        const patient = await this.patientModel.findOne({id: patientId}).exec();
        if (!patient) {
            throw new HttpException({message: 'Không tìm thấy thông tin bệnh nhân'}, HttpStatus.NOT_FOUND);
        }

        const checkData = await this.checkConfigBookingRulePatientUmc({partnerId, subjectId });

        if (checkData) {
            return this.validateBookingRuleForPatientUmcGroup({
                patient: patient.toObject(),
                ...checkData,
                subjectId,
            })
        } else {
            return patient;
        }
    }

    async insertPatientZalo(formData: UserZaloInfoDto) {
        const { name: fullname, sex, phone, ward_id, district_id, city_id, dateOfBirth, address } = formData;

        if (!phone || !fullname || sex) {
            throw new HttpException("Không đủ thông tin tạo hồ sơ từ Zalopay", HttpStatus.BAD_REQUEST);
        }

        const [surname, name] = this.utilService.splitFullName(fullname);
        const mobile = `${phone}`.replace(/^[+]?84|^0/, '+84').replace(/^9/, '+849').replace(/^3/, '+843').replace('+84', '0');
        const birthdateMM = moment(dateOfBirth);

        let birthdate;
        let birthyear;
        if (birthdateMM.isValid()) {
            birthdate = birthdateMM.format('YYYY-MM-DD');
            birthyear = birthdateMM.get('year');
        }

        const patientInsert: any = {
            id: this.utilService.generateUUID(),
            surname,
            name,
            mobile,
            birthyear,
            birthdate,
            sex,
            address,
            ward_id,
            district_id,
            city_id,
            sourceId: 'zalopay',
        };

        const findNation = await this.nationModel.findOne({ id: patientInsert.dantoc_id }).exec();
        if (findNation) {
            const nationObj = findNation.toObject();
            patientInsert.nation = nationObj._id;
            patientInsert.dantoc_id = nationObj.id;
        }
        if (!!patientInsert.address) {
        }
        const findWard = await this.wardModel.findOne({ id: patientInsert.ward_id }).exec();
        if (findWard) {
            const wardObj = findWard.toObject();
            patientInsert.ward = wardObj._id;
            patientInsert.ward_id = wardObj.id;
        }
        const findDistrict = await this.districtModel.findOne({ id: patientInsert.district_id }).exec();
        if (findDistrict) {
            const districtObj = findDistrict.toObject();
            patientInsert.district = districtObj._id;
            patientInsert.district_id = districtObj.id;
        }
        const findCity = await this.cityModel.findOne({ id: patientInsert.city_id }).exec();
        if (findCity) {
            const cityObject = findCity.toObject();
            patientInsert.city = cityObject._id;
            patientInsert.city_id = cityObject.id;
        }

        const patientNew = new this.patientModel(patientInsert);
        return patientNew.save();
    }

    async checkConfigBookingRulePatientUmc(formData: any) {
        if (!formData?.subjectId) {
            return null;
        }
        const { partnerId } = formData;
        const partnerBranchUmc = await this.globalSettingService.findByKeyAndRepoName('PARTNER_BRANCH_UMC_VALID_AGE');
        const setPartnerBranchUmc = new Set(partnerBranchUmc ? partnerBranchUmc.split(',') : []);
        const [
            subjectAgeLimits, // subject check age umc luồng chính cho khoa nhi và lão
            subjectCheckAgeLimitAnother, // subject umc cho các case khác
            partner,
            subjectNhiGroupUmc, // subject config để nhận biết lão khoa khi các umc có nhiều chi nhánh subject id khác nhau
            subjectLkGroupUmc, // subject config để nhận biết lão khoa khi các umc có nhiều chi nhánh subject id khác nhau
        ] = await Promise.all([
            this.globalSettingService.findByKeyAndRepoName('UMC_AGE_LIMIT_WITH_EXAM'),
            this.globalSettingService.findByKeyAndRepoName('UMC_AGE_LIMIT_ANOTHER_EXAM'),
            this.hospitalModel.findOne({ partnerId }),
            this.globalSettingService.findByKeyAndRepoName('SUBJECT_NHI_GROUP_UMC'),
            this.globalSettingService.findByKeyAndRepoName('SUBJECT_LAO_GROUP_UMC'),
        ]);
        const subjectAgeLimitObjs = JSON.parse(subjectAgeLimits);
        const ignoreSubject = subjectAgeLimitObjs.find(subject => subject.subjectId === formData?.subjectId);

        if (setPartnerBranchUmc.has(partnerId) && ignoreSubject?.condition !== 'ignore') {
            const subjectCheckAgeLimitAnotherObjs = JSON.parse(subjectCheckAgeLimitAnother);
            const checkSubjectCheckAgeLimitAnotherObj = subjectCheckAgeLimitAnotherObjs.find(subject => formData?.subjectId === subject.subjectId);

            const setSubjectNhiGroupUmc = new Set(subjectNhiGroupUmc.split(',') || []);
            const setSubjectLaoGroupUmc = new Set(subjectLkGroupUmc.split(',') || []);
            const subjectNd = subjectAgeLimitObjs.find(sub => setSubjectNhiGroupUmc.has(sub.subjectId) && formData?.subjectId === sub.subjectId);
            const subjectLk = subjectAgeLimitObjs.find(sub => setSubjectLaoGroupUmc.has(sub.subjectId) && formData?.subjectId === sub.subjectId);
            // const subjectNd = subjectAgeLimitObjs.find(sub => sub.subjectId === 'umc_ND');
            // const subjectLk = subjectAgeLimitObjs.find(sub => sub.subjectId === 'umc_PW');

            return {
                checkSubjectCheckAgeLimitAnotherObj,
                subjectNd,
                subjectLk,
                partner
            };
        } else {
            return null;
        }
    }

    async validateBookingRuleForPatientUmcGroup(formData: any) {
        const {
            patient,
            checkSubjectCheckAgeLimitAnotherObj,
            subjectNd,
            subjectLk,
            partner
        } = formData;

        let newPatient = patient;
        patient.birthyear = moment(patient?.birthdate).isValid() ? moment(patient?.birthdate).get('years') : Number(patient?.birthyear);

        if (!checkSubjectCheckAgeLimitAnotherObj) {
            if (subjectNd?.subjectId === formData.subjectId) {
                // truong hợp 16 <= ho so < 60
                console.log('subjectNd: ', subjectNd)
                console.log('patient.birthyear: ', patient.birthyear)
                const { subjectId, patientYear, warningMessage } = subjectNd;
                const subject = await this.subjectModel.findOne({ id: subjectId });
                const checkAgeLt = Number(moment().format('YYYY')) - patient.birthyear >= +patientYear;
                const handleWarningMessage = warningMessage.replace('{PARTNER}', partner.name).replace('{SUBJECT}', subject.name);
                newPatient = {
                    ...patient,
                    ...(checkAgeLt && { warningMessage: handleWarningMessage }),
                };
            } else if (subjectLk?.subjectId === formData.subjectId) {
                // truong hợp 16 <= ho so < 60
                console.log('subjectLk: ', subjectLk)
                console.log('patient.birthyear: ', patient.birthyear)
                const { subjectId, patientYear, warningMessage } = subjectLk;
                const checkAgeGt = Number(moment().format('YYYY')) - patient.birthyear < +patientYear;
                const subject = await this.subjectModel.findOne({ id: subjectId });
                const handleWarningMessage = warningMessage.replace('{PARTNER}', partner.name).replace('{SUBJECT}', subject.name);
                newPatient = {
                    ...patient,
                    ...(checkAgeGt && { warningMessage: handleWarningMessage }),
                };
            }
        } else {
            // nhỏ hơn qui định = true
            const { condition, patientYear, warningMessage, subjectId } = checkSubjectCheckAgeLimitAnotherObj;
            const checkAgeLessConfig = await this.checkOldAccepted(patient, patientYear);
            const subject = await this.subjectModel.findOne({ id: subjectId });
            const handleMessage = warningMessage.replace('{PARTNER}', partner.name).replace('{SUBJECT}', subject.name);
            if (condition === 'lt') {
                newPatient = {
                    ...patient,
                    ...(checkAgeLessConfig === true && { warningMessage: handleMessage }),
                };
            } else if (condition === 'gt' && checkAgeLessConfig === false) {
                newPatient = {
                    ...patient,
                    ...(checkAgeLessConfig === false && { warningMessage: handleMessage }),
                };
            }
        }

        return {
            ...newPatient,
            birthdate: moment(patient.birthdate).isValid() ? moment(patient.birthdate, 'YYYY-MM-DD').format('DD/MM/YYYY') : '',
        };

    }

    async getConfigBtnUser(appid: string, partnerId: string, authorization: string, formData?: YearOldValidationDto): Promise<any> {
        const url = `${this.urlConfigService.getUrlApiWeb}/mongo/patient/config-btn`;
        try {
            const { data } = await this.httpService.post(
                url,
                formData,
                { headers: { appid, partnerId, authorization } },
            ).toPromise();
            return data;
        } catch (error) {
            const message = error?.response.data.message
            this.logger.error(`Error when exec getConfigBtnUser. Cause: ${message}`);
            throw new HttpException(message, HttpStatus.BAD_REQUEST)
        }
    }

    /**
     * Helper method để emit event khi có thay đổi CMND
     */
    private emitCmndUpdatedEvent(patientId: string, userId: string, oldCmnd: string, newCmnd: string, source: 'create' | 'update') {
        if (oldCmnd !== newCmnd) {
            const eventPayload = new PatientCmndUpdatedDto({
                patientId,
                userId,
                oldCmnd,
                newCmnd,
                updatedAt: new Date(),
                source
            });

            this.emitService.emit(EVENT_PATIENT_CMND_UPDATED, eventPayload);
        }
    }
}
