import { UserSchema } from './../user/schemas/user.schema';
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { PERMISSION_COLLECTION_NAME, USER_PERMISSION_COLLECTION_NAME } from './schema/constant';
import { PermissionSchema } from './schema/permission.schema';
import { UserPermissionSchema } from './schema/user-permission.schema';
import { UserPermissionController } from './user-permission.controller';
import { UserPermissionService } from './user-permission.service';
import { GlobalSettingModule } from 'src/global-setting/global-setting.module';
import { UserModule } from 'src/user/user.module';
import { CskhModule } from '../cskh/cskh.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: PERMISSION_COLLECTION_NAME, schema: PermissionSchema },
      { name: USER_COLLECTION_NAME, schema: UserSchema },
      { name: USER_PERMISSION_COLLECTION_NAME, schema: UserPermissionSchema },
    ]),
    UserModule,
    GlobalSettingModule,
    CskhModule
  ],
  controllers: [UserPermissionController],
  providers: [UserPermissionService],
  exports: [UserPermissionService],
})
export class UserPermissionModule {}
