import * as mongoose from 'mongoose';
import { BOOKING_REFERRAL_COLLECTION_NAME } from './constants';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';

const Schema = mongoose.Schema;

export const BookingReferralSchema = new Schema(
    {
        bookingCode: { type: String, required: true },
        transactionId: { type: String, required: true },
        partnerId: { type: String, required: true },
        appId: { type: String, required: true },
        userId: { type: String, required: true },
        bookingId: { type: String, required: true },
        booking: { type: Schema.Types.ObjectId, ref: BOOKING_COLLECTION_NAME, required: true },
        referralCode: { type: String, required: true },
        date: { type: Date, required: true },
    },
    {
        collection: BOOKING_REFERRAL_COLLECTION_NAME,
        timestamps: true,
    },
);
