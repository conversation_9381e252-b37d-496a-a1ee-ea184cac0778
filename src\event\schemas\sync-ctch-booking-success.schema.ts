import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { SYNC_CTCH_BOOKING_SUCCESS } from './constants';

const Schema = mongoose.Schema;

export const SyncCTCHBookingSuccessSchema = new Schema({
    processId: { type: String },
    id: { type: Number, required: true },
    // idAutoIncrement: { type: Number, required: true },
    sourceId: { type: String, default: 'ctchhcm' },
    date_create: { type: Date }, // lấy đúng giờ bên mysql
    syncStatus: { type: String, default: 'success' }, // pending -> active -> success| errored
    extra: { type: Object },
}, {
    collection: SYNC_CTCH_BOOKING_SUCCESS,
    timestamps: true,
}).plugin(jsonMongo);
