import { ApiProperty } from '@nestjs/swagger';
import { IsMobilePhone, IsNotEmpty, MaxLength } from 'class-validator';
import { SmsDto } from './sms.dto';

export class SendSmsMessageHubDTO extends SmsDto {
    @ApiProperty({ description: 'Số điện thoại', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập số điện thoại!' })
    @IsMobilePhone('vi-VN', { strictMode: false }, { message: 'Số điện thoại gửi lên không đúng định dạng!' })
    readonly receiver: string;

    // Hard code because SMS is require only TEXT String
    readonly messageType: string = 'TEXT';

    @ApiProperty({ description: 'signKey', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập signKey!' })
    readonly signKey?: string;

    @ApiProperty({ description: 'clientId', required: true, type: String })
    @IsNotEmpty({ message: '<PERSON>ui lòng nhập clientId!' })
    readonly clientId?: string;
}
