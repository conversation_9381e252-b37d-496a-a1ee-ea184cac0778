import * as mongoose from 'mongoose';
import { SLOT_TIME_CONFIRMED_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const SlotTimeConfirmedSchema = new Schema(
    {
        bookingId: { type: Number },

        bookingTimeId: { type: Number },

        scheduleId: { type: Number },

        isDeleted: { type: Number },

        bookingDate: { type: String },

        bookingCode: { type: String },

        partnerId: { type: String },

        date: { type: Date },

        constrainKey: { type: String, unique: true },
    },
    { collection: SLOT_TIME_CONFIRMED_COLLECTION_NAME, versionKey: false, timestamps: true },
);
