import { Modu<PERSON> } from '@nestjs/common';
import { <PERSON>hidong1Controller } from './nhidong1.controller';
import { Nhidong1Service } from './nhidong1.service';
import { BOOKING_COLLECTION_NAME, PAYMENT_COLLECTION_NAME } from '../booking-gateway/schemas/constants';
import { PATIENT_CODE_COLLECTION_NAME, PATIENT_COLLECTION_NAME } from '../patient-mongo/schemas/constants';
import { PatientSchema } from '../patient-mongo/schemas/patient.schema';
import { PaymentSchema } from '../booking-gateway/schemas/payment.schema';
import { RoomSchema } from '../room-mongo/schemas/room.schema';
import { ServiceSchema } from '../service-mongo/schemas/service.schema';
import { SERVICE_COLLECTION_NAME } from '../service-mongo/schemas/constants';
import { ROOM_COLLECTION_NAME } from '../room-mongo/schemas/constants';
import { MongooseModule } from '@nestjs/mongoose';
import { SUBJECT_COLLECTION_NAME } from '../subject-mongo/schemas/constants';
import { SubjectSchema } from '../subject-mongo/schemas/subject.schema';
import { BookingSchema } from '../booking-gateway/schemas/booking.schema';
import { PatientCodeSchema } from '../patient-mongo/schemas/patient-codes.schema';
import { PAYMENT_METHOD_MAPPINGS_COLLECTION } from './schemas/constants';
import { PaymentMethodMappingSchema } from './schemas/nd1-payment-mapping.schema';

@Module({
    imports: [
        MongooseModule.forFeature([
            { name: PATIENT_COLLECTION_NAME, schema: PatientSchema },
            { name: PAYMENT_COLLECTION_NAME, schema: PaymentSchema },
            { name: ROOM_COLLECTION_NAME, schema: RoomSchema },
            { name: SUBJECT_COLLECTION_NAME, schema: SubjectSchema },
            { name: BOOKING_COLLECTION_NAME, schema: BookingSchema },
            { name: PATIENT_CODE_COLLECTION_NAME, schema: PatientCodeSchema },
            { name: PAYMENT_METHOD_MAPPINGS_COLLECTION, schema: PaymentMethodMappingSchema },
            { name: SERVICE_COLLECTION_NAME, schema: ServiceSchema }])
    ],
    controllers: [Nhidong1Controller],
    providers: [Nhidong1Service],
})
export class Nhidong1Module {
}
