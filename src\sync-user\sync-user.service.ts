import { Injectable, Inject } from '@nestjs/common';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { PkhHttpService } from 'src/config/config.http.service';
import { UrlConfigService } from 'src/config/config.url.service';
import { RestfulAPIOldHospitalConfigService } from 'src/config/config.rest-api.old.hospital';
import { isMobilePhone } from 'class-validator';
import { UserService } from 'src/user/user.service';
import { PatientMongoService } from 'src/patient-mongo/patient-mongo.service';
import { IUser } from 'src/user/interfaces/user.interface';
import { IPatient } from 'src/patient-mongo/intefaces/patient.inteface';
import { ISyncUserTrungVuongProcess } from 'src/sync-trung-vuong-medpro/interfaces/sync-user-trungvuong-process.inteface';
import { InjectModel } from '@nestjs/mongoose';
import { BOOKING_COLLECTION_NAME } from 'src/booking-gateway/schemas/constants';
import { Model } from 'mongoose';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { ISignInProvider } from 'src/user/interfaces/sign-in-provider.interface';
import { ISyncUserDaLieuProcess } from 'src/sync-da-lieu-medpro/interfaces/sync-user-dalieu-process.inteface';
@Injectable()
export class SyncUserService {

    private getEnv: string = '';
    private userTable = 'user';

    constructor(
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        private readonly httpService: PkhHttpService,
        private readonly urlConfigService: UrlConfigService,
        private readonly restApiMapping: RestfulAPIOldHospitalConfigService,
        private readonly userService: UserService,
        private readonly patientMongoService: PatientMongoService,
    ) {
        this.getEnv = this.urlConfigService.getEnv();
    }

    async syncOneUserDaLieu(syncProcess: ISyncUserDaLieuProcess): Promise<any> {
        /* tiến hành xử lý */
        const username = syncProcess.username;
        const fullname = syncProcess.fullname;
        const isMobile = isMobilePhone(username);
        if (isMobile) {
            /* Tiến hành tạo user */
            const userInfo = await this.userService.syncDaLieuUserV1ToMedproId(syncProcess.id, username, fullname);
            return userInfo;
        } else { /* Chuyển social này vào signProvider. Sẽ có trường hợp trùng signProvider thì phải merge patients */
            const userInfo = await this.userService.syncUserV1DaLieuSocialToMedproId(syncProcess);
            return userInfo;
        }
    }

    async syncOneUser(userId: number): Promise<any> {
        /* tìm lại dòng user */
        const user = await this.pkhPatientKnex(this.userTable).where({
            id: userId,
        }).first();

        if (!user) {
            return null;
        }

        /* tiến hành xử lý */
        const username = user.username;
        const fullname = user.fullname;
        const isMobile = isMobilePhone(username);
        // console.log('isMobile', isMobile)
        if (isMobile) {
            /* tiến hành đồng bộ user */
            return this.userService.syncUserV1ToMedproId(user.id, username, fullname);
            // return userInfo;
        } else {
            const syncProcess = {
                id: user.id,
                username,
                fullname,
            };
            // console.log(syncProcess);
            return this.userService.syncUserV1DHYDSocialToMedproId(syncProcess);
            // return this.userService.syncUserV1DaLieuSocialToMedproId(user);
        }
    }

    async preAddPatientToUser(userIdV1: number, medproId: string, patientRefId: string): Promise<any> {
        const user: IUser = await this.userService.getMedproIdUserIdV1(userIdV1, medproId);
        const patient: IPatient = await this.patientMongoService.getPatientByREf(patientRefId);
        await this.addPatientToUser(user, patient);
    }

    async addPatientToUser(user: IUser, patient: IPatient): Promise<any> {
        return this.patientMongoService.syncAddPatientToUser(user, patient);
    }

    async upgradeUserTrungVuong(user: ISyncUserTrungVuongProcess): Promise<any> {
        try {
            /* Tìm lại thông tin user */
            const getUser: IUser = await this.userService.getUserByMongoUserId(user.id);
            /* Kiểm tra thông tin xem số điện thoại hay social */
            const isMobile = isMobilePhone(getUser.username);
            if (isMobile) {
                /* Kiểm tra xem số điện thoại này đã có trên medpro 1.1 hay ko */
                const existsMedpro11 = await this.userService.checkExistsUserByUsernameMedproId(getUser.username);
                if (existsMedpro11) {
                    /* Lấy thông tin hồ sơ medpro 1.0 chuyển qua medpro 1.1 */
                    const newMergeUser = await this.userService.mergeUserMedproId(getUser, existsMedpro11);
                    /* tiến hành cập nhật lại booking */
                    await this.bookingModel.updateMany({
                        userId: getUser._id,
                    }, {
                        userId: existsMedpro11._id,
                        prevUserId: getUser._id,
                    }).exec();
                    return newMergeUser;
                } else {
                    /* Tiến hành lưu user constraints trước */
                    await this.userService.createUserConstraints(getUser);
                    /* tiến hành update user medpro 1.0 lên 1.1 */
                    const userUpgrade = await this.userService.upgradeUserTrungVuong(getUser);
                    /* tiến hành tạo signProvider */
                    await this.userService.createNewPasswordProvider(getUser);
                    return userUpgrade;
                }
            } else { /* Chuyển social này vào signProvider. Sẽ có trường hợp trùng signProvider thì phải merge patients */
                const isZaloProvider = `${user.username}`.includes('zl_');
                if (isZaloProvider) { /* Kiểm tra xem user là zalo hay ko */
                    const zaloProvider: ISignInProvider = await this.userService.createNewZaloProvider(getUser);
                    /* check lại xem zaloProvider.user với getUser._id */
                    if (zaloProvider.user === getUser._id) { /* Tiến hành nâng cấp */
                        const userUpgrade = await this.userService.upgradeSocialUserTrungVuong(getUser);
                        return userUpgrade;
                    } else {
                        /* Lấy thông tin hồ sơ medpro 1.0 chuyển qua medpro 1.1 */
                        // tslint:disable-next-line: variable-name
                        const existsMedpro11_2 = await this.userService.getUserByMongoUserId(zaloProvider.user);
                        /* tiến hành merge user */
                        const newMergeUser = await this.userService.mergeUserMedproId(getUser, existsMedpro11_2);
                        /* tiến hành cập nhật lại booking */
                        await this.bookingModel.updateMany({
                            userId: getUser._id,
                        }, {
                            userId: existsMedpro11_2._id,
                            prevUserId: getUser._id,
                        }).exec();
                        return newMergeUser;
                    }
                } else { /* Xử lý cho facebook. google */
                    /* Kiểm tra xem getUser.username có trong signInProvider hay ko */
                    const firebaseProvider: ISignInProvider = await this.userService.createNewFirebaseProvider(getUser);
                    if (firebaseProvider.user === getUser._id) {
                        const userUpgrade = await this.userService.upgradeSocialUserTrungVuong(getUser);
                        return userUpgrade;
                    } else {
                        /* Lấy thông tin hồ sơ medpro 1.0 chuyển qua medpro 1.1 */
                        // tslint:disable-next-line: variable-name
                        const existsMedpro11_2 = await this.userService.getUserByMongoUserId(firebaseProvider.user);
                        /* tiến hành merge user */
                        const newMergeUser = await this.userService.mergeUserMedproId(getUser, existsMedpro11_2);
                        /* tiến hành cập nhật lại booking */
                        await this.bookingModel.updateMany({
                            userId: getUser._id,
                        }, {
                            userId: existsMedpro11_2._id,
                            prevUserId: getUser._id,
                        }).exec();
                        return newMergeUser;
                    }
                }
            }
        } catch (error) {
            console.log(error);
            return null;
        }

    }

    async checkZaloIdV2(zaloId: string): Promise<any> {
        return this.userService.checkZaloId(zaloId);
    }

    async checkFirebaseIdV2(firebaseId: string): Promise<any> {
        return this.userService.checkFirebaseIdV2(firebaseId);
    }
}
