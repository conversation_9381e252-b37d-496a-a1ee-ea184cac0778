import { Body, HttpException, HttpService, HttpStatus } from '@nestjs/common';
import { Controller, Headers, HttpCode, Post } from '@nestjs/common';
import { UrlConfigService } from 'src/config/config.url.service';

@Controller('webhook')
export class WebhookController {

    constructor(
        private http: HttpService,
        private configUrlService: UrlConfigService,
    ) {
    }

    @Post('care-soft')
    @HttpCode(200)
    async careSoftCallback(@Body() body: any, @Headers('x-hub-signature') signature: string): Promise<any> {
        try {
            const res = await this.http.post(`${this.configUrlService.getGatewayAPIUrl()}/webhook/care-soft`, body, {
                headers: {
                    'x-hub-signature': signature,
                },
            }).toPromise();
            return res;
        } catch (error) {
            const status = error?.response?.data?.statusCode || HttpStatus.BAD_REQUEST;
            const dataError = {
                statusCode: status,
                message: error?.response?.data?.message || '<PERSON><PERSON> thống chưa xử lý được thao tác này. Vui lòng thử lại.',
            };
            throw new HttpException(dataError, status);
        }
    }

}
