export const PATIENT_EXAMINATION_COLLECTION_NAME = 'patient-files';
export const HEALTH_INDEX_COLLECTION_NAME = 'health-indexs';
export const LIST_CDN_IMAGE_COLLECTION_NAME = 'list-cdn-image';
export const PATIENT_COLLECTION_NAME = 'patients';
export const PATIENT_VERSION_COLLECTION_NAME = 'patient-versions';
export const PATIENT_CODE_COLLECTION_NAME = 'patient_codes';
export const PATIENT_SEARCH_LOG_COLLECTION_NAME = 'patient-search-log';
export const RELATIVE_TYPE_COLLECTION_NAME = 'relation_types';
export const MEDPRO = 'medpro';
export const PATIENT_PROFILE_COLLECTION_NAME = 'patient-profiles';
export const PATIENT_XC_COLLECTION_NAME = 'patient_xc';
export const PATIENT_RELATION_COLLECTION_NAME = 'patient-relations';
export const PATIENT_SORT_COLLECTION_NAME = 'patients_sorts';
export const allowedMimeTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.oasis.opendocument.text',
    'application/vnd.oasis.opendocument.spreadsheet',
    'application/vnd.oasis.opendocument.presentation',
    'text/plain',
    'text/markdown',
    'application/rtf',
    'application/xml'
];
export const PENDING_PATINET_HEALTH_INDEX = 'pending-patients'
