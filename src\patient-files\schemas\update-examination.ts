import * as json<PERSON>ongo from '@meanie/mongoose-to-json';
import * as mongoose from 'mongoose';
import { USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { PATIENT_EXAMINATION_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const ExaminationResultSchema = new Schema({
    id: String,
    patientId: String,
    userId: { type: Schema.Types.ObjectId, ref: USER_COLLECTION_NAME },
    name: { type: String, required: false },
    listImage: { type: [String], required: true },
    dateExamination: { type: String, required: true },
    description: { type: String, required: false },
    isDeleted: { type: Boolean, default: false },
    hospitalName: { type: String, required: false },
    source: { type: String, default: 'user', required: false },
}, {
    collection: PATIENT_EXAMINATION_COLLECTION_NAME,
    timestamps: true,
}).plugin(jsonMongo);

