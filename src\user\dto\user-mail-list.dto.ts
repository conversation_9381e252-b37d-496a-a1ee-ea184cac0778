import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class UserMailListDto {
    @ApiProperty()
    @IsNotEmpty()
    partnerId: string;

    @ApiProperty({ required: false, default: undefined })
    isHaveBooking?: boolean;

    @ApiProperty({ required: false, default: undefined })
    isHaveProfile?: boolean;

    @ApiProperty()
    @IsNotEmpty()
    secretKey: string;
}
