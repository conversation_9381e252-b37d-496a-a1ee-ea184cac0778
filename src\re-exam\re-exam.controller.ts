import { Controller, Get, Post, Body, Headers, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ReExamService } from './re-exam.service';
import { ReExamQueryDTO } from './dto/re-exam-query.dto';
import { ReExamSearchDTO } from './dto/re-exam-search.dto';
import { ReExamOptionsDto } from './dto/re-exam-options.dto';
import { PortalPushReExamDataDto } from './dto/portal-push-re-exam-data.dto';

@Controller('re-exam')
@ApiTags('Quản lý Tái khám')
export class ReExamController {
    constructor(
        private readonly reExamService: ReExamService,
    ) { }

    @Post('search')
    async search(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() data: ReExamSearchDTO): Promise<any> {
        return this.reExamService.search(appid, partnerid, data);
    }

    @Post('process')
    async process(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() data: ReExamQueryDTO,
        ): Promise<any> {
        return this.reExamService.process(data, appid, partnerid);
    }

    @Post('booking-tree-data')
    async getBookingTreeData(
        @Headers('partnerid') partnerid: string,
        @Headers('appid') appid: string,
        @Body() data: ReExamQueryDTO,
        ): Promise<any> {
        return this.reExamService.processBookingTreeData(data, appid, partnerid);
    }

    @Post('test-transform-tai-kham')
    async testTransformTaiKham(): Promise<any> {
        return this.reExamService.testTransformTaiKham();
    }

    @Get('test-moment-reexam')
    async testMoment(): Promise<any> {
        return this.reExamService.testMoment();
    }
    // @Get('test-get-booking-tree')
    // async testGet(): Promise<any> {
    //     return this.reExamService.testGet();
    // }

    @Post('test-portal-push-data')
    async handlePortalPushData(@Body() body: PortalPushReExamDataDto) {
        return this.reExamService.handlePortalPushData(body)
    }
}
