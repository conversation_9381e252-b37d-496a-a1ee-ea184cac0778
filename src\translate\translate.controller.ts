import { TranslateDto } from './dto/translate.dto';
import { Body, Controller, Get, Headers, Post, Query } from '@nestjs/common';
import { TranslateService } from './translate.service';
import { ErrorMessageKey } from './enum/error-message.enum';

@Controller('translations')
export class TranslateController {

    constructor(private readonly service: TranslateService) {}

    @Post()
    getByBundle(
        @Headers('partnerid') partnerid: string,
        @Headers('locale') locale: string,
        @Body() formData: TranslateDto,
    ): Promise<any> {
        return this.service.getBundleByData(partnerid, locale, formData);
    }

    @Get('getCommonErrorMessage')
    getErrorMessage(
        @Headers('partnerid') partnerid: string,
        @Headers('locale') locale: string,
        @Query('key') key: string,
    ): Promise<any> {
        console.log('key: ', key)
        console.log('locale: ', locale)
        return this.service.getCommonErrorMessage(ErrorMessageKey[key], locale);
    }

    @Get('testGetByBundleKey')
    testGetByBundleKey(
        @Headers('partnerid') partnerid: string,
        @Headers('locale') locale: string,
        @Query('key') key: string,
    ): Promise<any> {
        console.log('key: ', key)
        console.log('locale: ', locale)
        return this.service.getByBundleKey('medpro', 'contacts',
            'hotline', 'default nè', key, locale)
    }
}
