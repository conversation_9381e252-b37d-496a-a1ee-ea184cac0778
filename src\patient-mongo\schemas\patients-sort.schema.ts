import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import { PATIENT_SORT_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const PatientSortSchema = new Schema(
    {
        patientId: String,
        createdPatient: Number,
        createdBooking: Number,
    },
    {
        collection: PATIENT_SORT_COLLECTION_NAME,
        timestamps: true,
        versionKey: false,
    },
).plugin(jsonMongo);
