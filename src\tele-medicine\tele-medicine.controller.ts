import { Controller, Post, HttpCode, HttpStatus, Get, Param, Body, UseGuards, Request, Query, Headers, HttpException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TeleMedicineService } from './tele-medicine.service';
import { DoctorService } from 'src/doctor/doctor.service';
import { OutDoctorsDTO } from 'src/doctor/dto/outDoctor.dto';
import { plainToClass } from 'class-transformer';
import { PatientDTO } from 'src/patient/dto/patient.dto';
import { PatientService } from 'src/patient/patient.service';
import { BookingService } from 'src/booking/booking.service';
import { OutBookingsDTO } from 'src/booking/dto/outBookings.dto';
import { PushNotifDTO } from 'src/push-notif/dto/push-notif.dto';
import { PushNotifService } from 'src/push-notif/push-notif.service';
import { PushDeviceDTO } from 'src/push-device/dto/push-device.dto';
import { PushDeviceService } from 'src/push-device/push-device.service';
import { AuthGuard } from '@nestjs/passport';
import { PushNotifUserDTO } from 'src/push-notif/dto/push-notif-user.dto';
import { AdminUserService } from 'src/admin-user/admin-user.service';
import { UserService } from 'src/user/user.service';
import { LoginWithUserAccessTokenDTO } from 'src/user/dto/login-with-user-access-token';
import { SubjectService } from 'src/subject/subject.service';
import { OutSubjectsDTO } from 'src/subject/dto/outSubjects.dto';
import { PushDeviceMongoDTO } from 'src/push-device/dto/push-device-mongo.dto';
import { UpdateMakeCallStatusDTO } from './dto/update-make-call-status.dto';

@Controller('telemed')
@ApiTags('TeleMedicine - Quản lý TeleMedicine')
export class TeleMedicineController {
    constructor(
        private readonly teleMedicineService: TeleMedicineService,
        private readonly doctorService: DoctorService,
        private readonly patientService: PatientService,
        private readonly bookingService: BookingService,
        private readonly pushNotifService: PushNotifService,
        private readonly pushDeviceService: PushDeviceService,
        private readonly adminUserService: AdminUserService,
        private readonly userService: UserService,
        private readonly subjectService: SubjectService,
    ) { }

    @Post('doctor/all')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({ summary: 'Lấy Danh sách Thông tin Bác sĩ.', description: 'Lấy Danh sách Thông tin Bác sĩ.' })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutDoctorsDTO,
        description: 'Lấy danh sách tỉnh thành.',
    })
    async allTeleMedicineDoctors(): Promise<OutDoctorsDTO> {
        return plainToClass(OutDoctorsDTO, await this.doctorService.allTeleMedicineDoctors());
    }

    @Post('subject/all')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({ summary: 'Lấy Danh sách Chuyên khoa của UMC.', description: 'Lấy Danh sách Chuyên khoa của UMC.' })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutSubjectsDTO,
        description: 'Lấy danh sách tỉnh thành.',
    })
    async allTeleMedicineUMCSubjects(): Promise<OutSubjectsDTO> {
        return plainToClass(OutSubjectsDTO, await this.subjectService.allTeleMedicineUMCSubjects());
    }

    @Get('patient/:id')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({ summary: 'Lấy thông tin bệnh nhân.', description: 'Lấy thông tin bệnh nhân.' })
    @HttpCode(HttpStatus.OK)
    async getUMCPatientById(@Param('id') id: number): Promise<PatientDTO> {
        return this.patientService.getUMCPatientById(id);
    }

    @Post('booking/all')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({ summary: 'Lấy Danh sách Đặt khám.', description: 'Lấy Danh sách Đặt khám.' })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        type: OutBookingsDTO,
        description: 'Lấy Danh sách Đặt khám.',
    })
    async allUMCTeleMedicineBookings(): Promise<OutBookingsDTO> {
        return plainToClass(OutBookingsDTO, await this.bookingService.allUMCTeleMedicineBookings());
    }

    @Post('notification/create-notification')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({ summary: 'Tạo Push Notification.', description: 'Tạo Push Notification.' })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        // type: OutDoctorsDTO,
        description: 'Tạo Push Notification.',
    })
    async createNotification(@Body() pushNotifDTO: PushNotifDTO): Promise<any> {
        return this.pushNotifService.createNotification(pushNotifDTO);
    }

    @Post('notification/create-notification-to-user')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Tạo Push Notification to 1 user.', description: 'Tạo Push Notification to 1 user.' })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        // type: OutDoctorsDTO,
        description: 'Tạo Push Notification to 1 user.',
    })
    async createNotificationToUser(@Body() pushNotifUserDTO: PushNotifUserDTO): Promise<any> {
        return this.pushNotifService.createNotificationToUser(pushNotifUserDTO);
    }

    @Post('makeCall')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({ summary: 'Tạo Push Notification to 1 user.', description: 'Tạo Push Notification to 1 user.' })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        // type: OutDoctorsDTO,
        description: 'Tạo Push Notification to 1 user.',
    })
    async makeCall(@Body() pushNotifUserDTO: PushNotifUserDTO): Promise<any> {
        return this.pushNotifService.createNotificationToUser(pushNotifUserDTO);
    }

    @Post('notification/register')
    @ApiOperation({ summary: 'Đăng ký nhận Push Notification.', description: 'Đăng ký nhận Push Notification.' })
    @HttpCode(HttpStatus.OK)
    @ApiResponse({
        status: HttpStatus.OK,
        // type: OutDoctorsDTO,
        description: 'Đăng ký nhận Push Notification.',
    })
    async registerNotification(
        @Headers('partnerid') partnerid: string, @Headers('appid') appid: string, @Body() pushDeviceDTO: PushDeviceMongoDTO): Promise<any> {
        return this.pushDeviceService.insertPushDeviceId(partnerid, appid, pushDeviceDTO);
    }

    // @Get('test')
    // async test(): Promise<any> {
    //     return this.pushDeviceService.getUMCLatestPushDeviceByUserId(141929);
    // }

    @Post('notification/update-user-device')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({ summary: 'Cập nhật Thiết bị thuộc về người dùng.', description: 'Cập nhật Thiết bị thuộc về người dùng.' })
    // @HttpCode(HttpStatus.OK)
    // @ApiResponse({
    //     status: HttpStatus.OK,
    //     // type: OutDoctorsDTO,
    //     description: 'Cập nhật Thiết bị thuộc về người dùng.',
    // })
    async updateUserDeviceNotification(@Request() req, @Body() pushDeviceDTO: PushDeviceMongoDTO): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException({
                message: 'Token không hợp lệ.',
                error: 401,
            }, HttpStatus.UNAUTHORIZED);
        }
        return this.pushDeviceService.updatePushUserDevice(pushDeviceDTO, user.userMongoId);
    }

    @Get('notification/update-user-device-manual')
    async updateUserDeviceManual(
        @Query('deviceId') deviceId: string,
        @Query('userId') userId: string,
    ): Promise<any> {
        return this.pushDeviceService.updatePushUserDeviceManual(deviceId, userId);
    }

    @Post('user-logout')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('user-jwt'))
    @ApiOperation({ summary: 'Log out' })
    async userLogout(@Request() req, @Body('clientId') clientId: string): Promise<any> {
        const user = req.user;
        if (typeof user.userMongoId === typeof undefined) {
            throw new HttpException({
                message: 'Token không hợp lệ.',
                error: 401,
            }, HttpStatus.UNAUTHORIZED);
        }
        return this.pushDeviceService.userLogout(user.userMongoId, clientId);
    }

    @Get('notification/users-device')
    @ApiBearerAuth()
    @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Cập nhật Thiết bị thuộc về người dùng.', description: 'Cập nhật Thiết bị thuộc về người dùng.' })
    async usersDevice(): Promise<any> {
        return this.pushDeviceService.getUMCUsersDevice();
    }

    @Get('auto-generate-token-id')
    // @ApiBearerAuth()
    // @UseGuards(AuthGuard('jwt'))
    @ApiOperation({ summary: 'Lấy token tự động.', description: 'Lấy token tự động.' })
    async autoGenerateTokenId(): Promise<any> {
        const userInfo = await this.adminUserService.createNewUserPatientSupportAutoGenerate({
            username: '<EMAIL>',
            password: '123456',
            fullname: 'USER TEST AUTO',
        });
        return this.userService.autoGenerateJWT(userInfo.username, userInfo.id);
    }

    @Get('login-with-user-id-access-token')
    @ApiOperation({ summary: 'Lấy token tự động.', description: 'Lấy token tự động.' })
    async loginWithUserIdAccessToken(@Query() loginWithUserAccessTokenDTO: LoginWithUserAccessTokenDTO): Promise<any> {
        return this.userService.loginWithUserIdAccessToken(loginWithUserAccessTokenDTO);
    }

    @Get('user-busy')
    @ApiOperation({ summary: 'Lấy token tự động.', description: 'Lấy token tự động.' })
    async userBusy(@Query('userId') userId: number): Promise<any> {
        return this.userService.userBusy(userId);
    }

    @Get('user-busy-booking')
    @ApiOperation({ summary: 'Lấy token tự động.', description: 'Lấy token tự động.' })
    async userBusyBooking(@Query('bookingId') bookingId: string): Promise<any> {
        return this.teleMedicineService.userBusy(bookingId);
    }

    @Post('make-call/update-status')
    // @ApiBearerAuth()
    @ApiOperation({ summary: 'Cập nhật trạng thái make call' })
    // @UseGuards(AuthGuard('user-jwt'))
    async updateMakeCallStatus(
        @Body() formData: UpdateMakeCallStatusDTO,
    ): Promise<any> {
        return this.teleMedicineService.updateMakeCallStatus(formData);
    }

    @Get('make-call/status')
    async getMakeCallStatus(@Query('bookingId') bookingId: string, @Query('role') role?: string): Promise<any> {
        return this.teleMedicineService.getMakeCallStatus(bookingId, role);
    }

}
