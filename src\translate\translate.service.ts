import { TranslateDto } from './dto/translate.dto';
import { UtilService } from './../config/util.service';
import { LOG_SERVICE_EVENT } from './../audit-log/constant';
import { UrlConfigService } from './../config/config.url.service';
import { ClientUtilService } from './../config/client-util.service';
import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import * as querystring from 'query-string';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { get } from 'lodash';
import { ErrorMessageKey, ErrorMessage } from './enum/error-message.enum';

@Injectable()
export class TranslateService {
    constructor(
        private readonly client: ClientUtilService,
        private readonly urlConfigService: UrlConfigService,
        private readonly eventEmitter: EventEmitter2,
        private readonly util: UtilService,
    ) { }

    async getByBundle(partnerId: string, collection: string, objectId: string, propDefault: any, key?: string, locale: string = 'vi'): Promise<any> {
        const defaultData =  typeof propDefault === 'object' ? propDefault : { [key || '']: propDefault };
        if (!locale || locale === 'vi' || !partnerId) {
            return defaultData;
        }
        // <partnerID>.<categoryId>.<ObjectID>.<attributeID>
        const bundleId = `${partnerId}.${collection}.${objectId}${key ? `.${key}` : ''}`;
        const url = querystring.stringifyUrl({
            url: `${this.urlConfigService.translationUrl}/api/translations`,
            query: {
                bundleId,
                language: locale,
            },
        });
        let data: any;
        try {
            data = await this.client.get(url);
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'getByBundle',
                summary: 'Translate portal error',
                nameParent: 'getByBundle',
                params: {url},
                errorBody: this.util.errorHandler(error),
                message: error?.response?.data?.message || error?.response?.message || error?.message || 'Translate portal error',
            });
            return defaultData;
        }

        if (!data || Object.keys(data).length === 0) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'getByBundle',
                summary: 'Translate portal error',
                nameParent: 'getByBundle',
                params: {url},
                message: 'Translate portal error null',
            });
            return defaultData;
        }
        return data;
    }

    async getByBundleId(bundleId: string, locale: string = 'vi', defaultData: any = {}): Promise<any> {
        if (!locale || locale === 'vi') {
            return defaultData;
        }

        const url = querystring.stringifyUrl({
            url: `${this.urlConfigService.translationUrl}/api/translations`,
            query: {
                bundleId,
                language: locale,
            },
        });

        let data: any;
        try {
            data = await this.client.get(url);
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'getByBundle',
                summary: 'Translate portal error',
                nameParent: 'getByBundle',
                params: {url},
                errorBody: this.util.errorHandler(error),
                message: error?.response?.data?.message || error?.response?.message || error?.message || 'Translate portal error',
            });
            return defaultData;
        }

        if (!data || Object.keys(data).length === 0) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'getByBundle',
                summary: 'Translate portal error',
                nameParent: 'getByBundle',
                params: {url},
                message: 'Translate portal error null',
            });
            return defaultData;
        }

        return data;
    }

    async getBundleByData(partnerId: string, locale: string, formData: TranslateDto) {
        let prop: string;
        try {
            prop = JSON.parse(formData.prop);
        } catch (error) {
            prop = formData.prop;
        }
        return this.getByBundle(
            partnerId,
            formData.collection,
            formData.objectId,
            prop,
            formData?.key,
            locale,
        );
    }

    async getCommonErrorMessage(key: ErrorMessageKey, locale?: string, defaultValue?: any) {
        const data = await this.getByBundle('medpro', 'common', 'errorMessage',
            defaultValue || ErrorMessage[key], key, locale)
        return get(data, key);
    }

    async getByBundleKey(partnerId: string, collection: string, objectId: string, propDefault: any, key?: string, locale: string = 'vi') {
        const dataTranslate = await this.getByBundle(partnerId, collection, objectId, propDefault, key, locale);
        return get(dataTranslate, key, propDefault)
    }

    /**
     * translate object của các collection chỉ có single record theo mỗi partner
     * Ví dụ partnerConfig, hospital, extraConfig,...
     * @param partnerId
     * @param collection
     * @param defaultData
     * @param locale
     */
    async getPartnerSigleObject(partnerId: string, collection: string, defaultData: any, locale: string = 'vi') {
        if (!locale || locale === 'vi' || !partnerId) {
            return defaultData;
        }

        const bundleId = `${partnerId}.${collection}`;
        const url = querystring.stringifyUrl({
            url: `${this.urlConfigService.translationUrl}/api/translations`,
            query: {
                bundleId,
                language: locale,
            },
        });

        let data: any;
        try {
            data = await this.client.get(url);
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'getByBundle',
                summary: 'Translate portal error',
                nameParent: 'getByBundle',
                params: {url},
                errorBody: this.util.errorHandler(error),
                message: error?.response?.data?.message || error?.response?.message || error?.message || 'Translate portal error',
            });
            return defaultData;
        }

        if (!data || Object.keys(data).length === 0) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'getByBundle',
                summary: 'Translate portal error',
                nameParent: 'getByBundle',
                params: {url},
                message: 'Translate portal error null',
            });
            return defaultData;
        }

        return data;
    }

    /**
     * translate object của các collection chỉ có single record theo mỗi partner và lấy theo key
     * Ví dụ partnerConfig, hospital, extraConfig,...
     * @param partnerId
     * @param collection
     * @param defaultData
     * @param locale
     */
    async getPartnerSigleObjectByKey(partnerId: string, collection: string, key: string, defaultData: any, locale: string = 'vi') {
        if (!locale || locale === 'vi' || !partnerId) {
            return defaultData;
        }

        const bundleId = `${partnerId}.${collection}.${key}`;
        const url = querystring.stringifyUrl({
            url: `${this.urlConfigService.translationUrl}/api/translations`,
            query: {
                bundleId,
                language: locale,
            },
        });

        let data: any;
        try {
            data = await this.client.get(url);
        } catch (error) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'getByBundle',
                summary: 'Translate portal error',
                nameParent: 'getByBundle',
                params: {url},
                errorBody: this.util.errorHandler(error),
                message: error?.response?.data?.message || error?.response?.message || error?.message || 'Translate portal error',
            });
            return defaultData;
        }

        if (!data || Object.keys(data).length === 0) {
            this.eventEmitter.emit(LOG_SERVICE_EVENT, {
                name: 'getByBundle',
                summary: 'Translate portal error',
                nameParent: 'getByBundle',
                params: {url},
                message: 'Translate portal error null',
            });
            return defaultData;
        }

        return get(data, key);
    }
}
