import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsMobilePhone, IsNotEmpty } from 'class-validator';
import { VerifyOTPDto } from './verify-otp.dto';

export class VerifyOTPMessageHubDto extends VerifyOTPDto {
    @ApiProperty({ description: 'Số điện thoại', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập số điện thoại!' })
    @IsMobilePhone('vi-VN', { strictMode: false }, { message: 'Số điện thoại gửi lên không đúng định dạng!' })
    readonly phone: string;

    @ApiProperty({ description: 'Vui lòng nhập otpToken', required: true, type: String })
    @IsNotEmpty({ message: 'otpToken is not empty' })
    @IsIn([6], { message: 'O<PERSON> bao gồm 6 kí tự' })
    readonly otpCode: string;
}
