import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsDateString, Max, Min } from 'class-validator';

export class PopupDTO {
    @ApiProperty({
        description: 'id mongo',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    id: string;

    @ApiProperty({
        description: 'detail',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    appId: string;

    @ApiProperty({
        description: 'detail',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    type: string;

    @ApiProperty({
        description: 'detail',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    url: string;

    @ApiProperty({
        description: 'detail',
        required: false,
        type: String,
    })
    @Transform(value => `${value}`.trim())
    dataType: string;

    @IsDateString()
    @Transform(value => `${value}`.trim())
    startDate: string;

    @Transform(value => `${value}`.trim())
    @IsDateString()
    endDate: string;

    @Min(0)
    @Type(() => Number)
    status: number;

    platforms: PopupPlatformDto[];
}

export class PopupPlatformDto {
    @Transform(value => `${value}`.trim())
    name: string;

    @Transform(value => `${value}`.trim())
    imageUrl: string;
}
