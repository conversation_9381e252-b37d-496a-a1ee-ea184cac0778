import { Document } from "mongoose";

export interface IPendingPatient extends Document {
  cmnd: string;
  healthData: {
    // Properties from parseHealthDataFromRow
    title?: string;
    hospitalName?: string;
    dateExamination?: Date;
    height?: number;
    weight?: number;
    bmi?: number;
    waist?: number;
    bp?: string;
    pulse?: number;
    temperature?: number;
    // Additional properties that might be used
    examination?: string;
    examinationId?: string;
    glucoseU?: string;
    respiratoryRate?: string;
    drugAllergyStatus?: string;
    ingredientAllergyStatus?: string;
    foodAllergyStatus?: string;
    familyHistoryPulse?: string;
    familyHistoryHypertension?: string;
    familyHistoryMental?: string;
    familyHistoryCancer?: string;
    familyHistoryAsthma?: string;
    familyHistoryEpilepsy?: string;
    familyHistoryTuberculosis?: string;
    // Keep original row for reference
    originalRow?: any;
  };
  status: string; // pending, no_match, error (synced sẽ bị xóa)
  createdAt?: Date;
  linkedAt?: Date; // Thời gian xử lý
  linkedPatientIds?: string[]; // Danh sách patient IDs đã được link (trước khi xóa)
  errorMessage?: string; // Thông báo lỗi nếu status = 'error'
}