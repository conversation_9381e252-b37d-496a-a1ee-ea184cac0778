import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { RELATIVE_TYPE_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { Model } from 'mongoose';
import { IRelative } from 'src/patient-mongo/intefaces/relative.inteface';

@Injectable()
export class RelativeService {
    constructor(
        @InjectModel(RELATIVE_TYPE_COLLECTION_NAME) private relativeModel: Model<IRelative>,
    ) { }

    async getAll(appId: string, partnerId: string): Promise<any> {
        return this.relativeModel.find({ partnerId }).exec();
    }

}
