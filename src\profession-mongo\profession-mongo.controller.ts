import { Controller, Get, Headers } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ProfessionMongoService } from './profession-mongo.service';

@Controller('profession-mongo')
@ApiTags('<PERSON><PERSON><PERSON> nghiệp - <PERSON>uản lý danh mục Nghề nghiệp trên MongoDB')
export class ProfessionMongoController {
    constructor(
        private readonly professionMongoService: ProfessionMongoService,
    ) { }

    @Get('get-all-by-partner')
    async find(@Headers('partnerid') partnerid: string): Promise<any> {
        return this.professionMongoService.find(partnerid);
    }

    @Get('seed-data')
    async seed(@Headers('partnerid') partnerid: string): Promise<any> {
        return this.professionMongoService.seed(partnerid);
    }
}
