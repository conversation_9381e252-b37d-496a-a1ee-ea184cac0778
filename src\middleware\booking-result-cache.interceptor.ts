import { CacheInterceptor, ExecutionContext, Injectable } from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class BookingResultCacheInterceptor extends CacheInterceptor {

    trackBy(context: ExecutionContext): string {
        const request = context.switchToHttp().getRequest<Request>();
        return `${request.path}_${this.jsonToQueryString(request.body)}`;
    }

    jsonToQueryString = (obj: { [key: string]: any } = {}): string => {
        return Object.keys(obj)
          .map(key => {
            return `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`;
          })
          .join('&');
    };
}
