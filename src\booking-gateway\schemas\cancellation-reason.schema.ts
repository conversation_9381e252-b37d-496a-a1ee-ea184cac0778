import * as mongoose from 'mongoose';
import * as jsonMongo from '@meanie/mongoose-to-json';
import {  CANCELLATION_REASON_COLLECTION_NAME } from './constants';

const Schema = mongoose.Schema;

export const CancellationReasonSchema = new Schema(
    {
        code: String,
        reasonText: String,
        status: String,
    },
    {
        collection: CANCELLATION_REASON_COLLECTION_NAME,
        timestamps: true,
    },
).plugin(jsonMongo);
