import { Controller, Post, Body, Query, Get, Param, Delete, Headers, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ServiceMongoService } from './service-mongo.service';
import { plainToClass } from 'class-transformer';
import { CreateServiceDTO } from './dto/create-service.dto';
import { OutCreateServiceDTO } from './dto/out-create-service.dto';
import { UpdateServiceDTO } from './dto/update-service.dto';
import { ValidReExamDateDto } from './dto/valid-re-exam-date.dto';
import { SearchServiceDto } from './dto/search-service.dto';
import { PackageDetailQueryDto } from './dto/package-detail-query.dto';
import { HeadersDto } from '../common/base/headers.dto';
import { DoctorDetailQueryDto } from './dto/doctor-detail-query.dto';
import { SearchDtoPipe } from 'src/common/pipe/search-dto.pipe';

@Controller('mongo/service')
@ApiTags('Dịch vụ - Quản lý D<PERSON>ch vụ trên MongoDB')
export class ServiceMongoController {
    constructor(private readonly serviceMongoService: ServiceMongoService) {}

    @Post('create')
    async create(@Query() createServiceDTO: CreateServiceDTO): Promise<OutCreateServiceDTO> {
        return plainToClass(OutCreateServiceDTO, await this.serviceMongoService.create(createServiceDTO));
    }

    @Get('view/:id')
    async findById(@Param('id') id: string): Promise<any> {
        return this.serviceMongoService.findOne(id);
    }

    @Post('update')
    async update(@Query() updateServiceDTO: UpdateServiceDTO): Promise<any> {
        return this.serviceMongoService.updateOne(updateServiceDTO);
    }

    @Get('get-all-by-hospital')
    async find(@Query('hospitalId') hospitalId: string): Promise<any> {
        return this.serviceMongoService.find(hospitalId);
    }

    @Delete('delete/:id')
    async deleteById(@Param('id') id: string): Promise<any> {
        return this.serviceMongoService.deleteById(id);
    }

    @Post('valid-re-exam-date')
    @HttpCode(HttpStatus.OK)
    async validReExamDate(@Headers('partnerid') partnerId: string, @Body() formData: ValidReExamDateDto) {
        return this.serviceMongoService.validDayOff(partnerId, formData);
    }

    @Post('search')
    async search(
        @Body() body: SearchServiceDto,
        @Headers() headers: any,
    ): Promise<any> {
        return this.serviceMongoService.search122(body, headers);
    }

    @Get('doctor-telemed')
    async getDoctorTelemed(@Query() query: SearchServiceDto, @Headers() headers: any): Promise<any> {
        return this.serviceMongoService.getDoctorTelemed(query, headers);
    }

    @Post('package/list')
    async listPackage(@Body() body: SearchServiceDto, @Headers() headers: any): Promise<any> {
        return this.serviceMongoService.getAllPackage(body, headers);
    }

    @Get('package/list')
    async getListPackage(@Query() query: SearchServiceDto, @Headers() headers: any): Promise<any> {
        return this.serviceMongoService.getAllPackage(query, headers);
    }

    @Get('package')
    async getPackgeDetail(@Query() query: PackageDetailQueryDto): Promise<any> {
        return this.serviceMongoService.getPackageDetail(query);
    }


    @Post('doctor/list')
    async listDoctor(@Body() body: SearchServiceDto, @Headers() headers: any): Promise<any> {
        return this.serviceMongoService.getAllDoctor(body, headers);
    }

    @Get('doctor/list')
    async getListDoctor(@Query() query: SearchServiceDto, @Headers() headers: any): Promise<any> {
        return this.serviceMongoService.getAllDoctor(query, headers);
    }

    @Get('doctor')
    async getDoctorDetail(@Query() query: DoctorDetailQueryDto): Promise<any> {
        return this.serviceMongoService.getDoctorDetail(query);
    }

    @Get('search/search-keywords')
    async searchKeywords(@Headers() headers: HeadersDto): Promise<any> {
        return this.serviceMongoService.searchKeywords(headers);
    }
}
