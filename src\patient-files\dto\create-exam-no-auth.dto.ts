import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDateString, IsNotEmpty, IsOptional } from 'class-validator';
import { createHealthIndexDTO } from './health-index.dto';

export class createExamNoAuthDTO extends createHealthIndexDTO {
    @ApiProperty()
    @IsNotEmpty()
    name: string;

    @ApiProperty()
    @IsNotEmpty()
    userId: string;

    @ApiProperty()
    @IsDateString(
        { strict: true },
        {
            message: 'Thông tin ngày khám. ISOString',
        },
    )
    @IsNotEmpty()
    dateExamination: string;

    @IsOptional()
    @Transform(value => `${value}`.trim())
    readonly description: string;

    @IsNotEmpty()
    patientId: string;

    @IsOptional()
    @IsNotEmpty()
    files: [string];

    @ApiProperty()
    @IsOptional()
    hospitalName: string;

    @IsOptional()
    source: string;
}