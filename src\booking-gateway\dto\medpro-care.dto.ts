import { Transform } from 'class-transformer';
import { ArrayMaxSize, IsArray, IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class MedproCareDto {
    @IsNotEmpty()
    @Transform(value => `${value}`.trim())
    name: string
    
    @IsNotEmpty()
    @Transform(value => `${value}`.trim())
    id: string
    
    @IsOptional()
    @IsArray()
    @ArrayMaxSize(1)
    addonServices: MedproCareAddonService[]
    
    @IsOptional()
    @Transform(value => Boolean(value))
    note: string
}

export class MedproCareAddonService {
    @IsNotEmpty()
    @Transform(value => `${value}`.trim())
    name: string
    
    @IsNotEmpty()
    @Transform(value => `${value}`.trim())
    id: string
    
    @IsOptional()
    @Transform(value => `${value}`.trim())
    description: string
    
    @IsOptional()
    @Transform(value => `${value}`.trim())
    currency: string
    
    @IsOptional()
    @Transform(value => `${value}`.trim())
    duration: string
    
    @IsOptional()
    @Transform(value => `${value}`.trim())
    price: number
    
    @IsOptional()
    @Transform(value => `${value}`.trim())
    originalPrice: number
}