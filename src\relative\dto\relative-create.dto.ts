import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';

export class RelativeCreateDto {
    @ApiProperty({ description: 'code' })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'Code không được bỏ trống!' })
    code: string;

    @ApiProperty({ description: 'name' })
    @Transform(value => `${value}`.trim())
    @IsNotEmpty({ message: 'Name không được bỏ trống!' })
    name: string;

    @ApiProperty({ description: 'status' })
    @Transform(value => Number(value))
    @IsNotEmpty({ message: 'Trạng thái không được bỏ trống!' })
    status: number;

    @ApiProperty({ description: 'image url' })
    @Transform(value => `${value}`.trim())
    image?: string;
}
