import { IsNotEmpty, IsString, IsArray, ValidateNested, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class CancelBookingDto {
    @IsArray()
    @IsString({ each: true })
    @IsNotEmpty({ each: true })
    reasonIds: string[];

    @IsOptional()
    @IsString()
    otherContent?: string;

    @IsOptional()
    @IsString()
    bankCode?: string;

    @IsOptional()
    @IsString()
    bankName?: string;

    @IsOptional()
    @IsString()
    bankNumber?: string;

    @IsOptional()
    @IsString()
    accountName?: string;
}
