import { LOG_SERVICE_EVENT } from 'src/audit-log/constant';
import { ISignInProvider } from './../user/interfaces/sign-in-provider.interface';
import { UrlConfigService } from './../config/config.url.service';
import { ClientUtilService } from './../config/client-util.service';
import { Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { IBooking } from 'src/booking-gateway/intefaces/booking.inteface';
import { ICancelReservations } from 'src/booking-gateway/intefaces/cancel-reservations.interface';
import { IHospitalFee } from 'src/booking-gateway/intefaces/hospital-fee.inteface';
import { IMailReceiver } from 'src/booking-gateway/intefaces/mail-receiver.interface';
import { IPayment } from 'src/booking-gateway/intefaces/payment.inteface';
import { IRetryTransaction } from 'src/booking-gateway/intefaces/retry-transactions.interface';
import {
    BOOKING_COLLECTION_NAME,
    PAYMENT_COLLECTION_NAME,
    HOSPITAL_FEE_COLLECTION_NAME,
    RETRY_TRASACTION_COLLECTION_NAME,
    CANCEL_RESERVATIONS_COLLECTION_NAME,
    MAIL_RECEIVER_COLLECTION_NAME,
} from 'src/booking-gateway/schemas/constants';
import { PKH_PATIENT_CONNECTION } from 'src/config/pkhPatientConnection';
import { ICountry } from 'src/country-mongo/interfaces/country.interface';
import { COUNTRY_COLLECTION_NAME } from 'src/country-mongo/schemas/constants';
import { IEvent } from 'src/event/intefaces/event.inteface';
import { EVENT_COLLECTION_NAME, NOTIFICATION_COLLECTION } from 'src/event/schemas/constants';
import { IFeature } from 'src/feature-mongo/interfaces/feature.interface';
import { FEATURE_COLLECTION_NAME } from 'src/feature-mongo/schemas/constants';
import { IHospital } from 'src/hospital-mongo/interfaces/hospital.interface';
import { HOSPITAL_COLLECTION_NAME } from 'src/hospital-mongo/schemas/constants';
import { IPartnerConfig } from 'src/partner-config/intefaces/partner-config.inteface';
import { PARTNER_CONFIG_COLLECTION_NAME } from 'src/partner-config/schemas/constants';
import { IPatientVersion } from 'src/patient-mongo/intefaces/patient-version.inteface';
import { IPatient } from 'src/patient-mongo/intefaces/patient.inteface';
import { IPatientSort } from 'src/patient-mongo/intefaces/patients-sort.interface';
import { PATIENT_SORT_COLLECTION_NAME, PATIENT_COLLECTION_NAME, PATIENT_VERSION_COLLECTION_NAME } from 'src/patient-mongo/schemas/constants';
import { IPushDeviceError } from 'src/push-device/intefaces/push-device-error.interface';
import { IPushDevice } from 'src/push-device/intefaces/push-device.inteface';
import { PUSH_DEVICE_COLLECTION_NAME, PUSH_DEVICE_ERROR_COLLECTION_NAME } from 'src/push-device/schemas/constants';
import { IUser } from 'src/user/interfaces/user.interface';
import { SIGNIN_PROVIDER_COLLECTION_NAME, USER_COLLECTION_NAME } from 'src/user/schemas/constants';
import { TopicMailer } from './topic';
import { get } from 'lodash';
import { MAIL_PROCESS_COLLECTION, MAIL_TEMPLATE_COLLECTION, ProcessStatus } from './schema/constant';
import { IMailTemplate } from './interface/mail-template.interface';
import * as moment from 'moment';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import { GlobalSettingService } from 'src/global-setting/global-setting.service';
import { UtilService } from 'src/config/util.service';
import { IMailProcess } from './interface/mail-process.interface';
import { ConfigRepoService } from 'src/config/config.repo.service';
import validator from 'validator';
import { ClientNatsService } from 'src/config/client-nats.service';

@Injectable()
export class MailService {
    private readonly api: string;

    constructor(
        @InjectModel(PARTNER_CONFIG_COLLECTION_NAME) private partnerConfigModel: Model<IPartnerConfig>,
        @InjectModel(PUSH_DEVICE_COLLECTION_NAME) private pushDeviceModel: Model<IPushDevice>,
        @InjectModel(EVENT_COLLECTION_NAME) private eventModel: Model<IEvent>,
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(PAYMENT_COLLECTION_NAME) private paymentModel: Model<IPayment>,
        @InjectModel(USER_COLLECTION_NAME) private userModel: Model<IUser>,
        @InjectModel(HOSPITAL_FEE_COLLECTION_NAME) private hospitalFeeModel: Model<IHospitalFee>,
        @InjectModel(HOSPITAL_COLLECTION_NAME) private hospitalModel: Model<IHospital>,
        @InjectModel(COUNTRY_COLLECTION_NAME) private readonly countryModel: Model<ICountry>,
        @InjectModel(PATIENT_SORT_COLLECTION_NAME) private readonly patientSortModel: Model<IPatientSort>,
        @Inject(PKH_PATIENT_CONNECTION) private readonly pkhPatientKnex,
        @InjectModel(NOTIFICATION_COLLECTION) private notificationModel: Model<IEvent>,
        @InjectModel(RETRY_TRASACTION_COLLECTION_NAME) private readonly retryTransactionModel: Model<IRetryTransaction>,
        @InjectModel(PATIENT_COLLECTION_NAME) private readonly patientModel: Model<IPatient>,
        @InjectModel(PUSH_DEVICE_ERROR_COLLECTION_NAME) private readonly pushDeviceErrorModel: Model<IPushDeviceError>,
        @InjectModel(CANCEL_RESERVATIONS_COLLECTION_NAME) private readonly cancelReservationModel: Model<ICancelReservations>,
        @InjectModel(MAIL_RECEIVER_COLLECTION_NAME) private readonly mailReceiveModel: Model<IMailReceiver>,
        @InjectModel(PATIENT_VERSION_COLLECTION_NAME) private readonly patientVersionModel: Model<IPatientVersion>,
        @InjectModel(FEATURE_COLLECTION_NAME) private featureModel: Model<IFeature>,
        @InjectModel(MAIL_TEMPLATE_COLLECTION) private mailTemplateModel: Model<IMailTemplate>,
        @InjectModel(SIGNIN_PROVIDER_COLLECTION_NAME) private signInProviderModel: Model<ISignInProvider>,
        @InjectModel(MAIL_PROCESS_COLLECTION) private readonly mailProcessModel: Model<IMailProcess>,
        private readonly client: ClientUtilService,
        private readonly clientNats: ClientNatsService,
        private readonly urlConfig: UrlConfigService,
        private readonly global: GlobalSettingService,
        private readonly configRepoService: ConfigRepoService,
        private readonly utilService: UtilService,
    ) {
        this.api = `${this.urlConfig.mailerService}/api/mailer/publish`;
    }

    @OnEvent(TopicMailer.User.USER_REGISTER_SUCCESS)
    async onUserRegisterSuccess(userData: any): Promise<void> {
        const { userId, partnerId = '', appId = '' } = userData;
        const topic = TopicMailer.User.USER_REGISTER_SUCCESS;
        const [template, user, hospital, app] = await Promise.all([
            this.mailTemplateModel.findOne({ template: topic }, { propertyMedproMaps: true }),
            this.userModel.findById(userId).exec(),
            this.getHospital(partnerId),
            this.getHospital(appId),
        ]);
        if (!user || !template?.propertyMedproMaps) {
            return;
        }
        let formData = this.initFormData();
        const propertyMedproMaps = JSON.parse(template?.propertyMedproMaps);
        formData = this.getFormDataAppAndHospital(formData, propertyMedproMaps, hospital, app);
        formData = await this.getFormDataUser(formData, propertyMedproMaps, user.toObject());

        try {
            await this.client.post(this.api, formData, {
                topic,
            });
        } catch (error) {
            await this.addMailToWaitingWhenFail(topic, formData);
        }
    }

    @OnEvent(TopicMailer.User.USER_CREATE_PATIENT_SUCCESS)
    async onUserCreatePatientSuccess(userData: any): Promise<void> {
        const { userId, patientId, partnerId = '', appId = '' } = userData;
        const topic = TopicMailer.User.USER_CREATE_PATIENT_SUCCESS;
        const [template, user, patient, hospital, app] = await Promise.all([
            this.mailTemplateModel.findOne({ template: topic }, { propertyMedproMaps: true }),
            this.userModel.findById(userId).exec(),
            this.patientModel.findOne({ id: patientId }).exec(),
            this.getHospital(partnerId),
            this.getHospital(appId),
        ]);
        if (!user || !patient || !template?.propertyMedproMaps) {
            return;
        }
        let formData = this.initFormData();
        const propertyMedproMaps = JSON.parse(template?.propertyMedproMaps);
        formData = this.getFormDataAppAndHospital(formData, propertyMedproMaps, hospital, app);
        formData = await this.getFormDataUser(formData, propertyMedproMaps, user.toObject());
        formData = await this.getFormDataPatient(formData, propertyMedproMaps, patient.toObject());
        try {
            await this.client.post(this.api, formData, {
                topic,
            });
        } catch (error) {
            await this.addMailToWaitingWhenFail(topic, formData);
        }
    }

    @OnEvent(TopicMailer.User.USER_BOOKING_RESERVE_SUCCESS)
    async onUserBookingSuccess(bookingObjectId: string): Promise<void> {
        const topic = TopicMailer.User.USER_BOOKING_RESERVE_SUCCESS;
        const [template, booking, urlBookingShare] = await Promise.all([
            this.mailTemplateModel.findOne({ template: topic }, { propertyMedproMaps: true }),
            this.getBookingById(bookingObjectId),
            this.global.findByKeyAndRepoName('BOOKING_URL_SHARE'),
        ]);

        if (!booking || !template?.propertyMedproMaps) {
            return;
        }

        const [user, hospital, app] = await Promise.all([
            this.userModel.findById(booking.userId).exec(),
            this.getHospital(booking.partnerId),
            this.getHospital(booking.appId),
        ]);

        const bookingObj = booking.toObject();

        if (!user) {
            return;
        }

        let formData = this.initFormData();
        const propertyMedproMaps = JSON.parse(template?.propertyMedproMaps);
        formData = this.getFormDataBooking(formData, bookingObj, propertyMedproMaps, urlBookingShare);
        formData = this.getFormDataAppAndHospital(formData, propertyMedproMaps, hospital, app);
        formData = await this.getFormDataUser(formData, propertyMedproMaps, user.toObject());

        try {
            await this.client.post(this.api, formData, {
                topic,
            });
        } catch (error) {
            await this.addMailToWaitingWhenFail(topic, formData);
        }
    }

    @OnEvent(TopicMailer.User.USER_UPDATE_BOOKING_SUCCESS)
    async onUserUpdateBookingSuccess(bookingObjectId: string): Promise<void> {
        const topic = TopicMailer.User.USER_UPDATE_BOOKING_SUCCESS;
        const [template, booking, urlBookingShare] = await Promise.all([
            this.mailTemplateModel.findOne({ template: topic }, { propertyMedproMaps: true }),
            this.getBookingById(bookingObjectId),
            this.global.findByKeyAndRepoName('BOOKING_URL_SHARE'),
        ]);

        if (!booking || !template?.propertyMedproMaps) {
            return;
        }

        const [user, hospital, app] = await Promise.all([
            this.userModel.findById(booking.userId).exec(),
            this.getHospital(booking.partnerId),
            this.getHospital(booking.appId),
        ]);

        const bookingObj = booking.toObject();

        if (!user) {
            return;
        }

        let formData = this.initFormData();
        const propertyMedproMaps = JSON.parse(template?.propertyMedproMaps);
        formData = this.getFormDataBooking(formData, bookingObj, propertyMedproMaps, urlBookingShare);
        formData = this.getFormDataAppAndHospital(formData, propertyMedproMaps, hospital, app);
        formData = await this.getFormDataUser(formData, propertyMedproMaps, user.toObject());

        try {
            await this.client.post(this.api, formData, {
                topic,
            });
        } catch (error) {
            await this.addMailToWaitingWhenFail(topic, formData);
        }
    }

    @OnEvent(TopicMailer.User.USER_BOOKING_CANCEL_SUCCESS)
    async onUserCancelBookingSuccess(bookingObjectId: string): Promise<void> {
        const topic = TopicMailer.User.USER_BOOKING_CANCEL_SUCCESS;
        const [template, booking, urlBookingShare] = await Promise.all([
            this.mailTemplateModel.findOne({ template: topic }, { propertyMedproMaps: true }),
            this.getBookingById(bookingObjectId),
            this.global.findByKeyAndRepoName('BOOKING_URL_SHARE'),
        ]);

        if (!booking || !template?.propertyMedproMaps) {
            return;
        }

        const [user, hospital, app] = await Promise.all([
            this.userModel.findById(booking.userId).exec(),
            this.getHospital(booking.partnerId),
            this.getHospital(booking.appId),
        ]);

        let bookingObj = booking.toObject();
        bookingObj = {
            ...bookingObj,
            canceledDate: moment(booking?.canceledDate).add(7, 'hours').format('HH:mm DD-MM-YYYY'),
        };

        let formData = this.initFormData();
        const propertyMedproMaps = JSON.parse(template?.propertyMedproMaps);
        formData = this.getFormDataBooking(formData, bookingObj, propertyMedproMaps, urlBookingShare);
        formData = this.mapKeyConfigToDataSendMail(formData, bookingObj?.cancellationReason, propertyMedproMaps, 'cancellation');
        formData = await this.getFormDataUser(formData, propertyMedproMaps, user.toObject());
        formData = this.getFormDataAppAndHospital(formData, propertyMedproMaps, hospital, app);

        try {
            await this.client.post(this.api, formData, {
                topic,
            });
        } catch (error) {
            await this.addMailToWaitingWhenFail(topic, formData);
        }
    }

    async onUserReExam(): Promise<void> {

    }

    @OnEvent(TopicMailer.Patient.PATIENT_CREATE_SUCCESS)
    async onPatientCreateSuccess(patientData: any): Promise<void> {
        const { userId, patientId, partnerId = '', appId = '' } = patientData;
        const topic = TopicMailer.Patient.PATIENT_CREATE_SUCCESS;
        const [template, user, patient, hospital, app] = await Promise.all([
            this.mailTemplateModel.findOne({ template: topic }, { propertyMedproMaps: true }),
            this.userModel.findById(userId).exec(),
            this.patientModel.findOne({ id: patientId }).exec(),
            this.getHospital(partnerId),
            this.getHospital(appId),
        ]);
        if (!user || !patient || !template?.propertyMedproMaps) {
            return;
        }
        let formData = this.initFormData();
        const propertyMedproMaps = JSON.parse(template?.propertyMedproMaps);
        formData = this.getFormDataAppAndHospital(formData, propertyMedproMaps, hospital, app);
        formData = await this.getFormDataUser(formData, propertyMedproMaps, user.toObject());
        formData = await this.getFormDataPatient(formData, propertyMedproMaps, patient.toObject());
        try {
            await this.client.post(this.api, formData, {
                topic,
            });
        } catch (error) {
            await this.addMailToWaitingWhenFail(topic, formData);
        }
    }

    @OnEvent(TopicMailer.Patient.PATIENT_UPDATE_SUCCESS)
    async onPatientUpdateBookingSuccess(bookingObjectId: string): Promise<void> {
        const topic = TopicMailer.Patient.PATIENT_UPDATE_SUCCESS;
        const [template, booking, urlBookingShare] = await Promise.all([
            this.mailTemplateModel.findOne({ template: topic }, { propertyMedproMaps: true }),
            this.getBookingById(bookingObjectId),
            this.global.findByKeyAndRepoName('BOOKING_URL_SHARE'),
        ]);

        if (!booking || !template?.propertyMedproMaps) {
            return;
        }

        const [user, hospital, app] = await Promise.all([
            this.userModel.findById(booking.userId).exec(),
            this.getHospital(booking.partnerId),
            this.getHospital(booking.appId),
        ]);

        let bookingObj = booking.toObject();
        bookingObj = {
            ...bookingObj,
            canceledDate: moment(booking?.canceledDate).add(7, 'hours').format('HH:mm DD-MM-YYYY'),
        };

        let formData = this.initFormData();
        const propertyMedproMaps = JSON.parse(template?.propertyMedproMaps);
        formData = this.getFormDataBooking(formData, bookingObj, propertyMedproMaps, urlBookingShare);
        formData = this.mapKeyConfigToDataSendMail(formData, bookingObj?.cancellationReason, propertyMedproMaps, 'cancellation');
        formData = await this.getFormDataUser(formData, propertyMedproMaps, user.toObject());
        formData = this.getFormDataAppAndHospital(formData, propertyMedproMaps, hospital, app);

        try {
            await this.client.post(this.api, formData, {
                topic,
            });
        } catch (error) {
            await this.addMailToWaitingWhenFail(topic, formData);
        }
    }

    @OnEvent(TopicMailer.Patient.PATIENT_BOOKING_RESERVE_SUCCESS)
    async onPatientBookingSuccess(bookingObjectId: string): Promise<void> {
        const topic = TopicMailer.Patient.PATIENT_BOOKING_RESERVE_SUCCESS;
        const [template, booking, urlBookingShare] = await Promise.all([
            this.mailTemplateModel.findOne({ template: topic }, { propertyMedproMaps: true }),
            this.getBookingById(bookingObjectId),
            this.global.findByKeyAndRepoName('BOOKING_URL_SHARE'),
        ]);

        if (!booking || !template?.propertyMedproMaps) {
            return;
        }

        const [user, hospital, app] = await Promise.all([
            this.userModel.findById(booking.userId).exec(),
            this.getHospital(booking.partnerId),
            this.getHospital(booking.appId),
        ]);

        const bookingObj = booking.toObject();

        if (!user) {
            return;
        }

        let formData = this.initFormData();
        const propertyMedproMaps = JSON.parse(template?.propertyMedproMaps);
        formData = this.getFormDataBooking(formData, bookingObj, propertyMedproMaps, urlBookingShare);
        formData = this.getFormDataAppAndHospital(formData, propertyMedproMaps, hospital, app);
        formData = await this.getFormDataUser(formData, propertyMedproMaps, user.toObject());

        try {
            // await this.client.post(this.api, formData, {
            //     topic,
            // });
            await this.clientNats.request('mail.publish', formData, { topic }, this.api)
        } catch (error) {
            await this.addMailToWaitingWhenFail(topic, formData);
        }
    }


    @OnEvent(TopicMailer.Patient.PATIENT_BOOKING_CANCEL_SUCCESS)
    async onPatientCancelBookingSuccess(bookingObjectId: string): Promise<void> {
        const topic = TopicMailer.Patient.PATIENT_BOOKING_CANCEL_SUCCESS;
        const [template, booking, urlBookingShare] = await Promise.all([
            this.mailTemplateModel.findOne({ template: topic }, { propertyMedproMaps: true }),
            this.getBookingById(bookingObjectId),
            this.global.findByKeyAndRepoName('BOOKING_URL_SHARE'),
        ]);

        if (!booking || !template?.propertyMedproMaps) {
            return;
        }

        const [user, hospital, app] = await Promise.all([
            this.userModel.findById(booking.userId).exec(),
            this.getHospital(booking.partnerId),
            this.getHospital(booking.appId),
        ]);

        let bookingObj = booking.toObject();
        bookingObj = {
            ...bookingObj,
            canceledDate: moment(booking?.canceledDate).add(7, 'hours').format('HH:mm DD-MM-YYYY'),
        };

        let formData = this.initFormData();
        const propertyMedproMaps = JSON.parse(template?.propertyMedproMaps);
        formData = this.getFormDataBooking(formData, bookingObj, propertyMedproMaps, urlBookingShare);
        formData = this.mapKeyConfigToDataSendMail(formData, bookingObj?.cancellationReason, propertyMedproMaps, 'cancellation');
        formData = this.mapKeyConfigToDataSendMail(formData, user.toObject(), propertyMedproMaps, 'user');
        formData = this.getFormDataAppAndHospital(formData, propertyMedproMaps, hospital, app);

        try {
            await this.client.post(this.api, formData, {
                topic,
            });
        } catch (error) {
            await this.addMailToWaitingWhenFail(topic, formData);
        }
    }

    async getHospital(partnerId: string): Promise<IHospital> {
        return this.hospitalModel.findOne({ partnerId }, { name: true, address: true, partnerId: true }).exec();
    }

    async getBookingById(bookingObjectId: string): Promise<IBooking> {
        return this.bookingModel.findById(bookingObjectId, {
            service: true, serviceId: true, subject: true, subjectId: true, doctor: true, sectionId: true, section: true,
            room: true, date: true, patient: true, partnerId: true, appId: true, roomId: true, insuranceType: true,
            transactionId: true, insuranceCode: true, sequenceNumber: true, checkInRoom: true, bookingNote: true,
            insuranceChoice: true, patientVersionId: true, patientId: true, status: true, noPayment: true,
            bookingCodeV1: true, syncStatus: true, bookingCode: true, serviceInfo: true, bookingInternalId: true,
            addonServices: true, id: true, treeId: true, implementAgent: true, implementLocation: true, userId: true,
            smsCode: true, cancellationReason: true, canceledDate: true,
        })
            .populate('service')
            .populate({ path: 'subject', select: 'name' })
            .populate({ path: 'room', select: 'name' })
            .populate({ path: 'doctor', select: 'name' })
            .populate({ path: 'section', select: 'name' })
            .populate({ path: 'cancellationReason', select: 'reasonText' })
            .populate({
                path: 'patient', select: {
                    name: true, surname: true, sex: true,
                    birthdate: true, birthyear: true, code: true,
                    email: true,
                },
            })
            .exec();
    }

    getFormDataBooking(formData: any, bookingObj: any, propertyMedproMaps: any, urlBookingShare: string): any {
        const patient = bookingObj.patient;
        bookingObj = {
            ...bookingObj,
            patient: {
                ...patient,
                fullname: `${patient.surname} ${patient.name}`,
            },
            date: moment(bookingObj.date).add(7, 'hours').format('HH:mm DD-MM-YYYY'),
            bookingUrl: `${urlBookingShare}`.replace('{smsCode}', bookingObj?.smsCode),
        };
        formData = this.mapKeyConfigToDataSendMail(formData, bookingObj, propertyMedproMaps, 'booking');
        formData = this.mapKeyConfigToDataSendMail(formData, bookingObj?.patient, propertyMedproMaps, 'patient');
        formData = this.mapKeyConfigToDataSendMail(formData, bookingObj?.subject, propertyMedproMaps, 'subject');
        formData = this.mapKeyConfigToDataSendMail(formData, bookingObj?.service, propertyMedproMaps, 'service');
        formData = this.mapKeyConfigToDataSendMail(formData, bookingObj?.doctor, propertyMedproMaps, 'doctor');
        return formData;
    }

    getFormDataAppAndHospital(formData: any, propertyMedproMaps: any, hospital: IHospital, app?: IHospital): any {
        formData = this.mapKeyConfigToDataSendMail(formData, hospital.toObject(), propertyMedproMaps, 'hospital');
        if (app) {
            formData = this.mapKeyConfigToDataSendMail(formData, app.toObject(), propertyMedproMaps, 'app');
        } else {
            formData = this.mapKeyConfigToDataSendMail(formData, hospital.toObject(), propertyMedproMaps, 'app');
        }
        return formData;
    }

    async getFormDataUser(formData: any, propertyMedproMaps: any, userObj: any): Promise<any> {
        const siginProvider = await this.signInProviderModel.findOne({ user: userObj._id, type: 'firebase' }, { email: true }).exec();
        userObj = {
            ...userObj,
            email: validator.isEmail(siginProvider?.email) ?  siginProvider.email : '',
        };
        formData = this.mapKeyConfigToDataSendMail(formData, userObj, propertyMedproMaps, 'user');

        return formData;
    }

    async getFormDataPatient(formData: any, propertyMedproMaps: any, patient: any): Promise<any> {
        patient =  {
            ...patient,
            fullname: `${patient.surname} ${patient.name}`,
            birthdate: moment(patient?.birthdate) ? moment(patient.birthdate).format('DD/MM/YYYY') : `${patient.birthyear}`,
        };
        formData = this.mapKeyConfigToDataSendMail(formData, patient, propertyMedproMaps, 'patient');
        return formData;
    }

    async addMailToWaitingWhenFail(topic: string, formData: any): Promise<any> {
        await (new this.mailProcessModel({
            ...formData,
            topic,
            status: ProcessStatus.WAITING,
            data: formData,
        })).save();
    }

    initFormData(): any {
        return {
            repo: this.configRepoService.getRepoName(),
        };
    }

    mapKeyConfigToDataSendMail(formData: any, data?: any, propertyConfig?: any, collection?: string): Promise<any> {
        if (!data || !propertyConfig) {
            return formData;
        }
        let overrideData: any;
        if (collection) {
            for (const key of Object.keys(data)) {
                overrideData = {
                    ...overrideData,
                    ...{ [`${collection}.${key}`]: get(data, key) },
                };
            }
        }

        for (const key of Object.keys(propertyConfig)) {
            const propKey = get(propertyConfig, key, '');
            const value = get(overrideData || data, `${propKey}`);
            if (value) {
                formData = { ...formData, ...{ [`${key}`]: value } };
            }
        }
        return formData;
    }
}
