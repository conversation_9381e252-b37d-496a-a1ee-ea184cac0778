import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { BOOKING_COLLECTION_NAME, PAYMENT_COLLECTION_NAME } from '../booking-gateway/schemas/constants';
import { InjectModel } from '@nestjs/mongoose';
import { PATIENT_CODE_COLLECTION_NAME, PATIENT_COLLECTION_NAME } from '../patient-mongo/schemas/constants';
import { IBooking } from '../booking-gateway/intefaces/booking.inteface';
import { IPatientCodes } from '../patient-mongo/intefaces/patient-codes.inteface';
import { IPaymentMethodMapping } from './interfaces/IPaymentMethodMapping';
import * as moment from 'moment';
import { PAYMENT_METHOD_MAPPINGS_COLLECTION } from './schemas/constants';
import { IPayment } from '../booking-gateway/intefaces/payment.inteface';

@Injectable()
export class Nhidong1Service {
    constructor(
        @InjectModel(BOOKING_COLLECTION_NAME) private bookingModel: Model<IBooking>,
        @InjectModel(PATIENT_CODE_COLLECTION_NAME) private patientCodeModel: Model<IPatientCodes>,
        @InjectModel(PAYMENT_COLLECTION_NAME) private paymentModel: Model<IPayment>,
        @InjectModel(PAYMENT_METHOD_MAPPINGS_COLLECTION) private paymentMethodMappingModel: Model<IPaymentMethodMapping>,
    ) {}

    async getBookingByDate(queryDate: string): Promise<any> {
        const startDate = moment(queryDate, 'DDMMYYYY').startOf('day');
        const endDate = moment(queryDate, 'DDMMYYYY').endOf('day');

        if (!startDate.isValid() || !endDate.isValid()) {
            throw new HttpException({ message: 'Ngày không hợp lệ' }, HttpStatus.BAD_REQUEST);
        }

        const bookings = await this.bookingModel
            .find({
                partnerId: 'nhidong1',
                date: {
                    $gte: startDate.toDate(),
                    $lte: endDate.toDate(),
                },
                status: { $gt: 0 },
            })
            .populate('patient')
            .populate('room')
            .populate('service')
            .populate('subject')
            .exec();

        const allPaymentMethodMapping = await this.paymentMethodMappingModel.find({}).exec();
        const paymentMethodMapping = allPaymentMethodMapping.reduce((map, obj) => {
            map[obj.v2] = obj.v1;
            return map;
        }, {});

        const promises = bookings.map(async b => {
            const bObj = b.toObject();
            const {
                date,
                partnerId,
                transactionId,
                patient,
                appId,
                platform,
                sequenceNumber,
                insuranceCode,
                bookingCodeV1,
                syncPatientIdV1,
                patientMSBNV1,
                syncUserIdV1,
                syncBookingIdV1,
                room,
                service,
                subject,
            } = bObj;

            const payment = await this.paymentModel.findOne({ transactionId: b.transactionId }).exec();
            const { amount, status, transferFee, totalFee, medproFee, paymentMethod, subTotal, chargeFeeInfo } = payment;

            const {
                name,
                surname,
                sex,
                address,
                city_id,
                district_id,
                ward_id,
                mobile,
                email,
                id,
                code,
                birthdate,
                birthyear,
                cmnd,
                dantoc_id,
                country_code,
                profession_id,
                relation,
            } = patient;
            const patientCodeDocument = await this.patientCodeModel.findOne({ appId, partnerId, patientId: id }).exec();
            const { patientCode } = patientCodeDocument || {};
            const { relative_email, relative_name, relative_mobile, relative_type_id } = relation || {};

            return {
                // id:
                user_id: syncUserIdV1,
                transaction_code_tt: transactionId,
                method_id: paymentMethodMapping[paymentMethod], // 1: thẻ, 2 visa, 3 atm, 4,5,6, 7 momo;
                // transdate: '20211222074311', //
                amount,
                amount_original: subTotal, // tiền khám
                amount_service: medproFee, //
                amount_gate: transferFee, //
                rate_percent: chargeFeeInfo.rate_percent,
                rate_const: chargeFeeInfo.rate_const,
                // card_code: 'MOMO',
                status: payment.status,
                // refund_status: 0, //
                // hoadon_info: '',
                booking: [
                    {
                        id: syncBookingIdV1,
                        transaction_code_gd: bookingCodeV1,
                        user_id: syncUserIdV1,
                        booking_date: moment(date).format('YYYY-MM-DD'),
                        booking_phone: mobile,
                        booking_number: sequenceNumber,
                        bv_time: moment(date).format('HH:MM'),
                        // nd1_schedule_id: 4106,
                        // nd1_booking_time_id: 16908,
                        // nd1_patient_id: 19769,
                        // nd1_payment_id: 33430,
                        email,
                        platform,
                        app: appId,
                        // bhyt_accept: 0, //dịch vụ có y tế hay?
                        // rebooking_code: null,
                        status: b.status,
                        // refund_status: 0,
                        // is_deleted: 0,
                        // is_accepted: 0,
                        // is_register_msbn: 0,
                        patient: {
                            id: syncPatientIdV1,
                            bv_id: patientMSBNV1,
                            old_bv_id: null,
                            medpro_id: patient.id,
                            name,
                            surname,
                            sex,
                            bhyt: insuranceCode,
                            cmnd,
                            mobile,
                            email,
                            birthdate,
                            birthyear,
                            address,
                            city_id,
                            district_id,
                            ward_id,
                            dantoc_id,
                            country_code,
                            profession_id,
                            // note: null,
                            // is_medpro: 0,
                            // origin_id: null,
                            relative: {
                                // id: 19887,
                                // nd1_patient_id: 19769,
                                name: relative_name,
                                mobile: relative_mobile,
                                email: relative_email,
                                nd1_relative_type_id: relative_type_id, // map
                            },
                        },
                        room: {
                            // id: 630,
                            bv_id: room?.code,
                            name: room?.name,
                            price: service?.price,
                            // nd1_service_id: 4,
                            // nd1_section_id: 2,
                            // nd1_subject_id: 8,
                            description: room?.description,
                            bhyt: null,
                            is_old: 0,
                            subject: {
                                // id: 8,
                                bv_id: subject?.code,
                                name: subject?.name,
                                description: subject?.description,
                                status: subject?.status,
                            },
                            service: {
                                // id: 4,
                                name: service?.name,
                                price: service?.price,
                                // pay: 500000,
                                description: service?.description,
                            },
                        },
                        booking_time: {
                            id: 16908,
                            from: 7.5,
                            to: 8.0,
                            number: null,
                            number_from: 10,
                            number_to: 14,
                            number_skip: null,
                            step: 2,
                            nd1_room_id: 630,
                            nd1_schedule_id: 4106,
                            max_slot: 3,
                            time_per_slot: 5,
                            buoi: 1,
                            is_old: 0,
                            ca: 'C1',
                        },
                    },
                ],
            };
        });

        return Promise.all(promises);
    }
}
