import { ApiProperty } from '@nestjs/swagger';
import { IsMobilePhone, IsNotEmpty } from 'class-validator';
import { OtpDto } from './otp.dto';

export class OtpMessageServiceDto extends OtpDto {
    @ApiProperty({ description: 'Vui lòng nhập clientId!', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập clientId!' })
    @IsMobilePhone('vi-VN', { strictMode: false }, { message: 'clientId không đúng định dạng. Vui lòng kiểm tra lại!' })
    clientId: string;

    @ApiProperty({ description: 'Vui lòng nhập hospitalName!', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập hospitalName!' })
    readonly hospitalName?: string;

    @ApiProperty({ description: 'Vui lòng nhập partner!', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập partner!' })
    readonly partner?: string;

    @ApiProperty({ description: 'Vui lòng nhập accessKey!', required: true, type: String })
    @IsNotEmpty({ message: 'Vui lòng nhập accessKey!' })
    readonly accessKey?: string;
}
