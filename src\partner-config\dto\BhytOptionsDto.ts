import {ApiProperty} from '@nestjs/swagger';
import {Transform} from 'class-transformer';
import {IsNotEmpty, IsNumber} from 'class-validator';

export class BhytOptionsDto {
    @ApiProperty({ required: false, description: 'title'})
    @IsNotEmpty({ message: 'title không được để trống!'})
    @Transform(value => `${value}`.trim())
    title: string;

    @ApiProperty({ required: false, description: 'value'})
    @IsNotEmpty({ message: 'value không được để trống!'})
    @IsNumber()
    @Transform(value => Number(value))
    value: number;
}
