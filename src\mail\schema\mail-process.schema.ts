import * as mongoose from 'mongoose';
import { MAIL_PROCESS_COLLECTION } from './constant';
import * as uuid from 'uuid';

const Schema = mongoose.Schema;

export const MailProcessSchema = new Schema({
    id: { type: String, default: uuid.v4().replace(/-/g, '') },
    topic: { type: String },
    appId: { type: String },
    partnerId: { type: String },
    userId: { type: String },
    status: { type: String },
    data: { type: Schema.Types.Mixed },
    repo: { type: String },
}, {
    collection: MAIL_PROCESS_COLLECTION,
    timestamps: true,
});
