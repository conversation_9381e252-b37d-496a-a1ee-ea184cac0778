import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsDateString, IsNotEmpty, ValidateIf } from 'class-validator';

export class GetAllPayment {
    @ApiProperty()
    @IsNotEmpty()
    price: number;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    treeId?: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    serviceId?: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    subjectId?: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    roomId?: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    doctorId?: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    @IsDateString(
        { strict: true },
        {
            message: 'Thông tin thời gian đặt khám. ISOString',
        },
    )
    @ValidateIf(o => o.bookingDate)
    bookingDate?: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    patientId: string;

    @ApiProperty({
        description: 'addonServices',
        required: false,
    })
    addonServices?: string;

    @ApiProperty()
    @Transform(value => `${value}`.trim())
    bookingId?: string;

    @ApiProperty()
    @Transform(value => Number(value))
    groupId?: number;

    shareToPay?: boolean;

    @ApiProperty()
    medproCareServiceIds?: string[];
}
